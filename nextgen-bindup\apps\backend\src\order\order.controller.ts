import {
  Controller,
  Get,
  Param,
  Post,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { OrderService } from './order.service';
import { GetOrdersQueryDto } from './dto/get-order.dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { Response } from 'express';

@UseGuards(AuthGuard)
@Controller('order')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Get('search/:siteId')
  async getOrders(
    @Param('siteId') siteId: string,
    @Query() query: GetOrdersQueryDto,
  ) {
    return await this.orderService.getOrders(siteId, query);
  }

  @Get('one/:id')
  async getOrderById(@Param('id') id: string) {
    return await this.orderService.findOrderById(+id);
  }

  @Post('mark-as-paid/:id')
  async markAsPaid(@Param('id') id: string) {
    return await this.orderService.markAsPaid(+id);
  }

  @Get('download/:siteId')
  async downloadCsv(
    @Param('siteId') siteId: string,
    @Query() query: GetOrdersQueryDto,
    @Res() res: Response,
  ) {
    const csvStream = await this.orderService.downloadCSV(siteId, query);
    res.header('Content-Type', 'text/csv');
    res.header(
      'Content-Disposition',
      `attachment; filename="orders_${siteId}_${new Date().toISOString().slice(0, 10)}.csv"`,
    );
    res.header('Cache-Control', 'no-cache');
    csvStream.pipe(res);
  }
}
