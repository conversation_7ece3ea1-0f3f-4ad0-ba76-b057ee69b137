import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateTemplatePagesTable1750649574429
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}template_pages`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'templatePageId',
        type: 'integer',
        isNullable: false,
        default: '0',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'templatePageId');
  }
}
