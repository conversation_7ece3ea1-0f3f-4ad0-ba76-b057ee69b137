import { CreateProjectReq, Project } from './dto/project.dto';
import { DataSource, Repository } from 'typeorm';
import { ProjectEntity } from './entities/project.entity';
export declare class ProjectService {
    private readonly dataSource;
    readonly projectRepo: Repository<ProjectEntity>;
    constructor(dataSource: DataSource);
    findById(id: number): Promise<ProjectEntity>;
    getProjectsByUser(userId: string): Promise<Project[]>;
    private groupProjectsWithFolders;
    createProject(userId: string, projectData: CreateProjectReq): Promise<ProjectEntity>;
    updateProject(id: number, data: Partial<ProjectEntity>): Promise<ProjectEntity>;
    deleteProject(id: number): Promise<boolean>;
}
