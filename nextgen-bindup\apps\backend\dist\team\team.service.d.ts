import { Repository } from 'typeorm';
import { TeamEntity } from './entities/team.entity';
export declare class TeamService {
    readonly teamRepo: Repository<TeamEntity>;
    constructor();
    findById(id: number): Promise<TeamEntity>;
    getAllByRootUserId(rootUserId: string): Promise<TeamEntity[]>;
    create(userId: string, data: TeamEntity): Promise<TeamEntity>;
    update(id: number, userId: string, data: Partial<TeamEntity>): Promise<TeamEntity>;
}
