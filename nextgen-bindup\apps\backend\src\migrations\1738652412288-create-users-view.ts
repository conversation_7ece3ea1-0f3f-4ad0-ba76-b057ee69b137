import { MigrationInterface, QueryRunner, View } from 'typeorm';

export class CreateUsersView1738652412288 implements MigrationInterface {
  VIEW_NAME: string = `${process.env.ENTITY_PREFIX || ''}users`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createView(
      new View({
        schema: process.env.DATABASE_SCHEMA,
        name: this.VIEW_NAME,
        expression: `
          select
            user_id as userId,
            email,
            identity_data->>'name' AS name,
            identity_data->>'avatar' AS avatar,
            identity_data->>'group' as group
          from auth.identities;
        `,
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropView(
      `${process.env.DATABASE_SCHEMA}.${this.VIEW_NAME}`,
    );
  }
}
