import { Injectable, UnauthorizedException } from '@nestjs/common';
import * as jose from 'jose';

@Injectable()
export class SiteAuthService {
  constructor() {}

  async validateTokenAndGetSite(token: string): Promise<{
    siteId: string;
  }> {
    try {
      const secret = new TextEncoder().encode('SHOP_SECRET_KEY');
      const { payload } = await jose.jwtVerify(token, secret);
      return {
        siteId: payload.siteId as string,
      };
    } catch (error) {
      console.error('Token validation error:', error.message);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  async generateToken(siteId: string): Promise<string> {
    const secret = new TextEncoder().encode('SHOP_SECRET_KEY');
    const jwt = await new jose.SignJWT({ siteId })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .sign(secret);
    return jwt;
  }
}
