"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jose = require("jose");
const version_history_service_1 = require("../version-history/version-history.service");
const user_info_entity_1 = require("../user-info/entities/user-info.entity");
const identity_service_1 = require("../identity/identity.service");
const user_info_service_1 = require("../user-info/user-info.service");
const user_info_enum_1 = require("../user-info/enum/user-info.enum");
const app_exception_1 = require("../common/exceptions/app.exception");
let AuthService = class AuthService {
    constructor(versionHistoryService, identityService, userInfoService) {
        this.versionHistoryService = versionHistoryService;
        this.identityService = identityService;
        this.userInfoService = userInfoService;
    }
    async createTempToken(userId, regDto) {
        const jwtPayload = {
            userId: userId,
            type: regDto.type,
            siteId: regDto.siteId,
            projectId: regDto.projectId,
            versionId: regDto.versionId,
        };
        const secret = new TextEncoder().encode(process.env.APP_JWT);
        const token = await new jose.SignJWT(jwtPayload)
            .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
            .setExpirationTime('30s')
            .sign(secret);
        const result = {
            token: token,
        };
        return result;
    }
    async verifyTempToken(token) {
        const secret = new TextEncoder().encode(process.env.APP_JWT);
        const { payload } = await jose.jwtVerify(token, secret);
        const payloadBody = payload;
        const newToken = await new jose.SignJWT({ userId: payloadBody.userId })
            .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
            .setExpirationTime('360d')
            .sign(secret);
        let siteVersion;
        if (payload.versionId) {
            siteVersion = await this.versionHistoryService.getSiteVersion(+payload.versionId);
        }
        const result = {
            token: newToken,
            userId: payloadBody.userId,
            type: payloadBody.type,
            siteId: payloadBody.siteId,
            projectId: payloadBody.projectId,
            siteVersion: siteVersion,
        };
        return result;
    }
    async verifyDesignEditorToken(token) {
        const secret = new TextEncoder().encode(process.env.APP_JWT);
        const { payload } = await jose.jwtVerify(token, secret);
        const payloadBody = payload;
        let siteVersion;
        if (payload.versionId) {
            siteVersion = await this.versionHistoryService.getSiteVersion(+payload.versionId);
        }
        const result = {
            token: token,
            userId: payloadBody.userId,
            type: payloadBody.type,
            siteId: payloadBody.siteId,
            projectId: payloadBody.projectId,
            siteVersion: siteVersion,
        };
        return result;
    }
    async signUp(input) {
        const identity = await this.identityService.createIdentity({
            email: input.email,
            password: input.password,
        });
        const userInfo = await this.userInfoService.userInfoRepo.findOneBy({
            userId: identity.user.userId,
        });
        const now = new Date();
        if (userInfo) {
            await this.userInfoService.userInfoRepo.update({
                userId: userInfo.userId,
            }, {
                name: input.name,
                updatedAt: now,
            });
        }
        else {
            const newUserInfo = new user_info_entity_1.UserInfoEntity();
            newUserInfo.userId = identity.user.userId;
            newUserInfo.name = input.name;
            newUserInfo.type = user_info_enum_1.UserType.INDIVIDUAL;
            newUserInfo.countryCode = user_info_enum_1.CountryCode.JAPAN;
            newUserInfo.createdAt = now;
            newUserInfo.updatedAt = now;
            await this.userInfoService.userInfoRepo.save(newUserInfo);
        }
        const jwtPayload = {
            userId: identity.user.userId,
            email: identity.user.email,
        };
        const accessToken = await this.createAccessToken(jwtPayload);
        return {
            accessToken,
            user: identity.user,
        };
    }
    async login(input) {
        const identity = await this.identityService.signInWithPassword(input.email, input.password);
        const jwtPayload = {
            userId: identity.user.userId,
            email: identity.user.email,
        };
        const accessToken = await this.createAccessToken(jwtPayload);
        return {
            accessToken,
            user: identity.user,
        };
    }
    async forgotPwd(input) {
        await this.identityService.forgotPasswordForEmail(input.email, input.redirectTo);
        return true;
    }
    async resetPwd(input) {
        await this.identityService.changePassword(input.email, input.password);
        return true;
    }
    async createAccessToken(payload) {
        const secret = new TextEncoder().encode(process.env.APP_JWT);
        const accessToken = await new jose.SignJWT(payload)
            .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
            .setExpirationTime('360d')
            .sign(secret);
        return accessToken;
    }
    async changePassword(email, data) {
        try {
            await this.identityService.signInWithPassword(email, data.currentPwd);
        }
        catch (e) {
            console.log(e);
            throw new app_exception_1.AppException('myaccount.change_pwd.error.current_pwd_invalid');
        }
        await this.identityService.changePassword(email, data.newPassword);
        return true;
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [version_history_service_1.VersionHistoryService,
        identity_service_1.IdentityService,
        user_info_service_1.UserInfoService])
], AuthService);
//# sourceMappingURL=auth.service.js.map