{"name": "debug-fabulous", "version": "0.0.4", "description": "visionmedia debug extensions rolled into one", "main": "index.js", "scripts": {"test": "mocha ./test/**/*test.js"}, "repository": {"type": "git", "url": "http://www.github.com/nmccready/debug-fabulous"}, "keywords": ["debug", "lazy", "lazy-eval"], "author": "<PERSON>", "license": "MIT", "dependencies": {"debug": "2.X", "lazy-debug-legacy": "0.0.X", "object-assign": "4.1.0"}, "devDependencies": {"babel-eslint": "7.X", "chai": "3.X", "eslint": "3.X", "hook-std": "0.X", "mocha": "3.X"}}