import { forwardRef, Module } from '@nestjs/common';
import { ProductController } from './product.controller';
import { ProductService } from './product.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductEntity } from './entities/product.entity';
import { ProductStocksModule } from 'src/product-stocks/product-stocks.module';
import { CsvService } from './csv.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProductEntity]),
    forwardRef(() => ProductStocksModule),
  ],
  controllers: [ProductController],
  providers: [ProductService, CsvService],
  exports: [ProductService],
})
export class ProductModule {}
