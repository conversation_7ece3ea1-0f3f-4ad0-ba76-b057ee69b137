import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';
import {
  DnsRecordEntity,
  DNSRecordStatus,
  DNSRecordType,
} from '../../../dto/dns-record.type';
import { dnsRecordsService } from '../../../services/dns-record-service';
import ConfirmationDialog from '../../common/ConfirmmationDialog';

type Props = {
  siteId: number;
  projectId: number;
};

export const DNSRecords: FC<Props> = ({ projectId, siteId }) => {
  const { t } = useTranslation();

  const [dnsRecords, setDNSRecords] = useState<DnsRecordEntity[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<DnsRecordEntity | null>(
    null,
  );
  const [editingDnsRecord, setEditingDnsRecord] =
    useState<DnsRecordEntity | null>(null);
  const [newDnsRecord, setNewDnsRecord] = useState<DnsRecordEntity>({
    id: undefined,
    type: DNSRecordType.A,
    subdomain: '',
    value: '',
    ttl: 0,
    status: DNSRecordStatus.ACTIVE,
    projectId: projectId,
    siteId: siteId,
  });

  useEffect(() => {
    const fetchDNSRecords = async () => {
      const records = await dnsRecordsService.findBySiteId(siteId);
      setDNSRecords(records);
    };
    fetchDNSRecords();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleInputChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = event.target;
    setNewDnsRecord({ ...newDnsRecord, [name]: value });
    if (isEditing && editingDnsRecord) {
      setEditingDnsRecord({ ...editingDnsRecord, [name]: value });
    }
  };

  const handleSelectChange = (event: SelectChangeEvent) => {
    const { name, value } = event.target;
    setNewDnsRecord({ ...newDnsRecord, [name]: value });
    if (isEditing && editingDnsRecord) {
      setEditingDnsRecord({ ...editingDnsRecord, [name]: value });
    }
  };

  const handleCreate = async () => {
    try {
      await dnsRecordsService.create(newDnsRecord);
      setOpenDialog(false);
      const records = await dnsRecordsService.findBySiteId(siteId);
      setDNSRecords(records);
      setNewDnsRecord({
        type: DNSRecordType.A,
        subdomain: '',
        value: '',
        ttl: 3600,
        status: DNSRecordStatus.ACTIVE,
        projectId: projectId,
        siteId: siteId,
      });
    } catch (error) {
      console.error('Error creating DNS record:', error);
    }
  };

  const handleEdit = (dnsRecord: DnsRecordEntity) => {
    setEditingDnsRecord(dnsRecord);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const handleUpdate = async () => {
    if (!editingDnsRecord || !editingDnsRecord.id) return;
    try {
      await dnsRecordsService.update(editingDnsRecord.id, editingDnsRecord);
      setOpenDialog(false);
      const records = await dnsRecordsService.findByProjectId(projectId);
      setDNSRecords(records);
      setEditingDnsRecord(null);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating DNS record:', error);
    }
  };

  const handleDelete = (dnsRecord: DnsRecordEntity) => {
    setRecordToDelete(dnsRecord);
    setOpenConfirmDialog(true);
  };

  const confirmDelete = async () => {
    if (!recordToDelete || !recordToDelete.id) return;
    try {
      await dnsRecordsService.delete(recordToDelete.id);
      const records = await dnsRecordsService.findByProjectId(projectId);
      setDNSRecords(records);
      setOpenConfirmDialog(false);
      setRecordToDelete(null);
    } catch (error) {
      console.error('Error deleting DNS record:', error);
    }
  };

  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography>{t('site.own_domain.dns_records.title')}</Typography>
        <Button
          variant="outlined"
          onClick={() => {
            setOpenDialog(true);
            setIsEditing(false);
            setEditingDnsRecord(null);
          }}
        >
          {t('common.add')}
        </Button>
      </Box>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>{t('site.own_domain.dns_records.type.label')}</TableCell>
            <TableCell>{t('site.own_domain.dns_records.sub_domain')}</TableCell>
            <TableCell>{t('site.own_domain.dns_records.value')}</TableCell>
            <TableCell>{t('site.own_domain.dns_records.priority')}</TableCell>
            <TableCell>
              {t('site.own_domain.dns_records.status.label')}
            </TableCell>
            <TableCell></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {dnsRecords.map(dnsRecord => (
            <TableRow
              key={dnsRecord.id}
              sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
            >
              <TableCell component="th" scope="row">
                {dnsRecord.type}
              </TableCell>
              <TableCell>{dnsRecord.subdomain}</TableCell>
              <TableCell>{dnsRecord.value}</TableCell>
              <TableCell>{dnsRecord.ttl}</TableCell>
              <TableCell>{dnsRecord.status}</TableCell>
              <TableCell align="right">
                <IconButton onClick={() => handleEdit(dnsRecord)}>
                  <EditIcon />
                </IconButton>
                <IconButton>
                  <ContentCopyIcon />
                </IconButton>
                <IconButton onClick={() => handleDelete(dnsRecord)}>
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <Dialog
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setEditingDnsRecord(null);
          setIsEditing(false);
        }}
      >
        <DNSRecordForm
          isEditing={isEditing}
          initialDnsRecord={editingDnsRecord || newDnsRecord}
          handleInputChange={handleInputChange}
          handleSelectChange={handleSelectChange}
          onCreate={handleCreate}
          onUpdate={handleUpdate}
          onCancel={() => {
            setOpenDialog(false);
            setEditingDnsRecord(null);
            setIsEditing(false);
          }}
        />
      </Dialog>

      <ConfirmationDialog
        open={openConfirmDialog}
        title={t('delete_confirm.title')}
        message={t('delete_confirm.message', {
          name: recordToDelete?.subdomain,
        })}
        onConfirm={confirmDelete}
        onCancel={() => {
          setOpenConfirmDialog(false);
          setRecordToDelete(null);
        }}
      />
    </>
  );
};

export const DNSRecordForm: FC<{
  isEditing: boolean;
  initialDnsRecord: DnsRecordEntity;
  handleInputChange: (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void;
  handleSelectChange: (event: SelectChangeEvent) => void;
  onCreate: () => void;
  onUpdate: () => void;
  onCancel: () => void;
}> = ({
  isEditing,
  initialDnsRecord,
  handleInputChange,
  handleSelectChange,
  onCreate,
  onUpdate,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [dnsRecord, setDnsRecord] = useState(initialDnsRecord);

  useEffect(() => {
    setDnsRecord(initialDnsRecord);
  }, [initialDnsRecord]);

  return (
    <Stack sx={{ width: '600px' }}>
      <Typography
        sx={{ paddingBlock: 2, paddingInline: 3 }}
        variant="subtitle2"
        color="textPrimary"
      >
        {t('site.own_domain.dns_records.form.title')}
      </Typography>
      <DialogContent sx={{ paddingBlock: 1 }}>
        <Stack direction={'column'} spacing={2}>
          <FormControl variant="filled" fullWidth size="small">
            <InputLabel id="type-label">
              {t('site.own_domain.dns_records.type.label')}
            </InputLabel>
            <Select
              labelId="type-label"
              id="type"
              name="type"
              value={dnsRecord.type}
              label={t('site.own_domain.dns_records.type.label')}
              onChange={handleSelectChange}
            >
              {Object.values(DNSRecordType).map(type => (
                <MenuItem key={type} value={type}>
                  {type}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <TextField
            id="subdomain"
            name="subdomain"
            label={t('site.own_domain.dns_records.sub_domain')}
            variant="filled"
            onChange={handleInputChange}
            value={dnsRecord.subdomain}
            size="small"
          />

          <TextField
            id="value"
            name="value"
            label={t('site.own_domain.dns_records.value')}
            variant="filled"
            onChange={handleInputChange}
            value={dnsRecord.value}
            size="small"
          />

          <TextField
            id="ttl"
            name="ttl"
            label={'TTL'}
            variant="filled"
            type="number"
            onChange={handleInputChange}
            value={dnsRecord.ttl}
            size="small"
          />

          <FormControl variant="filled" fullWidth size="small">
            <InputLabel id="status-label">
              {t('site.own_domain.dns_records.status.label')}
            </InputLabel>
            <Select
              labelId="status-label"
              id="status"
              name="status"
              value={dnsRecord.status}
              label={t('site.own_domain.dns_records.status.label')}
              onChange={handleSelectChange}
            >
              {Object.values(DNSRecordStatus).map(status => (
                <MenuItem key={status} value={status}>
                  {status}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button variant="text" onClick={onCancel}>
          {t('common.cancel')}
        </Button>
        <Button
          color="inherit"
          variant="outlined"
          onClick={isEditing ? onUpdate : onCreate}
        >
          {isEditing ? t('common.update') : t('common.create')}
        </Button>
      </DialogActions>
    </Stack>
  );
};
