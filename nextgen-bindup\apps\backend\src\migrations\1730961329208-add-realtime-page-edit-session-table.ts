import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRealtimePageEditSessionTable1730961329208
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}page_edit_sessions`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
