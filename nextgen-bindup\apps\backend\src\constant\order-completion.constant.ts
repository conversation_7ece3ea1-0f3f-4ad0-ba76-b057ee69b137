export const ORDER_COMPLETION_DISPLAY_TEXT = `注文内容がショップに送信されました。
ご登録いただいたアドレスに確認のメールをお送りいたします。
      
※24時間経過してもショップより注文完了メールが届かない場合、ショップまでお問い合わせください。`;

export const ORDER_COMPLETION_EMAIL_SUBJECT = (shopName: string) =>
  `【${shopName}】注文内容のご確認（自動送信メール）`;

export const ORDER_COMPLETION_EMAIL_HEADER = (
  shopName: string,
) => `---------------------------------------------------------------------
本メールはお客様のご注文情報時に送信される、自動配信メールです。
ショップからの確認の連絡、または商品の発送をもって売買契約成立となります。
---------------------------------------------------------------------
${shopName}をご利用いただき、まことにありがとうございます。
下記の内容でご注文を承りましたのでご連絡申し上げます。`;

export const ORDER_COMPLETION_EMAIL_FOOTER = (
  shopName: string,
  email: string,
) => `─────────────問い合わせ─────────────────
メールによるお問い合せ　${email}
${shopName}
────────────────────────────────`;
