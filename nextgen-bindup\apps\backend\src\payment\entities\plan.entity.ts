import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('plans', { schema: process.env.DATABASE_SCHEMA })
export class PlanEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: string;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string;

  @Column({
    name: 'stripeProductId',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  stripeProductId: string;

  @Column({
    name: 'stripePriceId',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  stripePriceId: string;

  @Column({
    name: 'price',
    type: 'integer',
    nullable: false,
  })
  price: number;

  @Column({
    name: 'currency',
    type: 'varchar',
    length: 10,
    nullable: false,
  })
  currency: string;

  @Column({
    name: 'interval',
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  interval: string;

  @Column({
    name: 'isActive',
    type: 'boolean',
    nullable: false,
    default: true,
  })
  isActive: boolean;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
