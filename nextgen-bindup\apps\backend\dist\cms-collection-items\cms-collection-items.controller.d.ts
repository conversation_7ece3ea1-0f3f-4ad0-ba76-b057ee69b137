import { CmsCollectionItemsService } from './cms-collection-items.service';
import { CmsCollectionItemEntity } from './entities/cms-collection-items.entity';
import { PaginationDto } from './dto/pagination.dto';
export declare class CmsCollectionItemsController {
    private readonly collectionItemsService;
    constructor(collectionItemsService: CmsCollectionItemsService);
    create(collectionItemEntity: CmsCollectionItemEntity): Promise<CmsCollectionItemEntity>;
    update(id: string, data: Partial<CmsCollectionItemEntity>): Promise<CmsCollectionItemEntity>;
    getById(id: string): Promise<CmsCollectionItemEntity>;
    getByCollectionId(cmsCollectionId: string, dto: PaginationDto): Promise<{
        data: CmsCollectionItemEntity[];
        count: number;
    }>;
    getBySiteId(siteId: string): Promise<Record<string, CmsCollectionItemEntity[]>>;
    delete(id: string): Promise<boolean>;
}
