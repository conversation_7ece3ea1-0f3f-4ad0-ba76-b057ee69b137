import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCmsArticleTable1743410233708 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}cms_articles`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.renameTable(this.TABLE_NAME, 'cms_collection_items');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.renameTable('cms_collection_items', this.TABLE_NAME);
  }
}
