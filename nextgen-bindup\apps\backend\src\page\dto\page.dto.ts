import { DatasourcePropDto } from '@nextgen-bindup/common/dto/setting-properties/datasource-prop.dto';
import { PageStatus, PageType } from '../types/page.type';

export interface RenderPage {
  id: number;
  parentId: number;
  type: PageType;
  datasource: DatasourcePropDto;
  name: string;
  status: PageStatus;
  url: string;
  isPrivate: boolean;
  isHome: boolean;
  isDeleted: boolean;

  paths: string[];
  ssgID: string;
}
