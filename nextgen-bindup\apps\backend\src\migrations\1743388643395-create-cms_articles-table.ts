import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateCmsArticles1743388643395 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}cms_articles`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'cmsCollectionId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            length: '250',
            isNullable: false,
          },
          {
            name: 'data',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'varchar',
            length: '15',
            isNullable: false,
            default: `'draft'`,
          },
          {
            name: 'siteId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'rootUserId',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );

    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'IDX_cms_articles_cmscollectionid',
        columnNames: ['cmsCollectionId'],
        isUnique: false,
      }),
    );

    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'IDX_cms_articles_siteid',
        columnNames: ['siteId'],
        isUnique: false,
      }),
    );

    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'IDX_cms_articles_rootuserid',
        columnNames: ['rootUserId'],
        isUnique: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(
      this.TABLE_NAME,
      'IDX_cms_articles_cmscollectionid',
    );
    await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_cms_articles_siteid');
    await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_cms_articles_rootuserid');

    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
