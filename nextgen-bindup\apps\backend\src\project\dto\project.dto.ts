import { ProjectFolderEntity } from 'src/project-folders/entities/project-folders.entity';

export interface Project {
  id: string;
  userId: string;
  name: string;
  description?: string;
  created_at: string;
  projectFolders?: ProjectFolderEntity[];
}

export interface CreateProjectReq {
  name: string;
}

export interface ProjectRawData {
  id: string;
  name: string;
  created_at: string;
  projectFolders?: ProjectFolderEntity[];
}
