import { UserInfoEntity } from './entities/user-info.entity';
import { Repository } from 'typeorm';
export declare class UserInfoService {
    readonly userInfoRepo: Repository<UserInfoEntity>;
    constructor();
    updateRecently(userId: string, siteId: number): Promise<boolean>;
    findByUserId(userId: string): Promise<UserInfoEntity | null>;
    update(userId: string, data: Partial<UserInfoEntity>): Promise<UserInfoEntity>;
}
