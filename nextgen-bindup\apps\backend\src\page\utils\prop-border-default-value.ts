import { BorderPropDto } from '@nextgen-bindup/common/dto/setting-properties/border-prop.dto';

export const PROP_BORDER_DEFAULT_VALUE = (
  ts: number,
  prop?: Partial<BorderPropDto>,
): BorderPropDto => ({
  top: {
    color: '#000',
    width: { value: '0', unit: 'px' },
    borderStyle: 'none',
  },
  right: {
    color: '#000',
    width: { value: '0', unit: 'px' },
    borderStyle: 'none',
  },
  bottom: {
    color: '#000',
    width: { value: '0', unit: 'px' },
    borderStyle: 'none',
  },
  left: {
    color: '#000',
    width: { value: '0', unit: 'px' },
    borderStyle: 'none',
  },
  radiusTopLeft: {
    width: { unit: 'px', value: '0' },
    height: { unit: 'px', value: '0' },
    isDetail: false,
  },
  radiusTopRight: {
    width: { unit: 'px', value: '0' },
    height: { unit: 'px', value: '0' },
    isDetail: false,
  },
  radiusBottomLeft: {
    width: { unit: 'px', value: '0' },
    height: { unit: 'px', value: '0' },
    isDetail: false,
  },
  radiusBottomRight: {
    width: { unit: 'px', value: '0' },
    height: { unit: 'px', value: '0' },
    isDetail: false,
  },
  isDetail: false,
  ts: ts,
  ...(prop || undefined),
});
