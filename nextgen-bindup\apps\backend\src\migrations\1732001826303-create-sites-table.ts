import { SiteStatus } from 'src/site/types/site.type';
import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateSiteTable1732001826303 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}sites`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'projectId',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'projectFolderId',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'managementName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'smallint',
            isNullable: false,
            default: `'${SiteStatus.DRAFT}'`,
          },
          {
            name: 'url',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isSearch',
            type: 'boolean',
            isNullable: true,
          },
          {
            name: 'thumb',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'headCode',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'bodyCode',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );

    //alter publication supabase_realtime add table this.TABLE_NAME;
    await queryRunner.query(
      `alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );

    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
