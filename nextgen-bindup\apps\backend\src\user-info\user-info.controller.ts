import { Body, Controller, Get, Param, Put, UseGuards } from '@nestjs/common';
import { UserInfoService } from './user-info.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { ExtractUser } from 'src/auth/user.decorator';
import { JwtPayloadDto } from 'src/auth/dto/auth.dto';
import { UserInfoEntity } from './entities/user-info.entity';

@Controller('user-info')
@UseGuards(AuthGuard)
export class UserInfoController {
  constructor(private readonly userInfoService: UserInfoService) {}

  @Put('recently/:siteId')
  async updateRecently(
    @ExtractUser() user: JwtPayloadDto,
    @Param('siteId') siteId: string,
  ) {
    return await this.userInfoService.updateRecently(user.userId, +siteId);
  }

  @Get('me')
  async me(@ExtractUser() user: JwtPayloadDto) {
    return await this.userInfoService.findByUserId(user.userId);
  }

  @Put('update')
  async update(
    @ExtractUser() user: JwtPayloadDto,
    @Body() data: Partial<UserInfoEntity>,
  ) {
    return await this.userInfoService.update(user.userId, data);
  }
}
