import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { OrderEntity } from './order.entity';
import { ProductEntity } from 'src/product/entities/product.entity';
import { ProductType } from 'src/product/enum/product.enum';

@Entity('order_items', { schema: process.env.DATABASE_SCHEMA })
export class OrderItemEntity {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int' })
  orderItemId: number;

  @ManyToOne(() => OrderEntity, order => order.orderItems)
  @JoinColumn({ name: 'orderId' })
  order: OrderEntity;

  @Column({
    name: 'orderId',
    type: 'integer',
    nullable: false,
  })
  orderId: number;

  @ManyToOne(() => ProductEntity)
  @JoinColumn({ name: 'productId' })
  product: ProductEntity;

  @Column({
    name: 'productId',
    type: 'integer',
    nullable: false,
  })
  productId: number;

  @Column({
    name: 'productName',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  productName: string;

  @Column({
    name: 'productType',
    type: 'varchar',
    length: 10,
    nullable: false,
  })
  productType: ProductType;

  @Column({
    name: 'unitPrice',
    type: 'bigint',
    nullable: false,
  })
  unitPrice: number;

  @Column({
    name: 'salePrice',
    type: 'bigint',
    nullable: false,
  })
  salePrice?: number;

  @Column({
    name: 'displayPrice',
    type: 'bigint',
    nullable: false,
  })
  displayPrice: number;

  @Column({
    name: 'individualShippingCharges',
    type: 'bigint',
    transformer: {
      to: (value: number | string) => value,
      from: (value: string) => Number(value),
    },
    nullable: false,
  })
  individualShippingCharges: number;

  @Column({
    name: 'quantity',
    type: 'int',
    nullable: false,
  })
  quantity: number;

  @Column({
    name: 'attributes',
    type: 'jsonb',
    nullable: true,
  })
  attributes: Record<string, string>;

  @Column({
    name: 'subtotal',
    type: 'bigint',
    nullable: false,
  })
  subtotal: number;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;

  @Column({
    name: 'image',
    type: 'varchar',
    nullable: true,
  })
  image: string;
}
