import { Inject, Injectable } from '@nestjs/common';
import {
  AuthResponse,
  SupabaseClient,
  UserResponse,
} from '@supabase/supabase-js';
import {
  CreateIdentityReq,
  CreateIdentityRes,
  LoginIdentityRes,
} from './dto/identity.dto';
import { AppException } from 'src/common/exceptions/app.exception';

@Injectable()
export class IdentityService {
  constructor(@Inject('SUPABASE_CLIENT') private supabase: SupabaseClient) {}

  async createIdentity(input: CreateIdentityReq): Promise<CreateIdentityRes> {
    const res: AuthResponse = await this.supabase.auth.signUp({
      email: input.email,
      password: input.password,
    });

    if (res.error) throw new AppException(`api.error.${res.error.code}`);
    if (!res?.data?.user) throw new AppException('api.error.unknown_error');
    if (!res?.data?.session)
      throw new AppException('api.error.user_is_existed');

    const result: CreateIdentityRes = {
      accessToken: res.data?.session?.access_token,
      user: {
        userId: res.data.user.id,
        email: res.data.user.email,
      },
    };

    return result;
  }

  async signInWithPassword(
    email: string,
    password: string,
  ): Promise<LoginIdentityRes> {
    const res: AuthResponse = await this.supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (res.error) throw new AppException(`api.error.${res.error.code}`);
    if (!res?.data?.user) throw new AppException('api.error.unknown_error');
    if (!res?.data?.session) throw new AppException('api.error.unknown_error');

    const result: LoginIdentityRes = {
      accessToken: res.data?.session?.access_token,
      user: {
        userId: res.data.user.id,
        email: res.data.user.email,
      },
    };

    return result;
  }

  async forgotPasswordForEmail(
    email: string,
    redirectTo: string,
  ): Promise<void> {
    const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${redirectTo}?email=${encodeURIComponent(email)}`,
    });

    if (error) throw new AppException(`api.error.${error.code}`);
  }

  async changePassword(email: string, password: string): Promise<void> {
    const res: UserResponse = await this.supabase.auth.updateUser({
      email,
      password,
    });

    if (res.error) throw new AppException(`api.error.${res.error.code}`);
    if (res.error) throw new AppException(res.error.message);
    if (!res?.data?.user) throw new AppException('api.error.unknown_error');
  }
}
