import { Modu<PERSON> } from '@nestjs/common';
import { FontsetController } from './fontset.controller';
import { FontsetService } from './fontset.service';
import { FontsetEntity } from './entities/fontset.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProjectModule } from 'src/project/project.module';

@Module({
  imports: [TypeOrmModule.forFeature([FontsetEntity]), ProjectModule],
  controllers: [FontsetController],
  providers: [FontsetService],
  exports: [FontsetService],
})
export class FontsetModule {}
