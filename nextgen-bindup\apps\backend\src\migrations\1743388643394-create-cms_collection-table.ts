import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateCmsCollection1743388643394 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}cms_collection`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'siteId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            length: '250',
            isNullable: false,
          },
          {
            name: 'struct',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'rootUserId',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );

    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'IDX_cms_collection_siteid',
        columnNames: ['siteId'],
        isUnique: false,
      }),
    );

    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'IDX_cms_collection_rootuserid',
        columnNames: ['rootUserId'],
        isUnique: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_cms_collection_siteid');
    await queryRunner.dropIndex(
      this.TABLE_NAME,
      'IDX_cms_collection_rootuserid',
    );

    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
