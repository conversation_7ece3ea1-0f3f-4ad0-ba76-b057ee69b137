"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageEditSessionService = void 0;
const common_1 = require("@nestjs/common");
const page_edit_session_entity_1 = require("./entities/page-edit-session.entity");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
let PageEditSessionService = class PageEditSessionService {
    async cronJobDeletePageEditSession() {
        try {
            await this.pageRepo.delete({
                updatedAt: (0, typeorm_1.LessThan)(new Date(Date.now() - 5000)),
            });
        }
        catch (error) {
            console.log(error);
        }
    }
};
exports.PageEditSessionService = PageEditSessionService;
__decorate([
    (0, typeorm_2.InjectRepository)(page_edit_session_entity_1.PageEditSessionEntity),
    __metadata("design:type", typeorm_1.Repository)
], PageEditSessionService.prototype, "pageRepo", void 0);
exports.PageEditSessionService = PageEditSessionService = __decorate([
    (0, common_1.Injectable)()
], PageEditSessionService);
//# sourceMappingURL=page-edit-session.service.js.map