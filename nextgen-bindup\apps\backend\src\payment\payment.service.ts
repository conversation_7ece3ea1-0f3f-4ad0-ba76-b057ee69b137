import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import Stripe from 'stripe';
import { PlanEntity } from './entities/plan.entity';
import { SubscriptionEntity } from './entities/subscription.entity';
import { UserInfoEntity } from 'src/user-info/entities/user-info.entity';
import { Repository } from 'typeorm';
import {
  PlanDto,
  SubscriptionResponse,
  SubscriptionStatus,
} from './dto/payment.dto';
import { UserPaymentService } from './user-payment.service';

@Injectable()
export class PaymentService implements OnModuleInit {
  @InjectRepository(PlanEntity)
  private readonly planRepo: Repository<PlanEntity>;
  @InjectRepository(SubscriptionEntity)
  private readonly subscriptionRepo: Repository<SubscriptionEntity>;
  @InjectRepository(UserInfoEntity)
  private readonly userInfoRepo: Repository<UserInfoEntity>;

  private stripe: Stripe;

  constructor(
    private readonly configService: ConfigService,
    private readonly userPaymentService: UserPaymentService,
  ) {
    if (this.configService.get('STRIPE_SECRET_KEY')) {
      this.stripe = new Stripe(this.configService.get('STRIPE_SECRET_KEY'), {
        apiVersion: '2025-05-28.basil',
      });
    }
  }

  async onModuleInit() {
    // await this.syncPlansFromStripe();
    // await this.syncPaidInvoices();
  }

  async syncPlansFromStripe(): Promise<void> {
    try {
      const stripePrices = await this.stripe.prices.list({
        active: true,
        limit: 100,
      });

      const dbPlans = await this.planRepo.find();
      const stripePriceIds = stripePrices.data.map(price => price.id);

      for (const price of stripePrices.data) {
        const product = (await this.stripe.products.retrieve(
          price.product as string,
        )) as Stripe.Product;

        const existingPlan = dbPlans.find(
          plan => plan.stripePriceId === price.id,
        );

        const planData = {
          stripeProductId: product.id,
          stripePriceId: price.id,
          price: price.unit_amount,
          name: product.name || 'Default Plan',
          currency: price.currency,
          interval: price.recurring?.interval,
          isActive: true,
        };

        if (existingPlan) {
          await this.planRepo.update({ id: existingPlan.id }, planData);
        } else {
          await this.planRepo.save(planData);
        }
      }

      for (const dbPlan of dbPlans) {
        if (!stripePriceIds.includes(dbPlan.stripePriceId)) {
          await this.planRepo.update({ id: dbPlan.id }, { isActive: false });
        }
      }
    } catch {}
  }

  async syncPaidInvoices() {
    const now = new Date();
    // Start of today (00:00:00)
    const todayStart = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
    );
    // Start of yesterday
    const yesterdayStart = new Date(todayStart);
    yesterdayStart.setDate(yesterdayStart.getDate() - 1);
    // End of today (23:59:59.999)
    const todayEnd = new Date(todayStart);
    todayEnd.setDate(todayEnd.getDate() + 1);
    todayEnd.setMilliseconds(-1);
    // Convert to UNIX timestamps (in seconds)
    const fromTimestamp = Math.floor(yesterdayStart.getTime() / 1000);
    const toTimestamp = Math.floor(todayEnd.getTime() / 1000);
    const allInvoices: Stripe.Invoice[] = [];
    let startingAfter: string | undefined = undefined;

    while (true) {
      const response = await this.stripe.invoices.list({
        created: {
          gte: fromTimestamp,
          lte: toTimestamp,
        },
        status: 'paid',
        limit: 100,
        starting_after: startingAfter,
      });

      allInvoices.push(...response.data);

      if (!response.has_more) {
        break;
      }

      startingAfter = response.data[response.data.length - 1].id;
    }

    for (const invoice of allInvoices) {
      try {
        const stripeSubscriptionId =
          invoice.parent.subscription_details.subscription?.toString();

        if (!stripeSubscriptionId) {
          console.warn(
            `Invoice ${invoice.id} does not have a subscription ID.`,
          );
          continue;
        }

        const lineItem = invoice.lines.data[0];
        const stripePlanId = lineItem.pricing.price_details.price as string;
        const plan = await this.planRepo.findOne({
          where: { stripePriceId: stripePlanId },
        });

        if (!plan) {
          console.error(`Cannot find plan for stripePriceId: ${stripePlanId}.`);
          continue;
        }

        const customerId = invoice.customer as string;
        const customer = (await this.stripe.customers.retrieve(
          customerId,
        )) as any;
        const userId = customer.metadata.userId;

        if (!userId) {
          console.error(
            `Cannot find userId in metadata of customer ${customerId}.`,
          );
          continue;
        }

        const existingSubscription = await this.subscriptionRepo.findOne({
          where: { userId },
        });

        if (existingSubscription) {
          existingSubscription.currentPeriodStart = new Date(
            lineItem.period.start * 1000,
          );
          existingSubscription.currentPeriodEnd = new Date(
            lineItem.period.end * 1000,
          );
          existingSubscription.status = SubscriptionStatus.Active;
          existingSubscription.planId = plan.id;
          existingSubscription.stripeSubscriptionId = stripeSubscriptionId;
          await this.subscriptionRepo.save(existingSubscription);
          console.log(
            `Renewing subscription ${stripeSubscriptionId} from invoice ${invoice.id}.`,
          );
        } else {
          await this.subscriptionRepo.insert({
            userId,
            planId: plan.id,
            stripeSubscriptionId,
            status: SubscriptionStatus.Active,
            currentPeriodStart: new Date(lineItem.period.start * 1000),
            currentPeriodEnd: new Date(lineItem.period.end * 1000),
          });

          console.log(
            `Creating new subscription ${stripeSubscriptionId} from invoice ${invoice.id}.`,
          );
        }
        const user = await this.userInfoRepo.findOne({ where: { userId } });
        if (user && !user.stripeCustomerId) {
          await this.userInfoRepo.update(
            { userId },
            {
              stripeCustomerId: customerId,
            },
          );
          console.log(`Update stripeCustomerId for user ${userId}`);
        }
      } catch (error) {
        console.error(`Error processing invoice ${invoice.id}:`, error);
      }
    }
  }

  async getHistorySubscription(
    userId: string,
  ): Promise<SubscriptionResponse[]> {
    // query all subscriptions of user from stripe
    // get customer stripe
    const user = await this.userInfoRepo.findOne({ where: { userId } });
    if (!user) {
      throw new Error('User not found');
    }
    const stripeCustomerId = user.stripeCustomerId;
    // get all subscriptions of user from stripe
    const subscriptions = await this.stripe.subscriptions.list({
      customer: stripeCustomerId,
    });

    const subscriptionsFormated: SubscriptionResponse[] = [];
    for (const subscription of subscriptions.data) {
      const currentPeriodStart =
        subscription.items.data[0].current_period_start;
      const currentPeriodEnd = subscription.items.data[0].current_period_end;
      const stripePricePlanId = subscription.items.data[0].plan.id;
      const plan = await this.planRepo.findOne({
        where: { stripePriceId: stripePricePlanId },
      });
      subscriptionsFormated.push({
        userId: userId,
        planId: plan.id,
        planName: plan.name,
        planPrice: plan.price,
        planCurrency: plan.currency,
        stripeSubscriptionId: subscription.id,
        status: subscription.status as SubscriptionStatus,
        currentPeriodStart: new Date(currentPeriodStart * 1000),
        currentPeriodEnd: new Date(currentPeriodEnd * 1000),
        isActive: subscription.status === 'active',
      });
    }

    return subscriptionsFormated;
  }

  async getUserSubscription(
    userId: string,
  ): Promise<SubscriptionResponse | null> {
    const subscription = await this.subscriptionRepo.findOne({
      where: { userId },
      order: { currentPeriodEnd: 'DESC' },
    });

    if (!subscription) {
      return null;
    }

    const plan = await this.planRepo.findOne({
      where: { id: subscription.planId },
    });
    if (!plan) {
      throw new Error('Plan not found');
    }
    let imageUrl = '';
    const stripePlan = await this.stripe.prices.retrieve(plan.stripePriceId);
    if (stripePlan) {
      // get image from product stripe
      const stripeProduct = await this.stripe.products.retrieve(
        plan.stripeProductId,
      );
      if (
        stripeProduct &&
        stripeProduct.images &&
        stripeProduct.images.length > 0
      ) {
        imageUrl = stripeProduct.images[0];
      }
    }

    const isActive = subscription.currentPeriodEnd > new Date();

    const result: SubscriptionResponse = {
      ...subscription,
      isActive,
      planName: plan.name,
      planPrice: plan.price,
      planCurrency: plan.currency,
      planImageUrl: imageUrl,
      planDescription: '',
    };

    return result;
  }

  async handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    try {
      const stripeSubscriptionId =
        invoice.parent.subscription_details.subscription?.toString();

      if (!stripeSubscriptionId) {
        console.error('Not Found subscription ID');
        return;
      }

      const subscription = await this.subscriptionRepo.findOne({
        where: { stripeSubscriptionId },
      });

      const stripePlanId = invoice.lines.data[0].pricing.price_details
        .price as string;
      const plan = await this.planRepo.findOne({
        where: { stripePriceId: stripePlanId },
      });
      const customerId = invoice.customer as string;
      const customer = (await this.stripe.customers.retrieve(
        customerId,
      )) as any;
      const lineItem = invoice.lines.data[0];
      if (!subscription) {
        this.subscriptionRepo.insert({
          userId: customer.metadata.userId,
          planId: plan.id,
          stripeSubscriptionId,
          status: SubscriptionStatus.Active,
          currentPeriodStart: new Date(lineItem.period.start * 1000),
          currentPeriodEnd: new Date(lineItem.period.end * 1000),
        });
        // update user info with stripeCustomerId
        const user = await this.userInfoRepo.findOne({
          where: { userId: invoice.metadata.userId },
        });
        if (!user) {
          console.error(
            `Not Found user with userId ${invoice.metadata.userId}`,
          );
          return;
        }
        await this.userInfoRepo.update(
          {
            userId: user.userId,
          },
          { stripeCustomerId: invoice.customer?.toString() },
        );
        return;
      }

      subscription.status = SubscriptionStatus.Active;
      subscription.currentPeriodStart = new Date(lineItem.period.start * 1000);
      subscription.currentPeriodEnd = new Date(lineItem.period.end * 1000);

      await this.subscriptionRepo.save(subscription);
      console.log(`Update success subscription ${stripeSubscriptionId}`);
    } catch (error) {
      console.error('Error handling payment succeeded:', error);
    }
  }

  async handlePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    const stripeSubscriptionId =
      invoice.parent.subscription_details.subscription?.toString();

    if (!stripeSubscriptionId) {
      console.error('Not found subscription ID');
      return;
    }

    const subscription = await this.subscriptionRepo.findOne({
      where: { stripeSubscriptionId },
    });

    if (!subscription) {
      console.error(`Subscription ${stripeSubscriptionId} not found in DB`);
      return;
    }

    if (subscription.status === 'active') {
      subscription.status = SubscriptionStatus.PastDue;
    } else {
      subscription.status = SubscriptionStatus.Incomplete;
    }

    await this.subscriptionRepo.save(subscription);
  }

  async createSubscription(
    userId: string,
    planId: string,
    stripeSubscriptionId: string,
  ): Promise<SubscriptionEntity> {
    const plan = await this.planRepo.findOne({ where: { id: planId } });
    if (!plan) {
      throw new Error('Plan not found');
    }

    const newSubscription = this.subscriptionRepo.create({
      userId,
      planId,
      stripeSubscriptionId,
      status: SubscriptionStatus.Active,
      currentPeriodStart: new Date(),
      currentPeriodEnd: this.calculatePeriodEnd(plan),
    });

    return this.subscriptionRepo.save(newSubscription);
  }

  async cancelSubscription(userId: string): Promise<boolean> {
    console.log('Cancel subscription for user:', userId);
    const subscription = await this.subscriptionRepo.findOne({
      where: { userId: userId },
    });

    if (!subscription) {
      throw new Error('Subscription not found');
    }

    const subscriptionId = subscription.stripeSubscriptionId;

    try {
      await this.stripe.subscriptions.update(
        subscription.stripeSubscriptionId,
        {
          cancel_at_period_end: true,
        },
      );

      await this.subscriptionRepo.update(
        { id: subscription.id },
        { status: SubscriptionStatus.Canceled, cancelAt: new Date() },
      );

      console.log(
        `Subscription with ID ${subscriptionId} was canceled successfully.`,
      );
      return true;
    } catch (error) {
      console.error(`Error canceling subscription: ${error.message}`);
      throw new Error('Failed to cancel subscription on Stripe');
    }
  }

  private calculatePeriodEnd(plan: PlanEntity): Date {
    const periodEnd = new Date();
    switch (plan.interval) {
      case 'day':
        periodEnd.setDate(periodEnd.getDate() + 1);
        break;
      case 'week':
        periodEnd.setDate(periodEnd.getDate() + 7);
        break;
      case 'month':
        periodEnd.setMonth(periodEnd.getMonth() + 1);
        break;
      case 'year':
        periodEnd.setFullYear(periodEnd.getFullYear() + 1);
        break;
      default:
        throw new Error('Invalid plan interval');
    }
    return periodEnd;
  }

  async getPlans(): Promise<PlanDto[]> {
    const plans = await this.planRepo.find({ where: { isActive: true } });
    // get info plan from stripe
    const planDtos = [];
    for (const plan of plans) {
      const _plan: PlanDto = {
        ...plan,
        imageUrl: '',
        description: '',
      };
      const stripePlan = await this.stripe.prices.retrieve(plan.stripePriceId);
      if (stripePlan) {
        // get image from product stripe
        const stripeProduct = await this.stripe.products.retrieve(
          plan.stripeProductId,
        );
        if (
          stripeProduct &&
          stripeProduct.images &&
          stripeProduct.images.length > 0
        ) {
          _plan.imageUrl = stripeProduct.images[0];
        }
      }
      planDtos.push(_plan);
    }

    return planDtos;
  }

  verifyWebhookSignature(rawBody: Buffer, signature: string): Stripe.Event {
    const endpointSecret = this.configService.get('STRIPE_WEBHOOK_SECRET');
    if (!endpointSecret) {
      throw new Error('Webhook secret not configured');
    }
    try {
      return this.stripe.webhooks.constructEvent(
        rawBody,
        signature,
        endpointSecret,
      );
    } catch (err) {
      throw new Error(`Webhook Error: ${err.message}`);
    }
  }

  async handleWebhookEvent(event: Stripe.Event) {
    console.log('event type:', event.type);
    switch (event.type) {
      case 'plan.updated':
      case 'plan.created': {
        const plan = event.data.object as Stripe.Plan;
        await this.handleAddOrUpdatePlan(plan);
        break;
      }
      case 'plan.deleted': {
        const plan = event.data.object as Stripe.Plan;
        await this.handleDeletePlan(plan.id);
        break;
      }

      case 'product.updated': {
        const product = event.data.object as Stripe.Product;
        console.log('product updated:', JSON.stringify(product));
        // check if product is archived
        if (product.active === false) {
          await this.handleProductArchived(product.id);
          break;
        } else {
          // get all plans with this product id from stripe
          const stripePlans = await this.stripe.prices.list({
            product: product.id,
          });
          // create plans from stripe
          for (const plan of stripePlans.data) {
            await this.planRepo.save({
              stripeProductId: product.id,
              stripePriceId: plan.id,
              price: plan.unit_amount,
              name: product.name || 'Default Plan',
              currency: plan.currency,
              interval: plan.recurring?.interval,
              isActive: true,
            });
          }
        }
        break;
      }

      // HANDLE PAYMENT INTENT EVENTS
      case 'invoice.payment_succeeded':
        const data = event.data.object as Stripe.Invoice;
        const metadata = data.metadata;
        const type = metadata.type;
        if (type === 'order') {
          await this.userPaymentService.handleEventCheckoutSessionCompleted(
            metadata,
          );
          return;
        }
        await this.handlePaymentSucceeded(event.data.object);
        break;
      case 'invoice.payment_failed':
        await this.handlePaymentFailed(event.data.object);
        break;

      default:
        console.warn(`⚠️ Unhandled event type: ${event.type}`);
    }
  }

  async handleProductArchived(stripeProductId: string) {
    const existingPlans = await this.planRepo.find({
      where: { stripeProductId },
    });
    if (existingPlans.length > 0) {
      await this.planRepo.update({ stripeProductId }, { isActive: false });
      console.log(`Product Archived: ${JSON.stringify(existingPlans)}`);
    } else {
      console.log(`No plans found for product ID: ${stripeProductId}`);
    }
  }

  async handleAddOrUpdatePlan(plan: Stripe.Plan) {
    const productId = plan.product as string;
    // get product details
    const product = await this.stripe.products.retrieve(productId);
    const planId = plan.id;
    // in-active old plans
    const existingPlans = await this.planRepo.find({
      where: { stripePriceId: planId, stripeProductId: productId },
    });
    if (existingPlans.length > 0) {
      await this.planRepo.update(
        { stripePriceId: planId, stripeProductId: productId },
        { isActive: false },
      );
    }
    // create new plans
    await this.planRepo.save({
      stripeProductId: productId,
      stripePriceId: planId,
      price: plan.amount,
      name: product.name || 'Default Plan',
      currency: plan.currency,
      interval: plan.interval,
      isActive: true,
    });
  }

  async handleDeletePlan(planId: string) {
    const existingPlans = await this.planRepo.find({
      where: { stripePriceId: planId },
    });
    if (existingPlans.length > 0) {
      await this.planRepo.update(
        { stripePriceId: planId },
        { isActive: false },
      );
      console.log(`Plan Deleted: ${JSON.stringify(existingPlans)}`);
    } else {
      console.log(`No plans found for plan ID: ${planId}`);
    }
  }

  async getPlanById(planId: string): Promise<PlanEntity | null> {
    const plan = await this.planRepo.findOne({
      where: { id: planId, isActive: true },
    });
    if (!plan) {
      return null;
    }
    return plan;
  }

  async createCheckoutSession(
    userId: string,
    planId: string,
  ): Promise<{ url: string }> {
    try {
      const plan = await this.getPlanById(planId);
      if (!plan) {
        throw new Error('Plan not found');
      }
      const stripeCustomerId = await this.getOrCreateCustomer(userId);
      // check exist subscription active then reject
      const subscription = await this.subscriptionRepo.findOne({
        where: { userId, status: SubscriptionStatus.Active },
      });
      if (subscription) {
        throw new Error('User already has an active subscription');
      }

      // Tạo Checkout Session
      const session = await this.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        mode: 'subscription',
        customer: stripeCustomerId,
        line_items: [
          {
            price: plan.stripePriceId,
            quantity: 1,
          },
        ],
        success_url: 'http://localhost:5273/billing-success',
        cancel_url: 'http://localhost:5273',
        metadata: {
          userId,
          planId,
        },
      });

      return { url: session.url };
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw new Error('Unable to create checkout session');
    }
  }

  async getOrCreateCustomer(userId: string): Promise<string> {
    const user = await this.userInfoRepo.findOne({ where: { userId: userId } });

    if (!user) {
      throw new Error('User not found');
    }

    if (user.stripeCustomerId) {
      return user.stripeCustomerId;
    }

    const customer = await this.stripe.customers.create({
      metadata: { userId },
    });

    await this.userInfoRepo.update(userId, { stripeCustomerId: customer.id });

    return customer.id;
  }
}
