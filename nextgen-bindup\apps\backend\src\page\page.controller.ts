import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Res,
  UseGuards,
} from '@nestjs/common';
import { Response } from 'express';
import { PageService } from './page.service';
import { PageEntity } from './entities/page.entity';
import { PublishService } from 'src/publish/publish.service';
import { ZipService } from './zip.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { ExtractUser } from 'src/auth/user.decorator';
import { JwtPayloadDto } from 'src/auth/dto/auth.dto';
import { CreateBlogRequest } from '@nextgen-bindup/common/dto/blog';

@Controller('pages')
export class PageController {
  constructor(
    private readonly pageService: PageService,
    private readonly publishService: PublishService,
    private readonly zipService: ZipService,
  ) {}

  @Get('download/:projectId/:siteId')
  async zipAndDownload(
    @Param('projectId') projectId: string,
    @Param('siteId') siteId: string,
    @Res() res: Response,
  ) {
    try {
      const forderPath = await this.publishService.buildStaticSite(
        +projectId,
        +siteId,
        {
          pageIds: [],
        },
      );
      const zipBuffer = await this.zipService.zipFolder(forderPath);
      res.setHeader('Content-Type', 'application/zip');
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=${siteId}-archive.zip`,
      );
      res.status(HttpStatus.OK).send(zipBuffer);
    } catch (error) {
      console.error(error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).send('Error zipping folder');
    }
  }

  @Post('create/:projectId')
  @UseGuards(AuthGuard)
  async createPage(
    @ExtractUser() user: JwtPayloadDto,
    @Param('projectId') projectId: string,
    @Body() pageData: PageEntity,
  ) {
    pageData.userId = user.userId;
    return this.pageService.createPage(+projectId, pageData);
  }

  @Put('update/:id')
  async updatePage(
    @Param('id') pageId: string,
    @Body() pageData: Partial<PageEntity>,
  ) {
    return this.pageService.updatePage(+pageId, pageData);
  }

  @Delete('delete/:id')
  async deletePage(@Param('id') pageId: string) {
    return this.pageService.deletePage(+pageId);
  }

  @Get('by-site/:siteId')
  async getPagesBySiteId(@Param('siteId') siteId: string) {
    return this.pageService.getPagesBySiteId(+siteId);
  }

  @Get('one/:pageId')
  async getPageByd(@Param('pageId') pageId: string) {
    return this.pageService.getById(+pageId);
  }

  @Post('blog/:projectId')
  @UseGuards(AuthGuard)
  async createBlog(
    @ExtractUser() user: JwtPayloadDto,
    @Param('projectId') projectId: string,
    @Body() data: CreateBlogRequest,
  ) {
    data.userId = user.userId;
    return this.pageService.createBlog(+projectId, data);
  }
}
