"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderTable1747621333973 = void 0;
const typeorm_1 = require("typeorm");
class UpdateOrderTable1747621333973 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}orders`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'siteId',
            type: 'integer',
            isNullable: false,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'projectId',
            type: 'integer',
            isNullable: false,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'shippingFee',
            type: 'bigint',
            isNullable: true,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'taxFee',
            type: 'bigint',
            isNullable: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
        await queryRunner.dropColumn(this.TABLE_NAME, 'projectId');
    }
}
exports.UpdateOrderTable1747621333973 = UpdateOrderTable1747621333973;
//# sourceMappingURL=1747621333973-update-order-table.js.map