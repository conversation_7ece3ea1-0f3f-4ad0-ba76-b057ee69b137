import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { OrderItemEntity } from './order-item.entity';
import { OrderStatus } from '../enum/order.enum';
import { PaymentMethodType } from '../enum/payment-method-type.enum';

@Entity('orders', { schema: process.env.DATABASE_SCHEMA })
export class OrderEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @Column({
    name: 'lastName',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  lastName: string;

  @Column({
    name: 'firstName',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  firstName: string;

  @Column({
    name: 'lastNameKana',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  lastNameKana: string;

  @Column({
    name: 'firstNameKana',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  firstNameKana: string;

  @Column({
    name: 'email',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  email: string;

  @Column({
    name: 'postalCode',
    type: 'varchar',
    length: 10,
    nullable: false,
  })
  postalCode: string;

  @Column({
    name: 'prefecture',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  prefecture: string;

  @Column({
    name: 'addressLine1',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  addressLine1: string;

  @Column({
    name: 'addressLine2',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  addressLine2: string;

  @Column({
    name: 'phoneNumber',
    type: 'varchar',
    length: 20,
    nullable: false,
  })
  phoneNumber: string;

  @Column({
    name: 'shippingLastName',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  shippingLastName: string;

  @Column({
    name: 'shippingFirstName',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  shippingFirstName: string;

  @Column({
    name: 'shippingLastNameKana',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  shippingLastNameKana: string;

  @Column({
    name: 'shippingFirstNameKana',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  shippingFirstNameKana: string;

  @Column({
    name: 'shippingEmail',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  shippingEmail: string;

  @Column({
    name: 'shippingPostalCode',
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  shippingPostalCode: string;

  @Column({
    name: 'shippingPrefecture',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  shippingPrefecture: string;

  @Column({
    name: 'shippingAddressLine1',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  shippingAddressLine1: string;

  @Column({
    name: 'shippingAddressLine2',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  shippingAddressLine2: string;

  @Column({
    name: 'shippingPhoneNumber',
    type: 'varchar',
    length: 20,
    nullable: false,
  })
  shippingPhoneNumber: string;

  @Column({
    name: 'orderStatus',
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  orderStatus: OrderStatus;

  @Column({
    name: 'checkoutSessionUrl',
    type: 'varchar',
    length: 2048,
    nullable: false,
  })
  checkoutSessionUrl: string;

  @Column({
    name: 'checkoutSessionId',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  checkoutSessionId: string;

  @Column({
    name: 'additionalInformation',
    type: 'text',
    nullable: true,
  })
  additionalInformation?: string;

  @OneToMany(() => OrderItemEntity, orderItem => orderItem.order)
  orderItems?: OrderItemEntity[];

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;

  @Column({
    name: 'subtotal',
    type: 'bigint',
    nullable: true,
  })
  subtotal: number;

  @Column({
    name: 'shippingFee',
    type: 'bigint',
    nullable: true,
  })
  shippingFee: number;

  @Column({
    name: 'platformFee',
    type: 'bigint',
    nullable: true,
  })
  platformFee: number;

  @Column({
    name: 'paymentGatewayFee',
    type: 'bigint',
    nullable: true,
  })
  paymentGatewayFee: number;

  @Column({
    name: 'total',
    type: 'bigint',
    nullable: true,
  })
  total: number;

  @Column({
    name: 'shopNetPayout',
    type: 'bigint',
    nullable: true,
  })
  shopNetPayout: number;

  @Column({
    name: 'paymentMethodType',
    type: 'bigint',
    nullable: true,
  })
  paymentMethodType: PaymentMethodType;
}
