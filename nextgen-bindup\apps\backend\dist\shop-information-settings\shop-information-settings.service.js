"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShopInformationSettingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
const shop_information_settings_entity_1 = require("./entities/shop-information-settings.entity");
const site_service_1 = require("../site/site.service");
const shipping_note__settings_entity_1 = require("../shipping-note-settings/entities/shipping-note--settings.entity");
const delivery_receipt_settings_entity_1 = require("../delivery-receipt-settings/entities/delivery-receipt-settings.entity");
const order_complete_settings_entity_1 = require("../order-complete-settings/entities/order-complete-settings.entity");
const payment_method_entity_1 = require("../payment-method/entities/payment-method.entity");
const get_default_shop_setting_1 = require("../utils/get-default-shop-setting");
let ShopInformationSettingService = class ShopInformationSettingService {
    constructor(dataSource, shopInformationSettingRepo, siteService) {
        this.dataSource = dataSource;
        this.shopInformationSettingRepo = shopInformationSettingRepo;
        this.siteService = siteService;
    }
    async create(shopInformationSettingEntity) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const now = new Date();
            const setting = new shop_information_settings_entity_1.ShopInformationSettingEntity();
            setting.siteId = shopInformationSettingEntity.siteId;
            setting.isMaintenance = shopInformationSettingEntity.isMaintenance;
            setting.shopName = shopInformationSettingEntity.shopName;
            setting.logoImages = shopInformationSettingEntity.logoImages;
            setting.colorTheme = shopInformationSettingEntity.colorTheme;
            setting.shopUrl = shopInformationSettingEntity.shopUrl;
            setting.isSetupGuide = shopInformationSettingEntity.isSetupGuide;
            setting.guideUrl = shopInformationSettingEntity.guideUrl;
            setting.email = shopInformationSettingEntity.email;
            setting.isAddPrivacy = shopInformationSettingEntity.isAddPrivacy;
            setting.privacyUrl = shopInformationSettingEntity.privacyUrl;
            setting.taxMode = shopInformationSettingEntity.taxMode;
            setting.taxRate = shopInformationSettingEntity.taxRate;
            setting.taxRegulation = shopInformationSettingEntity.taxRegulation;
            setting.createdAt = now;
            setting.updatedAt = now;
            const savedSetting = await queryRunner.manager.save(setting);
            const defaultSetting = (0, get_default_shop_setting_1.getDefaultSetting)(setting.shopName, setting.email);
            await queryRunner.manager.save(shipping_note__settings_entity_1.ShippingNoteSettingEntity, {
                siteId: savedSetting.siteId,
                shippingFees: [],
                isFreeShippingCondition: false,
                shippingFeeDetail: {},
                freeShippingCondition: 0,
                freeShippingAmount: 0,
                note: defaultSetting.shippingNote.note,
                createdAt: now,
                updatedAt: now,
            });
            await queryRunner.manager.save(payment_method_entity_1.PaymentMethodEntity, {
                siteId: savedSetting.siteId,
                ...defaultSetting.paymentMethod,
                cashOnDelivery: defaultSetting.paymentMethod.cashOnDelivery,
                bankTransfer: defaultSetting.paymentMethod.bankTransfer,
                stripePaymentGateway: defaultSetting.paymentMethod.stripePaymentGateway,
                postalTransfer: defaultSetting.paymentMethod.postalTransfer,
                createdAt: now,
                updatedAt: now,
            });
            await queryRunner.manager.save(delivery_receipt_settings_entity_1.DeliveryReceiptSettingEntity, {
                siteId: savedSetting.siteId,
                header: defaultSetting.delivery.headerText,
                footer: defaultSetting.delivery.footerText,
                createdAt: now,
                updatedAt: now,
            });
            await queryRunner.manager.save(order_complete_settings_entity_1.OrderCompletionSettingEntity, {
                siteId: savedSetting.siteId,
                displayText: defaultSetting.orderCompletion.displayText,
                emailSubject: defaultSetting.orderCompletion.emailSubject,
                emailHeader: defaultSetting.orderCompletion.emailHeader,
                emailFooter: defaultSetting.orderCompletion.emailFooter,
                createdAt: now,
                updatedAt: now,
            });
            await queryRunner.commitTransaction();
            return savedSetting;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async update(id, settingData) {
        const setting = await this.shopInformationSettingRepo.findOneBy({ id: id });
        if (!setting)
            throw new app_exception_1.AppException('api.error.shop_information_setting_not_found');
        delete settingData.id;
        delete settingData.siteId;
        delete settingData.createdAt;
        settingData.updatedAt = new Date();
        await this.shopInformationSettingRepo.update(id, settingData);
        return { ...setting, ...settingData };
    }
    async findById(id) {
        return await this.shopInformationSettingRepo.findOneBy({ id });
    }
    async findOneBySiteId(siteId) {
        return await this.shopInformationSettingRepo.findOneBy({ siteId });
    }
    async delete(id) {
        const setting = await this.shopInformationSettingRepo.findOneBy({ id });
        if (!setting)
            throw new app_exception_1.AppException('api.error.shop_information_setting_not_found');
        await this.shopInformationSettingRepo.delete(id);
        return true;
    }
};
exports.ShopInformationSettingService = ShopInformationSettingService;
exports.ShopInformationSettingService = ShopInformationSettingService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __param(1, (0, typeorm_1.InjectRepository)(shop_information_settings_entity_1.ShopInformationSettingEntity)),
    __metadata("design:paramtypes", [typeorm_2.DataSource,
        typeorm_2.Repository,
        site_service_1.SiteService])
], ShopInformationSettingService);
//# sourceMappingURL=shop-information-settings.service.js.map