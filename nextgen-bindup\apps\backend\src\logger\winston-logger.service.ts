import { Injectable } from '@nestjs/common';
import * as winston from 'winston';
import { LogData } from './dto/log-data.dto';

@Injectable()
export class WinstonLoggerService {
  private logger: winston.Logger;

  /* istanbul ignore next */
  constructor() {
    const wlFormatter = winston.format(info => {
      if (info.meta) {
        const { meta, ...others } = info.meta as any;
        Object.assign(info, others, { meta });
      }

      return info;
    })();
    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
        winston.format.timestamp({ format: 'YYYY/MM/DD HH:mm:ss' }),
        wlFormatter,
        winston.format.json(),
      ),
      transports: [new winston.transports.Console()],
    });
  }

  /* istanbul ignore next */
  info(logData: LogData) {
    this.logger.info(logData);
  }

  /* istanbul ignore next */
  error(logData: LogData) {
    this.logger.error(logData);
  }

  /* istanbul ignore next */
  warn(logData: LogData) {
    this.logger.warn(logData);
  }

  /* istanbul ignore next */
  debug(logData: LogData) {
    this.logger.debug(logData);
  }
}
