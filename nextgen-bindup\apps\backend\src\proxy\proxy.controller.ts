import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { SiteGuard } from './site.guard';
import { SiteId } from './site-auth.decorater';
import { ShippingNoteSettingService } from 'src/shipping-note-settings/shipping-note-settings.service';
import { ShopInformationSettingService } from 'src/shop-information-settings/shop-information-settings.service';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { OrderService } from 'src/order/order.service';
import { ProductStocksService } from 'src/product-stocks/product-stocks.service';
import { CartItem } from 'src/product-stocks/dto/product-stock.dto';
import { UserPaymentService } from 'src/payment/user-payment.service';
import { CreateCheckoutSession } from 'src/payment/dto/create-checkout-session.dto';
import { PaymentMethodType } from 'src/order/enum/payment-method-type.enum';
import { Request } from 'express';
import { SiteService } from 'src/site/site.service';
import { OrderCompletionSettingService } from 'src/order-complete-settings/order-complete-settings.service';

@Controller('proxy')
export class ProxyController {
  constructor(
    private readonly shippingNoteSettingService: ShippingNoteSettingService,
    private readonly shopInformationSettingService: ShopInformationSettingService,
    private readonly paymentMethodService: PaymentMethodService,
    private readonly orderService: OrderService,
    private readonly productStockService: ProductStocksService,
    private readonly userPaymentService: UserPaymentService,
    private readonly siteService: SiteService,
    private readonly orderCompletionSettingService: OrderCompletionSettingService,
  ) {}

  @Post('check-stock')
  @UseGuards(SiteGuard)
  checkStock(
    @SiteId() siteId: number,
    @Body() { cartItems }: { cartItems: CartItem[] },
  ) {
    return this.productStockService.checkStock(siteId, cartItems);
  }

  @Get('shop-information-setting')
  @UseGuards(SiteGuard)
  getShopInformation(@SiteId() siteId: number) {
    return this.shopInformationSettingService.findOneBySiteId(siteId);
  }

  @Get('shipping-note-setting')
  @UseGuards(SiteGuard)
  getShippingNoteSetting(@SiteId() siteId: number) {
    return this.shippingNoteSettingService.findOneBySiteId(siteId);
  }

  @Get('payment-method-setting')
  @UseGuards(SiteGuard)
  getPaymentMethods(@SiteId() siteId: number) {
    return this.paymentMethodService.getPaymentMethodBySiteId(siteId);
  }

  @Get('order-completion-setting')
  @UseGuards(SiteGuard)
  getOrderCompletionSettings(@SiteId() siteId: number) {
    return this.orderCompletionSettingService.findOneBySiteId(siteId);
  }

  @Get('order-detail/:orderId')
  getOrderDetail(@Param('orderId') orderId: string) {
    return this.orderService.findOrderById(+orderId);
  }

  @Post('checkout')
  @UseGuards(SiteGuard)
  async checkout(
    @SiteId() siteId: number,
    @Req() req: Request,
    @Body() body: CreateCheckoutSession,
  ) {
    const origin = req.get('origin') || req.get('referer') || '';
    const cleanedOrigin = origin.split('/').slice(0, 3).join('/');
    const createOrder = body.createOrder;
    if (!createOrder) {
      throw new Error('Create order data is required');
    }
    const site = await this.siteService.findById(siteId);
    if (!site) {
      throw new Error('Site not found');
    }
    createOrder.siteId = siteId;
    if (body.paymentMethodType === PaymentMethodType.CREDIT_CARD) {
      const checkout = await this.userPaymentService.checkoutWithCreaditCard(
        cleanedOrigin,
        createOrder,
      );
      return {
        success: true,
        paymentMethodType: PaymentMethodType.CREDIT_CARD,
        url: checkout.url,
      };
    }
    if (body.paymentMethodType === PaymentMethodType.BANK_TRANSFER) {
      const checkout = await this.userPaymentService.checkoutWithBankTransfer(
        body.createOrder,
      );
      return {
        success: true,
        paymentMethodType: PaymentMethodType.BANK_TRANSFER,
        orderId: checkout.orderId,
      };
    }
    if (body.paymentMethodType === PaymentMethodType.CASH_ON_DELIVERY) {
      const checkout = await this.userPaymentService.checkoutWithCashOnDelivery(
        body.createOrder,
      );
      return {
        success: true,
        paymentMethodType: PaymentMethodType.CASH_ON_DELIVERY,
        orderId: checkout.orderId,
      };
    }
    if (body.paymentMethodType === PaymentMethodType.POSTAL_TRANSFER) {
      const checkout = await this.userPaymentService.checkoutWithPostalTransfer(
        body.createOrder,
      );
      return {
        success: true,
        paymentMethodType: PaymentMethodType.POSTAL_TRANSFER,
        orderId: checkout.orderId,
      };
    }
  }
}
