import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateShopInformationSettingsTable1748401492925
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}shop_information_settings`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isGenerated: true,
            generationStrategy: 'increment',
            isPrimary: true,
          },
          {
            name: 'siteId',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'isMaintenance',
            type: 'boolean',
            isNullable: false,
          },
          {
            name: 'shopName',
            type: 'varchar',
            length: '250',
            isNullable: false,
          },
          {
            name: 'logoImages',
            type: 'text',
            isArray: true,
            isNullable: true,
          },
          {
            name: 'colorTheme',
            type: 'varchar',
            length: '250',
            isNullable: false,
          },
          {
            name: 'shopUrl',
            type: 'varchar',
            length: '250',
            isNullable: true,
          },
          {
            name: 'isSetupGuide',
            type: 'boolean',
            isNullable: false,
          },
          {
            name: 'guideUrl',
            type: 'varchar',
            length: '250',
            isNullable: true,
          },
          {
            name: 'email',
            type: 'varchar',
            length: '250',
            isNullable: false,
          },
          {
            name: 'isAddPrivacy',
            type: 'boolean',
            isNullable: false,
          },
          {
            name: 'privacyUrl',
            type: 'varchar',
            length: '250',
            isNullable: true,
          },
          {
            name: 'taxMode',
            type: 'varchar',
            length: '250',
            isNullable: false,
          },
          {
            name: 'taxRate',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'taxRegulation',
            type: 'varchar',
            length: '250',
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
