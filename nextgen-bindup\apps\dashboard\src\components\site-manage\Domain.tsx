import { type FC } from 'react';
import { Stack } from '@mui/material';
import { DNSRecords } from './dns-record/DnsRecords';
import NameServers from './name-server/NameServers';
import Subdomains from './subdomain/Subdomains';

type Props = {
  siteId: number;
  projectId: number;
};
const Domain: FC<Props> = ({ siteId, projectId }) => (
  <>
    <Stack spacing={4}>
      <DNSRecords siteId={siteId} projectId={projectId} />
      <NameServers siteId={siteId} projectId={projectId} />
      <Subdomains siteId={siteId} projectId={projectId} />
    </Stack>
  </>
);

export default Domain;
