"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSiteVersionTable1740974200208 = void 0;
const typeorm_1 = require("typeorm");
class UpdateSiteVersionTable1740974200208 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}site_versions`;
    }
    async up(queryRunner) {
        const userIdColumn = new typeorm_1.TableColumn({
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, userIdColumn);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'userId');
    }
}
exports.UpdateSiteVersionTable1740974200208 = UpdateSiteVersionTable1740974200208;
//# sourceMappingURL=1740974200208-update-site-version-table.js.map