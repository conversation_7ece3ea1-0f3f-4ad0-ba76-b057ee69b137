import { Injectable } from '@nestjs/common';
import { DnsRecordEntity } from './entities/dns-record.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { AppException } from 'src/common/exceptions/app.exception';

@Injectable()
export class DnsRecordService {
  @InjectRepository(DnsRecordEntity)
  readonly dnsRecordRepo: Repository<DnsRecordEntity>;

  async create(data: DnsRecordEntity): Promise<DnsRecordEntity> {
    delete data.id;

    const now = new Date();
    data.createdAt = now;
    data.updatedAt = now;

    return await this.dnsRecordRepo.save(data);
  }

  async update(
    id: number,
    data: Partial<DnsRecordEntity>,
  ): Promise<DnsRecordEntity> {
    const entity = await this.dnsRecordRepo.findOneBy({ id });
    if (!entity) throw new AppException('api.error.dns_record_not_found');

    const now = new Date();
    data.updatedAt = now;

    delete data.id;
    await this.dnsRecordRepo.update(id, data);
    return { ...entity, ...data };
  }

  async delete(id: number): Promise<boolean> {
    const entity = await this.dnsRecordRepo.findOneBy({ id });
    if (!entity) throw new AppException('api.error.dns_record_not_found');

    await this.dnsRecordRepo.delete(id);
    return true;
  }

  async findBySiteId(siteId: number): Promise<DnsRecordEntity[]> {
    return await this.dnsRecordRepo.findBy({ siteId });
  }

  async findByProjectId(projectId: number): Promise<DnsRecordEntity[]> {
    return await this.dnsRecordRepo.findBy({ projectId });
  }
}
