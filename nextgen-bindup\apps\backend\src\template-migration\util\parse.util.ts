import { DOM<PERSON>arser } from '@xmldom/xmldom';
import {
  BlockData_Content,
  BlockData_Content_Attributes,
  BlockData_Group,
} from '../dto/blockdata-content.dto';
import { Site5_Resource } from '../dto/site5_resource.dto';
import { Site4_BlockData } from '../dto/site4_blockdata.dto';

const COMMENT_CLASSNAME = 'comment';
const TAG_KAIDAN = 'kaidan';

export class ParseUtil {
  static parseContent(
    blockData: Site4_BlockData,
    resources: Site5_Resource[],
  ): BlockData_Content[] {
    const result: BlockData_Content[] = [];

    const content = `<bind>${blockData.content}</bind>`;
    const doc = new DOMParser().parseFromString(content, 'text/xml');
    const root = doc.documentElement;

    for (const node of root.childNodes) {
      if (node.nodeType !== node.ELEMENT_NODE) continue;
      const elem = node as unknown as Element;
      const content: BlockData_Content = ParseUtil.parseChildContent(
        blockData.blockdataId,
        elem,
        resources,
      );

      // Bỏ qua element có class="comment" nhưng không phải là H1
      if (
        content.attributes.class === COMMENT_CLASSNAME &&
        content.nodeName !== 'H1'
      )
        continue;
      result.push(content);
    }

    return result;
  }

  static parseChildContent(
    blockdataId: number,
    elem: Element,
    resources: Site5_Resource[],
  ): BlockData_Content {
    let index: number = 1;
    const attributes: Record<string, string> = {};
    if (elem.hasAttributes()) {
      for (const attr of elem.attributes) {
        attributes[attr.name] = attr.value;
      }
    }

    const content: BlockData_Content = {
      index: index++,
      nodeName: elem.nodeName,
      text: '',
      attributes: attributes as BlockData_Content_Attributes,
      children: [],
    };

    if (content.attributes.tag) {
      const resourceId: number = Number(content.attributes.tag);
      content.resource = resources.find(
        resource =>
          resource.resourceId === resourceId &&
          resource.blockdataId === blockdataId,
      );
    }

    let childIndex: number = 1;
    for (const node of elem.childNodes) {
      if (node.nodeType === 3) {
        const text = node.textContent;
        if (text && text.trim()) {
          // Preserve &nbsp; by not trimming and handling them specially
          const processedText = text.replace(/\u00A0/g, '&nbsp;');
          content.children.push({
            index: childIndex++,
            nodeName: '#text',
            text: processedText,
            attributes: {} as BlockData_Content_Attributes,
            children: [],
          });
        }
      } else if (node.nodeType === 1) {
        // element
        const childElement = node as Element;
        const childContent = ParseUtil.parseChildContent(
          blockdataId,
          childElement,
          resources,
        );

        // Bỏ qua element có class="comment" nhưng không phải là H1
        if (
          childContent.attributes.class === COMMENT_CLASSNAME &&
          childContent.nodeName !== 'H1'
        )
          continue;
        content.children.push(childContent);
      }
    }

    return content;
  }

  /* Tách các content ra thành từng group dựa vào tag="kaidan"
  INPUT:
    - RAW:
      <P><img src="" tag="17935" /></P>
      <P><img src="img/bdRtfObject_kaidan.png" tag="kaidan" style="border:none" /></P>
      <P style="text-align: right;"><img src="" tag="17934" /></P>
    - JSON:
    [
      {
        nodeName: 'P',
        children: [
          {
            nodeName: 'img',
            attributes: { "src": "", "tag": "17935" }
          }
        ]
      },
      {
        nodeName: 'P',
        children: [
          {
            nodeName: 'img',
            attributes: { "src": "img/bdRtfObject_kaidan.png", "tag": "kaidan" }
          }
        ]
      },
      {
        nodeName: 'P',
        children: [
          {
            nodeName: 'img',
            attributes: { "src": "", "tag": "17934" }
          }
        ]
      }
    ]
  OUTPUT:
    [
      {
        contents: [
          {
            nodeName: 'P',
            children: [
              {
                nodeName: 'img',
                attributes: { "src": "", "tag": "17935" }
              }
            ]
          }
        ]
      },
      {
        contents: [
          {
            nodeName: 'P',
            children: [
              {
                nodeName: 'img',
                attributes: { "src": "", "tag": "17934" }
              }
            ]
          }
        ]
      }
    ]
  */
  static groupChild(dataContents: BlockData_Content[]): BlockData_Group[] {
    let index: number = 0;
    const result: BlockData_Group[] = [{ index: index + 1, contents: [] }];

    for (const child of dataContents) {
      let isKaidan: boolean = false;
      if (child.attributes.tag === TAG_KAIDAN) isKaidan = true;
      else if (
        child.children.length &&
        child.children[0].attributes.tag === TAG_KAIDAN
      )
        isKaidan = true;

      if (isKaidan) {
        index++;
        result.push({ index: index + 1, contents: [] });
      } else {
        result[index].contents.push(child);
      }
    }
    return result;
  }

  static parseStyleString(styleString: string) {
    return styleString
      .split(';')
      .filter(s => s.trim() !== '')
      .reduce((acc, item) => {
        const [key, value] = item.split(':');
        if (key && value) {
          acc[key.trim()] = value.trim();
        }
        return acc;
      }, {});
  }
}
