"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubDomainController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../auth/auth.guard");
const sub_domain_service_1 = require("./sub-domain.service");
const sub_domain_entity_1 = require("./entities/sub-domain.entity");
let SubDomainController = class SubDomainController {
    constructor(subDomainService) {
        this.subDomainService = subDomainService;
    }
    async create(data) {
        return await this.subDomainService.create(data);
    }
    async update(id, data) {
        return await this.subDomainService.update(+id, data);
    }
    async delete(id) {
        return await this.subDomainService.delete(+id);
    }
    async findBySiteId(siteId) {
        return await this.subDomainService.findBySiteId(+siteId);
    }
    async findByProjectId(projectId) {
        return await this.subDomainService.findByProjectId(+projectId);
    }
};
exports.SubDomainController = SubDomainController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [sub_domain_entity_1.SubDomainEntity]),
    __metadata("design:returntype", Promise)
], SubDomainController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SubDomainController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SubDomainController.prototype, "delete", null);
__decorate([
    (0, common_1.Get)('site/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SubDomainController.prototype, "findBySiteId", null);
__decorate([
    (0, common_1.Get)('project/:projectId'),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SubDomainController.prototype, "findByProjectId", null);
exports.SubDomainController = SubDomainController = __decorate([
    (0, common_1.Controller)('sub-domain'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [sub_domain_service_1.SubDomainService])
], SubDomainController);
//# sourceMappingURL=sub-domain.controller.js.map