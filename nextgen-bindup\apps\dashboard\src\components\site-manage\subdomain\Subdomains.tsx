import { FC, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import {
  Box,
  Typography,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  IconButton,
  Dialog,
  TextField,
  Button,
  Stack,
  DialogActions,
  Switch,
  FormControlLabel,
} from '@mui/material';
import { SubdomainEntity } from '../../../dto/subdomain.type';
import { subdomainService } from '../../../services/subdomain-service';
import ConfirmationDialog from '../../common/ConfirmmationDialog';

type Props = {
  siteId: number;
  projectId: number;
};

const Subdomains: FC<Props> = ({ projectId, siteId }) => {
  const { t } = useTranslation();
  const [subdomains, setSubdomains] = useState<SubdomainEntity[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<SubdomainEntity | null>(
    null,
  );
  const [editingSubdomain, setEditingSubdomain] =
    useState<SubdomainEntity | null>(null);
  const [newSubdomain, setNewSubdomain] = useState<SubdomainEntity>({
    id: undefined,
    projectId: projectId,
    subdomain: '',
    directory: '',
    https: false,
    ssl: false,
    siteId: siteId,
  });
  const [openDialog, setOpenDialog] = useState(false);

  useEffect(() => {
    const fetchSubdomains = async () => {
      const data = await subdomainService.findBySiteId(siteId);
      setSubdomains(data);
    };
    fetchSubdomains();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setNewSubdomain({ ...newSubdomain, [name]: value });
    if (isEditing && editingSubdomain) {
      setEditingSubdomain({ ...editingSubdomain, [name]: value });
    }
  };

  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = event.target;
    setNewSubdomain({ ...newSubdomain, [name]: checked });
    if (isEditing && editingSubdomain) {
      setEditingSubdomain({ ...editingSubdomain, [name]: checked });
    }
  };

  const handleCreate = async () => {
    try {
      await subdomainService.create(newSubdomain);
      setOpenDialog(false);
      const data = await subdomainService.findBySiteId(siteId);
      setSubdomains(data);
      setNewSubdomain({
        id: undefined,
        projectId: projectId,
        subdomain: '',
        directory: '',
        https: false,
        ssl: false,
        siteId: siteId,
      });
    } catch (error) {
      console.error('Error creating subdomain:', error);
    }
  };

  const handleEdit = (subdomain: SubdomainEntity) => {
    setEditingSubdomain(subdomain);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const handleUpdate = async () => {
    if (!editingSubdomain || !editingSubdomain.id) return;
    try {
      await subdomainService.update(editingSubdomain.id, editingSubdomain);
      setOpenDialog(false);
      const data = await subdomainService.findBySiteId(siteId);
      setSubdomains(data);
      setEditingSubdomain(null);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating subdomain:', error);
    }
  };

  const handleDelete = (subdomain: SubdomainEntity) => {
    setRecordToDelete(subdomain);
    setOpenConfirmDialog(true);
  };

  const confirmDelete = async () => {
    if (!recordToDelete || !recordToDelete.id) return;
    try {
      await subdomainService.delete(recordToDelete.id);
      const records = await subdomainService.findBySiteId(siteId);
      setSubdomains(records);
      setOpenConfirmDialog(false);
      setRecordToDelete(null);
    } catch (error) {
      console.error('Error deleting DNS record:', error);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography>{t('site.own_domain.subdomain.title')}</Typography>
        <Button
          variant="outlined"
          onClick={() => {
            setOpenDialog(true);
            setIsEditing(false);
            setEditingSubdomain(null);
          }}
        >
          {t('common.add')}
        </Button>
      </Box>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>{t('site.own_domain.subdomain.subdomain')}</TableCell>
            <TableCell>{t('site.own_domain.subdomain.directory')}</TableCell>
            <TableCell>
              {t('site.own_domain.subdomain.https_forward.label')}
            </TableCell>
            <TableCell>{t('site.own_domain.subdomain.ssl.label')}</TableCell>
            <TableCell></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {subdomains.map(subdomain => (
            <TableRow
              key={subdomain.id}
              sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
            >
              <TableCell component="th" scope="row">
                {subdomain.subdomain}
              </TableCell>
              <TableCell>{subdomain.directory}</TableCell>
              <TableCell>
                {subdomain.https
                  ? t('site.own_domain.subdomain.https_forward.configured')
                  : t('site.own_domain.subdomain.not_set')}
              </TableCell>
              <TableCell>
                {subdomain.ssl
                  ? t('site.own_domain.subdomain.ssl.configured')
                  : t('site.own_domain.subdomain.not_set')}
              </TableCell>
              <TableCell align="right">
                <IconButton onClick={() => handleEdit(subdomain)}>
                  <EditIcon />
                </IconButton>
                <IconButton onClick={() => handleDelete(subdomain)}>
                  <DeleteIcon />
                </IconButton>
                <IconButton>
                  <ContentCopyIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <SubdomainForm
          isEditing={isEditing}
          initialSubdomain={editingSubdomain || newSubdomain}
          handleInputChange={handleInputChange}
          handleSwitchChange={handleSwitchChange}
          onCreate={handleCreate}
          onUpdate={handleUpdate}
          onCancel={() => {
            setOpenDialog(false);
            setEditingSubdomain(null);
            setIsEditing(false);
          }}
        />
      </Dialog>

      <ConfirmationDialog
        open={openConfirmDialog}
        title={t('delete_confirm.title')}
        message={t('delete_confirm.message', {
          name: recordToDelete?.subdomain,
        })}
        onConfirm={confirmDelete}
        onCancel={() => {
          setOpenConfirmDialog(false);
          setRecordToDelete(null);
        }}
      />
    </Box>
  );
};

const SubdomainForm: FC<{
  isEditing: boolean;
  initialSubdomain: SubdomainEntity;
  handleInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleSwitchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onCreate: () => void;
  onUpdate: () => void;
  onCancel: () => void;
}> = ({
  isEditing,
  initialSubdomain,
  handleInputChange,
  handleSwitchChange,
  onCreate,
  onUpdate,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [subdomain, setSubdomain] = useState(initialSubdomain);

  useEffect(() => {
    setSubdomain(initialSubdomain);
  }, [initialSubdomain]);

  return (
    <Stack sx={{ width: '400px', padding: 2 }}>
      <Typography variant="subtitle1">
        {isEditing
          ? t('site.own_domain.subdomain.form.edit_subdomain')
          : t('site.own_domain.subdomain.form.add_subdomain')}
      </Typography>
      <TextField
        label={t('site.own_domain.subdomain.subdomain')}
        name="subdomain"
        value={subdomain.subdomain}
        onChange={handleInputChange}
        fullWidth
        margin="normal"
      />
      <TextField
        label={t('site.own_domain.subdomain.directory')}
        name="directory"
        value={subdomain.directory}
        onChange={handleInputChange}
        fullWidth
        margin="normal"
      />
      <FormControlLabel
        control={
          <Switch
            checked={subdomain.https}
            onChange={handleSwitchChange}
            name="https"
          />
        }
        label={t('site.own_domain.subdomain.https_forward.label')}
      />
      <FormControlLabel
        control={
          <Switch
            checked={subdomain.ssl}
            onChange={handleSwitchChange}
            name="ssl"
          />
        }
        label={t('site.own_domain.subdomain.ssl.label')}
      />
      <DialogActions>
        <Button onClick={onCancel}>{t('common.cancel')}</Button>
        <Button variant="contained" onClick={isEditing ? onUpdate : onCreate}>
          {isEditing ? t('common.update') : t('common.create')}
        </Button>
      </DialogActions>
    </Stack>
  );
};

export default Subdomains;
