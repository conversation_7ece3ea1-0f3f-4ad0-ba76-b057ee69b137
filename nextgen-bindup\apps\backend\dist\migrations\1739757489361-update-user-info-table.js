"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateuserInfoTable1739757489361 = void 0;
const typeorm_1 = require("typeorm");
class UpdateuserInfoTable1739757489361 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}user_info`;
    }
    async up(queryRunner) {
        const column = new typeorm_1.TableColumn({
            name: 'recentlySite',
            type: 'jsonb',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, column);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'recentlySite');
    }
}
exports.UpdateuserInfoTable1739757489361 = UpdateuserInfoTable1739757489361;
//# sourceMappingURL=1739757489361-update-user-info-table.js.map