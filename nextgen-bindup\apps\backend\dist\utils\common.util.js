"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isEmptyNumber = exports.isInteger = exports.ConvertToSlug = exports.MAX_PRODUCT_QUANTITY_VALUE = exports.MAX_PRICE_VALUE = exports.NEW_TS = void 0;
const NEW_TS = () => new Date().getTime();
exports.NEW_TS = NEW_TS;
exports.MAX_PRICE_VALUE = 999999;
exports.MAX_PRODUCT_QUANTITY_VALUE = 99999;
const ConvertToSlug = (text) => {
    return text
        .toLowerCase()
        .replace(/ /g, '-')
        .replace(/[^\w-]+/g, '');
};
exports.ConvertToSlug = ConvertToSlug;
const isInteger = (value, opt) => {
    if (value === null || value === undefined)
        return false;
    if (value.toString() === '')
        return false;
    if (!Number.isInteger(value))
        return false;
    if (opt) {
        if (opt.min && value < opt.min)
            return false;
        if (opt.max && value > opt.max)
            return false;
    }
    return true;
};
exports.isInteger = isInteger;
const isEmptyNumber = (value) => {
    if (value === null || value === undefined)
        return true;
    if (value.toString() === '')
        return true;
    return false;
};
exports.isEmptyNumber = isEmptyNumber;
//# sourceMappingURL=common.util.js.map