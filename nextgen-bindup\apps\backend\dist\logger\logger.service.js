"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerService = void 0;
const common_1 = require("@nestjs/common");
const winston_logger_service_1 = require("./winston-logger.service");
let LoggerService = class LoggerService {
    constructor(winstonLoggerService) {
        this.winstonLoggerService = winstonLoggerService;
    }
    async info(shop, message, meta) {
        this.winstonLoggerService.info({
            message: message,
            meta: meta,
        });
    }
    async error(shop, message, meta) {
        this.winstonLoggerService.error({
            message: message,
            meta: meta,
        });
    }
    async warn(shop, message, meta) {
        this.winstonLoggerService.warn({
            message: message,
            meta: meta,
        });
    }
    async debug(shop, message, meta) {
        this.winstonLoggerService.debug({
            message: message,
            meta: meta,
        });
    }
};
exports.LoggerService = LoggerService;
exports.LoggerService = LoggerService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLoggerService])
], LoggerService);
//# sourceMappingURL=logger.service.js.map