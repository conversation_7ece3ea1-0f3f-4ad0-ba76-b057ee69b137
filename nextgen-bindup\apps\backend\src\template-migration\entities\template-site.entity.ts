import { SiteStatus } from 'src/site/types/site.type';
import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('template_sites', { schema: process.env.DATABASE_SCHEMA })
export class TemplateSiteEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'templateId',
    type: 'integer',
    nullable: false,
  })
  templateId: number;

  @Column({
    name: 'managementName',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  managementName: string;

  @Column({
    name: 'status',
    type: 'smallint',
    nullable: false,
  })
  status: SiteStatus = SiteStatus.DRAFT;

  @Column({
    name: 'url',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  url: string;

  @Column({
    name: 'title',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  title: string;

  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
  })
  description: string;

  @Column({
    name: 'isSearch',
    type: 'boolean',
    nullable: true,
  })
  isSearch: boolean;

  @Column({
    name: 'thumb',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  thumb: string;

  @Column({
    name: 'headCode',
    type: 'text',
    nullable: true,
  })
  headCode: string;

  @Column({
    name: 'bodyCode',
    type: 'text',
    nullable: true,
  })
  bodyCode: string;

  @Column({
    name: 'isArchived',
    type: 'boolean',
    nullable: true,
  })
  isArchived: boolean;

  @Column({
    name: 'userId',
    type: 'varchar',
    length: '36',
    nullable: false,
  })
  userId: string;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
