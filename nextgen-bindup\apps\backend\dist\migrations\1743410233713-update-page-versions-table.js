"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePageVersionsTable1743410233713 = void 0;
class UpdatePageVersionsTable1743410233713 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}page_versions`;
    }
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}
      ALTER COLUMN type TYPE VARCHAR(15);
    `);
    }
    async down(queryRunner) {
        console.log(!!queryRunner);
    }
}
exports.UpdatePageVersionsTable1743410233713 = UpdatePageVersionsTable1743410233713;
//# sourceMappingURL=1743410233713-update-page-versions-table.js.map