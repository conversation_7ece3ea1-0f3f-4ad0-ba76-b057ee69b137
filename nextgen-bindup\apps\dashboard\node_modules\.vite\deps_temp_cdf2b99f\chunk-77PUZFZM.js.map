{"version": 3, "sources": ["../../../../../node_modules/@mui/material/node_modules/react-is/cjs/react-is.development.js", "../../../../../node_modules/@mui/material/node_modules/react-is/index.js"], "sourcesContent": ["/**\n * @license React\n * react-is.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function typeOf(object) {\n      if (\"object\" === typeof object && null !== object) {\n        var $$typeof = object.$$typeof;\n        switch ($$typeof) {\n          case REACT_ELEMENT_TYPE:\n            switch (((object = object.type), object)) {\n              case REACT_FRAGMENT_TYPE:\n              case REACT_PROFILER_TYPE:\n              case REACT_STRICT_MODE_TYPE:\n              case REACT_SUSPENSE_TYPE:\n              case REACT_SUSPENSE_LIST_TYPE:\n              case REACT_VIEW_TRANSITION_TYPE:\n                return object;\n              default:\n                switch (((object = object && object.$$typeof), object)) {\n                  case REACT_CONTEXT_TYPE:\n                  case REACT_FORWARD_REF_TYPE:\n                  case REACT_LAZY_TYPE:\n                  case REACT_MEMO_TYPE:\n                    return object;\n                  case REACT_CONSUMER_TYPE:\n                    return object;\n                  default:\n                    return $$typeof;\n                }\n            }\n          case REACT_PORTAL_TYPE:\n            return $$typeof;\n        }\n      }\n    }\n    var REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_VIEW_TRANSITION_TYPE = Symbol.for(\"react.view_transition\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\n    exports.ContextConsumer = REACT_CONSUMER_TYPE;\n    exports.ContextProvider = REACT_CONTEXT_TYPE;\n    exports.Element = REACT_ELEMENT_TYPE;\n    exports.ForwardRef = REACT_FORWARD_REF_TYPE;\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.Lazy = REACT_LAZY_TYPE;\n    exports.Memo = REACT_MEMO_TYPE;\n    exports.Portal = REACT_PORTAL_TYPE;\n    exports.Profiler = REACT_PROFILER_TYPE;\n    exports.StrictMode = REACT_STRICT_MODE_TYPE;\n    exports.Suspense = REACT_SUSPENSE_TYPE;\n    exports.SuspenseList = REACT_SUSPENSE_LIST_TYPE;\n    exports.isContextConsumer = function (object) {\n      return typeOf(object) === REACT_CONSUMER_TYPE;\n    };\n    exports.isContextProvider = function (object) {\n      return typeOf(object) === REACT_CONTEXT_TYPE;\n    };\n    exports.isElement = function (object) {\n      return (\n        \"object\" === typeof object &&\n        null !== object &&\n        object.$$typeof === REACT_ELEMENT_TYPE\n      );\n    };\n    exports.isForwardRef = function (object) {\n      return typeOf(object) === REACT_FORWARD_REF_TYPE;\n    };\n    exports.isFragment = function (object) {\n      return typeOf(object) === REACT_FRAGMENT_TYPE;\n    };\n    exports.isLazy = function (object) {\n      return typeOf(object) === REACT_LAZY_TYPE;\n    };\n    exports.isMemo = function (object) {\n      return typeOf(object) === REACT_MEMO_TYPE;\n    };\n    exports.isPortal = function (object) {\n      return typeOf(object) === REACT_PORTAL_TYPE;\n    };\n    exports.isProfiler = function (object) {\n      return typeOf(object) === REACT_PROFILER_TYPE;\n    };\n    exports.isStrictMode = function (object) {\n      return typeOf(object) === REACT_STRICT_MODE_TYPE;\n    };\n    exports.isSuspense = function (object) {\n      return typeOf(object) === REACT_SUSPENSE_TYPE;\n    };\n    exports.isSuspenseList = function (object) {\n      return typeOf(object) === REACT_SUSPENSE_LIST_TYPE;\n    };\n    exports.isValidElementType = function (type) {\n      return \"string\" === typeof type ||\n        \"function\" === typeof type ||\n        type === REACT_FRAGMENT_TYPE ||\n        type === REACT_PROFILER_TYPE ||\n        type === REACT_STRICT_MODE_TYPE ||\n        type === REACT_SUSPENSE_TYPE ||\n        type === REACT_SUSPENSE_LIST_TYPE ||\n        (\"object\" === typeof type &&\n          null !== type &&\n          (type.$$typeof === REACT_LAZY_TYPE ||\n            type.$$typeof === REACT_MEMO_TYPE ||\n            type.$$typeof === REACT_CONTEXT_TYPE ||\n            type.$$typeof === REACT_CONSUMER_TYPE ||\n            type.$$typeof === REACT_FORWARD_REF_TYPE ||\n            type.$$typeof === REACT_CLIENT_REFERENCE ||\n            void 0 !== type.getModuleId))\n        ? !0\n        : !1;\n    };\n    exports.typeOf = typeOf;\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,OAAO,QAAQ;AACtB,YAAI,aAAa,OAAO,UAAU,SAAS,QAAQ;AACjD,cAAI,WAAW,OAAO;AACtB,kBAAQ,UAAU;AAAA,YAChB,KAAK;AACH,sBAAU,SAAS,OAAO,MAAO,QAAS;AAAA,gBACxC,KAAK;AAAA,gBACL,KAAK;AAAA,gBACL,KAAK;AAAA,gBACL,KAAK;AAAA,gBACL,KAAK;AAAA,gBACL,KAAK;AACH,yBAAO;AAAA,gBACT;AACE,0BAAU,SAAS,UAAU,OAAO,UAAW,QAAS;AAAA,oBACtD,KAAK;AAAA,oBACL,KAAK;AAAA,oBACL,KAAK;AAAA,oBACL,KAAK;AACH,6BAAO;AAAA,oBACT,KAAK;AACH,6BAAO;AAAA,oBACT;AACE,6BAAO;AAAA,kBACX;AAAA,cACJ;AAAA,YACF,KAAK;AACH,qBAAO;AAAA,UACX;AAAA,QACF;AAAA,MACF;AACA,UAAI,qBAAqB,OAAO,IAAI,4BAA4B,GAC9D,oBAAoB,OAAO,IAAI,cAAc,GAC7C,sBAAsB,OAAO,IAAI,gBAAgB,GACjD,yBAAyB,OAAO,IAAI,mBAAmB,GACvD,sBAAsB,OAAO,IAAI,gBAAgB;AACnD,aAAO,IAAI,gBAAgB;AAC3B,UAAI,sBAAsB,OAAO,IAAI,gBAAgB,GACnD,qBAAqB,OAAO,IAAI,eAAe,GAC/C,yBAAyB,OAAO,IAAI,mBAAmB,GACvD,sBAAsB,OAAO,IAAI,gBAAgB,GACjD,2BAA2B,OAAO,IAAI,qBAAqB,GAC3D,kBAAkB,OAAO,IAAI,YAAY,GACzC,kBAAkB,OAAO,IAAI,YAAY,GACzC,6BAA6B,OAAO,IAAI,uBAAuB,GAC/D,yBAAyB,OAAO,IAAI,wBAAwB;AAC9D,cAAQ,kBAAkB;AAC1B,cAAQ,kBAAkB;AAC1B,cAAQ,UAAU;AAClB,cAAQ,aAAa;AACrB,cAAQ,WAAW;AACnB,cAAQ,OAAO;AACf,cAAQ,OAAO;AACf,cAAQ,SAAS;AACjB,cAAQ,WAAW;AACnB,cAAQ,aAAa;AACrB,cAAQ,WAAW;AACnB,cAAQ,eAAe;AACvB,cAAQ,oBAAoB,SAAU,QAAQ;AAC5C,eAAO,OAAO,MAAM,MAAM;AAAA,MAC5B;AACA,cAAQ,oBAAoB,SAAU,QAAQ;AAC5C,eAAO,OAAO,MAAM,MAAM;AAAA,MAC5B;AACA,cAAQ,YAAY,SAAU,QAAQ;AACpC,eACE,aAAa,OAAO,UACpB,SAAS,UACT,OAAO,aAAa;AAAA,MAExB;AACA,cAAQ,eAAe,SAAU,QAAQ;AACvC,eAAO,OAAO,MAAM,MAAM;AAAA,MAC5B;AACA,cAAQ,aAAa,SAAU,QAAQ;AACrC,eAAO,OAAO,MAAM,MAAM;AAAA,MAC5B;AACA,cAAQ,SAAS,SAAU,QAAQ;AACjC,eAAO,OAAO,MAAM,MAAM;AAAA,MAC5B;AACA,cAAQ,SAAS,SAAU,QAAQ;AACjC,eAAO,OAAO,MAAM,MAAM;AAAA,MAC5B;AACA,cAAQ,WAAW,SAAU,QAAQ;AACnC,eAAO,OAAO,MAAM,MAAM;AAAA,MAC5B;AACA,cAAQ,aAAa,SAAU,QAAQ;AACrC,eAAO,OAAO,MAAM,MAAM;AAAA,MAC5B;AACA,cAAQ,eAAe,SAAU,QAAQ;AACvC,eAAO,OAAO,MAAM,MAAM;AAAA,MAC5B;AACA,cAAQ,aAAa,SAAU,QAAQ;AACrC,eAAO,OAAO,MAAM,MAAM;AAAA,MAC5B;AACA,cAAQ,iBAAiB,SAAU,QAAQ;AACzC,eAAO,OAAO,MAAM,MAAM;AAAA,MAC5B;AACA,cAAQ,qBAAqB,SAAU,MAAM;AAC3C,eAAO,aAAa,OAAO,QACzB,eAAe,OAAO,QACtB,SAAS,uBACT,SAAS,uBACT,SAAS,0BACT,SAAS,uBACT,SAAS,4BACR,aAAa,OAAO,QACnB,SAAS,SACR,KAAK,aAAa,mBACjB,KAAK,aAAa,mBAClB,KAAK,aAAa,sBAClB,KAAK,aAAa,uBAClB,KAAK,aAAa,0BAClB,KAAK,aAAa,0BAClB,WAAW,KAAK,eAClB,OACA;AAAA,MACN;AACA,cAAQ,SAAS;AAAA,IACnB,GAAG;AAAA;AAAA;;;ACpIL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": []}