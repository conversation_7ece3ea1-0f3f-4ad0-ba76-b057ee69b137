import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateDnsRecordsTable1738652405179 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}dns_records`; // Tên bảng

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'projectId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'type',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'subdomain',
            type: 'varchar',
            length: '255',
            isNullable: true, // Cho phép null
          },
          {
            name: 'value',
            type: 'varchar',
            length: '255',
            isNullable: true, // Cho phép null
          },
          {
            name: 'ttl',
            type: 'int',
            isNullable: true, // Cho phép null
          },
          {
            name: 'status',
            type: 'varchar',
            length: '255',
            isNullable: true, // Cho phép null
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );
    await queryRunner.query(
      `alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
