import { Injectable } from '@nestjs/common';
import { PageService } from 'src/page/page.service';
import { ReadFileUtil } from './util/read-file.util';
import { Site2_Page } from './dto/site2_page.dto';
import { Block_Area, Site3_Block } from './dto/site3_block.dto';
import { Site4_BlockData } from './dto/site4_blockdata.dto';
import { Site5_Resource } from './dto/site5_resource.dto';
import { Site6_Srclist1 } from './dto/site6_srclist1.dto';
import { PageEntity } from 'src/page/entities/page.entity';
import { TemplateUtil } from './util/template.util';
import * as path from 'path';
import * as fs from 'fs/promises';
import { TemplateEntity } from 'src/template-migration/entities/template.entity';
import { TemplateSiteEntity } from 'src/template-migration/entities/template-site.entity';
import { TemplatePageEntity } from 'src/template-migration/entities/template-page.entity';
import { Template_Site } from './dto/template_site.dto';
import { Site0_Site } from './dto/site0_site.dto';
import { Site1_Corner } from './dto/site1_corner.dto';
import { SiteStatus } from 'src/site/types/site.type';
import { NEW_TS } from 'src/utils/common.util';
import { PageStatus, PageType } from 'src/page/types/page.type';
import { ComponentType } from 'src/page/types/component.type';
import { PROP_ACTION_DEFAULT_VALUE } from 'src/page/utils/prop-action-default-value';
import { PROP_BACKGROUND_LIST_DEFAULT_VALUE } from 'src/page/utils/prop-background-default-value';
import { PROP_BORDER_DEFAULT_VALUE } from 'src/page/utils/prop-border-default-value';
import { PROP_EFFECT_LIST_DEFAULT_VALUE } from 'src/page/utils/prop-effect-default-value';
import { PROP_FILTER_DEFAULT_VALUE } from 'src/page/utils/prop-filter-default-value';
import { PROP_POSITION_DEFAULT_VALUE } from 'src/page/utils/prop-position-default-value';
import { PROP_PAGE_SIZE_DEFAULT_VALUE } from 'src/page/utils/prop-size-default-value';
import { PROP_SPACING_DEFAULT_VALUE } from 'src/page/utils/prop-spacing-default-value';
import { DataSource, QueryRunner } from 'typeorm';
import { SiteEntity } from 'src/site/entities/site.entity';
import { InjectDataSource } from '@nestjs/typeorm';
import { TemplatePageIdDto } from './dto/template_page_id.dto';

class ProcessedPageData extends TemplatePageEntity {
  oldId: number;
  oldParentId: number;
}

@Injectable()
export class TemplateMigrationService {
  constructor(
    @InjectDataSource() private readonly dataSource: DataSource,
    private pageService: PageService,
  ) {}

  async importTemplate() {
    const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
    const pages: Site2_Page[] = ReadFileUtil.readPages(dbdata);
    const blocks: Site3_Block[] = ReadFileUtil.readBlocks(dbdata);
    const blockDatas: Site4_BlockData[] = ReadFileUtil.readBlockDatas(dbdata);
    const srcList1: Site6_Srclist1[] = ReadFileUtil.readSrcList1(dbdata);
    const resources: Site5_Resource[] = ReadFileUtil.readResources(
      dbdata,
      srcList1,
    );

    const pageId: number = parseInt(process.env.TEST_TEMPLATE_PAGE_ID);
    const pageEntity: PageEntity = await this.pageService.getById(pageId);

    const page: Site2_Page = pages.find(page => page.seq === 1);
    const templateUtil: TemplateUtil = new TemplateUtil({
      page: page,
      blocks: blocks,
      blockDatas: blockDatas,
      resources: resources,
    });

    templateUtil.createBillboard();
    // HEAD - 01
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.HEADER,
      seq: 1,
    });

    // HEAD - 02
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.HEADER,
      seq: 2,
    });

    // BILLBOARD =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.BILLBOARD,
      seq: 1,
    });

    // MAIN =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 1,
    });

    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 2,
    });

    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 3,
    });

    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 4,
    });

    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 5,
    });

    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 6,
    });

    // FOOTER =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.FOOTER,
      seq: 1,
    });

    // FOOTER - 02
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.FOOTER,
      seq: 2,
    });

    // UPDATE DATA =======================================
    await this.pageService.updateComponent(
      pageEntity.id,
      pageEntity.components,
    );

    console.log('\x1b[32mTOP\x1b[33m - %s\x1b[0m', new Date());
  }

  async importPorfolioTemplate() {
    const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
    const pages: Site2_Page[] = ReadFileUtil.readPages(dbdata);
    const blocks: Site3_Block[] = ReadFileUtil.readBlocks(dbdata);
    const blockDatas: Site4_BlockData[] = ReadFileUtil.readBlockDatas(dbdata);
    const srcList1: Site6_Srclist1[] = ReadFileUtil.readSrcList1(dbdata);
    const resources: Site5_Resource[] = ReadFileUtil.readResources(
      dbdata,
      srcList1,
    );

    const pageId: number = 400;
    const pageEntity: PageEntity = await this.pageService.getById(pageId);

    const page: Site2_Page = pages.find(page => page.seq === 2);
    const templateUtil: TemplateUtil = new TemplateUtil({
      page: page,
      blocks: blocks,
      blockDatas: blockDatas,
      resources: resources,
    });

    templateUtil.createBillboard();
    templateUtil.createSideA();

    // HEAD - 01
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.HEADER,
      seq: 1,
    });

    // HEAD - 02
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.HEADER,
      seq: 2,
    });

    // BILLBOARD =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.BILLBOARD,
      seq: 1,
    });

    // SIDE_A =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.SIDE_A,
      seq: 1,
    });

    // MAIN =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 1,
    });

    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 2,
    });

    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 3,
    });

    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 4,
    });

    // FOOTER =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.FOOTER,
      seq: 1,
    });

    // FOOTER - 02
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.FOOTER,
      seq: 2,
    });

    // UPDATE DATA =======================================
    await this.pageService.updateComponent(
      pageEntity.id,
      pageEntity.components,
    );

    console.log('\x1b[32mPorfolio\x1b[33m - %s\x1b[0m', new Date());
  }

  async importContactTemplate() {
    const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
    const pages: Site2_Page[] = ReadFileUtil.readPages(dbdata);
    const blocks: Site3_Block[] = ReadFileUtil.readBlocks(dbdata);
    const blockDatas: Site4_BlockData[] = ReadFileUtil.readBlockDatas(dbdata);
    const srcList1: Site6_Srclist1[] = ReadFileUtil.readSrcList1(dbdata);
    const resources: Site5_Resource[] = ReadFileUtil.readResources(
      dbdata,
      srcList1,
    );

    const pageId: number = 416;
    const pageEntity: PageEntity = await this.pageService.getById(pageId);

    const page: Site2_Page = pages.find(page => page.seq === 5);
    const templateUtil: TemplateUtil = new TemplateUtil({
      page: page,
      blocks: blocks,
      blockDatas: blockDatas,
      resources: resources,
    });

    templateUtil.createBillboard();
    templateUtil.createSideA();

    // HEAD - 01
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.HEADER,
      seq: 1,
    });

    // HEAD - 02
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.HEADER,
      seq: 2,
    });

    // BILLBOARD =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.BILLBOARD,
      seq: 1,
    });

    // SIDE_A =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.SIDE_A,
      seq: 1,
    });

    // MAIN =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 1,
    });

    pageEntity.components = templateUtil.migrateBlock(
      {
        areaId: Block_Area.MAIN,
        seq: 2,
      },
      true,
    );

    // FOOTER =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.FOOTER,
      seq: 1,
    });

    // FOOTER - 02
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.FOOTER,
      seq: 2,
    });

    // UPDATE DATA =======================================
    await this.pageService.updateComponent(
      pageEntity.id,
      pageEntity.components,
    );

    console.log('\x1b[32mContact\x1b[33m - %s\x1b[0m', new Date());
  }

  async importPhotoTemplate() {
    const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
    const pages: Site2_Page[] = ReadFileUtil.readPages(dbdata);
    const blocks: Site3_Block[] = ReadFileUtil.readBlocks(dbdata);
    const blockDatas: Site4_BlockData[] = ReadFileUtil.readBlockDatas(dbdata);
    const srcList1: Site6_Srclist1[] = ReadFileUtil.readSrcList1(dbdata);
    const resources: Site5_Resource[] = ReadFileUtil.readResources(
      dbdata,
      srcList1,
    );

    const pageId: number = 308;
    const pageEntity: PageEntity = await this.pageService.getById(pageId);

    const page: Site2_Page = pages.find(page => page.seq === 3);
    const templateUtil: TemplateUtil = new TemplateUtil({
      page: page,
      blocks: blocks,
      blockDatas: blockDatas,
      resources: resources,
    });

    templateUtil.createSideA();

    // HEAD - 01
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.HEADER,
      seq: 1,
    });

    // HEAD - 02
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.HEADER,
      seq: 2,
    });

    templateUtil.createBillboard();
    // BILLBOARD =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.BILLBOARD,
      seq: 1,
    });

    // SIDE_A =======================================
    pageEntity.components = templateUtil.migrateBlock(
      {
        areaId: Block_Area.SIDE_A,
        seq: 1,
      },
      true,
    );

    // MAIN =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 1,
    });

    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 2,
    });

    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 3,
    });

    // FOOTER =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.FOOTER,
      seq: 1,
    });

    // FOOTER - 02
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.FOOTER,
      seq: 2,
    });

    // UPDATE DATA =======================================
    await this.pageService.updateComponent(
      pageEntity.id,
      pageEntity.components,
    );

    console.log('\x1b[32mDONE\x1b[33m - %s\x1b[0m', new Date());
  }

  async importProfileTemplate() {
    const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
    const pages: Site2_Page[] = ReadFileUtil.readPages(dbdata);
    const blocks: Site3_Block[] = ReadFileUtil.readBlocks(dbdata);
    const blockDatas: Site4_BlockData[] = ReadFileUtil.readBlockDatas(dbdata);
    const srcList1: Site6_Srclist1[] = ReadFileUtil.readSrcList1(dbdata);
    const resources: Site5_Resource[] = ReadFileUtil.readResources(
      dbdata,
      srcList1,
    );

    const pageId: number = 418;
    const pageEntity: PageEntity = await this.pageService.getById(pageId);

    const page: Site2_Page = pages.find(page => page.seq === 4);

    const templateUtil: TemplateUtil = new TemplateUtil({
      page: page,
      blocks: blocks,
      blockDatas: blockDatas,
      resources: resources,
    });

    // templateUtil.createBillboard();
    templateUtil.createSideA();
    // pageEntity.components = templateUtil.migrateBlock(
    //   {
    //     areaId: Block_Area.SIDE_A,
    //     seq: 1,
    //   },
    //   true,
    // );

    // MAIN =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 1,
    });
    // UPDATE DATA =======================================
    await this.pageService.updateComponent(
      pageEntity.id,
      pageEntity.components,
    );

    console.log('\x1b[32mDONE\x1b[33m - %s\x1b[0m', new Date());
  }

  async importTempTemplate() {
    const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
    const pages: Site2_Page[] = ReadFileUtil.readPages(dbdata);
    const blocks: Site3_Block[] = ReadFileUtil.readBlocks(dbdata);
    const blockDatas: Site4_BlockData[] = ReadFileUtil.readBlockDatas(dbdata);
    const srcList1: Site6_Srclist1[] = ReadFileUtil.readSrcList1(dbdata);
    const resources: Site5_Resource[] = ReadFileUtil.readResources(
      dbdata,
      srcList1,
    );

    const pageId: number = 418;
    const pageEntity: PageEntity = await this.pageService.getById(pageId);

    const page: Site2_Page = pages.find(page => page.seq === 6);

    const templateUtil: TemplateUtil = new TemplateUtil({
      page: page,
      blocks: blocks,
      blockDatas: blockDatas,
      resources: resources,
    });

    // templateUtil.createBillboard();
    templateUtil.createSideA();
    // pageEntity.components = templateUtil.migrateBlock(
    //   {
    //     areaId: Block_Area.SIDE_A,
    //     seq: 1,
    //   },
    //   true,
    // );

    // MAIN =======================================
    pageEntity.components = templateUtil.migrateBlock({
      areaId: Block_Area.MAIN,
      seq: 1,
    });
    // UPDATE DATA =======================================
    await this.pageService.updateComponent(
      pageEntity.id,
      pageEntity.components,
    );

    console.log('\x1b[32mDONE\x1b[33m - %s\x1b[0m', new Date());
  }

  async importTemplateToDB(userId: string): Promise<number> {
    const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
    const templatePath = path.resolve(dbdata, 'template_site.json');
    const siteFilePath = path.resolve(dbdata, 'site0_site.json');
    const cornerFilePath = path.resolve(dbdata, 'site1_corner.json');
    const pageFilePath = path.resolve(dbdata, 'site2_page.json');

    try {
      await fs.access(templatePath);
      await fs.access(siteFilePath);
      await fs.access(cornerFilePath);
      await fs.access(pageFilePath);
    } catch (error) {
      throw new Error(`File not found: ${error.message}`);
    }

    const processedData = await this.processTemplateData(userId, dbdata);
    const templateId = await this.insertProcessedData(processedData);
    return templateId;
  }

  async createSite(
    templateId: number,
    userId: string,
    projectId: number,
    siteId: number,
    projectFolderId?: number,
  ): Promise<Array<TemplatePageIdDto>> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get template
      const template = await queryRunner.manager
        .getRepository(TemplateEntity)
        .findOneBy({ id: templateId });

      if (!template) {
        throw new Error(`Template with id ${templateId} not found`);
      }

      // Get template site
      const templateSite = await queryRunner.manager
        .getRepository(TemplateSiteEntity)
        .findOneBy({ templateId: templateId });

      if (!templateSite) {
        throw new Error(`Template site for template ${templateId} not found`);
      }

      // Get template pages
      const templatePages = await queryRunner.manager
        .getRepository(TemplatePageEntity)
        .findBy({ templateSiteId: templateSite.id });

      if (!templatePages || templatePages.length === 0) {
        throw new Error(
          `No template pages found for template site ${templateSite.id}`,
        );
      }

      // Create site
      const createdSite = await queryRunner.manager
        .getRepository(SiteEntity)
        .findOneBy({ id: siteId });

      createdSite.projectFolderId = projectFolderId;
      createdSite.managementName = templateSite.managementName;
      createdSite.status = templateSite.status;
      createdSite.url = this.generateSlug(templateSite.managementName);
      createdSite.title = templateSite.title;
      createdSite.description = templateSite.description;
      createdSite.isSearch = templateSite.isSearch;
      createdSite.thumb = templateSite.thumb;
      createdSite.headCode = templateSite.headCode;
      createdSite.bodyCode = templateSite.bodyCode;
      createdSite.isArchived = false;
      createdSite.userId = userId;
      await queryRunner.manager.getRepository(SiteEntity).save(createdSite);

      // remove all old page
      await queryRunner.manager.getRepository(PageEntity).delete({
        projectId: projectId,
        siteId: siteId,
      });

      // Find root page
      const templateRootPage = templatePages.find(
        p => p.type === PageType.ROOT,
      );
      if (!templateRootPage) {
        throw new Error('Template root page not found');
      }

      // Create root page
      const rootPage = {
        ...templateRootPage,
        siteId: createdSite.id,
        projectId: projectId,
      };
      const createdRootPage = await queryRunner.manager
        .getRepository(PageEntity)
        .save(rootPage);

      const pageIdMapping = new Map<number, number>();
      pageIdMapping.set(templateRootPage.id, createdRootPage.id);
      const templatesToCreate = templatePages.filter(
        p => p.type !== PageType.ROOT,
      );

      // Sort templates by hierarchy
      const sortedTemplates =
        this.sortTemplatePagesByHierarchy(templatesToCreate);

      // Create all pages
      const templatePageIds: Array<TemplatePageIdDto> =
        await this.createAllPagesFromTemplate(
          queryRunner,
          sortedTemplates,
          createdSite.id,
          projectId,
          userId,
          pageIdMapping,
        );

      // Update children relationships
      await this.updateRealPageChildren(
        queryRunner,
        templatePages,
        pageIdMapping,
      );

      await queryRunner.commitTransaction();

      return templatePageIds;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async updateRealPageChildren(
    queryRunner: QueryRunner,
    templatePages: TemplatePageEntity[],
    pageIdMapping: Map<number, number>,
  ): Promise<void> {
    for (const templatePage of templatePages) {
      if (templatePage.children && templatePage.children.length > 0) {
        const realPageId = pageIdMapping.get(templatePage.id);
        if (realPageId) {
          const realChildrenIds: number[] = [];

          for (const templateChildId of templatePage.children) {
            const realChildId = pageIdMapping.get(templateChildId);
            if (realChildId) {
              realChildrenIds.push(realChildId);
            } else {
              console.warn(
                `Warning: Child page ${templateChildId} not found in mapping for parent ${templatePage.id}`,
              );
            }
          }

          if (realChildrenIds.length > 0) {
            await queryRunner.manager
              .getRepository(PageEntity)
              .update(realPageId, {
                children: realChildrenIds,
              });
          }
        }
      }
    }
  }

  private async createAllPagesFromTemplate(
    queryRunner: QueryRunner,
    templatePages: TemplatePageEntity[],
    siteId: number,
    projectId: number,
    userId: string,
    pageIdMapping: Map<number, number>,
  ): Promise<Array<TemplatePageIdDto>> {
    const createdPages: Array<TemplatePageIdDto> = [];

    for (const templatePage of templatePages) {
      let realParentId: number | null = null;
      if (templatePage.parentId) {
        realParentId = pageIdMapping.get(templatePage.parentId) || null;
      }

      const pageData = new PageEntity();
      pageData.type = templatePage.type;
      pageData.parentId = realParentId;
      pageData.projectId = projectId;
      pageData.siteId = siteId;
      pageData.name = templatePage.name;
      pageData.components =
        templatePage.components ||
        this.createDefaultPageComponents(templatePage.ts);
      pageData.ts = templatePage.ts;
      pageData.status = templatePage.status;
      pageData.url = templatePage.url;
      pageData.title = templatePage.title;
      pageData.description = templatePage.description;
      pageData.isSearch = templatePage.isSearch;
      pageData.thumb = templatePage.thumb;
      pageData.headCode = templatePage.headCode;
      pageData.bodyCode = templatePage.bodyCode;
      pageData.isPrivate = templatePage.isPrivate;
      pageData.isHome = templatePage.isHome;
      pageData.children = [];
      pageData.userId = userId;
      pageData.isDeleted = false;

      const createdPage = await queryRunner.manager
        .getRepository(PageEntity)
        .save(pageData);

      pageIdMapping.set(templatePage.id, createdPage.id);

      createdPages.push({
        id: createdPage.id,
        templatePageId: templatePage.templatePageId,
        parentId: realParentId,
      });
    }

    return createdPages;
  }

  private sortTemplatePagesByHierarchy(
    pages: TemplatePageEntity[],
  ): TemplatePageEntity[] {
    const result: TemplatePageEntity[] = [];
    const processed = new Set<number>();

    const addPage = (page: TemplatePageEntity) => {
      if (processed.has(page.id)) return;

      if (page.parentId) {
        const parent = pages.find(p => p.id === page.parentId);
        if (parent && !processed.has(parent.id)) {
          addPage(parent);
        }
      }

      result.push(page);
      processed.add(page.id);
    };

    for (const page of pages) {
      addPage(page);
    }

    return result;
  }

  private async processTemplateData(
    userId: string,
    dbData: string,
  ): Promise<{
    template: TemplateEntity;
    templateSite: TemplateSiteEntity;
    rootTemplatePage: ProcessedPageData;
    folderTemplatePages: ProcessedPageData[];
    templatePages: ProcessedPageData[];
  }> {
    // Read data
    const oldTemplates: Template_Site[] =
      ReadFileUtil.readTemplateSites(dbData);
    const oldSites: Site0_Site[] = ReadFileUtil.readSites(dbData);
    const oldCorners: Site1_Corner[] = ReadFileUtil.readCorners(dbData);
    const oldPages: Site2_Page[] = ReadFileUtil.readPages(dbData);

    if (!oldTemplates || oldTemplates.length === 0) {
      throw new Error('Invalid templates data');
    }
    if (!oldSites || oldSites.length === 0) {
      throw new Error('Invalid sites data');
    }
    if (!oldCorners || oldCorners.length === 0) {
      throw new Error('Invalid corners data');
    }
    if (!oldPages || oldPages.length === 0) {
      throw new Error('Invalid pages data');
    }

    // Process data
    const oldTemplate = oldTemplates[0];
    const template: TemplateEntity = {
      tmpSiteId: oldTemplate.id,
      name: oldTemplate.title,
      titleDetail: oldTemplate.titleDetail,
      seq: oldTemplate.seq,
      description: oldTemplate.description,
      category: oldTemplate.category,
      smartblock: oldTemplate.smartblock === 'true',
      pages: oldTemplate.pagelistJson,
      id: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    };

    const site = oldSites[0];
    const templateSite: TemplateSiteEntity = {
      managementName: site.name || `Imported Site ${Date.now()}`,
      status: site.dispFlg === 1 ? SiteStatus.PUBLISHED : SiteStatus.DRAFT,
      url: this.generateSlug(site.name || ''),
      title: site.name || 'Imported Site',
      description: `Imported from template ${site.siteVersion || 'v1'}`,
      isSearch: true,
      thumb: '',
      headCode: '',
      bodyCode: '',
      userId: userId,
      templateId: 0,
      isArchived: false,
      id: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    };

    // Find root corner
    const rootCorner = oldCorners.find(corner => !corner.parentCornerId);
    if (!rootCorner) {
      throw new Error('No root corner found (corner without parent)');
    }

    // Create root page from root corner
    let ts = NEW_TS();
    const rootTemplatePage: ProcessedPageData = {
      type: PageType.ROOT,
      parentId: null,
      templateId: 0,
      templateSiteId: 0,
      templatePageId: 0,
      name: rootCorner.name || 'ROOT',
      components: {},
      ts: ts++,
      status: PageStatus.PUBLISHED,
      url: this.generateSlug(rootCorner.name || 'root'),
      title: rootCorner.name || 'ROOT',
      description: '',
      isSearch: true,
      thumb: '',
      headCode: '',
      bodyCode: '',
      isPrivate: false,
      isHome: false,
      children: [],
      userId: userId,
      isDeleted: false,
      oldId: rootCorner.cornerId,
      oldParentId: 0,
      id: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    };

    // Process folders data
    const folderCorners = oldCorners.filter(corner => corner.parentCornerId);
    const folderTemplatePages: ProcessedPageData[] = folderCorners.map(
      corner => ({
        type: PageType.DIRECTORY,
        parentId: null,
        templateId: 0,
        templateSiteId: 0,
        templatePageId: 0,
        name: corner.name || `Folder ${corner.cornerId}`,
        components: {},
        ts: ts++,
        status: PageStatus.PUBLISHED,
        url: this.generateSlug(corner.name || `folder-${corner.cornerId}`),
        title: corner.name || `Folder ${corner.cornerId}`,
        description: '',
        isSearch: true,
        thumb: '',
        headCode: '',
        bodyCode: '',
        isPrivate: false,
        isHome: false,
        children: [],
        userId: userId,
        isDeleted: false,
        oldId: corner.cornerId,
        oldParentId: corner.parentCornerId,
        id: undefined,
        createdAt: undefined,
        updatedAt: undefined,
      }),
    );

    // Process pages data
    const sortedPages = oldPages.sort((a, b) => a.seq - b.seq);
    const templatePages: ProcessedPageData[] = sortedPages.map(page => ({
      type: PageType.PAGE,
      parentId: null,
      templateId: 0,
      templateSiteId: 0,
      templatePageId: page.pageId,
      name: page.name,
      components: this.createDefaultPageComponents(ts++),
      ts: ts++,
      status: page.publicFlg === 1 ? PageStatus.PUBLISHED : PageStatus.DRAFT,
      url: this.generateSlug(page.name),
      title: page.title || page.name,
      description: '',
      isSearch: page.publicFlg === 1,
      thumb: '',
      headCode: page.headSetsJson.scpt || '',
      bodyCode: '',
      isPrivate: page.publicFlg !== 1,
      isHome: false,
      children: [],
      userId: userId,
      isDeleted: false,
      oldId: page.pageId,
      oldParentId: page.parentCornerId,
      id: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    }));

    return {
      template,
      templateSite,
      rootTemplatePage,
      folderTemplatePages,
      templatePages,
    };
  }

  private generateSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  private createDefaultPageComponents(ts: number): Record<string, any> {
    return {
      __page__: {
        id: '__page__',
        type: ComponentType.Page,
        name: 'Page',
        parentId: undefined,
        properties: {
          ts: ts,
          actions: PROP_ACTION_DEFAULT_VALUE(ts),
          marginPadding: PROP_SPACING_DEFAULT_VALUE(ts),
          size: PROP_PAGE_SIZE_DEFAULT_VALUE(ts),
          border: PROP_BORDER_DEFAULT_VALUE(ts),
          position: PROP_POSITION_DEFAULT_VALUE(ts),
          backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts),
          effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
          filter: PROP_FILTER_DEFAULT_VALUE(ts),
        },
        children: ['__main__'],
        breakpoint: {
          tablet: { ts: ts },
          phone: { ts: ts },
        },
        ts: ts,
      },
      __main__: {
        id: '__main__',
        type: ComponentType.Main,
        name: 'Main',
        parentId: '__page__',
        properties: {
          ts: ts,
          actions: PROP_ACTION_DEFAULT_VALUE(ts),
          marginPadding: PROP_SPACING_DEFAULT_VALUE(ts),
          size: PROP_PAGE_SIZE_DEFAULT_VALUE(ts, {
            height: { value: '', unit: 'auto' },
            minHeight: { value: '100', unit: '%' },
          }),
          border: PROP_BORDER_DEFAULT_VALUE(ts),
          position: PROP_POSITION_DEFAULT_VALUE(ts),
          backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts),
          effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
          filter: PROP_FILTER_DEFAULT_VALUE(ts),
        },
        children: [],
        breakpoint: {
          tablet: { ts: ts },
          phone: { ts: ts },
        },
        ts: ts,
      },
    };
  }

  private async insertProcessedData(processedData: {
    template: TemplateEntity;
    templateSite: TemplateSiteEntity;
    rootTemplatePage: ProcessedPageData;
    folderTemplatePages: ProcessedPageData[];
    templatePages: ProcessedPageData[];
  }): Promise<number> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Remove old template
      const oldTemplate = await queryRunner.manager
        .getRepository(TemplateEntity)
        .findOneBy({ tmpSiteId: processedData.template.tmpSiteId });

      if (oldTemplate) {
        processedData.template.id = oldTemplate.id;

        await queryRunner.manager.getRepository(TemplatePageEntity).delete({
          templateId: oldTemplate.id,
        });
        await queryRunner.manager.getRepository(TemplateSiteEntity).delete({
          templateId: oldTemplate.id,
        });
      }

      // Create template
      const template = await queryRunner.manager
        .getRepository(TemplateEntity)
        .save(processedData.template);

      // Create template site
      const templateSiteData = {
        ...processedData.templateSite,
        templateId: template.id,
      };
      const templateSite = await queryRunner.manager
        .getRepository(TemplateSiteEntity)
        .save(templateSiteData);

      // Create root template page
      const rootTemplatePageData = {
        ...processedData.rootTemplatePage,
        templateId: template.id,
        templateSiteId: templateSite.id,
      };
      const rootPage = await queryRunner.manager
        .getRepository(TemplatePageEntity)
        .save(rootTemplatePageData);

      // Create folder template pages
      const createdFolders = await this.createFolderTemplatePages(
        queryRunner,
        processedData.folderTemplatePages,
        rootPage.id,
        template.id,
        templateSite.id,
      );

      // Update root page children
      const topLevelFolderIds = createdFolders
        .filter(f => f.oldParentId === processedData.rootTemplatePage.oldId)
        .map(f => f.id);
      await queryRunner.manager
        .getRepository(TemplatePageEntity)
        .update(rootPage.id, {
          children: topLevelFolderIds,
        });

      // Create template pages
      const createdTemplatePages = await this.createTemplatePages(
        queryRunner,
        processedData.templatePages,
        createdFolders,
        templateSite.id,
        rootPage.id,
      );

      // Update folder children
      await this.updateFolderTemplatePageChildren(
        queryRunner,
        createdFolders,
        createdTemplatePages,
      );

      // Update root page children
      const rootPageIds = createdTemplatePages
        .filter(p => p.parentId === null)
        .map(p => p.id);

      const currentRootChildren = topLevelFolderIds;
      const updatedRootChildren = [...currentRootChildren, ...rootPageIds];

      await queryRunner.manager
        .getRepository(TemplatePageEntity)
        .update(rootPage.id, {
          children: updatedRootChildren,
        });

      await queryRunner.commitTransaction();

      return template.id;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async createFolderTemplatePages(
    queryRunner: QueryRunner,
    folders: ProcessedPageData[],
    rootPageId: number,
    templateId: number,
    templateSiteId: number,
  ): Promise<
    Array<{
      id: number;
      name: string;
      oldId: number;
      oldParentId?: number;
    }>
  > {
    const sortedFolders = this.sortFoldersByHierarchy(folders);
    const createdFolders: Array<{
      id: number;
      name: string;
      oldId: number;
      oldParentId?: number;
    }> = [];

    for (const folder of sortedFolders) {
      let parentId = rootPageId;
      if (folder.oldParentId) {
        const parentFolder = createdFolders.find(
          f => f.oldId === folder.oldParentId,
        );
        if (parentFolder) {
          parentId = parentFolder.id;
        }
      }

      const folderData = {
        ...folder,
        parentId,
        templateId,
        templateSiteId,
      };

      const createdFolder = await queryRunner.manager
        .getRepository(TemplatePageEntity)
        .save(folderData);

      createdFolders.push({
        id: createdFolder.id,
        name: createdFolder.name,
        oldId: folder.oldId,
        oldParentId: folder.oldParentId,
      });
    }

    return createdFolders;
  }

  private async createTemplatePages(
    queryRunner: QueryRunner,
    templatePages: ProcessedPageData[],
    createdFTPages: Array<{ id: number; oldId: number }>,
    templateSiteId: number,
    rootPageId: number,
  ): Promise<
    Array<{
      id: number;
      parentId: number;
    }>
  > {
    const createdTemplatePages: Array<{
      id: number;
      parentId: number;
    }> = [];

    for (const page of templatePages) {
      const parentFolder = createdFTPages.find(
        f => f.oldId === page.oldParentId,
      );

      let parentId: number;

      if (parentFolder) {
        parentId = parentFolder.id;
      } else {
        parentId = rootPageId;
      }

      const pageData = {
        ...page,
        parentId,
        templateSiteId,
      };

      const createdTemplatePage = await queryRunner.manager
        .getRepository(TemplatePageEntity)
        .save(pageData);

      createdTemplatePages.push({
        id: createdTemplatePage.id,
        parentId: parentFolder ? parentFolder.id : null,
      });
    }

    return createdTemplatePages;
  }

  private async updateFolderTemplatePageChildren(
    queryRunner: QueryRunner,
    createdFTPages: Array<{
      id: number;
      oldId: number;
      oldParentId?: number;
    }>,
    createdTemplatePages: Array<{
      id: number;
      parentId: number | null;
    }>,
  ): Promise<void> {
    for (const folder of createdFTPages) {
      const folderPages = createdTemplatePages.filter(
        page => page.parentId === folder.id,
      );

      const childrenIds = folderPages.map(page => page.id);

      await queryRunner.manager
        .getRepository(TemplatePageEntity)
        .update(folder.id, {
          children: childrenIds,
        });
    }
  }

  private sortFoldersByHierarchy(
    folders: ProcessedPageData[],
  ): ProcessedPageData[] {
    const result: ProcessedPageData[] = [];
    const processed = new Set<number>();

    const addFolder = (folder: ProcessedPageData) => {
      if (processed.has(folder.oldId)) return;

      if (folder.oldParentId) {
        const parent = folders.find(f => f.oldId === folder.oldParentId);
        if (parent && !processed.has(parent.oldId)) {
          addFolder(parent);
        }
      }

      result.push(folder);
      processed.add(folder.oldId);
    };

    for (const folder of folders) {
      addFolder(folder);
    }

    return result;
  }
}
