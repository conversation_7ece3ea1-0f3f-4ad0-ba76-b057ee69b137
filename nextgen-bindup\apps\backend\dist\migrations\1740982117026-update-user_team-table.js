"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUserTeamTable1740982117026 = void 0;
const typeorm_1 = require("typeorm");
class UpdateUserTeamTable1740982117026 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}user_team`;
    }
    async up(queryRunner) {
        const column = new typeorm_1.TableColumn({
            name: 'isAdmin',
            type: 'boolean',
            default: false,
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, column);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'isAdmin');
    }
}
exports.UpdateUserTeamTable1740982117026 = UpdateUserTeamTable1740982117026;
//# sourceMappingURL=1740982117026-update-user_team-table.js.map