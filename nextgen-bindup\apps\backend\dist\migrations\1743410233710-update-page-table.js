"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePagesTable1743410233710 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePagesTable1743410233710 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}pages`;
    }
    async up(queryRunner) {
        const cmsCollectionIdColumn = new typeorm_1.TableColumn({
            name: 'cmsCollectionId',
            type: 'integer',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, cmsCollectionIdColumn);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'cmsCollectionId');
    }
}
exports.UpdatePagesTable1743410233710 = UpdatePagesTable1743410233710;
//# sourceMappingURL=1743410233710-update-page-table.js.map