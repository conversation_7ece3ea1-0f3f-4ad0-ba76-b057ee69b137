{"version": 3, "file": "read-file.util.js", "sourceRoot": "", "sources": ["../../../src/template-migration/util/read-file.util.ts"], "names": [], "mappings": ";;;AACA,yBAAyB;AAWzB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;AAE1C,MAAM,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AAKnC,MAAM,kBAAkB,GAAG,CAAC,OAAe,EAAU,EAAE;IAErD,MAAM,WAAW,GAAG,wBAAwB,CAAC;IAC7C,MAAM,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IAGjE,IAAI,OAAO,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAGvC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;IAElE,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF,MAAa,YAAY;IACvB,MAAM,CAAC,aAAa,CAAC,MAAc;QACjC,MAAM,aAAa,GAAoB,IAAI,CAAC,KAAK,CAC/C,EAAE,CAAC,YAAY,CAAC,GAAG,MAAM,uBAAuB,EAAE;YAChD,QAAQ,EAAE,MAAM;SACjB,CAAC,CACgB,CAAC;QAErB,KAAK,MAAM,SAAS,IAAI,aAAa,EAAE,CAAC;YACtC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAChD,OAAO,SAAS,CAAC,IAAI,CAAC;QACxB,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,MAAc;QAC7B,MAAM,KAAK,GAAiB,IAAI,CAAC,KAAK,CACpC,EAAE,CAAC,YAAY,CAAC,GAAG,MAAM,kBAAkB,EAAE;YAC3C,QAAQ,EAAE,MAAM;SACjB,CAAC,CACa,CAAC;QAElB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC,WAAW,CAAC;YAExB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC,KAAK,CAAC;YAElB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,MAAc;QAC/B,MAAM,OAAO,GAAmB,IAAI,CAAC,KAAK,CACxC,EAAE,CAAC,YAAY,CAAC,GAAG,MAAM,oBAAoB,EAAE;YAC7C,QAAQ,EAAE,MAAM;SACjB,CAAC,CACe,CAAC;QAEpB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC1C,OAAO,MAAM,CAAC,IAAI,CAAC;YAEnB,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9C,OAAO,MAAM,CAAC,MAAM,CAAC;QACvB,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,MAAc;QAC7B,MAAM,KAAK,GAAiB,IAAI,CAAC,KAAK,CACpC,EAAE,CAAC,YAAY,CAAC,GAAG,MAAM,kBAAkB,EAAE;YAC3C,QAAQ,EAAE,MAAM;SACjB,CAAC,CACa,CAAC;QAElB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1C,OAAO,IAAI,CAAC,MAAM,CAAC;YAEnB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC,QAAQ,CAAC;YAErB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC,UAAU,CAAC;YAEvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1C,OAAO,IAAI,CAAC,MAAM,CAAC;YAEnB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC,UAAU,CAAC;YAEvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC,OAAO,CAAC;YAEpB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC,SAAS,CAAC;YAEtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,MAAc;QAC9B,MAAM,MAAM,GAAkB,IAAI,CAAC,KAAK,CACtC,EAAE,CAAC,YAAY,CAAC,GAAG,MAAM,mBAAmB,EAAE;YAC5C,QAAQ,EAAE,MAAM;SACjB,CAAC,CACc,CAAC;QAEnB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACnB,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;YACvD,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;YACvD,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,MAAc;QAClC,MAAM,UAAU,GAAsB,IAAI,CAAC,KAAK,CAC9C,EAAE,CAAC,YAAY,CAAC,GAAG,MAAM,uBAAuB,EAAE;YAChD,QAAQ,EAAE,MAAM;SACjB,CAAC,CACkB,CAAC;QAEvB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,SAAS,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAClE,OAAO,SAAS,CAAC,aAAa,CAAC;YAE/B,SAAS,CAAC,OAAO,GAAG,kBAAkB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAE1D,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC1D,OAAO,SAAS,CAAC,SAAS,CAAC;YAE3B,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACpD,OAAO,SAAS,CAAC,MAAM,CAAC;QAC1B,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,MAAc;QAChC,MAAM,OAAO,GAAqB,IAAI,CAAC,KAAK,CAC1C,EAAE,CAAC,YAAY,CAAC,GAAG,MAAM,sBAAsB,EAAE;YAC/C,QAAQ,EAAE,MAAM;SACjB,CAAC,CACiB,CAAC;QAEtB,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;YAC1B,OAAO,GAAG,CAAC,OAAO,CAAC;YACnB,OAAO,GAAG,CAAC,kBAAkB,CAAC;QAChC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,aAAa,CAClB,MAAc,EACd,QAA0B;QAE1B,MAAM,SAAS,GAAqB,IAAI,CAAC,KAAK,CAC5C,EAAE,CAAC,YAAY,CAAC,GAAG,MAAM,sBAAsB,EAAE;YAC/C,QAAQ,EAAE,MAAM;SACjB,CAAC,CACiB,CAAC;QAEtB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YAEjC,OAAO,QAAQ,CAAC,aAAa,CAAC;YAE9B,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAChE,OAAO,QAAQ,CAAC,aAAa,CAAC;YAE9B,QAAQ,CAAC,OAAO,GAAG,QAAQ;iBACxB,MAAM,CACL,GAAG,CAAC,EAAE,CACJ,GAAG,CAAC,UAAU,KAAK,QAAQ,CAAC,UAAU;gBACtC,GAAG,CAAC,WAAW,KAAK,QAAQ,CAAC,WAAW,CAC3C;iBACA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,MAAc;QACrC,MAAM,aAAa,GAAoB,IAAI,CAAC,KAAK,CAC/C,EAAE,CAAC,YAAY,CAAC,GAAG,MAAM,qBAAqB,EAAE;YAC9C,QAAQ,EAAE,MAAM;SACjB,CAAC,CACgB,CAAC;QAErB,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;YACzC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC9D,OAAO,YAAY,CAAC,QAAQ,CAAC;YAE7B,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC5D,OAAO,YAAY,CAAC,OAAO,CAAC;YAE5B,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC9D,OAAO,YAAY,CAAC,QAAQ,CAAC;QAC/B,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;CACF;AApMD,oCAoMC"}