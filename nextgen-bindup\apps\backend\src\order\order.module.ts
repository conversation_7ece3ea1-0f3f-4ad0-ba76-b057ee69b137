import { Module } from '@nestjs/common';
import { OrderService } from './order.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrderEntity } from './entites/order.entity';
import { OrderItemEntity } from './entites/order-item.entity';
import { OrderController } from './order.controller';
import { ProductModule } from 'src/product/product.module';
import { ShippingNoteSettingModule } from 'src/shipping-note-settings/shipping-note-settings.module';
import { ShopInformationSettingModule } from 'src/shop-information-settings/shop-information-settings.module';
import { CsvService } from 'src/product/csv.service';

@Module({
  imports: [
    ProductModule,
    ShippingNoteSettingModule,
    ShopInformationSettingModule,
    TypeOrmModule.forFeature([OrderEntity]),
    TypeOrmModule.forFeature([OrderItemEntity]),
  ],
  providers: [OrderService, CsvService],
  exports: [OrderService],
  controllers: [OrderController],
})
export class OrderModule {}
