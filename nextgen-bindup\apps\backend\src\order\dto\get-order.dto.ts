import { IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';
import { OrderStatus } from '../enum/order.enum';
import { PaymentMethodType } from '../enum/payment-method-type.enum';

export class GetOrdersQueryDto {
  @IsOptional()
  page: number = 1;

  @IsOptional()
  limit: number = 10;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  email?: string;

  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined;
    return value as OrderStatus;
  })
  orderStatus?: OrderStatus;

  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined;
    return value as PaymentMethodType;
  })
  paymentMethodType?: PaymentMethodType;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;

  @IsOptional()
  @IsString()
  additionalInformation?: string;
}
