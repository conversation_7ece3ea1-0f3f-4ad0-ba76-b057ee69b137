"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUsersView1738652412288 = void 0;
const typeorm_1 = require("typeorm");
class CreateUsersView1738652412288 {
    constructor() {
        this.VIEW_NAME = `${process.env.ENTITY_PREFIX || ''}users`;
    }
    async up(queryRunner) {
        await queryRunner.createView(new typeorm_1.View({
            schema: process.env.DATABASE_SCHEMA,
            name: this.VIEW_NAME,
            expression: `
          select
            user_id as userId,
            email,
            identity_data->>'name' AS name,
            identity_data->>'avatar' AS avatar,
            identity_data->>'group' as group
          from auth.identities;
        `,
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropView(`${process.env.DATABASE_SCHEMA}.${this.VIEW_NAME}`);
    }
}
exports.CreateUsersView1738652412288 = CreateUsersView1738652412288;
//# sourceMappingURL=1738652412288-create-users-view.js.map