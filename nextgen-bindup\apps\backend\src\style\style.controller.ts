import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { StyleService } from './style.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { StyleEntity } from './entities/style.entity';

@Controller('styles')
@UseGuards(AuthGuard)
export class StyleController {
  constructor(private readonly styleService: StyleService) {}

  @Post('create')
  async create(@Body() assetEntity: StyleEntity) {
    return await this.styleService.create(assetEntity);
  }

  @Put('update/:styleId')
  async update(
    @Param('styleId') styleId: string,
    @Body() data: Partial<StyleEntity>,
  ) {
    return await this.styleService.update(+styleId, data);
  }

  @Get('one/:styleId')
  async getById(@Param('styleId') styleId: string) {
    return await this.styleService.findById(+styleId);
  }

  @Get('project/:projectId')
  async getByProjectId(@Param('projectId') projectId: string) {
    return await this.styleService.findByProjectId(+projectId);
  }

  @Get('site/:projectId/:siteId')
  async getBySiteId(
    @Param('projectId') projectId: string,
    @Param('siteId') siteId: string,
  ) {
    return await this.styleService.findBySiteId(+projectId, +siteId);
  }

  @Delete(':styleId')
  async delete(@Param('styleId') styleId: string) {
    return await this.styleService.delete(+styleId);
  }
}
