import { Injectable } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { AppException } from 'src/common/exceptions/app.exception';
import { ShopInformationSettingEntity } from './entities/shop-information-settings.entity';
import { SiteService } from 'src/site/site.service';
import { ShippingNoteSettingEntity } from '../shipping-note-settings/entities/shipping-note--settings.entity';
import { DeliveryReceiptSettingEntity } from '../delivery-receipt-settings/entities/delivery-receipt-settings.entity';
import { OrderCompletionSettingEntity } from '../order-complete-settings/entities/order-complete-settings.entity';
import { PaymentMethodEntity } from 'src/payment-method/entities/payment-method.entity';
import { getDefaultSetting } from 'src/utils/get-default-shop-setting';

@Injectable()
export class ShopInformationSettingService {
  constructor(
    @InjectDataSource() private readonly dataSource: DataSource,
    @InjectRepository(ShopInformationSettingEntity)
    private readonly shopInformationSettingRepo: Repository<ShopInformationSettingEntity>,
    private readonly siteService: SiteService,
  ) {}

  async create(
    shopInformationSettingEntity: ShopInformationSettingEntity,
  ): Promise<ShopInformationSettingEntity> {
    // Use QueryRunner to manage transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now: Date = new Date();
      const setting = new ShopInformationSettingEntity();
      setting.siteId = shopInformationSettingEntity.siteId;
      setting.isMaintenance = shopInformationSettingEntity.isMaintenance;
      setting.shopName = shopInformationSettingEntity.shopName;
      setting.logoImages = shopInformationSettingEntity.logoImages;
      setting.colorTheme = shopInformationSettingEntity.colorTheme;
      setting.shopUrl = shopInformationSettingEntity.shopUrl;
      setting.isSetupGuide = shopInformationSettingEntity.isSetupGuide;
      setting.guideUrl = shopInformationSettingEntity.guideUrl;
      setting.email = shopInformationSettingEntity.email;
      setting.isAddPrivacy = shopInformationSettingEntity.isAddPrivacy;
      setting.privacyUrl = shopInformationSettingEntity.privacyUrl;
      setting.taxMode = shopInformationSettingEntity.taxMode;
      setting.taxRate = shopInformationSettingEntity.taxRate;
      setting.taxRegulation = shopInformationSettingEntity.taxRegulation;
      setting.createdAt = now;
      setting.updatedAt = now;

      // Save shop information
      const savedSetting = await queryRunner.manager.save(setting);

      const defaultSetting = getDefaultSetting(setting.shopName, setting.email);
      // Create default shipping note settings
      await queryRunner.manager.save(ShippingNoteSettingEntity, {
        siteId: savedSetting.siteId,
        shippingFees: [],
        isFreeShippingCondition: false,
        shippingFeeDetail: {},
        freeShippingCondition: 0,
        freeShippingAmount: 0,
        note: defaultSetting.shippingNote.note,
        createdAt: now,
        updatedAt: now,
      });

      // Create default payment method settings
      await queryRunner.manager.save(PaymentMethodEntity, {
        siteId: savedSetting.siteId,
        ...defaultSetting.paymentMethod,
        cashOnDelivery: defaultSetting.paymentMethod.cashOnDelivery,
        bankTransfer: defaultSetting.paymentMethod.bankTransfer,
        stripePaymentGateway: defaultSetting.paymentMethod.stripePaymentGateway,
        postalTransfer: defaultSetting.paymentMethod.postalTransfer,
        createdAt: now,
        updatedAt: now,
      });

      // Create default delivery receipt settings
      await queryRunner.manager.save(DeliveryReceiptSettingEntity, {
        siteId: savedSetting.siteId,
        header: defaultSetting.delivery.headerText,
        footer: defaultSetting.delivery.footerText,
        createdAt: now,
        updatedAt: now,
      });

      // Create default order completion settings
      await queryRunner.manager.save(OrderCompletionSettingEntity, {
        siteId: savedSetting.siteId,
        displayText: defaultSetting.orderCompletion.displayText,
        emailSubject: defaultSetting.orderCompletion.emailSubject,
        emailHeader: defaultSetting.orderCompletion.emailHeader,
        emailFooter: defaultSetting.orderCompletion.emailFooter,
        createdAt: now,
        updatedAt: now,
      });

      // Commit transaction
      await queryRunner.commitTransaction();
      return savedSetting;
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }

  async update(
    id: number,
    settingData: Partial<ShopInformationSettingEntity>,
  ): Promise<ShopInformationSettingEntity> {
    const setting = await this.shopInformationSettingRepo.findOneBy({ id: id });
    if (!setting)
      throw new AppException('api.error.shop_information_setting_not_found');

    delete settingData.id;
    delete settingData.siteId;
    delete settingData.createdAt;
    settingData.updatedAt = new Date();

    await this.shopInformationSettingRepo.update(id, settingData);
    return { ...setting, ...settingData };
  }

  async findById(id: number): Promise<ShopInformationSettingEntity> {
    return await this.shopInformationSettingRepo.findOneBy({ id });
  }

  async findOneBySiteId(siteId: number): Promise<ShopInformationSettingEntity> {
    return await this.shopInformationSettingRepo.findOneBy({ siteId });
  }

  async delete(id: number): Promise<boolean> {
    const setting = await this.shopInformationSettingRepo.findOneBy({ id });
    if (!setting)
      throw new AppException('api.error.shop_information_setting_not_found');

    await this.shopInformationSettingRepo.delete(id);
    return true;
  }
}
