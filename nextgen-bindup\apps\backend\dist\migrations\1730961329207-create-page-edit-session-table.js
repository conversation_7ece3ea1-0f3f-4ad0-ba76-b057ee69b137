"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePageEditSessionTable1730961329207 = void 0;
const typeorm_1 = require("typeorm");
class CreatePageEditSessionTable1730961329207 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}page_edit_sessions`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'sessionId',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'pageId',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'userId',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'componentId',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'cursorPosition',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'editorSize',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }));
        await queryRunner.query(`
      ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}
      ADD CONSTRAINT unique_user_page_session UNIQUE ("userId", "pageId", "sessionId");
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME} DROP CONSTRAINT unique_user_page_session;`);
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreatePageEditSessionTable1730961329207 = CreatePageEditSessionTable1730961329207;
//# sourceMappingURL=1730961329207-create-page-edit-session-table.js.map