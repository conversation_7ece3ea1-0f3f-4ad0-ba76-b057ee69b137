"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const project_entity_1 = require("./entities/project.entity");
const typeorm_2 = require("@nestjs/typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
let ProjectService = class ProjectService {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async findById(id) {
        return await this.projectRepo.findOneBy({ id });
    }
    async getProjectsByUser(userId) {
        const query = `
      SELECT
        p.id,
        p.name,
        pf.id AS "projectFolders.id",
        pf.name AS "projectFolders.name"
      FROM projects p
      LEFT JOIN project_folders pf ON p.id = pf."projectId"
      WHERE p."userId" = $1
        OR p."userId" IN (SELECT ut."rootUserId"
                          FROM user_team ut
                          WHERE ut."userId" = $1)
    `;
        const projectRaws = await this.dataSource.query(query, [
            userId,
        ]);
        const groupedProjects = this.groupProjectsWithFolders(projectRaws);
        return groupedProjects;
    }
    groupProjectsWithFolders(projects) {
        const projectMap = new Map();
        projects.forEach(row => {
            const projectId = row.id;
            if (!projectMap.has(projectId)) {
                projectMap.set(projectId, {
                    ...row,
                    projectFolders: row['projectFolders.id']
                        ? [
                            {
                                id: row['projectFolders.id'],
                                name: row['projectFolders.name'],
                            },
                        ]
                        : [],
                });
                delete projectMap.get(projectId)['projectFolders.id'];
                delete projectMap.get(projectId)['projectFolders.name'];
            }
            else {
                if (row['projectFolders.id']) {
                    projectMap.get(projectId).projectFolders.push({
                        id: row['projectFolders.id'],
                        name: row['projectFolders.name'],
                    });
                }
            }
        });
        return Array.from(projectMap.values());
    }
    async createProject(userId, projectData) {
        const now = new Date();
        const project = new project_entity_1.ProjectEntity();
        project.userId = userId;
        project.name = projectData.name;
        project.createdAt = now;
        project.updatedAt = now;
        return await this.projectRepo.save(project);
    }
    async updateProject(id, data) {
        const project = await this.findById(id);
        if (!project)
            throw new app_exception_1.AppException('error.project_not_found');
        delete data.id;
        await this.projectRepo.update(id, { ...data });
        return { ...project, ...data };
    }
    async deleteProject(id) {
        await this.projectRepo.delete(id);
        return true;
    }
};
exports.ProjectService = ProjectService;
__decorate([
    (0, typeorm_2.InjectRepository)(project_entity_1.ProjectEntity),
    __metadata("design:type", typeorm_1.Repository)
], ProjectService.prototype, "projectRepo", void 0);
exports.ProjectService = ProjectService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], ProjectService);
//# sourceMappingURL=project.service.js.map