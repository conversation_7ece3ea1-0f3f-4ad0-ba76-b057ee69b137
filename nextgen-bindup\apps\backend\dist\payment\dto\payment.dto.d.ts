import { PlanEntity } from '../entities/plan.entity';
export interface SubscriptionResponse {
    userId: string;
    planId: string;
    stripeSubscriptionId: string;
    status: SubscriptionStatus;
    currentPeriodStart: Date;
    currentPeriodEnd: Date;
    planName: string;
    planPrice: number;
    planCurrency: string;
    isActive: boolean;
    planImageUrl?: string;
    planDescription?: string;
}
export declare enum SubscriptionStatus {
    Active = "active",
    Trialing = "trialing",
    PastDue = "past_due",
    Canceled = "canceled",
    Unpaid = "unpaid",
    Incomplete = "incomplete",
    IncompleteExpired = "incomplete_expired",
    Paused = "paused"
}
export interface PlanDto extends PlanEntity {
    imageUrl: string;
    description: string;
}
