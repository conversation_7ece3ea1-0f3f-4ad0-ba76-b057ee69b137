import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateTemplatesTable1750649574427 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}templates`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'tmpSiteId',
        type: 'varchar',
        length: '250',
        isNullable: false,
        default: `''`,
      }),
    );

    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'titleDetail',
        type: 'varchar',
        length: '250',
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'seq',
        type: 'integer',
        isNullable: false,
        default: '1',
      }),
    );

    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'smartblock',
        type: 'boolean',
        isNullable: false,
        default: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'tmpSiteId');
    await queryRunner.dropColumn(this.TABLE_NAME, 'titleDetail');
    await queryRunner.dropColumn(this.TABLE_NAME, 'seq');
    await queryRunner.dropColumn(this.TABLE_NAME, 'smartblock');
  }
}
