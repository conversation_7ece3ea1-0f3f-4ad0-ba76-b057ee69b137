"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateNameServersTable1741679542931 = void 0;
const typeorm_1 = require("typeorm");
class UpdateNameServersTable1741679542931 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}name_servers`;
    }
    async up(queryRunner) {
        const column = new typeorm_1.TableColumn({
            name: 'siteId',
            type: 'integer',
            isNullable: false,
        });
        await queryRunner.addColumn(this.TABLE_NAME, column);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
    }
}
exports.UpdateNameServersTable1741679542931 = UpdateNameServersTable1741679542931;
//# sourceMappingURL=1741679542931-update-name-servers-table.js.map