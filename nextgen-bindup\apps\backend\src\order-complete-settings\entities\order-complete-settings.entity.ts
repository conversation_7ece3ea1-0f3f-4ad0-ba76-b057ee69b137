import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('order_completion_settings', { schema: process.env.DATABASE_SCHEMA })
export class OrderCompletionSettingEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @Column({
    name: 'displayText',
    type: 'text',
    nullable: true,
  })
  displayText: string;

  @Column({
    name: 'emailSubject',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  emailSubject: string;

  @Column({
    name: 'emailHeader',
    type: 'text',
    nullable: true,
  })
  emailHeader: string;

  @Column({
    name: 'emailFooter',
    type: 'text',
    nullable: true,
  })
  emailFooter: string;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
