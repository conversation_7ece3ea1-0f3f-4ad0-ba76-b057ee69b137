"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePagesTable1739757489363 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePagesTable1739757489363 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}pages`;
    }
    async up(queryRunner) {
        const userIdColumn = new typeorm_1.TableColumn({
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, userIdColumn);
        const siteIdColumn = new typeorm_1.TableColumn({
            name: 'siteId',
            type: 'integer',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, siteIdColumn);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'userId');
        await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
    }
}
exports.UpdatePagesTable1739757489363 = UpdatePagesTable1739757489363;
//# sourceMappingURL=1739757489363-update-page-table.js.map