"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetEntity = void 0;
const typeorm_1 = require("typeorm");
let AssetEntity = class AssetEntity {
};
exports.AssetEntity = AssetEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], AssetEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'type',
        type: 'varchar',
        length: 20,
        nullable: false,
    }),
    __metadata("design:type", String)
], AssetEntity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'projectId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], AssetEntity.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'name',
        type: 'varchar',
        length: 255,
        nullable: false,
    }),
    __metadata("design:type", String)
], AssetEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'url',
        type: 'varchar',
        length: 500,
        nullable: false,
    }),
    __metadata("design:type", String)
], AssetEntity.prototype, "url", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'designState',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], AssetEntity.prototype, "designState", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], AssetEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], AssetEntity.prototype, "updatedAt", void 0);
exports.AssetEntity = AssetEntity = __decorate([
    (0, typeorm_1.Entity)('assets', { schema: process.env.DATABASE_SCHEMA })
], AssetEntity);
//# sourceMappingURL=asset.entity.js.map