{"version": 3, "file": "name-servers.service.js", "sourceRoot": "", "sources": ["../../src/name-servers/name-servers.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,sEAAiE;AACjE,qCAAqC;AACrC,6CAAmD;AACnD,sEAAmE;AAG5D,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAI7B,KAAK,CAAC,MAAM,CAAC,IAAsB;QACjC,OAAO,IAAI,CAAC,EAAE,CAAC;QAEf,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QAErB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,MAAM,CACV,EAAU,EACV,IAA+B;QAE/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,4BAAY,CAAC,gCAAgC,CAAC,CAAC;QAEtE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QAErB,OAAO,IAAI,CAAC,EAAE,CAAC;QACf,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAC3C,OAAO,EAAE,GAAG,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,4BAAY,CAAC,gCAAgC,CAAC,CAAC;QAEtE,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAc;QAC/B,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,SAAiB;QACrC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;IACzD,CAAC;CACF,CAAA;AA5CY,gDAAkB;AAEpB;IADR,IAAA,0BAAgB,EAAC,qCAAgB,CAAC;8BACV,oBAAU;0DAAmB;6BAF3C,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;GACA,kBAAkB,CA4C9B"}