{"version": 3, "file": "migrate-data-content.service.js", "sourceRoot": "", "sources": ["../../../src/template-migration/migrate/migrate-data-content.service.ts"], "names": [], "mappings": ";;;AAAA,yDAA+C;AAI/C,oEAKwD;AACxD,iDAA6C;AAE7C,oEAA8D;AAK9D,kGAAkG;AAElG,MAAa,yBAAyB;IAOpC,YAAY,GAA8C;QACxD,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;IACnC,CAAC;IAED,gBAAgB,CAAC,GAKhB;QACC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;QAC/B,IAAI,CAAC,EAAE,GAAG,IAAA,oBAAM,GAAE,CAAC;QAEnB,MAAM,EAAE,GAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QAC7F,MAAM,UAAU,GAAgB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvD,MAAM,UAAU,GAAc;YAC5B,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,8BAAa,CAAC,KAAK;YACzB,IAAI,EAAE,EAAE;YACR,QAAQ,EAAE,GAAG,CAAC,iBAAiB;YAC/B,UAAU,EAAE,UAAU;YACtB,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE;gBACV,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;gBACvB,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;aACvB;YACD,EAAE,EAAE,IAAI,CAAC,EAAE;SACZ,CAAC;QACF,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;QAC5C,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAelE,OAAO,UAAU,CAAC;IACpB,CAAC;IAGO,eAAe;QACrB,IAAI,UAAU,GAAgB,0BAAW,CAAC,UAAU,EAAE,CAAC;QAEvD,MAAM,QAAQ,GACZ,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;QAEtD,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,sCAAgB,CAAC,KAAK;gBACzB,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;gBACnD,MAAM;YAER,KAAK,sCAAgB,CAAC,KAAK;gBACzB,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;gBACnD,MAAM;YAER,KAAK,sCAAgB,CAAC,KAAK;gBACzB,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;gBACnD,MAAM;YAER,KAAK,sCAAgB,CAAC,KAAK;gBACzB,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;gBACnD,MAAM;YAER,KAAK,sCAAgB,CAAC,GAAG;gBACvB,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;gBACjD,MAAM;YAER,KAAK,sCAAgB,CAAC,SAAS;gBAC7B,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;gBACvD,MAAM;QACV,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,oBAAoB,CAAC,UAAuB;QAClD,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,oBAAoB,CAAC,UAAuB;QAClD,MAAM,WAAW,GACf,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,qBAAqB,CAAC;QAEzD,UAAU,CAAC,QAAQ,GAAG;YACpB,QAAQ,EACN,WAAW,KAAK,yCAAmB,CAAC,WAAW;gBAC/C,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC;gBACxB,CAAC,CAAC,GAAG;gBACL,CAAC,CAAC,GAAG;YACT,EAAE,EAAE,IAAI,CAAC,EAAE;SACZ,CAAC;QACF,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,oBAAoB,CAAC,UAAuB;QAClD,MAAM,QAAQ,GAAmB,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QAClE,IAAI,QAAQ,CAAC,gBAAgB,KAAK,GAAG;YAAE,OAAO,UAAU,CAAC;QAGzD,MAAM,OAAO,GAA0B,IAAA,kEAAkC,EACvE,IAAI,CAAC,EAAE,CACR,CAAC;QAEF,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YAChB,IAAI,EAAE,OAAO;YACb,GAAG,EAAE,sHAAsH;YAC3H,QAAQ,EAAE;gBACR,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;gBAC7B,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;gBAC7B,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;gBACnC,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;aACpC;YACD,MAAM,EAAE,QAAQ;YAChB,UAAU,EAAE,IAAI;SACC,CAAC,CAAC;QAGrB,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG;YACtB,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;YACjC,WAAW,EAAE,OAAO;SACrB,CAAC;QAEF,MAAM,OAAO,GAAW,0BAAW,CAAC,UAAU,CAC5C,QAAQ,CAAC,qBAAqB,CAC/B,CAAC;QACF,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,OAAO,KAAK,CAAC,EAAE,CAAC;YACzC,UAAU,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;YAClC,UAAU,CAAC,MAAM,CAAC,KAAK,GAAG;gBACxB,KAAK,EAAE,MAAM;gBACb,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;gBACjC,WAAW,EAAE,OAAO;aACrB,CAAC;YACF,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG;gBACzB,KAAK,EAAE,MAAM;gBACb,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;gBACjC,WAAW,EAAE,OAAO;aACrB,CAAC;QACJ,CAAC;QAGD,UAAU,CAAC,aAAa,CAAC,OAAO,GAAG;YACjC,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,GAAG,EAAE;gBACH,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,IAAI;aACZ;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,IAAI;aACZ;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,GAAG;aACX;YACD,QAAQ,EAAE,IAAI;SACf,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,oBAAoB,CAAC,UAAuB;QAClD,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,kBAAkB,CAAC,UAAuB;QAChD,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,wBAAwB,CAAC,UAAuB;QACtD,OAAO,UAAU,CAAC;IACpB,CAAC;CACF;AAlMD,8DAkMC"}