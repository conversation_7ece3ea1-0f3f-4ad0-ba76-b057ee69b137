"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.STRIPE_DESCRIPTION = exports.CASH_ON_DELIVERY_DESCRIPTION = exports.POSTAL_TRANSFER_DESCRIPTION = exports.ACCOUNT_BANK = exports.BANK_TRANSFER_DESCRIPTION = void 0;
exports.BANK_TRANSFER_DESCRIPTION = `【サンプル】

●ご注文後、自動返信メールとは別に当店よりメールにてお支払総額をお知らせ致します。

●お支払は以下の通りです。
お支払金額：（商品代金合計）＋（送料○○○円）＋振込手数料（お客様負担）
※○,○○○円（税込）以上お買上げのお客様は商品代金のみのお振込で結構です。

●おそれいりますが、振込手数料はお客様負担とさせて頂いております。

●ネットバンク以外でも、お近くの銀行窓口/ATMからもお振込いただけます。
　その場合、各銀行規定の他行宛振込手数料が掛かります。

●ご入金確認後、翌営業日での発送となります。
土日祝日は受注、発送業務がとまっております。
前日のご入金分は休み明けに発送となりますので、何卒ご了承ください。

●お振込時の控えは紛失しないようにご注意ください。

●ご注文日から7日以内にご入金確認の取れない場合はご注文をキャンセルさせて頂きますので、あらかじめご了承願います。`;
exports.ACCOUNT_BANK = `【サンプル】

振込先:○○銀行本店 普通 1234567 カ）サンプルショッピング
備考:●「ご注文主様」のお名前と、振込名義が違う場合には
メールにてご連絡くださいますようお願いいたします。`;
exports.POSTAL_TRANSFER_DESCRIPTION = `【サンプル】

郵便局に備え付けの払込取扱票に下記の必要事項を記入して、郵便局窓口またはATMよりお支払いください。

●ご注文後、自動返信メールとは別に当店よりメールにてお支払総額をお知らせ致します。

●お支払は以下の通りです。
お支払金額：（商品代金合計）＋（送料○○○円）＋振り替え手数料（お客様負担）
※○,○○○円（税込）以上お買上げのお客様は商品代金のみのお振り替えで結構です。

●おそれいりますが、振り替え手数料はお客様負担とさせて頂いております。

●ご入金確認後、翌営業日での発送となります。
土日祝日は受注、発送業務がとまっております。
前日のご入金分は休み明けに発送となりますので、何卒ご了承ください。

●お振り替え時の控えは紛失しないようにご注意ください。

●ご注文日から7日以内にご入金確認の取れない場合はご注文をキャンセルさせて頂きますので、あらかじめご了承願います。`;
exports.CASH_ON_DELIVERY_DESCRIPTION = `【サンプル】

業者:佐川急便、ヤマト運輸（当店指定） 備考:
●お支払総額は以下の通りです。
　商品代金合計＋送料＋代引手数料
　代引手数料：下表をご参照ください。
合計金額は、後ほど当店からお送りするメールをご確認ください。
●代金は商品配送時に配送員にお支払いください。
●ラッピングにつきましては現在承っておりません。ご了承ください。
【代引手数料料金表】 全国一律料金：315円
まとめ買い時の扱い １配送先につき、複数の商品をご注文いただいた場合で
も、代引手数料は上記料金表の金額になります。
代引手数料分消費税 この料金には消費税が含まれています`;
exports.STRIPE_DESCRIPTION = `【サンプル】

●クレジットカード（Visa/Mastercard/AMEXなど）でお支払いいただけます。

●手数料は無料です。

●ご注文完了後、決済が確認でき次第、翌営業日に発送いたします。

●安全な決済サービス「Stripe」を使用しておりますので、安心してご利用ください。`;
//# sourceMappingURL=payment-method.constant.js.map