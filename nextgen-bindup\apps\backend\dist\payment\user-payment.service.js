"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPaymentService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const user_info_entity_1 = require("../user-info/entities/user-info.entity");
const stripe_1 = require("stripe");
const typeorm_2 = require("typeorm");
const order_service_1 = require("../order/order.service");
const order_enum_1 = require("../order/enum/order.enum");
const event_emitter_1 = require("@nestjs/event-emitter");
const event_type_enum_1 = require("../common/event-type.enum");
const payment_method_service_1 = require("../payment-method/payment-method.service");
const shop_information_settings_service_1 = require("../shop-information-settings/shop-information-settings.service");
const order_complete_settings_service_1 = require("../order-complete-settings/order-complete-settings.service");
let UserPaymentService = class UserPaymentService {
    constructor(configService, orderService, paymentMethodService, shopInformationService, orderCompletionSettingService, eventEmitter) {
        this.configService = configService;
        this.orderService = orderService;
        this.paymentMethodService = paymentMethodService;
        this.shopInformationService = shopInformationService;
        this.orderCompletionSettingService = orderCompletionSettingService;
        this.eventEmitter = eventEmitter;
        if (this.configService.get('STRIPE_SECRET_KEY')) {
            this.stripe = new stripe_1.default(this.configService.get('STRIPE_SECRET_KEY'), {
                apiVersion: '2025-05-28.basil',
            });
        }
    }
    async getOrderEmailContext(order) {
        const [paymentMethod, shopInfo, orderCompletionSetting] = await Promise.all([
            this.paymentMethodService.getPaymentMethodBySiteId(order.siteId),
            this.shopInformationService.findOneBySiteId(order.siteId),
            this.orderCompletionSettingService.findOneBySiteId(order.siteId),
        ]);
        return {
            order,
            paymentMethod,
            shopInfo,
            orderCompletionSetting,
        };
    }
    async enableStripe(userId) {
        const user = await this.userInfoRepo.findOne({ where: { userId } });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        let stripeAccountId = user.stripeAccountId;
        if (!stripeAccountId) {
            const account = await this.stripe.accounts.create({
                type: 'standard',
                country: 'JP',
                capabilities: {
                    transfers: { requested: true },
                    card_payments: { requested: true },
                },
                business_type: 'individual',
                metadata: {
                    userId: user.userId,
                },
            });
            stripeAccountId = account.id;
            await this.userInfoRepo.update(user.userId, { stripeAccountId });
        }
        const accountLink = await this.stripe.accountLinks.create({
            account: stripeAccountId,
            refresh_url: `http://localhost:5173/cart-management/shop-setting`,
            return_url: `http://localhost:5173/cart-management/shop-setting`,
            type: 'account_onboarding',
        });
        return { url: accountLink.url };
    }
    async checkStripeStatus(userId) {
        const user = await this.userInfoRepo.findOne({ where: { userId } });
        if (!user || !user.stripeAccountId)
            return { connected: false };
        const account = await this.stripe.accounts.retrieve(user.stripeAccountId);
        return {
            connected: !!account.details_submitted,
            email: account.email,
            type: account.type,
            id: account.id,
            stripeDashboardUrl: `https://dashboard.stripe.com/${account.id}`,
        };
    }
    async handleEventCheckoutSessionCompleted(metadata) {
        const orderId = metadata.orderId;
        if (!orderId) {
            throw new common_1.NotFoundException('Order ID or User ID not found in session metadata');
        }
        const order = await this.orderService.findOrderById(+orderId);
        await this.orderService.updateOrder(order.id, {
            orderStatus: order_enum_1.OrderStatus.PAID,
        });
        order.orderStatus = order_enum_1.OrderStatus.PAID;
        const context = await this.getOrderEmailContext(order);
        this.eventEmitter.emit(event_type_enum_1.EventType.ORDER_CREATED, context);
    }
    async checkoutWithCreaditCard(domain, createOrderDto) {
        try {
            const paymentMethodSettings = await this.paymentMethodService.getPaymentMethodBySiteId(+createOrderDto.siteId);
            if (!paymentMethodSettings.stripePaymentGateway.isEnabled) {
                throw new common_1.NotFoundException('Stripe payment gateway is not enabled for this site');
            }
            const stripeAccountId = paymentMethodSettings.stripePaymentGateway.stripeAccountId;
            if (!stripeAccountId) {
                throw new common_1.NotFoundException('Stripe account not found for user/shop');
            }
            const order = await this.orderService.createOrder(createOrderDto, "CREDIT_CARD", order_enum_1.OrderStatus.WAITING_PAYMENT);
            const lineItems = order.orderItems.map(item => ({
                price_data: {
                    currency: 'jpy',
                    product_data: {
                        name: item.productName,
                        images: [item.image],
                    },
                    unit_amount: item.displayPrice,
                },
                quantity: item.quantity,
            }));
            if (order.shippingFee > 0) {
                lineItems.push({
                    price_data: {
                        currency: 'jpy',
                        product_data: {
                            name: 'Shipping Fee',
                        },
                        unit_amount: order.shippingFee,
                    },
                    quantity: 1,
                });
            }
            const session = await this.stripe.checkout.sessions.create({
                payment_method_types: ['card'],
                line_items: lineItems,
                invoice_creation: {
                    enabled: true,
                    invoice_data: {
                        metadata: {
                            type: 'order',
                            orderId: order.id.toString(),
                            siteId: order.siteId.toString(),
                            platformFee: order.platformFee.toString(),
                            paymentGatewayFee: order.paymentGatewayFee.toString(),
                            subtotal: order.subtotal.toString(),
                            shippingFee: order.shippingFee.toString(),
                            total: order.total.toString(),
                            shopNetPayout: order.shopNetPayout.toString(),
                        },
                    },
                },
                payment_intent_data: {
                    receipt_email: order.email,
                    application_fee_amount: Number(order.platformFee) + Number(order.paymentGatewayFee),
                    transfer_data: {
                        destination: stripeAccountId,
                    },
                    on_behalf_of: stripeAccountId,
                },
                metadata: {
                    type: 'order',
                    orderId: order.id.toString(),
                    siteId: order.siteId.toString(),
                    platformFee: order.platformFee.toString(),
                    paymentGatewayFee: order.paymentGatewayFee.toString(),
                    subtotal: order.subtotal.toString(),
                    shippingFee: order.shippingFee.toString(),
                    total: order.total.toString(),
                    shopNetPayout: order.shopNetPayout.toString(),
                },
                mode: 'payment',
                success_url: `${domain}/order-complete?orderId=${order.id.toString()}`,
                cancel_url: `${domain}/checkout`,
            });
            await this.orderService.updateOrder(order.id, {
                checkoutSessionId: session.id,
                checkoutSessionUrl: session.url,
            });
            return { url: session.url };
        }
        catch (err) {
            console.error('Error creating checkout session:', err);
            throw err;
        }
    }
    async checkoutWithBankTransfer(createOrderDto) {
        const order = await this.orderService.createOrder(createOrderDto, "BANK_TRANSFER", order_enum_1.OrderStatus.PLACED);
        const context = await this.getOrderEmailContext(order);
        this.eventEmitter.emit(event_type_enum_1.EventType.ORDER_CREATED, context);
        return {
            orderId: order.id,
        };
    }
    async checkoutWithCashOnDelivery(createOrderDto) {
        const order = await this.orderService.createOrder(createOrderDto, "CASH_ON_DELIVERY", order_enum_1.OrderStatus.PLACED);
        const context = await this.getOrderEmailContext(order);
        this.eventEmitter.emit(event_type_enum_1.EventType.ORDER_CREATED, context);
        return {
            orderId: order.id,
        };
    }
    async checkoutWithPostalTransfer(createOrderDto) {
        const order = await this.orderService.createOrder(createOrderDto, "POSTAL_TRANSFER", order_enum_1.OrderStatus.PLACED);
        const context = await this.getOrderEmailContext(order);
        this.eventEmitter.emit(event_type_enum_1.EventType.ORDER_CREATED, context);
        return {
            orderId: order.id,
        };
    }
    async retryHandleWaitingPendingOrder() {
        if (!this.stripe) {
            console.error('Stripe is not configured');
            return;
        }
        const orders = await this.orderService.getWaitingPaymentOrdersInLast2Days();
        for (const order of orders) {
            try {
                const session = await this.stripe.checkout.sessions.retrieve(order.checkoutSessionId);
                if (session.payment_status === 'paid') {
                    await this.orderService.updateOrder(order.id, {
                        orderStatus: order_enum_1.OrderStatus.PAID,
                    });
                    const context = await this.getOrderEmailContext(order);
                    this.eventEmitter.emit(event_type_enum_1.EventType.ORDER_CREATED, context);
                }
                else {
                    console.warn(`Order ${order.id} is still pending, payment status: ${session.payment_status}`);
                }
            }
            catch (error) {
                console.error(`Error retrieving session for order ${order.id}:`, error);
            }
        }
    }
};
exports.UserPaymentService = UserPaymentService;
__decorate([
    (0, typeorm_1.InjectRepository)(user_info_entity_1.UserInfoEntity),
    __metadata("design:type", typeorm_2.Repository)
], UserPaymentService.prototype, "userInfoRepo", void 0);
exports.UserPaymentService = UserPaymentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        order_service_1.OrderService,
        payment_method_service_1.PaymentMethodService,
        shop_information_settings_service_1.ShopInformationSettingService,
        order_complete_settings_service_1.OrderCompletionSettingService,
        event_emitter_1.EventEmitter2])
], UserPaymentService);
//# sourceMappingURL=user-payment.service.js.map