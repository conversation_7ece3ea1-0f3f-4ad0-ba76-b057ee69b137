import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { UserPaymentService } from './user-payment.service';
import { Cron, CronExpression } from '@nestjs/schedule';

@Controller('user-payment')
export class UserPaymentController {
  constructor(private readonly userPaymentService: UserPaymentService) {}
  @Post('enable')
  async enableStripe(@Body('userId') userId: string) {
    const result = await this.userPaymentService.enableStripe(userId);
    return {
      success: true,
      onboardingUrl: result.url,
    };
  }

  @Get('status')
  async checkStripeStatus(@Query('userId') userId: string) {
    const result = await this.userPaymentService.checkStripeStatus(userId);
    return result;
  }

  @Cron(CronExpression.EVERY_MINUTE)
  handleUpdateOrderStatus() {
    this.userPaymentService.retryHandleWaitingPendingOrder();
  }
}
