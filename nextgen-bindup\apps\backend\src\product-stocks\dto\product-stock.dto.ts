export enum StockErrorType {
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  EXCEED_PURCHASE_LIMIT = 'EXCEED_PURCHASE_LIMIT',
  NOT_ORDERABLE = 'NOT_ORDERABLE',
}

export class CartItem {
  productId: number;
  siteId: number;
  x: number;
  y: number;
  quantity: number;
}

export class CartItemResponse {
  productId: number;
  siteId: number;
  x: number;
  y: number;
  quantity: number;
  available: number;
  errorType: StockErrorType;
}

export class CheckStockResponse {
  allItemsAvailable: boolean;
  unavailableItems: CartItemResponse[];
}
