import { AxiosResponse } from 'axios';
import 'dayjs/locale/ja';
import { memo, useCallback, useEffect, useRef, useState, type FC } from 'react';
import { useTranslation } from 'react-i18next';
import { Delete, Edit, MoreVert } from '@mui/icons-material';
import AddIcon from '@mui/icons-material/Add';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Menu,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  TextField,
} from '@mui/material';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import IconButton from '@mui/material/IconButton';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import { grey } from '@mui/material/colors';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { useAuth } from '../../auth';
import { CreateTempTokenRes } from '../../dto/auth.type';
import { SiteEntity } from '../../dto/site.type';
import {
  SiteVersionEntity,
  VersionHistoryDto,
} from '../../dto/version-history.dto';
import apiClient from '../../services/api-service';
import { VersionHistoryService } from '../../services/version-history.service';
import i18n from '../../utils/i18n';
import ConfirmationDialog from '../common/ConfirmmationDialog';

type Props = {
  open: boolean;
  closeDrawer: () => void;
  site: SiteEntity;
};

const DEFAULT_DATA: VersionHistoryDto = {
  data: [],
  total: 0,
  page: 1,
  pageSize: 100,
};

const checkHasMore = (data: VersionHistoryDto) => {
  const { total, page, pageSize } = data;
  return page * pageSize < total;
};

const HistoryDrawer: FC<Props> = ({ open, closeDrawer, site }) => {
  const { session } = useAuth();

  const { t } = useTranslation();
  const [data, setData] = useState(DEFAULT_DATA);
  const drawerRef = useRef<HTMLDivElement>(null);
  const [query, setQuery] = useState({ year: null, month: null, page: 1 });
  const [hasMore, setHasMore] = useState(true);
  const [filterMode, setFilterMode] = useState<'year' | 'month-year'>('year');
  const [showFilter, setShowFilter] = useState(false);
  const [backupName, setBackupName] = useState('');

  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const fetchVersionHistory = useCallback(async () => {
    if (!site.id) return;
    try {
      const _data = await VersionHistoryService.getSiteVersions(
        +site.id,
        query.page,
        25,
        query.month,
        query.year,
      );
      const hasMore = checkHasMore(_data);
      setHasMore(hasMore);
      if (query.page === 1) {
        setData(_data);
      } else {
        setData(prev => ({
          data: [...prev.data, ..._data.data],
          total: _data.total,
          page: _data.page,
          pageSize: _data.pageSize,
        }));
      }
    } catch (error) {
      console.error(error);
    }
  }, [site.id, query]);

  useEffect(() => {
    fetchVersionHistory();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query]);

  const openDesignEditor = async (versionId: number) => {
    if (!site || !session?.accessToken) return;
    try {
      const response: AxiosResponse<CreateTempTokenRes> =
        await apiClient.post<CreateTempTokenRes>('/auth/tmp-access', {
          type: 'site_preview',
          siteId: site.id,
          projectId: site.projectId,
          versionId,
        });

      window.open(
        `${import.meta.env.VITE_DESIGN_EDITOR_ENPOINT}?token=${encodeURIComponent(response.data.token)}`,
        '_blank',
      );
    } catch (error) {
      console.error(error);
    }
  };

  const handleFilterClick = () => {
    setShowFilter(!showFilter);
  };

  const backup = async (name: string) => {
    if (!site.id) return;
    try {
      await VersionHistoryService.backupSite(site.id, name);

      fetchVersionHistory();
    } catch (error) {
      console.error(error);
    }
  };

  const formatDate = (date: Date) => {
    if (!date) return '';
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  };

  const handleFilterModeChange = (event: SelectChangeEvent) => {
    setFilterMode(event.target.value as 'year' | 'month-year');
  };

  const [filterYear, setFilterYear] = useState(null);
  const [filterMonthYear, setFilterMonthYear] = useState(null);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleYearChange = (date: any) => {
    setFilterYear(date);
    setQuery({ year: date?.year(), month: null, page: 1 });
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleMonthYearChange = (date: any) => {
    setFilterMonthYear(date);
    setQuery({
      year: date?.year(),
      month: date?.month() + 1,
      page: 1,
    });
  };

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedItem, setSelectedItem] = useState<SiteVersionEntity | null>(
    null,
  );

  const handleClick = (
    event: React.MouseEvent<HTMLElement>,
    version: SiteVersionEntity,
  ) => {
    setAnchorEl(event.currentTarget);
    setSelectedItem(version);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSelectedItem(null);
  };

  const handleEdit = () => {
    setBackupName(selectedItem?.versionName || '');
    setOpenEditDialog(true);
  };

  const handleDelete = () => {
    setOpenDeleteDialog(true);
  };
  return (
    <>
      <Drawer anchor="right" open={open} onClose={closeDrawer}>
        <Box
          ref={drawerRef}
          sx={{
            width: 240,
            height: '100%',
            backgroundColor: grey[200],
            overflowY: 'hidden',
          }}
          role="presentation"
          onClick={event => event.stopPropagation()}
          onKeyDown={event => event.stopPropagation()}
        >
          <Box>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingTop: 3,
                paddingLeft: 2,
              }}
            >
              <Typography>{t('version_history.title')}</Typography>
              <Box>
                <IconButton onClick={handleFilterClick}>
                  <FilterAltIcon />
                </IconButton>
                <IconButton
                  onClick={() => {
                    setOpenConfirmDialog(true);
                  }}
                >
                  <AddIcon />
                </IconButton>
              </Box>
            </Box>
            {showFilter && (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: 1,
                }}
              >
                <Stack spacing={2} direction="column">
                  <Select
                    label="Filter"
                    value={filterMode}
                    onChange={handleFilterModeChange}
                    sx={{ height: 30, minWidth: 150 }}
                  >
                    <MenuItem value="year">
                      {t('version_history.year')}
                    </MenuItem>
                    <MenuItem value="month-year">
                      {t('version_history.month_year')}
                    </MenuItem>
                  </Select>
                  <LocalizationProvider
                    dateAdapter={AdapterDayjs}
                    adapterLocale={i18n.language}
                  >
                    {filterMode === 'year' ? (
                      <DatePicker
                        label={t('version_history.year')}
                        views={['year']}
                        value={filterYear}
                        onChange={handleYearChange}
                      />
                    ) : (
                      <DatePicker
                        label={t('version_history.month_year')}
                        views={['month', 'year']}
                        value={filterMonthYear}
                        onChange={handleMonthYearChange}
                      />
                    )}
                  </LocalizationProvider>
                </Stack>
              </Box>
            )}
          </Box>

          <Box
            sx={{
              flex: 'auto',
              height: `calc(100% - ${showFilter ? 180 : 65}px)`,
              overflowY: 'auto',
            }}
          >
            {data.data.map(version => (
              <Box key={version.id}>
                <ListItem
                  key={`item-${version.id}`}
                  sx={{
                    '& .MuiListItemSecondaryAction-root': {
                      opacity: 0,
                      transition: 'opacity 0.3s ease',
                    },
                    '&:hover .MuiListItemSecondaryAction-root': {
                      opacity: 1,
                    },
                  }}
                  disablePadding
                  secondaryAction={
                    <>
                      <Box>
                        <IconButton
                          onClick={event => handleClick(event, version)}
                        >
                          <MoreVert />
                        </IconButton>
                        <Menu
                          anchorEl={anchorEl}
                          open={Boolean(anchorEl) && selectedItem === version}
                          onClose={handleClose}
                        >
                          <MenuItem onClick={handleEdit}>
                            <Edit fontSize="small" style={{ marginRight: 8 }} />
                            {t('common.edit')}
                          </MenuItem>
                          <MenuItem onClick={handleDelete}>
                            <Delete
                              fontSize="small"
                              style={{ marginRight: 8 }}
                            />
                            {t('common.btn_delete')}
                          </MenuItem>
                        </Menu>
                      </Box>
                    </>
                  }
                >
                  <ListItemButton onClick={() => openDesignEditor(version.id)}>
                    <ListItemText
                      primary={
                        <div>
                          <Typography variant="body2">
                            {version.versionName}
                          </Typography>
                          <Typography variant="body1">
                            {`${formatDate(new Date(version.versionCreatedAt))}`}
                          </Typography>
                        </div>
                      }
                    />
                  </ListItemButton>
                </ListItem>
              </Box>
            ))}

            {hasMore && (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Button
                  onClick={() => {
                    setQuery(prev => ({ ...prev, page: prev.page + 1 }));
                  }}
                >
                  {t('common.btn_load_more')}
                </Button>
              </Box>
            )}
          </Box>
        </Box>
      </Drawer>

      <ConfirmationDialog
        open={openDeleteDialog}
        title={t('delete_version_confirm.title')}
        message={t('delete_version_confirm.message', {
          versionName: selectedItem?.versionName,
        })}
        confirmColor="primary"
        confirmLabel={t('common.btn_delete')}
        onConfirm={async () => {
          if (!selectedItem?.id) return;
          try {
            await VersionHistoryService.deleteSiteVersion(selectedItem?.id);
            setQuery(prev => ({ ...prev, page: 1 }));
          } catch (error) {
            // TODO: alert error
            console.error('Error while delete site:', error);
          } finally {
            setOpenDeleteDialog(false);
            handleClose();
          }
        }}
        onCancel={() => {
          setOpenDeleteDialog(false);
        }}
      />

      <Dialog maxWidth={false} open={openEditDialog} onClose={() => {}}>
        <Stack sx={{ width: '600px' }}>
          <DialogContent>
            <Stack spacing={2}>
              <TextField
                label={t('backup_confirm.field.name')}
                variant="filled"
                value={backupName}
                onChange={event => setBackupName(event.target.value)}
              />
            </Stack>
          </DialogContent>

          <DialogActions>
            <Button
              variant="text"
              onClick={() => {
                setBackupName('');
                setOpenEditDialog(false);
                handleClose();
              }}
            >
              {t('common.btn_cancel')}
            </Button>

            <Button
              variant="contained"
              color="primary"
              onClick={async () => {
                if (!selectedItem?.id) return;
                try {
                  await VersionHistoryService.updateVersionName(
                    selectedItem?.id,
                    backupName,
                  );
                  setQuery(prev => ({ ...prev, page: 1 }));
                } catch (error) {
                  // TODO: alert error
                  console.error('Error while edit site version:', error);
                }
                setBackupName('');
                setOpenEditDialog(false);
                handleClose();
              }}
            >
              {t('common.btn_save')}
            </Button>
          </DialogActions>
        </Stack>
      </Dialog>

      <Dialog maxWidth={false} open={openConfirmDialog} onClose={() => {}}>
        <Stack sx={{ width: '600px' }}>
          <DialogContent>
            <Stack spacing={2}>
              <TextField
                label={t('backup_confirm.field.name')}
                variant="filled"
                value={backupName}
                onChange={event => setBackupName(event.target.value)}
              />
            </Stack>
          </DialogContent>

          <DialogActions>
            <Button
              variant="text"
              onClick={() => {
                setBackupName('');
                setOpenConfirmDialog(false);
              }}
            >
              {t('common.btn_cancel')}
            </Button>

            <Button
              variant="contained"
              color="primary"
              onClick={async () => {
                try {
                  await backup(backupName);
                } catch (error) {
                  // TODO: alert error
                  console.error('Error while delete site:', error);
                }
                setBackupName('');
                setOpenConfirmDialog(false);
              }}
            >
              {t('backup_confirm.backup_btn')}
            </Button>
          </DialogActions>
        </Stack>
      </Dialog>
    </>
  );
};

export default memo(HistoryDrawer, (prev, next) => prev.open === next.open);
