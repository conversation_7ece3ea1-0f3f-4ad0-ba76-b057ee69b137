{"version": 3, "file": "1738652412290-create-user-info-table.js", "sourceRoot": "", "sources": ["../../src/migrations/1738652412290-create-user-info-table.ts"], "names": [], "mappings": ";;;AAAA,qCAAiE;AAEjE,MAAa,gCAAgC;IAA7C;QACE,eAAU,GAAW,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,WAAW,CAAC;IAwKrE,CAAC;IAtKQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,WAAW,CAC3B,IAAI,eAAK,CAAC;YACR,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe;YACnC,IAAI,EAAE,IAAI,CAAC,UAAU;YACrB,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,SAAS,EAAE,IAAI;iBAChB;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,UAAU;oBAChB,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,GAAG;iBACb;gBACD;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,eAAe;oBACrB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,UAAU;oBAChB,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,gBAAgB;oBACtB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,UAAU;oBAChB,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,UAAU;oBAChB,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,UAAU;oBAChB,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,mBAAmB;oBACzB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,gBAAgB;oBACtB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,mBAAmB;oBACzB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,aAAa;oBACnB,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,sBAAsB;iBAChC;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,aAAa;oBACnB,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,sBAAsB;iBAChC;aACF;SACF,CAAC,EACF,IAAI,CACL,CAAC;QAGF,MAAM,WAAW,CAAC,KAAK,CACrB,iDAAiD,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,EAAE,CAClG,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,kDAAkD,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,EAAE,CACnG,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CACzB,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,EAAE,CACpD,CAAC;IACJ,CAAC;CACF;AAzKD,4EAyKC"}