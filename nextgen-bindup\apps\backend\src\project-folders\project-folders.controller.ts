import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ProjectFoldersService } from './project-folders.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { ProjectFolderEntity } from './entities/project-folders.entity';

@Controller('project-folders')
@UseGuards(AuthGuard)
export class ProjectFoldersController {
  constructor(private readonly projectFoldersService: ProjectFoldersService) {}

  @Post('create')
  async create(@Body() projectFolder: ProjectFolderEntity) {
    return await this.projectFoldersService.create(projectFolder);
  }

  @Get('project/:projectId')
  async getProjectsByUser(@Param('projectId') projectId: string) {
    return await this.projectFoldersService.findByProjectId(+projectId);
  }
}
