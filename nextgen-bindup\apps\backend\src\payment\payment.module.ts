import { Module } from '@nestjs/common';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { PlanEntity } from './entities/plan.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionEntity } from './entities/subscription.entity';
import { UserInfoEntity } from 'src/user-info/entities/user-info.entity';
import { UserPaymentController } from './user-payment.controller';
import { UserPaymentService } from './user-payment.service';
import { ProductModule } from 'src/product/product.module';
import { OrderModule } from 'src/order/order.module';
import { SiteModule } from 'src/site/site.module';
import { PaymentMethodModule } from 'src/payment-method/payment-method.module';
import { ShopInformationSettingModule } from 'src/shop-information-settings/shop-information-settings.module';
import { OrderCompletionSettingModule } from 'src/order-complete-settings/order-complete-settings.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PlanEntity]),
    TypeOrmModule.forFeature([SubscriptionEntity]),
    TypeOrmModule.forFeature([UserInfoEntity]),
    ProductModule,
    OrderModule,
    SiteModule,
    PaymentMethodModule,
    ShopInformationSettingModule,
    OrderCompletionSettingModule,
  ],
  controllers: [PaymentController, UserPaymentController],
  providers: [PaymentService, UserPaymentService],
  exports: [PaymentService, UserPaymentService],
})
export class PaymentModule {}
