"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderCompletionSettingModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const order_complete_settings_entity_1 = require("./entities/order-complete-settings.entity");
const order_complete_settings_controller_1 = require("./order-complete-settings.controller");
const order_complete_settings_service_1 = require("./order-complete-settings.service");
const site_module_1 = require("../site/site.module");
let OrderCompletionSettingModule = class OrderCompletionSettingModule {
};
exports.OrderCompletionSettingModule = OrderCompletionSettingModule;
exports.OrderCompletionSettingModule = OrderCompletionSettingModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([order_complete_settings_entity_1.OrderCompletionSettingEntity]),
            site_module_1.SiteModule,
        ],
        controllers: [order_complete_settings_controller_1.OrderCompletionSettingController],
        providers: [order_complete_settings_service_1.OrderCompletionSettingService],
        exports: [order_complete_settings_service_1.OrderCompletionSettingService],
    })
], OrderCompletionSettingModule);
//# sourceMappingURL=order-complete-settings.module.js.map