import {
  Column,
  CreateDate<PERSON>olumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Component } from '@nextgen-bindup/common/dto/component';
import { PageStatus, PageType } from '../types/page.type';
import { DatasourcePropDto } from '@nextgen-bindup/common/dto/setting-properties/datasource-prop.dto';

@Entity('pages', { schema: process.env.DATABASE_SCHEMA })
export class PageEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'type',
    type: 'varchar',
    length: 15,
    nullable: false,
    default: PageType.PAGE,
  })
  type: PageType = PageType.PAGE;

  @Column({
    name: 'parentId',
    type: 'integer',
    nullable: true,
  })
  parentId: number;

  @Column({
    name: 'projectId',
    type: 'integer',
    nullable: true,
  })
  projectId: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: true,
  })
  siteId: number;

  @Column({
    name: 'datasource',
    type: 'jsonb',
    nullable: true,
  })
  datasource: DatasourcePropDto;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  name: string;

  @Column({
    name: 'components',
    type: 'jsonb',
    nullable: true,
  })
  components: Record<string, Component>;

  @Column({
    name: 'ts',
    type: 'bigint',
    nullable: true,
  })
  ts: number;

  @Column({
    name: 'status',
    type: 'smallint',
    nullable: false,
    default: PageStatus.DRAFT,
  })
  status: PageStatus = PageStatus.DRAFT;

  @Column({
    name: 'url',
    type: 'varchar',
    length: 255,
  })
  url: string;

  @Column({
    name: 'title',
    type: 'varchar',
    length: 255,
  })
  title: string;

  @Column({
    name: 'description',
    type: 'text',
  })
  description: string;

  @Column({
    name: 'isSearch',
    type: 'boolean',
  })
  isSearch: boolean;

  @Column({
    name: 'thumb',
    type: 'varchar',
    length: 255,
  })
  thumb: string;

  @Column({
    name: 'headCode',
    type: 'text',
  })
  headCode: string;

  @Column({
    name: 'bodyCode',
    type: 'text',
  })
  bodyCode: string;

  @Column({
    name: 'isPrivate',
    type: 'boolean',
    nullable: false,
    default: false,
  })
  isPrivate: boolean = false;

  @Column({
    name: 'isHome',
    type: 'boolean',
    nullable: false,
    default: false,
  })
  isHome: boolean = false;

  @Column('int', { array: true, default: {} })
  children: number[];

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;

  @Column({
    name: 'userId',
    type: 'varchar',
    length: '36',
  })
  userId: string;

  @Column({
    name: 'isDeleted',
    type: 'boolean',
    nullable: true,
    default: false,
  })
  isDeleted: boolean;
}
