import { Repository } from 'typeorm';
import { CmsCollectionItemEntity } from './entities/cms-collection-items.entity';
import { PaginationDto } from './dto/pagination.dto';
import { CmsCollectionService } from 'src/cms-collection/cms-collection.service';
import { ProductService } from 'src/product/product.service';
import { ShopInformationSettingService } from 'src/shop-information-settings/shop-information-settings.service';
export declare class CmsCollectionItemsService {
    private readonly cmsCollectionService;
    private readonly productService;
    private readonly shopInformationSettingService;
    readonly collectionItemRepo: Repository<CmsCollectionItemEntity>;
    constructor(cmsCollectionService: CmsCollectionService, productService: ProductService, shopInformationSettingService: ShopInformationSettingService);
    create(collectionItemEntity: CmsCollectionItemEntity): Promise<CmsCollectionItemEntity>;
    update(id: number, collectionItemData: Partial<CmsCollectionItemEntity>): Promise<CmsCollectionItemEntity>;
    findById(id: number): Promise<CmsCollectionItemEntity>;
    findByCollectionId(cmsCollectionId: number, dto?: PaginationDto): Promise<{
        data: CmsCollectionItemEntity[];
        count: number;
    }>;
    findBySiteId(siteId: number): Promise<Record<string, CmsCollectionItemEntity[]>>;
    delete(id: number): Promise<boolean>;
}
