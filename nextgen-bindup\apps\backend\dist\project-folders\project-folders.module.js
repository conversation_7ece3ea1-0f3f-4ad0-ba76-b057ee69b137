"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectFoldersModule = void 0;
const common_1 = require("@nestjs/common");
const project_folders_controller_1 = require("./project-folders.controller");
const project_folders_service_1 = require("./project-folders.service");
const project_folders_entity_1 = require("./entities/project-folders.entity");
const typeorm_1 = require("@nestjs/typeorm");
let ProjectFoldersModule = class ProjectFoldersModule {
};
exports.ProjectFoldersModule = ProjectFoldersModule;
exports.ProjectFoldersModule = ProjectFoldersModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([project_folders_entity_1.ProjectFolderEntity])],
        controllers: [project_folders_controller_1.ProjectFoldersController],
        providers: [project_folders_service_1.ProjectFoldersService],
        exports: [project_folders_service_1.ProjectFoldersService],
    })
], ProjectFoldersModule);
//# sourceMappingURL=project-folders.module.js.map