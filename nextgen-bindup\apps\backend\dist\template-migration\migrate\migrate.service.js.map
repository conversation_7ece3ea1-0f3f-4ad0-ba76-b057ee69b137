{"version": 3, "file": "migrate.service.js", "sourceRoot": "", "sources": ["../../../src/template-migration/migrate/migrate.service.ts"], "names": [], "mappings": ";;;AACA,4DAAiE;AAKjE,2DAAsD;AAEtD,iDAAyE;AACzE,yDAA+C;AAE/C,oEAA8D;AAC9D,6EAAuE;AAKvE,mDAA+C;AAC/C,iFAA2E;AAE3E,MAAa,cAAc;IAYzB,YAAY,GAA6D;QAVzE,eAAU,GAA8B,eAAe,CACrD,yCAA0B,CAC3B,CAAC;QAMF,aAAQ,GAAW,CAAC,CAAC;QAGnB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,eAAe,CAAC;QAE3C,IAAI,CAAC,MAAM,GAAG,6BAAY,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,UAAU,GAAG,6BAAY,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,QAAQ,GAAqB,6BAAY,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1E,IAAI,CAAC,SAAS,GAAG,6BAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACrE,CAAC;IAED,WAAW,CAAC,GAGX;QACC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAElB,MAAM,UAAU,GAAkB,IAAI,CAAC,MAAM,CAAC,MAAM,CAClD,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAC3C,CAAC;QAEF,MAAM,gBAAgB,GACpB,IAAI,oDAAuB,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC/D,MAAM,kBAAkB,GACtB,IAAI,wDAAyB,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAEjE,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAoB,IAAI,CAAC,UAAU,CAAC,IAAI,CACrD,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,WAAW,KAAK,KAAK,CAAC,WAAW,CACzD,CAAC;YAEF,MAAM,UAAU,GAAW,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM,WAAW,GAAc,gBAAgB,CAAC,gBAAgB,CAAC;gBAC/D,UAAU;gBACV,KAAK;gBACL,SAAS;aACV,CAAC,CAAC;YAGH,MAAM,YAAY,GAAwB,sBAAS,CAAC,YAAY,CAC9D,SAAS,EACT,IAAI,CAAC,SAAS,CACf,CAAC;YACF,MAAM,UAAU,GAAsB,sBAAS,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YACzE,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,kBAAkB,CAAC,gBAAgB,CAAC;oBAClC,iBAAiB,EAAE,WAAW,CAAC,EAAE;oBACjC,KAAK;oBACL,SAAS;oBACT,SAAS;iBACV,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAGO,eAAe;QACrB,MAAM,EAAE,GAAG,iBAAiB,CAAC;QAC7B,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAAE,OAAO;QAChC,MAAM,EAAE,GAAW,IAAA,oBAAM,GAAE,CAAC;QAE5B,MAAM,UAAU,GAAgB,0BAAW,CAAC,UAAU,EAAE,CAAC;QACzD,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG;YACtB,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,GAAG;SACV,CAAC;QAEF,MAAM,cAAc,GAAc;YAChC,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,8BAAa,CAAC,KAAK;YACzB,IAAI,EAAE,WAAW;YACjB,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE,UAAU;YACtB,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE;gBACV,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAClB,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;aAClB;YACD,EAAE,EAAE,EAAE;SACP,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC;QACpD,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;IAC/D,CAAC;IAEO,WAAW;QACjB,MAAM,EAAE,GAAG,eAAe,CAAC;QAC3B,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAAE,OAAO;QAChC,MAAM,EAAE,GAAW,IAAA,oBAAM,GAAE,CAAC;QAE5B,MAAM,UAAU,GAAgB,0BAAW,CAAC,UAAU,EAAE,CAAC;QACzD,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG;YACtB,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,GAAG;SACV,CAAC;QACF,UAAU,CAAC,QAAQ,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAE7C,MAAM,KAAK,GAAc;YACvB,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,8BAAa,CAAC,QAAQ;YAC5B,IAAI,EAAE,WAAW;YACjB,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE,UAAU;YACtB,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE;gBACV,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAClB,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;aAClB;YACD,EAAE,EAAE,EAAE;SACP,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,WAAW;QACjB,MAAM,EAAE,GAAG,gBAAgB,CAAC;QAC5B,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAAE,OAAO;QAChC,MAAM,EAAE,GAAW,IAAA,oBAAM,GAAE,CAAC;QAE5B,MAAM,UAAU,GAAgB,0BAAW,CAAC,UAAU,EAAE,CAAC;QACzD,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG;YACtB,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,GAAG;SACV,CAAC;QACF,UAAU,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;QAC1D,UAAU,CAAC,QAAQ,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAE7C,MAAM,KAAK,GAAc;YACvB,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,8BAAa,CAAC,QAAQ;YAC5B,IAAI,EAAE,YAAY;YAClB,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE,UAAU;YACtB,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE;gBACV,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAClB,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;aAClB;YACD,EAAE,EAAE,EAAE;SACP,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,YAAY;QAClB,MAAM,EAAE,GAAG,YAAY,CAAC;QACxB,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAAE,OAAO;QAChC,MAAM,EAAE,GAAW,IAAA,oBAAM,GAAE,CAAC;QAE5B,MAAM,UAAU,GAAgB,0BAAW,CAAC,UAAU,EAAE,CAAC;QACzD,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG;YACvB,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;SACX,CAAC;QAEF,MAAM,MAAM,GAAc;YACxB,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,8BAAa,CAAC,MAAM;YAC1B,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE,UAAU;YACtB,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE;gBACV,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAClB,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;aAClB;YACD,EAAE,EAAE,EAAE;SACP,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACvD,CAAC;IAEO,YAAY;QAClB,MAAM,EAAE,GAAG,YAAY,CAAC;QACxB,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAAE,OAAO;QAChC,MAAM,EAAE,GAAW,IAAA,oBAAM,GAAE,CAAC;QAE5B,MAAM,UAAU,GAAgB,0BAAW,CAAC,UAAU,EAAE,CAAC;QACzD,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG;YACvB,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;SACX,CAAC;QAEF,MAAM,MAAM,GAAc;YACxB,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,8BAAa,CAAC,MAAM;YAC1B,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE,UAAU;YACtB,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE;gBACV,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAClB,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;aAClB;YACD,EAAE,EAAE,EAAE;SACP,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACvD,CAAC;IAEO,eAAe,CAAC,MAAkB;QACxC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,4BAAU,CAAC,MAAM;gBACpB,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,OAAO,YAAY,CAAC;YAEtB,KAAK,4BAAU,CAAC,SAAS;gBACvB,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,OAAO,iBAAiB,CAAC;YAE3B,KAAK,4BAAU,CAAC,IAAI;gBAClB,OAAO,UAAU,CAAC;YAEpB,KAAK,4BAAU,CAAC,MAAM;gBACpB,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,OAAO,eAAe,CAAC;YAEzB,KAAK,4BAAU,CAAC,MAAM;gBACpB,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,OAAO,gBAAgB,CAAC;YAE1B,KAAK,4BAAU,CAAC,MAAM;gBACpB,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,OAAO,YAAY,CAAC;YAEtB;gBACE,OAAO,eAAe,CAAC;QAC3B,CAAC;IACH,CAAC;CACF;AAxPD,wCAwPC"}