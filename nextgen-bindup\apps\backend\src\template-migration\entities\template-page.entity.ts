import { PageStatus, PageType } from 'src/page/types/page.type';
import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('template_pages', { schema: process.env.DATABASE_SCHEMA })
export class TemplatePageEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'templateId',
    type: 'integer',
    nullable: false,
  })
  templateId: number;

  @Column({
    name: 'templateSiteId',
    type: 'integer',
    nullable: false,
  })
  templateSiteId: number;

  @Column({
    name: 'templatePageId',
    type: 'integer',
    nullable: false,
  })
  templatePageId: number;

  @Column({
    name: 'type',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  type: PageType = PageType.PAGE;

  @Column({
    name: 'parentId',
    type: 'integer',
    nullable: true,
  })
  parentId: number;

  @Column({
    name: 'components',
    type: 'jsonb',
    nullable: true,
  })
  components: any;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string;

  @Column({
    name: 'ts',
    type: 'bigint',
    nullable: true,
  })
  ts: number;

  @Column({
    name: 'status',
    type: 'smallint',
    nullable: false,
  })
  status: PageStatus = PageStatus.DRAFT;

  @Column({
    name: 'url',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  url: string;

  @Column({
    name: 'title',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  title: string;

  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
  })
  description: string;

  @Column({
    name: 'isSearch',
    type: 'boolean',
    nullable: true,
  })
  isSearch: boolean;

  @Column({
    name: 'thumb',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  thumb: string;

  @Column({
    name: 'headCode',
    type: 'text',
    nullable: true,
  })
  headCode: string;

  @Column({
    name: 'bodyCode',
    type: 'text',
    nullable: true,
  })
  bodyCode: string;

  @Column({
    name: 'isPrivate',
    type: 'boolean',
    nullable: false,
    default: false,
  })
  isPrivate: boolean = false;

  @Column({
    name: 'isHome',
    type: 'boolean',
    nullable: false,
    default: false,
  })
  isHome: boolean = false;

  @Column('int', { array: true, default: {} })
  children: number[];

  @Column({
    name: 'userId',
    type: 'varchar',
    length: '36',
    nullable: false,
  })
  userId: string;

  @Column({
    name: 'isDeleted',
    type: 'boolean',
    nullable: false,
    default: false,
  })
  isDeleted: boolean = false;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
