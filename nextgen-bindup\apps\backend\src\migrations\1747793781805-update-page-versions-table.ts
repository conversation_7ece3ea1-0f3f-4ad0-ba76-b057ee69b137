import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdatePageVersionsTable1747793781805
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}page_versions`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'datasource',
        type: 'jsonb',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'datasource');
  }
}
