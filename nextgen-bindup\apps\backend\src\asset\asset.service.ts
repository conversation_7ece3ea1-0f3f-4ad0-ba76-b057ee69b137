import { Injectable } from '@nestjs/common';
import { AssetEntity } from './entities/asset.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProjectService } from 'src/project/project.service';
import { AppException } from 'src/common/exceptions/app.exception';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

@Injectable()
export class AssetService {
  @InjectRepository(AssetEntity)
  readonly assetRepo: Repository<AssetEntity>;

  constructor(private readonly projectService: ProjectService) {}

  async create(assetEntity: AssetEntity): Promise<AssetEntity> {
    const project = await this.projectService.findById(assetEntity.projectId);
    if (!project) throw new AppException('error.project_not_found');

    const now: Date = new Date();
    const asset = new AssetEntity();
    asset.type = assetEntity.type;
    asset.projectId = assetEntity.projectId;
    asset.name = assetEntity.name;
    asset.url = assetEntity.url;
    asset.createdAt = now;
    asset.updatedAt = now;
    return await this.assetRepo.save(asset);
  }

  async update(
    id: number,
    assetData: Partial<AssetEntity>,
  ): Promise<AssetEntity> {
    const asset = await this.assetRepo.findOneBy({ id: id });
    if (!asset) throw new AppException('asset.error.not_found');

    delete assetData.id;
    assetData.updatedAt = new Date();
    await this.assetRepo.update(
      id,
      assetData as QueryDeepPartialEntity<AssetEntity>,
    );
    return { ...asset, ...assetData };
  }

  async findById(id: number): Promise<AssetEntity> {
    return await this.assetRepo.findOneBy({ id });
  }

  async findByProjectId(projectId: number): Promise<AssetEntity[]> {
    return await this.assetRepo.findBy({ projectId });
  }

  async delete(id: number): Promise<boolean> {
    const asset: AssetEntity = await this.assetRepo.findOneBy({ id });
    if (!asset) throw new AppException('asset.error.not_found');

    await this.assetRepo.delete(asset.id);
    return true;
  }

  async findByUrl(url: string): Promise<AssetEntity> {
    return await this.assetRepo.findOneBy({ url });
  }
}
