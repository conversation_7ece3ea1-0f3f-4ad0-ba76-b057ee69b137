# NextGen BindUp - Test Environment

This directory contains the complete test environment setup for NextGen BindUp, designed to keep all test-related files separate from the main source code.

## 🚀 Quick Start

### One Command Setup and Start
```powershell
cd test
.\start_env.ps1
```

This single command will:
1. ✅ Check prerequisites (Node.js, Yarn)
2. 📁 Create test directories (`test-data`, `test-results`, `test-configs`)
3. ⚙️ Create environment configuration files
4. 📦 Install all project dependencies
5. 🚀 Start all services
6. 🌐 Display service URLs

## 📋 Available Commands

```powershell
# Complete setup and start (default)
.\start_env.ps1

# Only check if services are running
.\start_env.ps1 -CheckOnly

# Only stop all services
.\start_env.ps1 -StopOnly

# Show detailed help
.\start_env.ps1 -Help

# Enable verbose output
.\start_env.ps1 -Verbose
```

## 🌐 Service URLs

After running the script, access your services at:

- **Backend API**: http://localhost:4000
- **Live Editor**: http://localhost:3000
- **Design Editor**: http://localhost:5173
- **Dashboard**: http://localhost:5174
- **SSG**: http://localhost:4321

## 📁 Directory Structure

```
test/
├── start_env.ps1             # 🆕 Main setup and start script
├── package.json              # Test dependencies
├── yarn.lock                 # Locked dependencies
├── test-data/                # Test data files (created automatically)
├── test-results/             # Test execution results (created automatically)
├── test-configs/             # Environment configurations (created automatically)
│   ├── backend.env
│   ├── live-editor.env
│   ├── design-editor.env
│   └── dashboard.env
├── TestCases.md              # Test cases documentation
├── Tester_POV.md             # Tester perspective
└── [legacy scripts...]       # Original scripts still available
```

## 🔧 What the Script Does

### Setup Phase
- Verifies Node.js and Yarn are installed
- Creates necessary test directories within the test folder
- Generates environment configuration files in `test-configs/`
- Installs test dependencies (if package.json exists)
- Installs all project dependencies (root + all apps)

### Start Phase
- Copies environment files from `test-configs/` to each service directory
- Starts services in the correct order (Backend first)
- Automatically installs missing dependencies during startup
- Opens each service in a separate PowerShell window

### Management
- Health checks via HTTP requests to verify services are running
- Graceful service stopping by process ID
- Clear, colored output for better user experience

## 🛡️ Source Code Protection

**Important**: This script is designed to keep all test-related files within the `test/` folder:

- ✅ **Test files stay in test folder**: All test data, results, and configurations
- ✅ **Environment files are copied**: From `test-configs/` to service directories only when needed
- ✅ **No source code modification**: The main `nextgen-bindup/` source code remains untouched
- ✅ **Clean separation**: Test environment is completely isolated

## 🔧 Troubleshooting

### Common Issues

1. **Script must be run from test folder**
   ```powershell
   cd test
   .\start_env.ps1
   ```

2. **Services won't start**
   ```powershell
   .\start_env.ps1 -StopOnly
   .\start_env.ps1
   ```

3. **Port conflicts**
   ```powershell
   .\start_env.ps1 -CheckOnly  # See which services are running
   .\start_env.ps1 -StopOnly   # Stop conflicting services
   ```

4. **Dependency issues**
   - The script automatically handles dependency installation
   - Each service will install its own dependencies if missing

### Prerequisites
- Node.js (latest LTS version)
- Yarn package manager
- PowerShell (Windows)
- Run from the `test/` directory

## 📚 Legacy Scripts

The original scripts are still available for compatibility:
- `setup-test-environment-contained.ps1`
- `start-dev-simple.ps1`
- `install-dependencies.ps1`
- `check-services.ps1`
- `run-tests.ps1`

However, the new `start_env.ps1` provides a more comprehensive and user-friendly experience.

## 💡 Tips

- **First time**: Just run `.\start_env.ps1` - it handles everything
- **Daily use**: Use `.\start_env.ps1 -CheckOnly` to verify services
- **End of day**: Use `.\start_env.ps1 -StopOnly` to clean up
- **Need help**: Use `.\start_env.ps1 -Help` for detailed information

---

**🎯 Goal**: Provide a seamless development environment setup while keeping all test files organized and separate from the main source code.
