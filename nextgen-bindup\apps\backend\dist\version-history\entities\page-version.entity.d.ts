import { Component } from '@nextgen-bindup/common/dto/component';
import { PageStatus, PageType } from 'src/page/types/page.type';
import { DatasourcePropDto } from '@nextgen-bindup/common/dto/setting-properties/datasource-prop.dto';
export declare class PageVersionEntity {
    id: number;
    siteVersionId: number;
    pageId: number;
    type: PageType;
    parentId: number;
    projectId: number;
    siteId: number;
    datasource: DatasourcePropDto;
    name: string;
    components: Record<string, Component>;
    ts: number;
    status: PageStatus;
    url: string;
    title: string;
    description: string;
    isSearch: boolean;
    thumb: string;
    headCode: string;
    bodyCode: string;
    isPrivate: boolean;
    isHome: boolean;
    children: number[];
    createdAt: Date;
    updatedAt: Date;
    versionCreatedAt: Date;
    userId: string;
}
