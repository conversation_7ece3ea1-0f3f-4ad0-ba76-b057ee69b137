import { Body, Controller, Post, Put, UseGuards } from '@nestjs/common';
import { UserManagementService } from './user-management.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { InviteMemberReq } from './dto/invite-member.dto';
import { ExtractUser } from 'src/auth/user.decorator';
import { JwtPayloadDto } from 'src/auth/dto/auth.dto';
import { UpdateMemberOfTeamReq } from './dto/update-member-of-team.dto';

@Controller('user-management')
@UseGuards(AuthGuard)
export class UserManagementController {
  constructor(private readonly userManagementService: UserManagementService) {}

  @Post('invite-member')
  async inviteMember(
    @ExtractUser() user: JwtPayloadDto,
    @Body() input: InviteMemberReq,
  ) {
    await this.userManagementService.inviteMemberToTeam(user.userId, input);
    return true;
  }

  @Put('update-member-of-team')
  async updateMemberOfTeam(
    @ExtractUser() user: JwtPayloadDto,
    @Body() input: UpdateMemberOfTeamReq,
  ) {
    await this.userManagementService.updateMemberOfTeam(user.userId, input);
    return true;
  }
}
