import { Repository } from 'typeorm';
import { OrderCompletionSettingEntity } from './entities/order-complete-settings.entity';
import { SiteService } from 'src/site/site.service';
export declare class OrderCompletionSettingService {
    private readonly siteService;
    readonly orderCompletionSettingRepo: Repository<OrderCompletionSettingEntity>;
    constructor(siteService: SiteService);
    create(orderCompletionSettingEntity: OrderCompletionSettingEntity): Promise<OrderCompletionSettingEntity>;
    update(id: number, settingData: Partial<OrderCompletionSettingEntity>): Promise<OrderCompletionSettingEntity>;
    findById(id: number): Promise<OrderCompletionSettingEntity>;
    findOneBySiteId(siteId: number): Promise<OrderCompletionSettingEntity>;
    delete(id: number): Promise<boolean>;
}
