{"version": 3, "sources": ["../../../../../node_modules/@mui/material/DialogActions/DialogActions.js", "../../../../../node_modules/@mui/material/DialogActions/dialogActionsClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getDialogActionsUtilityClass } from \"./dialogActionsClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return composeClasses(slots, getDialogActionsUtilityClass, classes);\n};\nconst DialogActionsRoot = styled('div', {\n  name: 'MuiDialogActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8,\n  justifyContent: 'flex-end',\n  flex: '0 0 auto',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disableSpacing,\n    style: {\n      '& > :not(style) ~ :not(style)': {\n        marginLeft: 8\n      }\n    }\n  }]\n});\nconst DialogActions = /*#__PURE__*/React.forwardRef(function DialogActions(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogActions'\n  });\n  const {\n    className,\n    disableSpacing = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    disableSpacing\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DialogActionsRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogActions;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDialogActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogActions', slot);\n}\nconst dialogActionsClasses = generateUtilityClasses('MuiDialogActions', ['root', 'spacing']);\nexport default dialogActionsClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,6BAA6B,MAAM;AACjD,SAAO,qBAAqB,oBAAoB,IAAI;AACtD;AACA,IAAM,uBAAuB,uBAAuB,oBAAoB,CAAC,QAAQ,SAAS,CAAC;AAC3F,IAAO,+BAAQ;;;ADGf,yBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,CAAC,kBAAkB,SAAS;AAAA,EAC7C;AACA,SAAO,eAAe,OAAO,8BAA8B,OAAO;AACpE;AACA,IAAM,oBAAoB,eAAO,OAAO;AAAA,EACtC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,CAAC,WAAW,kBAAkB,OAAO,OAAO;AAAA,EACnE;AACF,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,UAAU,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,CAAC,WAAW;AAAA,IAClB,OAAO;AAAA,MACL,iCAAiC;AAAA,QAC/B,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,gBAAmC,iBAAW,SAASA,eAAc,SAAS,KAAK;AACvF,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,iBAAiB;AAAA,IACjB,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,mBAAmB;AAAA,IAC1C,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH,CAAC;AACD,OAAwC,cAAc,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQvF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI1B,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,wBAAQ;", "names": ["DialogActions", "_jsx", "PropTypes"]}