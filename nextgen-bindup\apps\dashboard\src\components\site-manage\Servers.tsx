import { type FC } from 'react';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';

const Servers: FC = () => (
  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
    {[...Array(10)].map((_, i) => (
      <TextField
        key={`sample-${i}`}
        label="○○○○○○○"
        variant="filled"
        fullWidth
      />
    ))}
  </Box>
);

export default Servers;
