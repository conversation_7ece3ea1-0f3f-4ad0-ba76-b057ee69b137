import dayjs from 'dayjs';
import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionActions,
  AccordionDetails,
  AccordionSummary,
  Button,
  Divider,
  FormControl,
  FormControlLabel,
  FormLabel,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { getErrMsg } from '@nextgen-bindup/common/utility';
import {
  CountryCode,
  Industry,
  JobType,
  Occupation,
  Prefecture,
  PREFECTURE_LIST,
  Sex,
  UserInfoEntity,
  UserType,
} from '../../../dto/user-info.type';
import { UserDto } from '../../../dto/user.dto';
import { userInfoService } from '../../../services/user-info.service';

export const PersonalInformation: FC<{
  user: UserDto;
  userInfoData: UserInfoEntity;
  onUpdate: () => void;
}> = ({ user, userInfoData, onUpdate }) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errorMsg, setErrorMsg] = useState<string>('');
  const [errorField, setErrorField] = useState<
    | ''
    | 'company'
    | 'lastNameHira'
    | 'firstNameHira'
    | 'sex'
    | 'countryCode'
    | 'postalCode'
    | 'prefecture'
    | 'municipalities'
    | 'street'
    | 'building'
    | 'phone'
    | 'fax'
    | 'occupation'
    | 'industry'
    | 'jobType'
    | 'birthday'
  >('');
  const [userInfo, setUserInfo] = useState<UserInfoEntity>(userInfoData);
  const [prefectures, setPrefectures] = useState<Prefecture[]>(
    PREFECTURE_LIST[userInfo?.countryCode || 1],
  );

  useEffect(() => {
    setPrefectures(PREFECTURE_LIST[userInfo?.countryCode || 1]);
  }, [userInfo?.countryCode]);

  const onSave = async () => {
    setIsLoading(true);
    setErrorMsg('');
    setErrorField('');

    try {
      if (!validate()) return;

      await userInfoService.update({
        userId: userInfo.userId,
        type: userInfo.type,
        company: userInfo.company,
        lastNameHira: userInfo.lastNameHira,
        firstNameHira: userInfo.firstNameHira,
        sex: userInfo.sex,
        countryCode: userInfo.countryCode,
        postalCode: userInfo.postalCode,
        prefecture: userInfo.prefecture,
        municipalities: userInfo.municipalities,
        street: userInfo.street,
        building: userInfo.building,
        phone: userInfo.phone,
        fax: userInfo.fax,
        occupation: userInfo.occupation,
        industry: userInfo.industry,
        jobType: userInfo.jobType,
        birthday: userInfo.birthday,
      });

      onUpdate();
    } catch (e) {
      setErrorMsg(t(getErrMsg(e)));
    } finally {
      setIsLoading(false);
    }
  };

  const validate = () => {
    if (userInfo.company && userInfo.company.length > 250) {
      setErrorField('company');
      setErrorMsg('myaccount.personal_info.error.company_length');
      return false;
    }

    // lastNameHira
    if (!userInfo.lastNameHira) {
      setErrorField('lastNameHira');
      setErrorMsg('myaccount.personal_info.error.last_name_hira_required');
      return false;
    }

    if (userInfo.lastNameHira.length > 250) {
      setErrorField('lastNameHira');
      setErrorMsg('myaccount.personal_info.error.last_name_hira_length');
      return false;
    }

    // firstNameHira
    if (!userInfo.firstNameHira) {
      setErrorField('firstNameHira');
      setErrorMsg('myaccount.personal_info.error.first_name_hira_required');
      return false;
    }

    if (userInfo.firstNameHira.length > 250) {
      setErrorField('firstNameHira');
      setErrorMsg('myaccount.personal_info.error.first_name_hira_length');
      return false;
    }

    // sex
    if (!userInfo.sex) {
      setErrorField('sex');
      setErrorMsg('myaccount.personal_info.error.sex_required');
      return false;
    }

    // countryCode
    if (!userInfo.countryCode) {
      setErrorField('countryCode');
      setErrorMsg('myaccount.personal_info.error.country_required');
      return false;
    }

    // postalCode
    if (!userInfo.postalCode) {
      setErrorField('postalCode');
      setErrorMsg('myaccount.personal_info.error.postal_code_required');
      return false;
    }

    if (userInfo.postalCode.length > 10) {
      setErrorField('postalCode');
      setErrorMsg('myaccount.personal_info.error.postalCode_length');
      return false;
    }

    // prefecture
    if (!userInfo.prefecture) {
      setErrorField('prefecture');
      setErrorMsg('myaccount.personal_info.error.prefecture_required');
      return false;
    }

    // municipalities
    if (!userInfo.municipalities) {
      setErrorField('municipalities');
      setErrorMsg('myaccount.personal_info.error.municipalities_required');
      return false;
    }

    if (userInfo.municipalities.length > 250) {
      setErrorField('municipalities');
      setErrorMsg('myaccount.personal_info.error.municipalities_length');
      return false;
    }

    // street
    if (!userInfo.street) {
      setErrorField('street');
      setErrorMsg('myaccount.personal_info.error.street_required');
      return false;
    }

    if (userInfo.street.length > 250) {
      setErrorField('street');
      setErrorMsg('myaccount.personal_info.error.street_length');
      return false;
    }

    // building
    if (userInfo.building && userInfo.building.length > 250) {
      setErrorField('building');
      setErrorMsg('myaccount.personal_info.error.building_length');
      return false;
    }

    // phone
    if (!userInfo.phone) {
      setErrorField('phone');
      setErrorMsg('myaccount.personal_info.error.phone_required');
      return false;
    }

    if (userInfo.phone.length > 50) {
      setErrorField('phone');
      setErrorMsg('myaccount.personal_info.error.phone_length');
      return false;
    }

    // fax
    if (userInfo.fax && userInfo.fax.length > 50) {
      setErrorField('fax');
      setErrorMsg('myaccount.personal_info.error.fax_length');
      return false;
    }

    // occupation
    if (!userInfo.occupation) {
      setErrorField('occupation');
      setErrorMsg('myaccount.personal_info.error.occupation_required');
      return false;
    }

    // industry
    if (!userInfo.industry) {
      setErrorField('industry');
      setErrorMsg('myaccount.personal_info.error.industry_required');
      return false;
    }

    // jobType
    if (!userInfo.jobType) {
      setErrorField('jobType');
      setErrorMsg('myaccount.personal_info.error.job_type_required');
      return false;
    }

    // birthday
    if (!userInfo.birthday) {
      setErrorField('birthday');
      setErrorMsg('myaccount.personal_info.error.birthday_required');
      return false;
    }

    return true;
  };

  return (
    <Accordion defaultExpanded sx={{ width: '100%' }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="h5" color="info">
          {t('myaccount.personal_info.title')}
        </Typography>
      </AccordionSummary>

      <AccordionDetails>
        <Stack spacing={3}>
          {/* Registration Type */}
          <FormControl size="small">
            <FormLabel id="type">
              {t('myaccount.personal_info.type.label')}
            </FormLabel>
            <RadioGroup
              row
              name="type"
              value={userInfo.type.toString()}
              onChange={e => {
                const type: UserType = parseInt(e.target.value);
                setUserInfo({ ...userInfo, ...{ type: type } });
              }}
            >
              <FormControlLabel
                value="1"
                control={<Radio size="small" sx={{ padding: ' 1px 9px' }} />}
                label={t('myaccount.personal_info.type.individual')}
              />
              <FormControlLabel
                value="2"
                control={<Radio size="small" sx={{ padding: ' 1px 9px' }} />}
                label={t('myaccount.personal_info.type.corporation')}
              />
            </RadioGroup>
          </FormControl>

          {/* Company/School Name */}
          <TextField
            label={t('myaccount.personal_info.company')}
            variant="outlined"
            size="small"
            error={errorField === 'company'}
            value={userInfo.company || ''}
            onChange={e => {
              setUserInfo({
                ...userInfo,
                ...{ company: e.target.value },
              });
            }}
          />

          {/* Fullname */}
          <TextField
            label={t('myaccount.personal_info.name')}
            variant="outlined"
            size="small"
            value={user.name || ''}
            disabled
          />

          {/* Name (in hiragana) */}
          <Stack spacing={3} direction="row">
            <TextField
              label={t('myaccount.personal_info.lastname_hira')}
              variant="outlined"
              size="small"
              required
              error={errorField === 'lastNameHira'}
              value={userInfo.lastNameHira || ''}
              onChange={e => {
                setUserInfo({
                  ...userInfo,
                  ...{ lastNameHira: e.target.value },
                });
              }}
            />
            <TextField
              label={t('myaccount.personal_info.firstname_hira')}
              variant="outlined"
              size="small"
              required
              error={errorField === 'firstNameHira'}
              value={userInfo.firstNameHira || ''}
              onChange={e => {
                setUserInfo({
                  ...userInfo,
                  ...{ firstNameHira: e.target.value },
                });
              }}
            />
          </Stack>

          {/* Sex */}
          <FormControl size="small" required error={errorField === 'sex'}>
            <FormLabel id="sex">
              {t('myaccount.personal_info.sex.label')}
            </FormLabel>
            <RadioGroup
              row
              name="sex"
              value={(userInfo.sex || '').toString()}
              onChange={e => {
                const sex: Sex = parseInt(e.target.value);
                setUserInfo({ ...userInfo, ...{ sex: sex } });
              }}
            >
              <FormControlLabel
                value="1"
                control={<Radio size="small" sx={{ padding: ' 1px 9px' }} />}
                label={t('myaccount.personal_info.sex.male')}
              />
              <FormControlLabel
                value="2"
                control={<Radio size="small" sx={{ padding: ' 1px 9px' }} />}
                label={t('myaccount.personal_info.sex.female')}
              />
              <FormControlLabel
                value="3"
                control={<Radio size="small" sx={{ padding: ' 1px 9px' }} />}
                label={t('myaccount.personal_info.sex.other')}
              />
            </RadioGroup>
          </FormControl>

          {/* Address */}
          <Typography variant="h6" sx={{ padding: 0 }}>
            {t('myaccount.personal_info.address')}
          </Typography>
          <Divider sx={{ marginTop: '1px !important' }} />
          <Stack
            spacing={3}
            direction="row"
            flexWrap={'wrap'}
            sx={{ justifyContent: 'flex-start' }}
          >
            {/* Country */}
            <FormControl
              size="small"
              required
              error={errorField === 'countryCode'}
            >
              <InputLabel id="countryCode">
                {t('myaccount.personal_info.country')}
              </InputLabel>
              <Select
                sx={{ minWidth: '200px' }}
                labelId="countryCode"
                autoWidth
                value={(userInfo.countryCode || CountryCode.JAPAN).toString()}
                label={t('myaccount.personal_info.country')}
                onChange={e => {
                  const countryCode: CountryCode = parseInt(e.target.value);
                  setUserInfo({
                    ...userInfo,
                    ...{ countryCode: countryCode },
                  });
                }}
              >
                <MenuItem value={`${CountryCode.JAPAN}`}>
                  {t(`country.v${CountryCode.JAPAN}`)}
                </MenuItem>
              </Select>
            </FormControl>

            {/* Postcode */}
            <TextField
              label={t('myaccount.personal_info.postcode')}
              variant="outlined"
              size="small"
              required
              error={errorField === 'postalCode'}
              value={userInfo.postalCode || ''}
              onChange={e => {
                setUserInfo({
                  ...userInfo,
                  ...{ postalCode: e.target.value },
                });
              }}
            />

            {/* Prefecture */}
            <FormControl
              size="small"
              required
              error={errorField === 'prefecture'}
            >
              <InputLabel id="prefecture">
                {t('myaccount.personal_info.prefecture')}
              </InputLabel>
              <Select
                sx={{ minWidth: '200px' }}
                labelId="prefecture"
                autoWidth
                value={(userInfo.prefecture || 0).toString()}
                label={t('myaccount.personal_info.prefecture')}
                onChange={e => {
                  const prefecture: number = parseInt(e.target.value);
                  setUserInfo({
                    ...userInfo,
                    ...{ prefecture: prefecture },
                  });
                }}
              >
                <MenuItem value={'0'}>‎</MenuItem>
                {prefectures.map(item => (
                  <MenuItem
                    value={item.value.toString()}
                    key={item.value.toString()}
                  >
                    {t(
                      `prefecture.country${userInfo.countryCode || CountryCode.JAPAN}.v${item.value}`,
                    )}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Municipalities */}
            <TextField
              label={t('myaccount.personal_info.municipalities')}
              variant="outlined"
              size="small"
              required
              error={errorField === 'municipalities'}
              value={userInfo.municipalities || ''}
              onChange={e => {
                setUserInfo({
                  ...userInfo,
                  ...{ municipalities: e.target.value },
                });
              }}
            />
          </Stack>

          {/* street */}
          <TextField
            label={t('myaccount.personal_info.street')}
            variant="outlined"
            size="small"
            required
            error={errorField === 'street'}
            value={userInfo.street || ''}
            onChange={e => {
              setUserInfo({
                ...userInfo,
                ...{ street: e.target.value },
              });
            }}
          />

          {/* building */}
          <TextField
            label={t('myaccount.personal_info.building')}
            variant="outlined"
            size="small"
            error={errorField === 'building'}
            value={userInfo.building || ''}
            onChange={e => {
              setUserInfo({
                ...userInfo,
                ...{ building: e.target.value },
              });
            }}
          />

          <Divider />
          {/* phone */}
          <TextField
            label={t('myaccount.personal_info.phone')}
            variant="outlined"
            size="small"
            required
            error={errorField === 'phone'}
            value={userInfo.phone || ''}
            onChange={e => {
              setUserInfo({
                ...userInfo,
                ...{ phone: e.target.value },
              });
            }}
          />

          {/* fax */}
          <TextField
            label={t('myaccount.personal_info.fax')}
            variant="outlined"
            size="small"
            error={errorField === 'fax'}
            value={userInfo.fax || ''}
            onChange={e => {
              setUserInfo({
                ...userInfo,
                ...{ fax: e.target.value },
              });
            }}
          />

          {/* occupation */}
          <FormControl
            size="small"
            required
            error={errorField === 'occupation'}
          >
            <InputLabel id="occupation">
              {t('myaccount.personal_info.occupation.label')}
            </InputLabel>
            <Select
              labelId="occupation"
              value={(userInfo.occupation || 0).toString()}
              label={t('myaccount.personal_info.occupation.label')}
              onChange={e => {
                const occupation: Occupation = parseInt(e.target.value);
                setUserInfo({
                  ...userInfo,
                  ...{ occupation: occupation },
                });
              }}
            >
              <MenuItem value={'0'}>‎</MenuItem>
              <MenuItem value={`${Occupation.COMPANY_EMPLOYEE}`}>
                {t(
                  `myaccount.personal_info.occupation.v${Occupation.COMPANY_EMPLOYEE}`,
                )}
              </MenuItem>
              <MenuItem value={`${Occupation.CIVIL_SERVANT}`}>
                {t(
                  `myaccount.personal_info.occupation.v${Occupation.CIVIL_SERVANT}`,
                )}
              </MenuItem>
              <MenuItem value={`${Occupation.PROFESSOR_LECTURER}`}>
                {t(
                  `myaccount.personal_info.occupation.v${Occupation.PROFESSOR_LECTURER}`,
                )}
              </MenuItem>
              <MenuItem value={`${Occupation.STUDENT}`}>
                {t(`myaccount.personal_info.occupation.v${Occupation.STUDENT}`)}
              </MenuItem>
              <MenuItem value={`${Occupation.FULLTIME_HOUSEWIFE}`}>
                {t(
                  `myaccount.personal_info.occupation.v${Occupation.FULLTIME_HOUSEWIFE}`,
                )}
              </MenuItem>
              <MenuItem value={`${Occupation.SELF_EMPLOYED}`}>
                {t(
                  `myaccount.personal_info.occupation.v${Occupation.SELF_EMPLOYED}`,
                )}
              </MenuItem>
              <MenuItem value={`${Occupation.UNEMPLOYED}`}>
                {t(
                  `myaccount.personal_info.occupation.v${Occupation.UNEMPLOYED}`,
                )}
              </MenuItem>
              <MenuItem value={`${Occupation.OTHER}`}>
                {t(`myaccount.personal_info.occupation.v${Occupation.OTHER}`)}
              </MenuItem>
              <MenuItem value={`${Occupation.DESIGNER}`}>
                {t(
                  `myaccount.personal_info.occupation.v${Occupation.DESIGNER}`,
                )}
              </MenuItem>
            </Select>
          </FormControl>

          {/* industry */}
          <FormControl size="small" required error={errorField === 'industry'}>
            <InputLabel id="industry">
              {t('myaccount.personal_info.industry.label')}
            </InputLabel>
            <Select
              labelId="industry"
              value={(userInfo.industry || 0).toString()}
              label={t('myaccount.personal_info.industry.label')}
              onChange={e => {
                const industry: Industry = parseInt(e.target.value);
                setUserInfo({
                  ...userInfo,
                  ...{ industry: industry },
                });
              }}
            >
              <MenuItem value={'0'}>‎</MenuItem>
              <MenuItem value={`${Industry.DESIGN_INDUSTRY}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.DESIGN_INDUSTRY}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.CREATIVE_INDUSTRY}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.CREATIVE_INDUSTRY}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.MANUFACTURING}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.MANUFACTURING}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.RETAIL}`}>
                {t(`myaccount.personal_info.industry.v${Industry.RETAIL}`)}
              </MenuItem>
              <MenuItem value={`${Industry.CONSTRUCTION}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.CONSTRUCTION}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.INFORMATION_AND_COMMUNICATIONS}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.INFORMATION_AND_COMMUNICATIONS}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.FOOD_AND_BEVERAGE_INDUSTRY}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.FOOD_AND_BEVERAGE_INDUSTRY}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.LEISURE_AND_TOURISM}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.LEISURE_AND_TOURISM}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.FINANCE_AND_REAL_ESTATE}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.FINANCE_AND_REAL_ESTATE}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.PROFESSIONAL}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.PROFESSIONAL}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.EDUCATION}`}>
                {t(`myaccount.personal_info.industry.v${Industry.EDUCATION}`)}
              </MenuItem>
              <MenuItem value={`${Industry.MEDICAL_AND_WELFARE}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.MEDICAL_AND_WELFARE}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.BEAUTY}`}>
                {t(`myaccount.personal_info.industry.v${Industry.BEAUTY}`)}
              </MenuItem>
              <MenuItem value={`${Industry.AGRICULTURE_FORESTRY_AND_FISHING}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.AGRICULTURE_FORESTRY_AND_FISHING}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.CIVIL_SERVANT}`}>
                {t(
                  `myaccount.personal_info.industry.v${Industry.CIVIL_SERVANT}`,
                )}
              </MenuItem>
              <MenuItem value={`${Industry.OTHER}`}>
                {t(`myaccount.personal_info.industry.v${Industry.OTHER}`)}
              </MenuItem>
            </Select>
          </FormControl>

          {/* jobType */}
          <FormControl size="small" required error={errorField === 'jobType'}>
            <InputLabel id="jobType">
              {t('myaccount.personal_info.job_type.label')}
            </InputLabel>
            <Select
              labelId="jobType"
              value={(userInfo.jobType || 0).toString()}
              label={t('myaccount.personal_info.job_type.label')}
              onChange={e => {
                const jobType: JobType = parseInt(e.target.value);
                setUserInfo({
                  ...userInfo,
                  ...{ jobType: jobType },
                });
              }}
            >
              <MenuItem value={'0'}>‎</MenuItem>
              <MenuItem value={`${JobType.EXECUTIVE}`}>
                {t(`myaccount.personal_info.job_type.v${JobType.EXECUTIVE}`)}
              </MenuItem>
              <MenuItem value={`${JobType.COMPANY_OFFICER}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.COMPANY_OFFICER}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.SELF_EMPLOYED}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.SELF_EMPLOYED}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.WEB_MANAGER_APPROVAL_PERSON}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.WEB_MANAGER_APPROVAL_PERSON}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.WEB_MANAGER_NONDECISION_MAKER}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.WEB_MANAGER_NONDECISION_MAKER}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.SALES}`}>
                {t(`myaccount.personal_info.job_type.v${JobType.SALES}`)}
              </MenuItem>
              <MenuItem value={`${JobType.PUBLIC_RELATIONS_AND_PROMOTION}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.PUBLIC_RELATIONS_AND_PROMOTION}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.ADMINISTRATION}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.ADMINISTRATION}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.MARKETING}`}>
                {t(`myaccount.personal_info.job_type.v${JobType.MARKETING}`)}
              </MenuItem>
              <MenuItem value={`${JobType.TEACHING_PROFESSION}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.TEACHING_PROFESSION}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.PROFESSIONAL_WEB_DESIGNER}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.PROFESSIONAL_WEB_DESIGNER}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.PROFESSIONAL_DESIGNER}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.PROFESSIONAL_DESIGNER}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.PROFESSIONAL_GRAPHIC_DESIGNER}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.PROFESSIONAL_GRAPHIC_DESIGNER}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.PROFESSIONAL_ILLUSTRATOR}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.PROFESSIONAL_ILLUSTRATOR}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.PROFESSIONAL_PHOTOGRAPHER}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.PROFESSIONAL_PHOTOGRAPHER}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.PROFESSIONAL_OCCUPATION_MUSICIAN}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.PROFESSIONAL_OCCUPATION_MUSICIAN}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.PROFESSIONAL_ENGINEER}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.PROFESSIONAL_ENGINEER}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.PROFESSIONAL_OTHER}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.PROFESSIONAL_OTHER}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.HOUSEWIFE}`}>
                {t(`myaccount.personal_info.job_type.v${JobType.HOUSEWIFE}`)}
              </MenuItem>
              <MenuItem value={`${JobType.STUDENT}`}>
                {t(`myaccount.personal_info.job_type.v${JobType.STUDENT}`)}
              </MenuItem>
              <MenuItem value={`${JobType.PARTTIME_WORKER}`}>
                {t(
                  `myaccount.personal_info.job_type.v${JobType.PARTTIME_WORKER}`,
                )}
              </MenuItem>
              <MenuItem value={`${JobType.UNEMPLOYED}`}>
                {t(`myaccount.personal_info.job_type.v${JobType.UNEMPLOYED}`)}
              </MenuItem>
              <MenuItem value={`${JobType.OTHER}`}>
                {t(`myaccount.personal_info.job_type.v${JobType.OTHER}`)}
              </MenuItem>
            </Select>
          </FormControl>

          {/* birthday */}
          <div className="date-input-small">
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                value={dayjs(userInfo.birthday)}
                label={t('myaccount.personal_info.birthday')}
                format="YYYY-MM-DD"
                onChange={e => {
                  setUserInfo({
                    ...userInfo,
                    ...{ birthday: e?.toDate() || undefined },
                  });
                }}
              />
            </LocalizationProvider>
          </div>
        </Stack>
      </AccordionDetails>

      <AccordionActions sx={{ marginBottom: '0.5rem' }}>
        <Typography variant="body1" color="error">
          {t(errorMsg)}
        </Typography>

        <Button
          size="large"
          color="info"
          variant="contained"
          onClick={onSave}
          loading={isLoading}
          loadingPosition="start"
        >
          {t('common.btn_save')}
        </Button>
      </AccordionActions>
    </Accordion>
  );
};
