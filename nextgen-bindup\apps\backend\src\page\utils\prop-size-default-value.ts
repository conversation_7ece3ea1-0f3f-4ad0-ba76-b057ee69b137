import { SizePropDto } from '@nextgen-bindup/common/dto/setting-properties/size-prop.dto';

export const PROP_SIZE_DEFAULT_VALUE = (
  ts: number,
  prop?: Partial<SizePropDto>,
): SizePropDto => ({
  ts: ts,
  width: {
    value: '',
    unit: 'auto',
  },
  height: {
    value: '',
    unit: 'auto',
  },
  minWidth: {
    value: '',
    unit: 'auto',
  },
  maxWidth: {
    value: '',
    unit: 'auto',
  },
  minHeight: {
    value: '',
    unit: 'auto',
  },
  maxHeight: {
    value: '',
    unit: 'auto',
  },
  overflow: 'unset',
  ...(prop || undefined),
});

export const PROP_PAGE_SIZE_DEFAULT_VALUE = (
  ts: number,
  prop?: Partial<SizePropDto>,
): SizePropDto => ({
  width: {
    value: '100',
    unit: '%',
  },
  height: {
    value: '',
    unit: 'auto',
  },
  minWidth: {
    value: '',
    unit: 'auto',
  },
  maxWidth: {
    value: '',
    unit: 'auto',
  },
  minHeight: {
    value: '100',
    unit: 'dvh',
  },
  maxHeight: {
    value: '',
    unit: 'auto',
  },
  overflow: 'unset',
  ts: ts,
  ...(prop || undefined),
});
