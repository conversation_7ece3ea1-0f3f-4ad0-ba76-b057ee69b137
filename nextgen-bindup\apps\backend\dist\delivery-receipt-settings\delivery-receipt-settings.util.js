"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDefaultDeliveryReceiptSettings = void 0;
const getDefaultDeliveryReceiptSettings = (shopName, email) => {
    const defaultSettings = {
        header: `
このたびは${shopName}・オンラインストアをご利用いただき、まことにありがとうございます。
なにかございましたらお気軽に${email}までご意見やご要望をお寄せください。
またのご利用を心からお待ちしております。
以下のとおり納品申し上げます。
      `,
        footer: `
${shopName}
〒000-0000 東京都○○区○○1-1-1
MAIL：${email}   
      `,
    };
    return defaultSettings;
};
exports.getDefaultDeliveryReceiptSettings = getDefaultDeliveryReceiptSettings;
//# sourceMappingURL=delivery-receipt-settings.util.js.map