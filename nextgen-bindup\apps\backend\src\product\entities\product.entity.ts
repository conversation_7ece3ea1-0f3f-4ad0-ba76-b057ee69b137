import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ProductType, ProductVariantType } from '../enum/product.enum';
import { ProductVariant } from '../dto/product-variant.dto';

@Entity(`products`, {
  schema: process.env.DATABASE_SCHEMA,
})
export class ProductEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @Column({
    name: 'isOrderable',
    type: 'boolean',
    nullable: false,
  })
  isOrderable: boolean;

  @Column({
    name: 'code',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string;

  @Column({
    name: 'title',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  title?: string;

  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
  })
  description?: string;

  @Column({
    name: 'images',
    type: 'text',
    nullable: true,
    array: true,
  })
  images: string[];

  @Column({
    name: 'priceLabel',
    type: 'varchar',
    length: '250',
    nullable: true,
  })
  priceLabel: string;

  @Column({
    name: 'saleLabel',
    type: 'varchar',
    length: '250',
    nullable: true,
  })
  saleLabel: string;

  @Column({
    name: 'price',
    type: 'bigint',
    transformer: {
      to: (value: number | string) => value,
      from: (value: string) => Number(value),
    },
    nullable: false,
  })
  price: number;

  @Column({
    name: 'sale',
    type: 'bigint',
    transformer: {
      to: (value: number | string) => value,
      from: (value: string) => Number(value),
    },
    nullable: false,
  })
  sale: number;

  @Column({
    name: 'purchaseLimitQuantity',
    type: 'integer',
    nullable: false,
  })
  purchaseLimitQuantity: number;

  @Column({
    name: 'individualShippingCharges',
    type: 'bigint',
    transformer: {
      to: (value: number | string) => value,
      from: (value: string) => Number(value),
    },
    nullable: false,
  })
  individualShippingCharges: number;

  @Column({
    name: 'fileDownload',
    type: 'jsonb',
    nullable: true,
  })
  fileDownload?: FileDownload;

  @Column({
    name: 'unlimitedPurchase',
    type: 'boolean',
    nullable: false,
  })
  unlimitedPurchase: boolean;

  @Column({
    name: 'productType',
    type: 'varchar',
    length: 10,
    nullable: false,
  })
  productType: ProductType;

  @Column({
    name: 'productVariantType',
    type: 'varchar',
    length: 20,
    nullable: false,
  })
  productVariantType: ProductVariantType;

  @Column({
    name: 'variants',
    type: 'jsonb',
  })
  variants: ProductVariant;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;

  @Column({
    name: 'isDeleted',
    type: 'boolean',
    nullable: true,
  })
  isDeleted: boolean;
}
export interface FileDownload {
  name: string;
  url: string;
  size: number;
  type: string;
}
