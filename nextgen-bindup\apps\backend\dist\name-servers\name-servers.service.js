"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NameServersService = void 0;
const common_1 = require("@nestjs/common");
const name_server_entity_1 = require("./entities/name-server.entity");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
let NameServersService = class NameServersService {
    async create(data) {
        delete data.id;
        const now = new Date();
        data.createdAt = now;
        data.updatedAt = now;
        return await this.nameServerRepo.save(data);
    }
    async update(id, data) {
        const entity = await this.nameServerRepo.findOneBy({ id });
        if (!entity)
            throw new app_exception_1.AppException('api.error.nameserver_not_found');
        const now = new Date();
        data.updatedAt = now;
        delete data.id;
        await this.nameServerRepo.update(id, data);
        return { ...entity, ...data };
    }
    async delete(id) {
        const entity = await this.nameServerRepo.findOneBy({ id });
        if (!entity)
            throw new app_exception_1.AppException('api.error.nameserver_not_found');
        await this.nameServerRepo.delete(id);
        return true;
    }
    async findBySiteId(siteId) {
        return await this.nameServerRepo.findBy({ siteId });
    }
    async findByProjectId(projectId) {
        return await this.nameServerRepo.findBy({ projectId });
    }
};
exports.NameServersService = NameServersService;
__decorate([
    (0, typeorm_2.InjectRepository)(name_server_entity_1.NameServerEntity),
    __metadata("design:type", typeorm_1.Repository)
], NameServersService.prototype, "nameServerRepo", void 0);
exports.NameServersService = NameServersService = __decorate([
    (0, common_1.Injectable)()
], NameServersService);
//# sourceMappingURL=name-servers.service.js.map