import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateUserTeam1740982117025 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}user_team`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'rootUserId',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'email',
            type: 'varchar',
            length: '500',
            isNullable: false,
          },
          {
            name: 'teamId',
            type: 'integer',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'IDX_user_team_unique',
        columnNames: ['rootUserId', 'email', 'teamId'],
        isUnique: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_user_team_unique');

    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
