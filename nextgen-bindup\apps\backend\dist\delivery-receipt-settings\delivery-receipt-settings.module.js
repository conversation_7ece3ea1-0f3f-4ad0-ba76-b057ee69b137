"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliveryReceiptSettingModule = void 0;
const common_1 = require("@nestjs/common");
const delivery_receipt_settings_controller_1 = require("./delivery-receipt-settings.controller");
const delivery_receipt_settings_service_1 = require("./delivery-receipt-settings.service");
const delivery_receipt_settings_entity_1 = require("./entities/delivery-receipt-settings.entity");
const typeorm_1 = require("@nestjs/typeorm");
let DeliveryReceiptSettingModule = class DeliveryReceiptSettingModule {
};
exports.DeliveryReceiptSettingModule = DeliveryReceiptSettingModule;
exports.DeliveryReceiptSettingModule = DeliveryReceiptSettingModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([delivery_receipt_settings_entity_1.DeliveryReceiptSettingEntity])],
        controllers: [delivery_receipt_settings_controller_1.DeliveryReceiptSettingsController],
        providers: [delivery_receipt_settings_service_1.DeliveryReceiptSettingsService],
        exports: [delivery_receipt_settings_service_1.DeliveryReceiptSettingsService],
    })
], DeliveryReceiptSettingModule);
//# sourceMappingURL=delivery-receipt-settings.module.js.map