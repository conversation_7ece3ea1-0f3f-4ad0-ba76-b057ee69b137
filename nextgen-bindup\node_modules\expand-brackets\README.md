# expand-brackets [![NPM version](https://img.shields.io/npm/v/expand-brackets.svg?style=flat)](https://www.npmjs.com/package/expand-brackets) [![NPM downloads](https://img.shields.io/npm/dm/expand-brackets.svg?style=flat)](https://npmjs.org/package/expand-brackets) [![Build Status](https://img.shields.io/travis/jonschlinkert/expand-brackets.svg?style=flat)](https://travis-ci.org/jonschlinkert/expand-brackets)

> Expand POSIX bracket expressions (character classes) in glob patterns.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install expand-brackets --save
```

## Usage

```js
var brackets = require('expand-brackets');

brackets('[![:lower:]]');
//=> '[^a-z]'
```

## .isMatch

Return true if the given string matches the bracket expression:

```js
brackets.isMatch('A', '[![:lower:]]');
//=> true

brackets.isMatch('a', '[![:lower:]]');
//=> false
```

## .makeRe

Make a regular expression from a bracket expression:

```js
brackets.makeRe('[![:lower:]]');
//=> /[^a-z]/
```

The following named POSIX bracket expressions are supported:

* `[:alnum:]`: Alphanumeric characters (`a-zA-Z0-9]`)
* `[:alpha:]`: Alphabetic characters (`a-zA-Z]`)
* `[:blank:]`: Space and tab (`[ t]`)
* `[:digit:]`: Digits (`[0-9]`)
* `[:lower:]`: Lowercase letters (`[a-z]`)
* `[:punct:]`: Punctuation and symbols. (`[!"#$%&'()*+, -./:;<=>?@ [\]^_``{|}~]`)
* `[:upper:]`: Uppercase letters (`[A-Z]`)
* `[:word:]`: Word characters (letters, numbers and underscores) (`[A-Za-z0-9_]`)
* `[:xdigit:]`: Hexadecimal digits (`[A-Fa-f0-9]`)

Collating sequences are not supported.

## Related projects

You might also be interested in these projects:

* [extglob](https://www.npmjs.com/package/extglob): Convert extended globs to regex-compatible strings. Add (almost) the expressive power of regular expressions to… [more](https://www.npmjs.com/package/extglob) | [homepage](https://github.com/jonschlinkert/extglob)
* [is-extglob](https://www.npmjs.com/package/is-extglob): Returns true if a string has an extglob. | [homepage](https://github.com/jonschlinkert/is-extglob)
* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern.… [more](https://www.npmjs.com/package/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob)
* [is-posix-bracket](https://www.npmjs.com/package/is-posix-bracket): Returns true if the given string is a POSIX bracket expression (POSIX character class). | [homepage](https://github.com/jonschlinkert/is-posix-bracket)
* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just… [more](https://www.npmjs.com/package/micromatch) | [homepage](https://github.com/jonschlinkert/micromatch)

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/expand-brackets/issues/new).

## Building docs

Generate readme and API documentation with [verb](https://github.com/verbose/verb):

```sh
$ npm install verb && npm run docs
```

Or, if [verb](https://github.com/verbose/verb) is installed globally:

```sh
$ verb
```

## Running tests

Install dev dependencies:

```sh
$ npm install -d && npm test
```

## Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

verb © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT license](https://github.com/jonschlinkert/expand-brackets/blob/master/LICENSE).

***

_This file was generated by [verb](https://github.com/verbose/verb), v, on April 01, 2016._