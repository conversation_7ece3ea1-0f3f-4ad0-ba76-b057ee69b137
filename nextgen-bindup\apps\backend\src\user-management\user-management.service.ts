import { Injectable } from '@nestjs/common';
import { IdentityService } from 'src/identity/identity.service';
import { InviteMemberReq } from './dto/invite-member.dto';
import { TeamEntity } from 'src/team/entities/team.entity';
import { AppException } from 'src/common/exceptions/app.exception';
import { UserTeamEntity } from 'src/user-team/entities/user-team.entity';
import { DataSource } from 'typeorm';
import { UserEntity } from 'src/user/entities/user.entity';
import { v4 as uuidv4 } from 'uuid';
import { UserInfoEntity } from 'src/user-info/entities/user-info.entity';
import { CountryCode, UserType } from 'src/user-info/enum/user-info.enum';
import {
  UpdateMemberOfTeamReq,
  UpdateTeamMemberData,
} from './dto/update-member-of-team.dto';
import { InjectDataSource } from '@nestjs/typeorm';

@Injectable()
export class UserManagementService {
  constructor(
    private readonly identityService: IdentityService,
    @InjectDataSource() private dataSource: DataSource,
  ) {}

  async inviteMemberToTeam(
    rootUserId: string,
    input: InviteMemberReq,
  ): Promise<void> {
    const now = new Date();
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Init team
      let team: TeamEntity;
      if (!input.team.id) {
        team = new TeamEntity();
        team.rootUserId = rootUserId;
        team.name = input.team.name;
        team.createdAt = now;
        team.updatedAt = now;
        team = await queryRunner.manager.getRepository(TeamEntity).create(team);
      } else {
        team = await queryRunner.manager
          .getRepository(TeamEntity)
          .findOneBy({ id: input.team.id });

        if (!team) throw new AppException('api.error.team_not_found');
      }

      // Get current user team
      const userTeams: UserTeamEntity[] = await queryRunner.manager
        .getRepository(UserTeamEntity)
        .find({ where: { rootUserId: rootUserId, teamId: team.id } });

      // check data
      for (const email of input.emails) {
        // this email is existed in this team
        if (userTeams.findIndex(item => item.email === email) !== -1) {
          continue;
        }

        const user: UserEntity = await queryRunner.manager
          .getRepository(UserEntity)
          .findOneBy({ email });

        if (user) {
          const userTeam: UserTeamEntity = new UserTeamEntity();
          userTeam.rootUserId = rootUserId;
          userTeam.teamId = team.id;
          userTeam.userId = user.userid;
          userTeam.email = email;
          userTeam.isAdmin = false;
          await queryRunner.manager
            .getRepository(UserTeamEntity)
            .save(userTeam);
        } else {
          // create user sign up
          try {
            const identity = await this.identityService.createIdentity({
              email: email,
              password: uuidv4(),
            });

            const userInfo = await queryRunner.manager
              .getRepository(UserInfoEntity)
              .findOneBy({
                userId: identity.user.userId,
              });

            if (userInfo) {
              await queryRunner.manager.getRepository(UserInfoEntity).update(
                {
                  userId: userInfo.userId,
                },
                {
                  name: email,
                  updatedAt: now,
                },
              );
            } else {
              const newUserInfo: UserInfoEntity = new UserInfoEntity();
              newUserInfo.userId = identity.user.userId;
              newUserInfo.name = email;
              newUserInfo.type = UserType.INDIVIDUAL;
              newUserInfo.countryCode = CountryCode.JAPAN;
              newUserInfo.createdAt = now;
              newUserInfo.updatedAt = now;
              await queryRunner.manager
                .getRepository(UserInfoEntity)
                .save(newUserInfo);
            }

            const userTeam: UserTeamEntity = new UserTeamEntity();
            userTeam.rootUserId = rootUserId;
            userTeam.teamId = team.id;
            userTeam.userId = identity.user.userId;
            userTeam.email = email;
            userTeam.isAdmin = false;
            await queryRunner.manager
              .getRepository(UserTeamEntity)
              .save(userTeam);
          } catch (e) {
            console.log(e);
          }
        }
      }

      await queryRunner.commitTransaction();
    } catch (e) {
      console.log(e);
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async updateMemberOfTeam(
    rootUserId: string,
    input: UpdateMemberOfTeamReq,
  ): Promise<void> {
    const now = new Date();
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get current team
      const team = await queryRunner.manager
        .getRepository(TeamEntity)
        .findOneBy({ id: input.teamId });

      if (!team) throw new AppException('api.error.team_not_found');

      // Get current user team
      const userTeams: UserTeamEntity[] = await queryRunner.manager
        .getRepository(UserTeamEntity)
        .find({ where: { rootUserId: rootUserId, teamId: input.teamId } });

      const orgTeamMember: Record<string, UserTeamEntity> = {};
      for (const member of userTeams) {
        orgTeamMember[member.id || 0] = member;
      }

      // update member
      const newTeamMembers: UpdateTeamMemberData[] = [...input.members];
      for (const member of input.members) {
        const id: number = member.id || 0;
        const index = newTeamMembers.findIndex(item => item.id === id);

        try {
          //------------------------------------
          // remove old member
          if (member.isDeleted) {
            await queryRunner.manager
              .getRepository(UserTeamEntity)
              .delete({ rootUserId: rootUserId, id: id });

            if (index >= 0) newTeamMembers.splice(index, 1);
            continue;
          }

          //------------------------------------
          // add new member
          if (id < 0) {
            const user: UserEntity = await queryRunner.manager
              .getRepository(UserEntity)
              .findOneBy({ email: member.email });

            if (user) {
              const userTeam: UserTeamEntity = new UserTeamEntity();
              userTeam.rootUserId = rootUserId;
              userTeam.teamId = team.id || 0;
              userTeam.userId = user.userid;
              userTeam.email = member.email;
              userTeam.isAdmin = member.isAdmin || false;
              await queryRunner.manager
                .getRepository(UserTeamEntity)
                .save(userTeam);

              if (index >= 0) newTeamMembers[index].id = userTeam.id;
            } else {
              // create user sign up
              try {
                const identity = await this.identityService.createIdentity({
                  email: member.email,
                  password: uuidv4(),
                });

                const userInfo = await queryRunner.manager
                  .getRepository(UserInfoEntity)
                  .findOneBy({
                    userId: identity.user.userId,
                  });

                if (userInfo) {
                  await queryRunner.manager
                    .getRepository(UserInfoEntity)
                    .update(
                      {
                        userId: userInfo.userId,
                      },
                      {
                        name: member.email,
                        updatedAt: now,
                      },
                    );
                } else {
                  const newUserInfo: UserInfoEntity = new UserInfoEntity();
                  newUserInfo.userId = identity.user.userId;
                  newUserInfo.name = member.email;
                  newUserInfo.type = UserType.INDIVIDUAL;
                  newUserInfo.countryCode = CountryCode.JAPAN;
                  newUserInfo.createdAt = now;
                  newUserInfo.updatedAt = now;
                  await queryRunner.manager
                    .getRepository(UserInfoEntity)
                    .save(newUserInfo);
                }

                const userTeam: UserTeamEntity = new UserTeamEntity();
                userTeam.rootUserId = rootUserId;
                userTeam.teamId = team.id || 0;
                userTeam.userId = identity.user.userId;
                userTeam.email = member.email;
                userTeam.isAdmin = member.isAdmin || false;
                await queryRunner.manager
                  .getRepository(UserTeamEntity)
                  .save(userTeam);

                if (index >= 0) newTeamMembers[index].id = userTeam.id;
              } catch (e) {
                console.log(e);
              }
            }

            continue;
          }

          //------------------------------------
          // update old member
          if (id > 0) {
            const oldMember = orgTeamMember[id];
            const oldIsAdmin: boolean = oldMember?.isAdmin || false;
            const newIsAdmin: boolean = member.isAdmin || false;

            if (oldIsAdmin !== newIsAdmin) {
              await queryRunner.manager
                .getRepository(UserTeamEntity)
                .update(newTeamMembers[index].id, {
                  rootUserId: rootUserId,
                  email: member.email,
                  isAdmin: member.isAdmin,
                });

              if (index >= 0) orgTeamMember[id].isAdmin = newIsAdmin;
            }
          }
        } catch (e) {
          console.log(e);
        }
      }

      //-------------------------------------
      await queryRunner.commitTransaction();
    } catch (e) {
      console.log(e);
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }
}
