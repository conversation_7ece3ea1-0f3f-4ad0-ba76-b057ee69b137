"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StyleModule = void 0;
const common_1 = require("@nestjs/common");
const style_service_1 = require("./style.service");
const style_controller_1 = require("./style.controller");
const typeorm_1 = require("@nestjs/typeorm");
const style_entity_1 = require("./entities/style.entity");
const project_module_1 = require("../project/project.module");
let StyleModule = class StyleModule {
};
exports.StyleModule = StyleModule;
exports.StyleModule = StyleModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([style_entity_1.StyleEntity]), project_module_1.ProjectModule],
        controllers: [style_controller_1.StyleController],
        providers: [style_service_1.StyleService],
        exports: [style_service_1.StyleService],
    })
], StyleModule);
//# sourceMappingURL=style.module.js.map