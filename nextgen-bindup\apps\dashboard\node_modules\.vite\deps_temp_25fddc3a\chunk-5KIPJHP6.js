import {
  defaultTheme_default,
  identifier_default,
  useTheme_default
} from "./chunk-TKJA6PJ7.js";
import {
  require_react
} from "./chunk-KGOVUTMV.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/@mui/material/styles/useTheme.js
var React = __toESM(require_react());
function useTheme() {
  const theme = useTheme_default(defaultTheme_default);
  if (true) {
    React.useDebugValue(theme);
  }
  return theme[identifier_default] || theme;
}

export {
  useTheme
};
//# sourceMappingURL=chunk-5KIPJHP6.js.map
