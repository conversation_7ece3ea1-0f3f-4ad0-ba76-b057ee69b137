import { StyleService } from './style.service';
import { StyleEntity } from './entities/style.entity';
export declare class StyleController {
    private readonly styleService;
    constructor(styleService: StyleService);
    create(assetEntity: StyleEntity): Promise<StyleEntity>;
    update(styleId: string, data: Partial<StyleEntity>): Promise<StyleEntity>;
    getById(styleId: string): Promise<StyleEntity>;
    getByProjectId(projectId: string): Promise<StyleEntity[]>;
    getBySiteId(projectId: string, siteId: string): Promise<StyleEntity[]>;
    delete(styleId: string): Promise<boolean>;
}
