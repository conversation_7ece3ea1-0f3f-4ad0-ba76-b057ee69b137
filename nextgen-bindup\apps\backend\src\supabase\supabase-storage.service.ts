import { Injectable, Inject } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SignUploadDto } from './dto/sign-upload.dto';

@Injectable()
export class SupabaseStorageService {
  private BUCKET_NAME: string = 'dev-bindup-bucket';

  constructor(@Inject('SUPABASE_CLIENT') private supabase: SupabaseClient) {}

  async createBucket(): Promise<void> {
    const getBucketResult = await this.supabase.storage.getBucket(
      this.BUCKET_NAME,
    );

    if (getBucketResult.data?.id === this.BUCKET_NAME) return;

    await this.supabase.storage.createBucket(this.BUCKET_NAME, {
      public: true,
    });
  }

  async createSignedUploadUrl(filepath: string): Promise<SignUploadDto> {
    const result = await this.supabase.storage
      .from(this.BUCKET_NAME)
      .createSignedUploadUrl(filepath, {
        upsert: true,
      });

    return result.data;
  }

  async getPublicUrl(filepath: string): Promise<string> {
    const result = await this.supabase.storage
      .from(this.BUCKET_NAME)
      .getPublicUrl(filepath);

    return result.data.publicUrl;
  }
}
