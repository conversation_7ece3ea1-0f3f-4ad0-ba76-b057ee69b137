import { Boolean1 } from './boolean.enum';
export interface Site1_Corner {
    tmpSiteId: number;
    cornerId: number;
    parentCornerId?: number;
    siteId: number;
    layer: number;
    seq: number;
    name: string;
    dispFlg: number;
    openFlg: number;
    publicFlg: number;
    labelColor: number;
    skinId: string;
    info: string;
    infoJson: Corner_Info;
    robots: string;
    robotsJson: Corner_Robots;
    version: number;
    delFlg: number;
    insDate: string;
    updDate: string;
}
export interface Corner_Info {
    corner_module: string;
    use_parent_module: string;
    fonttype: string;
}
export interface Corner_Robots {
    ison: string;
    desc: string;
    kw: string;
    use: Boolean1;
}
