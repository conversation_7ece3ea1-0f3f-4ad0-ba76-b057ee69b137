"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductStocksService = void 0;
const common_1 = require("@nestjs/common");
const product_stock_entity_1 = require("./entities/product-stock.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
const validate_exception_1 = require("../common/exceptions/validate.exception");
const common_util_1 = require("../utils/common.util");
const product_stock_dto_1 = require("./dto/product-stock.dto");
const product_service_1 = require("../product/product.service");
let ProductStocksService = class ProductStocksService {
    constructor(productService) {
        this.productService = productService;
    }
    async getBySite(siteId) {
        return await this.productStockRepo.find({
            where: { siteId: siteId },
            order: { productId: 'ASC', x: 'ASC', y: 'ASC' },
        });
    }
    async getByProduct(siteId, productId) {
        return await this.productStockRepo.find({
            where: { siteId: siteId, productId: productId },
            order: { productId: 'ASC', x: 'ASC', y: 'ASC' },
        });
    }
    async getByProductIds(siteId, productIds) {
        return await this.productStockRepo.find({
            where: { siteId: siteId, productId: (0, typeorm_2.In)(productIds) },
            order: { productId: 'ASC', x: 'ASC', y: 'ASC' },
        });
    }
    async getById(siteId, productId, stockId) {
        return await this.productStockRepo.findOneBy({
            id: stockId,
            siteId: siteId,
            productId: productId,
        });
    }
    validateProductStockData(productStockEntity) {
        if (!productStockEntity.siteId)
            throw new validate_exception_1.ValidateException('error.site_id_required');
        if (!productStockEntity.productId)
            throw new validate_exception_1.ValidateException('cart_management.product.error.product_required');
        if (!(0, common_util_1.isInteger)(productStockEntity.x, { min: 0, max: 5 })) {
            throw new validate_exception_1.ValidateException('error.bad_request');
        }
        if (!(0, common_util_1.isInteger)(productStockEntity.y, { min: 0, max: 5 })) {
            throw new validate_exception_1.ValidateException('error.bad_request');
        }
        if (!(0, common_util_1.isInteger)(productStockEntity.quantity, {
            min: 0,
            max: common_util_1.MAX_PRODUCT_QUANTITY_VALUE,
        })) {
            throw new validate_exception_1.ValidateException('cart_management.product.error.quantity.invalid');
        }
    }
    async create(entity) {
        const now = new Date();
        const productStock = new product_stock_entity_1.ProductStockEntity();
        productStock.siteId = entity.siteId;
        productStock.productId = entity.productId;
        productStock.x = entity.x;
        productStock.y = entity.y;
        productStock.quantity = entity.quantity;
        productStock.createdAt = now;
        productStock.updatedAt = now;
        this.validateProductStockData(productStock);
        return await this.productStockRepo.save(productStock);
    }
    async update(id, entity) {
        const productStock = await this.productStockRepo.findOneBy({
            id: id,
        });
        if (!productStock)
            throw new app_exception_1.AppException('api.error.product_stock_not_found');
        productStock.x = entity.x;
        productStock.y = entity.y;
        productStock.quantity = entity.quantity;
        productStock.updatedAt = new Date();
        this.validateProductStockData(productStock);
        await this.productStockRepo.update(id, productStock);
        return productStock;
    }
    async deleteByProductId(siteId, productId) {
        await this.productStockRepo.delete({
            siteId: siteId,
            productId: productId,
        });
    }
    async checkStock(siteId, cartItems) {
        if (!cartItems?.length) {
            return { allItemsAvailable: true, unavailableItems: [] };
        }
        const productIds = [...new Set(cartItems.map(item => item.productId))];
        const [products, allStocks] = await Promise.all([
            this.productService.findByIds(siteId, productIds),
            this.getByProductIds(siteId, productIds),
        ]);
        const productsMap = new Map(products.map(p => [p.id, p]));
        const stocksMap = new Map(allStocks.map(stock => [
            `${stock.productId}-${stock.x}-${stock.y}`,
            stock,
        ]));
        const unavailableItems = [];
        for (const cartItem of cartItems) {
            const product = productsMap.get(cartItem.productId);
            if (!product)
                continue;
            let errorType;
            let available = Infinity;
            if (!product.isOrderable) {
                errorType = product_stock_dto_1.StockErrorType.NOT_ORDERABLE;
                available = 0;
            }
            else if (!product.unlimitedPurchase) {
                if (product.purchaseLimitQuantity > 0 &&
                    cartItem.quantity > product.purchaseLimitQuantity) {
                    errorType = product_stock_dto_1.StockErrorType.EXCEED_PURCHASE_LIMIT;
                    available = product.purchaseLimitQuantity;
                }
                else {
                    const stockKey = `${cartItem.productId}-${cartItem.x}-${cartItem.y}`;
                    const stock = stocksMap.get(stockKey);
                    const stockQuantity = stock?.quantity || 0;
                    if (cartItem.quantity > stockQuantity) {
                        errorType = product_stock_dto_1.StockErrorType.OUT_OF_STOCK;
                        available = stockQuantity;
                    }
                }
            }
            if (errorType) {
                unavailableItems.push({
                    productId: cartItem.productId,
                    siteId: siteId,
                    x: cartItem.x,
                    y: cartItem.y,
                    quantity: cartItem.quantity,
                    available,
                    errorType,
                });
            }
        }
        return {
            allItemsAvailable: unavailableItems.length === 0,
            unavailableItems,
        };
    }
};
exports.ProductStocksService = ProductStocksService;
__decorate([
    (0, typeorm_1.InjectRepository)(product_stock_entity_1.ProductStockEntity),
    __metadata("design:type", typeorm_2.Repository)
], ProductStocksService.prototype, "productStockRepo", void 0);
exports.ProductStocksService = ProductStocksService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)((0, common_1.forwardRef)(() => product_service_1.ProductService))),
    __metadata("design:paramtypes", [product_service_1.ProductService])
], ProductStocksService);
//# sourceMappingURL=product-stocks.service.js.map