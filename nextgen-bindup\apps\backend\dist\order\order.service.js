"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
const order_entity_1 = require("./entites/order.entity");
const order_item_entity_1 = require("./entites/order-item.entity");
const order_enum_1 = require("./enum/order.enum");
const product_service_1 = require("../product/product.service");
const price_util_1 = require("../utils/price.util");
const shipping_note_settings_service_1 = require("../shipping-note-settings/shipping-note-settings.service");
const shop_information_settings_service_1 = require("../shop-information-settings/shop-information-settings.service");
const csv_service_1 = require("../product/csv.service");
let OrderService = class OrderService {
    constructor(productService, shippingNoteSettingService, shopInformationSettingService, csvService) {
        this.productService = productService;
        this.shippingNoteSettingService = shippingNoteSettingService;
        this.shopInformationSettingService = shopInformationSettingService;
        this.csvService = csvService;
    }
    async getOrders(siteId, query) {
        const queryBuilder = this.orderRepository
            .createQueryBuilder('orders')
            .leftJoinAndSelect('orders.orderItems', 'orderItems')
            .leftJoinAndSelect('orderItems.product', 'product')
            .where('orders.siteId = :siteId', { siteId });
        if (query.search?.trim()) {
            queryBuilder.andWhere('(orders.firstName ILIKE :search OR orders.lastName ILIKE :search OR orders.email ILIKE :search OR orders.phoneNumber ILIKE :search)', { search: `%${query.search.trim()}%` });
        }
        if (query.email) {
            queryBuilder.andWhere('orders.email = :email', { email: query.email });
        }
        if (query.phoneNumber) {
            queryBuilder.andWhere('orders.phoneNumber = :phoneNumber', {
                phoneNumber: query.phoneNumber,
            });
        }
        if (query.orderStatus) {
            queryBuilder.andWhere('orders.orderStatus = :orderStatus', {
                orderStatus: query.orderStatus,
            });
        }
        if (query.paymentMethodType) {
            queryBuilder.andWhere('orders.paymentMethodType = :paymentMethodType', {
                paymentMethodType: query.paymentMethodType,
            });
        }
        if (query.startDate) {
            queryBuilder.andWhere('orders.createdAt >= :startDate', {
                startDate: query.startDate,
            });
        }
        if (query.endDate) {
            queryBuilder.andWhere('orders.createdAt <= :endDate', {
                endDate: query.endDate,
            });
        }
        const skip = (query.page - 1) * query.limit;
        queryBuilder
            .orderBy('orders.createdAt', 'DESC')
            .skip(skip)
            .take(query.limit);
        const [orders, total] = await queryBuilder.getManyAndCount();
        return {
            data: orders,
            total,
            page: query.page,
            limit: query.limit,
            totalPage: Math.ceil(total / query.limit),
        };
    }
    async createOrder(dto, paymentMethodType, orderStatus) {
        try {
            const { orderItems, ...orderData } = dto;
            const { contact, shipping } = orderData;
            const productIds = orderItems.map(item => item.productId);
            const products = await this.productService.findByIds(orderData.siteId, productIds);
            const shippingNoteSetting = await this.shippingNoteSettingService.findOneBySiteId(orderData.siteId);
            const shopInformationSetting = await this.shopInformationSettingService.findOneBySiteId(orderData.siteId);
            if (!shippingNoteSetting) {
                throw new app_exception_1.AppException('api.error.shipping_note_setting_not_found');
            }
            if (!shopInformationSetting) {
                throw new app_exception_1.AppException('api.error.shop_information_not_found');
            }
            console.log('orderData:', orderData);
            const item = {
                siteId: orderData.siteId,
                lastName: contact.lastName,
                firstName: contact.firstName,
                lastNameKana: contact.lastNameKana,
                firstNameKana: contact.firstNameKana,
                email: contact.email,
                postalCode: contact.postalCode,
                prefecture: contact.prefecture,
                addressLine1: contact.addressLine1,
                addressLine2: contact.addressLine2,
                phoneNumber: contact.phoneNumber,
                shippingLastName: shipping?.lastName || '',
                shippingFirstName: shipping?.firstName || '',
                shippingLastNameKana: shipping?.lastNameKana || '',
                shippingFirstNameKana: shipping?.firstNameKana || '',
                shippingEmail: shipping?.email || '',
                shippingPostalCode: shipping?.postalCode || '',
                shippingPrefecture: shipping?.prefecture || '',
                shippingAddressLine1: shipping?.addressLine1 || '',
                shippingAddressLine2: shipping?.addressLine2 || '',
                shippingPhoneNumber: shipping?.phoneNumber || '',
                additionalInformation: orderData.additionalInformation,
                orderStatus: orderStatus,
                createdAt: new Date(),
                updatedAt: new Date(),
                checkoutSessionUrl: '',
                checkoutSessionId: '',
                subtotal: 0,
                shippingFee: 0,
                platformFee: 0,
                paymentGatewayFee: 0,
                total: 0,
                shopNetPayout: 0,
                paymentMethodType: paymentMethodType,
            };
            const order = this.orderRepository.create(item);
            const orderItemEntities = orderItems.map(item => {
                const product = products.find(p => p.id === item.productId);
                if (!product) {
                    throw new app_exception_1.AppException(`api.error.product_not_found: ${item.productId}`);
                }
                if (!product.isOrderable) {
                    throw new app_exception_1.AppException(`api.error.product_not_orderable: ${item.productId}`);
                }
                if (product.isDeleted) {
                    throw new app_exception_1.AppException(`api.error.product_is_deleted: ${item.productId}`);
                }
                if (item.quantity <= 0) {
                    throw new app_exception_1.AppException(`api.error.product_invalid_quantity: ${item.productId}`);
                }
                const displayPrice = (0, price_util_1.getDisplayPrice)(product, shopInformationSetting);
                const orderItem = this.orderItemRepository.create({
                    orderId: order.id,
                    productId: item.productId,
                    productName: product.name,
                    productType: product.productType,
                    unitPrice: product.price,
                    individualShippingCharges: product.individualShippingCharges,
                    salePrice: product.sale,
                    displayPrice: displayPrice,
                    quantity: item.quantity,
                    subtotal: displayPrice * item.quantity,
                    attributes: item.attributes,
                    image: product.images?.[0],
                });
                return orderItem;
            });
            const fee = (0, price_util_1.calculateFees)(orderItemEntities, shippingNoteSetting, 0.05, 0.036, order.paymentMethodType, shipping?.prefecture || '');
            order.subtotal = fee.subtotal;
            order.shippingFee = fee.shippingFee;
            order.platformFee = fee.platformFee;
            order.paymentGatewayFee = fee.paymentGatewayFee;
            order.total = fee.total;
            order.shopNetPayout = fee.shopNetPayout;
            order.checkoutSessionUrl = '';
            order.checkoutSessionId = '';
            const savedOrder = await this.orderRepository.save(order);
            await this.orderItemRepository.save(orderItemEntities.map(item => ({ ...item, orderId: savedOrder.id })));
            return this.findOrderById(savedOrder.id);
        }
        catch (error) {
            console.error('Error creating pending order:', error);
        }
    }
    async updateOrder(orderId, orderData) {
        const order = await this.orderRepository.findOneBy({ id: orderId });
        if (!order) {
            throw new app_exception_1.AppException('api.error.order_not_found');
        }
        delete orderData.id;
        delete orderData.createdAt;
        orderData.updatedAt = new Date();
        await this.orderRepository.update(orderId, orderData);
        return { ...order, ...orderData };
    }
    async findOrderById(orderId) {
        const order = await this.orderRepository.findOne({
            where: { id: orderId },
            relations: ['orderItems', 'orderItems.product'],
        });
        if (!order) {
            throw new app_exception_1.AppException('api.error.order_not_found');
        }
        return order;
    }
    async deleteOrder(orderId) {
        const order = await this.orderRepository.findOneBy({ id: orderId });
        if (!order) {
            throw new app_exception_1.AppException('api.error.order_not_found');
        }
        await this.orderRepository.delete(orderId);
        return true;
    }
    async getWaitingPaymentOrdersInLast2Days() {
        const twoDaysAgo = new Date();
        twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
        const orders = await this.orderRepository.find({
            where: {
                orderStatus: order_enum_1.OrderStatus.WAITING_PAYMENT,
                paymentMethodType: "CREDIT_CARD",
                createdAt: (0, typeorm_2.MoreThanOrEqual)(twoDaysAgo),
            },
            relations: ['orderItems', 'orderItems.product'],
        });
        return orders;
    }
    async downloadCSV(siteId, query) {
        const queryBuilder = this.orderRepository
            .createQueryBuilder('orders')
            .leftJoinAndSelect('orders.orderItems', 'orderItems')
            .leftJoinAndSelect('orderItems.product', 'product')
            .where('orders.siteId = :siteId', { siteId });
        if (query?.email) {
            queryBuilder.andWhere('orders.email = :email', { email: query.email });
        }
        if (query?.phoneNumber) {
            queryBuilder.andWhere('orders.phoneNumber = :phoneNumber', {
                phoneNumber: query.phoneNumber,
            });
        }
        if (query?.startDate) {
            queryBuilder.andWhere('orders.createdAt >= :startDate', {
                startDate: query.startDate,
            });
        }
        if (query?.endDate) {
            queryBuilder.andWhere('orders.createdAt <= :endDate', {
                endDate: query.endDate,
            });
        }
        if (query?.orderStatus) {
            queryBuilder.andWhere('orders.orderStatus = :orderStatus', {
                orderStatus: query.orderStatus,
            });
        }
        const orders = await queryBuilder.getMany();
        const headers = [
            '注文番号',
            '注文日時',
            '注文ステータス',
            '支払方法',
            '合計金額',
            '商品合計',
            '送料',
            'プラットフォーム手数料',
            '決済手数料',
            '店舗支払額',
            'お客様名（姓）',
            'お客様名（名）',
            'お客様名カナ（姓）',
            'お客様名カナ（名）',
            'メールアドレス',
            '電話番号',
            '郵便番号',
            '都道府県',
            '住所1',
            '住所2',
            '配送先名（姓）',
            '配送先名（名）',
            '配送先名カナ（姓）',
            '配送先名カナ（名）',
            '配送先メールアドレス',
            '配送先電話番号',
            '配送先郵便番号',
            '配送先都道府県',
            '配送先住所1',
            '配送先住所2',
            '商品コード',
            '商品名',
            '商品オプション',
            '商品タイプ',
            '単価',
            'セール価格',
            '表示価格',
            '数量',
            '小計',
            '追加情報',
        ];
        const rows = orders
            .map(order => order.orderItems.map(item => ({
            注文番号: order.id,
            注文日時: order.createdAt.toISOString(),
            注文ステータス: order.orderStatus,
            支払方法: order.paymentMethodType,
            合計金額: order.total,
            商品合計: order.subtotal,
            送料: order.shippingFee,
            プラットフォーム手数料: order.platformFee,
            決済手数料: order.paymentGatewayFee,
            店舗支払額: order.shopNetPayout,
            'お客様名（姓）': order.lastName,
            'お客様名（名）': order.firstName,
            'お客様名カナ（姓）': order.lastNameKana,
            'お客様名カナ（名）': order.firstNameKana,
            メールアドレス: order.email,
            電話番号: order.phoneNumber,
            郵便番号: order.postalCode,
            都道府県: order.prefecture,
            住所1: order.addressLine1,
            住所2: order.addressLine2,
            '配送先名（姓）': order.shippingLastName,
            '配送先名（名）': order.shippingFirstName,
            '配送先名カナ（姓）': order.shippingLastNameKana,
            '配送先名カナ（名）': order.shippingFirstNameKana,
            配送先メールアドレス: order.shippingEmail,
            配送先電話番号: order.shippingPhoneNumber,
            配送先郵便番号: order.shippingPostalCode,
            配送先都道府県: order.shippingPrefecture,
            配送先住所1: order.shippingAddressLine1,
            配送先住所2: order.shippingAddressLine2,
            商品コード: item.product.code,
            商品名: item.productName,
            商品オプション: this.formatProductOptions(item.attributes),
            商品タイプ: item.productType,
            単価: item.unitPrice,
            セール価格: item.salePrice,
            表示価格: item.displayPrice,
            数量: item.quantity,
            小計: item.subtotal,
            追加情報: order.additionalInformation,
        })))
            .flat();
        return await this.csvService.exportToCsv(rows, headers);
    }
    formatProductOptions(attributes) {
        if (!attributes)
            return '';
        const hasAttribute1 = 'attribute1' in attributes && attributes.attribute1;
        const hasAttribute2 = 'attribute2' in attributes && attributes.attribute2;
        if (hasAttribute1 && hasAttribute2) {
            return `${attributes.attribute1} - ${attributes.attribute2}`;
        }
        if (hasAttribute1) {
            return attributes.attribute1;
        }
        return '';
    }
    async markAsPaid(orderId) {
        const order = await this.findOrderById(orderId);
        if (!order) {
            throw new app_exception_1.AppException('api.error.order_not_found');
        }
        if (order.orderStatus === order_enum_1.OrderStatus.PAID) {
            throw new app_exception_1.AppException('api.error.order_already_paid');
        }
        order.orderStatus = order_enum_1.OrderStatus.PAID;
        order.updatedAt = new Date();
        return await this.orderRepository.save(order);
    }
};
exports.OrderService = OrderService;
__decorate([
    (0, typeorm_1.InjectRepository)(order_entity_1.OrderEntity),
    __metadata("design:type", typeorm_2.Repository)
], OrderService.prototype, "orderRepository", void 0);
__decorate([
    (0, typeorm_1.InjectRepository)(order_item_entity_1.OrderItemEntity),
    __metadata("design:type", typeorm_2.Repository)
], OrderService.prototype, "orderItemRepository", void 0);
exports.OrderService = OrderService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [product_service_1.ProductService,
        shipping_note_settings_service_1.ShippingNoteSettingService,
        shop_information_settings_service_1.ShopInformationSettingService,
        csv_service_1.CsvService])
], OrderService);
//# sourceMappingURL=order.service.js.map