import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreatePageEditSessionTable1730961329207
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}page_edit_sessions`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'sessionId',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'pageId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'userId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'componentId',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'cursorPosition',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'editorSize',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
    );

    await queryRunner.query(`
      ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}
      ADD CONSTRAINT unique_user_page_session UNIQUE ("userId", "pageId", "sessionId");
    `);

    // await queryRunner.query(`
    //   CREATE OR REPLACE FUNCTION delete_old_record_on_page_edit_sessions()
    //   RETURNS TRIGGER AS $$
    //   BEGIN
    //       DELETE FROM ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}
    //       WHERE "updatedAt" < NOW() - INTERVAL '5 seconds';
    //       RETURN NEW;
    //   END;
    //   $$ LANGUAGE plpgsql;
    // `);

    // await queryRunner.query(`
    //   CREATE TRIGGER trigger_delete_old_record_on_page_edit_sessions
    //   BEFORE INSERT OR UPDATE ON ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}
    //   FOR EACH ROW
    //   EXECUTE FUNCTION delete_old_record_on_page_edit_sessions();
    // `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // await queryRunner.query(
    //   `DROP TRIGGER IF EXISTS trigger_delete_old_record_on_page_edit_sessions ON ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME};`
    // );
    // await queryRunner.query(
    //   `DROP FUNCTION IF EXISTS delete_old_record_on_page_edit_sessions;`
    // );
    await queryRunner.query(
      `ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME} DROP CONSTRAINT unique_user_page_session;`,
    );
    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
