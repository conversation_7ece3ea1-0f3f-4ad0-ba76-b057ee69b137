import { PageEntity } from 'src/page/entities/page.entity';
import { PageVersionEntity } from './entities/page-version.entity';
import { SiteVersionEntity } from './entities/site-version.entity';
import { Repository } from 'typeorm';
import { SiteEntity } from 'src/site/entities/site.entity';
import { SiteVersionDto } from './version-history.dto';
export declare class VersionHistoryService {
    private pageRepository;
    private siteRepository;
    private siteVersionRepository;
    private pageVersionRepository;
    constructor(pageRepository: Repository<PageEntity>, siteRepository: Repository<SiteEntity>, siteVersionRepository: Repository<SiteVersionEntity>, pageVersionRepository: Repository<PageVersionEntity>);
    deleteSiteVersion(siteVersionId: number): Promise<void>;
    updateVersionName(siteVersionId: number, versionName: string): Promise<void>;
    backupSite(siteId: number, name: string): Promise<SiteVersionEntity>;
    restore(siteId: number, siteVersionId: number): Promise<boolean>;
    getSiteVersions(siteId: number, page?: number, pageSize?: number, year?: number, month?: number): Promise<SiteVersionDto>;
    getSiteVersion(siteVersionId: number): Promise<SiteVersionEntity>;
    getPagesBySiteVersionId(siteVersionId: number): Promise<PageEntity[]>;
    getPageBySiteVersionId(siteVersionId: number, pageId: number): Promise<PageEntity>;
}
