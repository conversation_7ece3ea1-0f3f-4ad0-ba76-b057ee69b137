import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { styled } from '@mui/material/styles';
import { getErrMsg } from '@nextgen-bindup/common/utility';
import { TeamDto } from '../../../dto/team.type';
import { teamService } from '../../../services/team.service';
import { userTeamService } from '../../../services/user-team.service';

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(0),
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
}));

export const InviteMember: FC<{
  onUpdate: (status: string) => void;
}> = ({ onUpdate }) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [step, setStep] = useState(1);
  const [errorField, setErrorField] = useState<{
    field: string;
    msg: string;
  } | null>(null);
  const [teams, setTeams] = useState<TeamDto[]>([]);
  const [teamId, setTeamId] = useState<number>(0);
  const [newTeam, setNewTeam] = useState<string>('');
  const [emails, setEmails] = useState<string>('');
  const [finalEmails, setFinalEmails] = useState<string[]>([]);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    initData();
  }, []);

  const initData = async () => {
    setIsLoading(true);
    try {
      const teams: TeamDto[] = await teamService.getAll();
      setTeams(teams);
    } catch (e) {
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  };

  const onNext = () => {
    if (isLoading) return;
    setIsLoading(true);
    setError('');

    try {
      if (!validate()) return;

      if (step === 1) {
        setStep(2);
      }
    } catch (e) {
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  };

  const onInvite = async () => {
    setIsLoading(true);
    setError('');
    try {
      await userTeamService.inviteMemberToTeam({
        team: {
          id: teamId,
          name: newTeam,
        },
        emails: finalEmails,
      });

      onUpdate('invite');
    } catch (e) {
      setError(t(getErrMsg(e)));
      console.log(e);
    } finally {
      setIsLoading(false);
    }
  };

  const validate = () => {
    if (teamId === 0) {
      if (!newTeam) {
        setErrorField({
          field: 'new_team',
          msg: t('member.invite_member.error.new_team_required'),
        });
        return false;
      }

      if (
        teams.find(item => item.name.toLowerCase() === newTeam.toLowerCase())
      ) {
        setErrorField({
          field: 'new_team',
          msg: t('member.invite_member.error.new_team_duplicate'),
        });
        return false;
      }
    }

    if (!emails.trim()) {
      setErrorField({
        field: 'email',
        msg: t('member.invite_member.error.email_required'),
      });
      return false;
    }

    const emailKey: Record<string, boolean> = {};
    const finalEmailList: string[] = [];
    const emailList: string[] = emails.split(',');
    for (const email of emailList) {
      if (email.trim().length === 0) continue;
      const isEmail = email
        .trim()
        .toLowerCase()
        .match(
          /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        );

      if (!isEmail) {
        setErrorField({
          field: 'email',
          msg: t('member.invite_member.error.email_invalid'),
        });
        return false;
      }

      const finalEmail = email.trim().toLowerCase();
      if (!emailKey[finalEmail]) {
        finalEmailList.push(finalEmail);
        emailKey[finalEmail] = true;
      }
    }

    if (emailList.length === 0) {
      setErrorField({
        field: 'email',
        msg: t('member.invite_member.error.email_required'),
      });
      return false;
    }

    setFinalEmails(finalEmailList);

    return true;
  };

  return (
    <BootstrapDialog open>
      <DialogTitle sx={{ m: 0, p: 1 }} id="customized-dialog-title">
        {t('member.invite_member.title')}
      </DialogTitle>

      <DialogContent dividers sx={{ width: '400px' }}>
        <Box id="step1" sx={{ display: step === 1 ? 'block' : 'none' }}>
          <Stack spacing={2}>
            <Box sx={{ padding: '1rem 1.5rem 0 1.5rem' }}>
              <Stack spacing={2}>
                <FormControl
                  size="small"
                  required
                  error={errorField?.field === 'team'}
                >
                  <InputLabel id="team">
                    {t('member.invite_member.team')}
                  </InputLabel>
                  <Select
                    sx={{ minWidth: '200px' }}
                    labelId="team"
                    autoWidth
                    value={teamId.toString()}
                    label={t('member.invite_member.team')}
                    onChange={e => {
                      const teamId: number = parseInt(e.target.value);
                      setTeamId(teamId);
                    }}
                  >
                    <MenuItem value={'0'}>
                      {t('member.invite_member.new_team')}
                    </MenuItem>
                    {teams.map(item => (
                      <MenuItem
                        value={(item.id || '0').toString()}
                        key={(item.id || '0').toString()}
                      >
                        {item.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <TextField
                  size="small"
                  fullWidth
                  sx={{ display: teamId === 0 ? 'block' : 'none' }}
                  label={t('member.invite_member.enter_new_team')}
                  variant="outlined"
                  value={newTeam}
                  onChange={e => {
                    setNewTeam(e.target.value);
                  }}
                  error={errorField?.field === `new_team`}
                  helperText={
                    errorField?.field === `new_team` ? errorField?.msg : ''
                  }
                />
              </Stack>
            </Box>

            <Box sx={{ padding: '0 1.5rem 1rem 1.5rem' }}>
              <TextField
                fullWidth
                size="small"
                label={t('member.invite_member.email')}
                variant="outlined"
                value={emails}
                multiline
                onChange={e => {
                  setEmails(e.target.value);
                }}
                error={errorField?.field === `email`}
                helperText={
                  errorField?.field === `email` ? errorField?.msg : ''
                }
              />
            </Box>
          </Stack>
        </Box>

        <Box id="step2" sx={{ display: step === 2 ? 'block' : 'none' }}>
          <Box sx={{ padding: '1rem 1.5rem 1rem 1.5rem' }}>
            <Stack spacing={2}>
              <Typography variant="body1" sx={{ lineHeight: '1' }}>
                {t('member.invite_member.confirm_msg_1')}
              </Typography>
              <Typography variant="body1" sx={{ lineHeight: '1' }}>
                {t('member.invite_member.confirm_msg_2')}
              </Typography>

              <Box sx={{ paddingLeft: '10px' }}>
                <Stack spacing={1}>
                  {finalEmails.map((item, index) => (
                    <Typography
                      variant="body1"
                      key={item}
                      sx={{ lineHeight: '1.2' }}
                    >
                      {index + 1}. {item}
                    </Typography>
                  ))}
                </Stack>
              </Box>

              {error ? (
                <Typography
                  variant="body1"
                  sx={{ lineHeight: '1' }}
                  color="error"
                >
                  {error}
                </Typography>
              ) : null}
            </Stack>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Box
          sx={{
            display: step === 1 ? 'flex' : 'none',
            gap: 2,
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <Button
            onClick={() => {
              onUpdate('cancel');
            }}
            color="inherit"
            variant="outlined"
            size="small"
            loading={isLoading}
          >
            {t('common.cancel')}
          </Button>

          <Button
            autoFocus
            onClick={onNext}
            color="success"
            variant="contained"
            size="small"
            loading={isLoading}
          >
            {t('common.next')}
          </Button>
        </Box>

        <Box
          sx={{
            display: step === 2 ? 'flex' : 'none',
            gap: 2,
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <Button
            onClick={() => {
              setError('');
              setStep(1);
            }}
            color="primary"
            variant="contained"
            size="small"
            loading={isLoading}
          >
            {t('common.go_back')}
          </Button>

          <Button
            autoFocus
            onClick={onInvite}
            color="success"
            variant="contained"
            size="small"
            loading={isLoading}
          >
            {t('common.btn_invite')}
          </Button>
        </Box>
      </DialogActions>
    </BootstrapDialog>
  );
};
