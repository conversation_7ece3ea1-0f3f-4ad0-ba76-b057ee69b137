import { Repository } from 'typeorm';
import { SubDomainEntity } from './entities/sub-domain.entity';
export declare class SubDomainService {
    readonly subDomainRepo: Repository<SubDomainEntity>;
    create(data: SubDomainEntity): Promise<SubDomainEntity>;
    update(id: number, data: Partial<SubDomainEntity>): Promise<SubDomainEntity>;
    delete(id: number): Promise<boolean>;
    findBySiteId(siteId: number): Promise<SubDomainEntity[]>;
    findByProjectId(projectId: number): Promise<SubDomainEntity[]>;
}
