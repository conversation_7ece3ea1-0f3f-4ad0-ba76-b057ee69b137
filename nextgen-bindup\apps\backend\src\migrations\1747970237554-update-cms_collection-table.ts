import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateCmsCollection1747970237554 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}cms_collection`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const dataTypeColumn: TableColumn = new TableColumn({
      name: 'dataType',
      type: 'integer',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, dataTypeColumn);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'dataType');
  }
}
