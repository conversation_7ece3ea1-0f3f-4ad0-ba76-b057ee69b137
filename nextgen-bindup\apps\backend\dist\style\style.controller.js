"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StyleController = void 0;
const common_1 = require("@nestjs/common");
const style_service_1 = require("./style.service");
const auth_guard_1 = require("../auth/auth.guard");
const style_entity_1 = require("./entities/style.entity");
let StyleController = class StyleController {
    constructor(styleService) {
        this.styleService = styleService;
    }
    async create(assetEntity) {
        return await this.styleService.create(assetEntity);
    }
    async update(styleId, data) {
        return await this.styleService.update(+styleId, data);
    }
    async getById(styleId) {
        return await this.styleService.findById(+styleId);
    }
    async getByProjectId(projectId) {
        return await this.styleService.findByProjectId(+projectId);
    }
    async getBySiteId(projectId, siteId) {
        return await this.styleService.findBySiteId(+projectId, +siteId);
    }
    async delete(styleId) {
        return await this.styleService.delete(+styleId);
    }
};
exports.StyleController = StyleController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [style_entity_1.StyleEntity]),
    __metadata("design:returntype", Promise)
], StyleController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('update/:styleId'),
    __param(0, (0, common_1.Param)('styleId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], StyleController.prototype, "update", null);
__decorate([
    (0, common_1.Get)('one/:styleId'),
    __param(0, (0, common_1.Param)('styleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StyleController.prototype, "getById", null);
__decorate([
    (0, common_1.Get)('project/:projectId'),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StyleController.prototype, "getByProjectId", null);
__decorate([
    (0, common_1.Get)('site/:projectId/:siteId'),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], StyleController.prototype, "getBySiteId", null);
__decorate([
    (0, common_1.Delete)(':styleId'),
    __param(0, (0, common_1.Param)('styleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StyleController.prototype, "delete", null);
exports.StyleController = StyleController = __decorate([
    (0, common_1.Controller)('styles'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [style_service_1.StyleService])
], StyleController);
//# sourceMappingURL=style.controller.js.map