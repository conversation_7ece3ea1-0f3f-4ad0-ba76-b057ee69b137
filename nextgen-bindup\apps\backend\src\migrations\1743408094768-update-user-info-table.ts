import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateUserInfoTable1743408094768 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}user_info`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const stripeCustomerIdColumn = new TableColumn({
      name: 'stripeCustomerId',
      type: 'varchar',
      length: '250',
      isNullable: true,
    });
    const activeSubscriptionColumn = new TableColumn({
      name: 'activeSubscription',
      type: 'boolean',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, stripeCustomerIdColumn);
    await queryRunner.addColumn(this.TABLE_NAME, activeSubscriptionColumn);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'stripeCustomerId');
    await queryRunner.dropColumn(this.TABLE_NAME, 'activeSubscription');
  }
}
