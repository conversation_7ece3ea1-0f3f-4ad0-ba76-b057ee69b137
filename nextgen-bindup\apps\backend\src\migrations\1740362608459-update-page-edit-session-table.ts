import {
  MigrationInterface,
  QueryRunner,
  TableC<PERSON>umn,
  TableUnique,
} from 'typeorm';

export class UpdatePageEditSessionTable1740362608459
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}page_edit_sessions`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME} DROP CONSTRAINT unique_user_page_session;`,
    );

    await queryRunner.addColumn(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
      new TableColumn({
        name: 'siteId',
        type: 'integer',
        isNullable: false,
      }),
    );

    await queryRunner.createUniqueConstraint(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
      new TableUnique({
        name: 'unique_user_site_session',
        columnNames: ['userId', 'pageId', 'siteId', 'sessionId'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropUniqueConstraint(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
      'unique_user_site_session',
    );

    await queryRunner.dropColumn(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
      'siteId',
    );

    await queryRunner.query(
      `ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME} ADD CONSTRAINT unique_user_page_session UNIQUE ("userId", "pageId", "siteId", "sessionId");`,
    );
  }
}
