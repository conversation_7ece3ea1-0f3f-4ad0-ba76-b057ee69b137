import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ProductStockEntity } from './entities/product-stock.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { AppException } from 'src/common/exceptions/app.exception';
import { ValidateException } from 'src/common/exceptions/validate.exception';
import { isInteger, MAX_PRODUCT_QUANTITY_VALUE } from 'src/utils/common.util';
import {
  CartItem,
  CartItemResponse,
  CheckStockResponse,
  StockErrorType,
} from './dto/product-stock.dto';
import { ProductService } from 'src/product/product.service';

@Injectable()
export class ProductStocksService {
  @InjectRepository(ProductStockEntity)
  readonly productStockRepo: Repository<ProductStockEntity>;

  constructor(
    @Inject(forwardRef(() => ProductService))
    private readonly productService: ProductService,
  ) {}

  async getBySite(siteId: number): Promise<ProductStockEntity[]> {
    return await this.productStockRepo.find({
      where: { siteId: siteId },
      order: { productId: 'ASC', x: 'ASC', y: 'ASC' },
    });
  }

  async getByProduct(
    siteId: number,
    productId: number,
  ): Promise<ProductStockEntity[]> {
    return await this.productStockRepo.find({
      where: { siteId: siteId, productId: productId },
      order: { productId: 'ASC', x: 'ASC', y: 'ASC' },
    });
  }

  async getByProductIds(
    siteId: number,
    productIds: number[],
  ): Promise<ProductStockEntity[]> {
    return await this.productStockRepo.find({
      where: { siteId: siteId, productId: In(productIds) },
      order: { productId: 'ASC', x: 'ASC', y: 'ASC' },
    });
  }

  async getById(
    siteId: number,
    productId: number,
    stockId: number,
  ): Promise<ProductStockEntity> {
    return await this.productStockRepo.findOneBy({
      id: stockId,
      siteId: siteId,
      productId: productId,
    });
  }

  validateProductStockData(productStockEntity: ProductStockEntity): void {
    if (!productStockEntity.siteId)
      throw new ValidateException('error.site_id_required');

    if (!productStockEntity.productId)
      throw new ValidateException(
        'cart_management.product.error.product_required',
      );

    if (!isInteger(productStockEntity.x, { min: 0, max: 5 })) {
      throw new ValidateException('error.bad_request');
    }

    if (!isInteger(productStockEntity.y, { min: 0, max: 5 })) {
      throw new ValidateException('error.bad_request');
    }

    if (
      !isInteger(productStockEntity.quantity, {
        min: 0,
        max: MAX_PRODUCT_QUANTITY_VALUE,
      })
    ) {
      throw new ValidateException(
        'cart_management.product.error.quantity.invalid',
      );
    }
  }

  async create(entity: ProductStockEntity): Promise<ProductStockEntity> {
    const now: Date = new Date();

    const productStock = new ProductStockEntity();
    productStock.siteId = entity.siteId;
    productStock.productId = entity.productId;
    productStock.x = entity.x;
    productStock.y = entity.y;
    productStock.quantity = entity.quantity;
    productStock.createdAt = now;
    productStock.updatedAt = now;

    this.validateProductStockData(productStock);
    return await this.productStockRepo.save(productStock);
  }

  async update(
    id: number,
    entity: ProductStockEntity,
  ): Promise<ProductStockEntity> {
    const productStock = await this.productStockRepo.findOneBy({
      id: id,
    });

    if (!productStock)
      throw new AppException('api.error.product_stock_not_found');

    productStock.x = entity.x;
    productStock.y = entity.y;
    productStock.quantity = entity.quantity;
    productStock.updatedAt = new Date();

    this.validateProductStockData(productStock);
    await this.productStockRepo.update(id, productStock);
    return productStock;
  }

  async deleteByProductId(siteId: number, productId: number): Promise<void> {
    await this.productStockRepo.delete({
      siteId: siteId,
      productId: productId,
    });
  }

  async checkStock(
    siteId: number,
    cartItems: CartItem[],
  ): Promise<CheckStockResponse> {
    if (!cartItems?.length) {
      return { allItemsAvailable: true, unavailableItems: [] };
    }

    // Get all unique product IDs
    const productIds = [...new Set(cartItems.map(item => item.productId))];

    // Fetch all products and stocks in parallel
    const [products, allStocks] = await Promise.all([
      this.productService.findByIds(siteId, productIds),
      this.getByProductIds(siteId, productIds),
    ]);

    // Create maps for faster lookups
    const productsMap = new Map(products.map(p => [p.id, p]));
    const stocksMap = new Map(
      allStocks.map(stock => [
        `${stock.productId}-${stock.x}-${stock.y}`,
        stock,
      ]),
    );

    const unavailableItems: CartItemResponse[] = [];

    // Check each cart item
    for (const cartItem of cartItems) {
      const product = productsMap.get(cartItem.productId);
      if (!product) continue;

      let errorType: StockErrorType | undefined;
      let available = Infinity;

      // Check if product is orderable first
      if (!product.isOrderable) {
        errorType = StockErrorType.NOT_ORDERABLE;
        available = 0;
      }
      // Only check purchase limit and stock if not unlimited purchase
      else if (!product.unlimitedPurchase) {
        // Check purchase limit
        if (
          product.purchaseLimitQuantity > 0 &&
          cartItem.quantity > product.purchaseLimitQuantity
        ) {
          errorType = StockErrorType.EXCEED_PURCHASE_LIMIT;
          available = product.purchaseLimitQuantity;
        }
        // Check stock if no purchase limit error
        else {
          const stockKey = `${cartItem.productId}-${cartItem.x}-${cartItem.y}`;
          const stock = stocksMap.get(stockKey);
          const stockQuantity = stock?.quantity || 0;

          if (cartItem.quantity > stockQuantity) {
            errorType = StockErrorType.OUT_OF_STOCK;
            available = stockQuantity;
          }
        }
      }

      // Add to unavailable items if any error
      if (errorType) {
        unavailableItems.push({
          productId: cartItem.productId,
          siteId: siteId,
          x: cartItem.x,
          y: cartItem.y,
          quantity: cartItem.quantity,
          available,
          errorType,
        });
      }
    }

    return {
      allItemsAvailable: unavailableItems.length === 0,
      unavailableItems,
    };
  }
}
