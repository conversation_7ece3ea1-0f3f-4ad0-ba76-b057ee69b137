import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateCmsCollectionTable1743388643396
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}cms_collection`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const title: TableColumn = new TableColumn({
      name: 'title',
      type: 'jsonb',
      isNullable: false,
    });
    await queryRunner.addColumn(this.TABLE_NAME, title);

    const slug: TableColumn = new TableColumn({
      name: 'slug',
      type: 'jsonb',
      isNullable: false,
    });
    await queryRunner.addColumn(this.TABLE_NAME, slug);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'title');
    await queryRunner.dropColumn(this.TABLE_NAME, 'slug');
  }
}
