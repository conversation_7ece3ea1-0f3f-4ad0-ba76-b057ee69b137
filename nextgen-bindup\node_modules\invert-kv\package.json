{"name": "invert-kv", "version": "1.0.0", "description": "Invert the key/value of an object. Example: {foo: 'bar'} → {bar: 'foo'}", "license": "MIT", "repository": "sindresorhus/invert-kv", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["object", "obj", "key", "value", "val", "kv", "invert"], "devDependencies": {"mocha": "*"}}