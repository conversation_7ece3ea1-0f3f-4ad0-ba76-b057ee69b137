{"version": 3, "file": "user-info.service.js", "sourceRoot": "", "sources": ["../../src/user-info/user-info.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,kEAA6D;AAC7D,qCAAqC;AAG9B,IAAM,eAAe,GAArB,MAAM,eAAe;IAI1B,gBAAe,CAAC;IAEhB,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,MAAc;QACjD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAEvD,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE;YAAE,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;QAE9D,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAC5B,EAAE,MAAM,EAAE,EACV,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,CACpC,CAAC;QAEF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAc;QAC/B,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,MAAM,CACV,MAAc,EACd,IAA6B;QAE7B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QAC/D,IAAI,CAAC,QAAQ;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAE3D,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;QACjD,OAAO,EAAE,GAAG,QAAQ,EAAE,GAAG,IAAI,EAAE,CAAC;IAClC,CAAC;CACF,CAAA;AAzCY,0CAAe;AAEjB;IADR,IAAA,0BAAgB,EAAC,iCAAc,CAAC;8BACV,oBAAU;qDAAiB;0BAFvC,eAAe;IAD3B,IAAA,mBAAU,GAAE;;GACA,eAAe,CAyC3B"}