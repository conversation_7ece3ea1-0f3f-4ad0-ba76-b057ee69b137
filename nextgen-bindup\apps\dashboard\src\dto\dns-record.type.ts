export type DnsRecordDto = {
  id?: number;
  projectId: number;
  siteId: number;
  type: string;
  subdomain: string;
  value: string;
  ttl: number;
  status: string;
};

export type DnsRecordEntity = DnsRecordDto & {
  createdAt?: Date;
  updatedAt?: Date;
};

export enum DNSRecordType {
  A = 'A',
  AAA = 'AAA',
  CNAME = 'CNAME',
  MX = 'MX',
  NS = 'NS',
  TXT = 'TXT',
  SOA = 'SOA',
  PTR = 'PTR',
  SRV = 'SRV',
  CAA = 'CAA',
  DS = 'DS',
}

export enum DNSRecordStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  ERROR = 'ERROR',
  EXPIRED = 'EXPIRED',
  DELETED = 'DELETED',
  LOCKED = 'LOCKED',
}
