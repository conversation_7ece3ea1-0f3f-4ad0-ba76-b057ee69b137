"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectFoldersController = void 0;
const common_1 = require("@nestjs/common");
const project_folders_service_1 = require("./project-folders.service");
const auth_guard_1 = require("../auth/auth.guard");
const project_folders_entity_1 = require("./entities/project-folders.entity");
let ProjectFoldersController = class ProjectFoldersController {
    constructor(projectFoldersService) {
        this.projectFoldersService = projectFoldersService;
    }
    async create(projectFolder) {
        return await this.projectFoldersService.create(projectFolder);
    }
    async getProjectsByUser(projectId) {
        return await this.projectFoldersService.findByProjectId(+projectId);
    }
};
exports.ProjectFoldersController = ProjectFoldersController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [project_folders_entity_1.ProjectFolderEntity]),
    __metadata("design:returntype", Promise)
], ProjectFoldersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('project/:projectId'),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectFoldersController.prototype, "getProjectsByUser", null);
exports.ProjectFoldersController = ProjectFoldersController = __decorate([
    (0, common_1.Controller)('project-folders'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [project_folders_service_1.ProjectFoldersService])
], ProjectFoldersController);
//# sourceMappingURL=project-folders.controller.js.map