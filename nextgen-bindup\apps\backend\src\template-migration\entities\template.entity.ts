import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { TemplateSite_Page } from '../dto/template_site.dto';

@Entity('templates', { schema: process.env.DATABASE_SCHEMA })
export class TemplateEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'tmpSiteId',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  tmpSiteId: string;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string;

  @Column({
    name: 'titleDetail',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  titleDetail: string;

  @Column({
    name: 'seq',
    type: 'integer',
    nullable: false,
  })
  seq: number;

  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
  })
  description: string;

  @Column({
    name: 'category',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  category: string;

  @Column({
    name: 'smartblock',
    type: 'boolean',
    nullable: false,
  })
  smartblock: boolean;

  @Column({
    name: 'pages',
    type: 'jsonb',
    nullable: true,
  })
  pages: TemplateSite_Page[];

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
