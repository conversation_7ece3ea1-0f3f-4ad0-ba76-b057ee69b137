import {
  DELIVERY_FOOTER_TEXT,
  DELIVERY_HEADER_TEXT,
} from 'src/constant/delivery.constant';
import {
  ORDER_COMPLETION_DISPLAY_TEXT,
  ORDER_COMPLETION_EMAIL_FOOTER,
  ORDER_COMPLETION_EMAIL_HEADER,
  ORDER_COMPLETION_EMAIL_SUBJECT,
} from 'src/constant/order-completion.constant';
import {
  ACCOUNT_BANK,
  BANK_TRANSFER_DESCRIPTION,
  CASH_ON_DELIVERY_DESCRIPTION,
  POSTAL_TRANSFER_DESCRIPTION,
  STRIPE_DESCRIPTION,
} from 'src/constant/payment-method.constant';
import { SHIPPING_NOTE } from 'src/constant/shipping-note.constant';

export const getDefaultSetting = (shopName: string, email: string) => {
  return {
    shippingNote: {
      note: SHIPPING_NOTE,
    },
    paymentMethod: {
      bankTransfer: {
        isEnabled: false,
        bankAccount: ACCOUNT_BANK,
        description: BANK_TRANSFER_DESCRIPTION,
      },
      postalTransfer: {
        isEnabled: false,
        bankAccount: ACCOUNT_BANK,
        description: POSTAL_TRANSFER_DESCRIPTION,
      },
      stripePaymentGateway: {
        isEnabled: false,
        description: STRIPE_DESCRIPTION,
        stripeAccountId: '',
      },
      cashOnDelivery: {
        isEnabled: false,
        description: CASH_ON_DELIVERY_DESCRIPTION,
        fee: [
          {
            fromAmount: 0,
            codFee: 0,
          },
        ],
      },
    },
    delivery: {
      headerText: DELIVERY_HEADER_TEXT(shopName, email),
      footerText: DELIVERY_FOOTER_TEXT(shopName, email),
    },
    orderCompletion: {
      displayText: ORDER_COMPLETION_DISPLAY_TEXT,
      emailSubject: ORDER_COMPLETION_EMAIL_SUBJECT(shopName),
      emailHeader: ORDER_COMPLETION_EMAIL_HEADER(shopName),
      emailFooter: ORDER_COMPLETION_EMAIL_FOOTER(shopName, email),
    },
  };
};
