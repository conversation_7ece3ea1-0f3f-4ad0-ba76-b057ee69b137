import { Modu<PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { VersionHistoryModule } from 'src/version-history/version-history.module';
import { IdentityModule } from 'src/identity/identity.module';
import { UserInfoModule } from 'src/user-info/user-info.module';

@Module({
  imports: [VersionHistoryModule, IdentityModule, UserInfoModule],
  controllers: [AuthController],
  providers: [AuthService],
})
export class AuthModule {}
