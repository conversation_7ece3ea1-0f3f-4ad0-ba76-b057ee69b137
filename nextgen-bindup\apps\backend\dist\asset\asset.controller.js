"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetController = void 0;
const common_1 = require("@nestjs/common");
const asset_service_1 = require("./asset.service");
const auth_guard_1 = require("../auth/auth.guard");
const asset_entity_1 = require("./entities/asset.entity");
let AssetController = class AssetController {
    constructor(assetService) {
        this.assetService = assetService;
    }
    async create(assetEntity) {
        return await this.assetService.create(assetEntity);
    }
    async update(assetId, data) {
        return await this.assetService.update(+assetId, data);
    }
    async getById(assetId) {
        return await this.assetService.findById(+assetId);
    }
    async getByProjectId(projectId) {
        return await this.assetService.findByProjectId(+projectId);
    }
    async delete(assetId) {
        return await this.assetService.delete(+assetId);
    }
    async findByUrl(url) {
        return await this.assetService.findByUrl(url);
    }
};
exports.AssetController = AssetController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [asset_entity_1.AssetEntity]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('update/:assetId'),
    __param(0, (0, common_1.Param)('assetId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "update", null);
__decorate([
    (0, common_1.Get)('one/:assetId'),
    __param(0, (0, common_1.Param)('assetId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "getById", null);
__decorate([
    (0, common_1.Get)('project/:projectId'),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "getByProjectId", null);
__decorate([
    (0, common_1.Delete)(':assetId'),
    __param(0, (0, common_1.Param)('assetId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "delete", null);
__decorate([
    (0, common_1.Get)('url/:url'),
    __param(0, (0, common_1.Param)('url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetController.prototype, "findByUrl", null);
exports.AssetController = AssetController = __decorate([
    (0, common_1.Controller)('assets'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [asset_service_1.AssetService])
], AssetController);
//# sourceMappingURL=asset.controller.js.map