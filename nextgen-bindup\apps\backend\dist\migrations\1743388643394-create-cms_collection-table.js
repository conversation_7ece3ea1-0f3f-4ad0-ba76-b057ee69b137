"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsCollection1743388643394 = void 0;
const typeorm_1 = require("typeorm");
class CreateCmsCollection1743388643394 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}cms_collection`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'siteId',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'struct',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'rootUserId',
                    type: 'varchar',
                    length: '36',
                    isNullable: false,
                },
                {
                    name: 'userId',
                    type: 'varchar',
                    length: '36',
                    isNullable: false,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_cms_collection_siteid',
            columnNames: ['siteId'],
            isUnique: false,
        }));
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_cms_collection_rootuserid',
            columnNames: ['rootUserId'],
            isUnique: false,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_cms_collection_siteid');
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_cms_collection_rootuserid');
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateCmsCollection1743388643394 = CreateCmsCollection1743388643394;
//# sourceMappingURL=1743388643394-create-cms_collection-table.js.map