import { Module } from '@nestjs/common';
import { AssetComponentController } from './asset-component.controller';
import { AssetComponentService } from './asset-component.service';
import { AssetComponent } from './entities/asset-component.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProjectModule } from 'src/project/project.module';

@Module({
  imports: [TypeOrmModule.forFeature([AssetComponent]), ProjectModule],
  controllers: [AssetComponentController],
  providers: [AssetComponentService],
  exports: [AssetComponentService],
})
export class ContentModule {}
