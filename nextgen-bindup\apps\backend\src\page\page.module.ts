import { forwardRef, Module } from '@nestjs/common';
import { PageController } from './page.controller';
import { PageService } from './page.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PageEntity } from './entities/page.entity';
import { PublishModule } from 'src/publish/publish.module';
import { ZipService } from './zip.service';
import { CmsCollectionModule } from 'src/cms-collection/cms-collection.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PageEntity]),
    forwardRef(() => PublishModule),
    CmsCollectionModule,
  ],
  controllers: [PageController],
  providers: [PageService, ZipService],
  exports: [PageService],
})
export class PageModule {}
