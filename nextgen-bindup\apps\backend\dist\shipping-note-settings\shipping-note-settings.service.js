"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShippingNoteSettingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
const shipping_note__settings_entity_1 = require("./entities/shipping-note--settings.entity");
let ShippingNoteSettingService = class ShippingNoteSettingService {
    constructor() { }
    async create(shippingNoteSettingEntity) {
        const now = new Date();
        const setting = new shipping_note__settings_entity_1.ShippingNoteSettingEntity();
        setting.siteId = shippingNoteSettingEntity.siteId;
        setting.shippingFee = shippingNoteSettingEntity.shippingFee;
        setting.isFreeShippingCondition =
            shippingNoteSettingEntity.isFreeShippingCondition;
        setting.shippingFeeDetail = shippingNoteSettingEntity.shippingFeeDetail;
        setting.note = shippingNoteSettingEntity.note;
        setting.createdAt = now;
        setting.updatedAt = now;
        return await this.shippingNoteSettingRepo.save(setting);
    }
    async update(id, settingData) {
        const setting = await this.shippingNoteSettingRepo.findOneBy({ id: id });
        if (!setting)
            throw new app_exception_1.AppException('api.error.shipping_note_setting_not_found');
        delete settingData.id;
        delete settingData.siteId;
        delete settingData.createdAt;
        settingData.updatedAt = new Date();
        await this.shippingNoteSettingRepo.update(id, settingData);
        return { ...setting, ...settingData };
    }
    async findById(id) {
        return await this.shippingNoteSettingRepo.findOneBy({ id });
    }
    async findOneBySiteId(siteId) {
        return await this.shippingNoteSettingRepo.findOneBy({ siteId });
    }
    async delete(id) {
        const setting = await this.shippingNoteSettingRepo.findOneBy({ id });
        if (!setting)
            throw new app_exception_1.AppException('api.error.shipping_note_setting_not_found');
        await this.shippingNoteSettingRepo.delete(id);
        return true;
    }
};
exports.ShippingNoteSettingService = ShippingNoteSettingService;
__decorate([
    (0, typeorm_1.InjectRepository)(shipping_note__settings_entity_1.ShippingNoteSettingEntity),
    __metadata("design:type", typeorm_2.Repository)
], ShippingNoteSettingService.prototype, "shippingNoteSettingRepo", void 0);
exports.ShippingNoteSettingService = ShippingNoteSettingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], ShippingNoteSettingService);
//# sourceMappingURL=shipping-note-settings.service.js.map