import { DnsRecordService } from './dns-record.service';
import { DnsRecordEntity } from './entities/dns-record.entity';
export declare class DnsRecordController {
    private readonly dnsRecordService;
    constructor(dnsRecordService: DnsRecordService);
    create(data: DnsRecordEntity): Promise<DnsRecordEntity>;
    update(id: string, data: Partial<DnsRecordEntity>): Promise<DnsRecordEntity>;
    delete(id: string): Promise<boolean>;
    findBySiteId(siteId: string): Promise<DnsRecordEntity[]>;
    findByProjectId(projectId: string): Promise<DnsRecordEntity[]>;
}
