import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ProductStocksService } from './product-stocks.service';
import { CartItem, CheckStockResponse } from './dto/product-stock.dto';

@Controller('product-stocks')
export class ProductStocksController {
  constructor(private readonly productStockService: ProductStocksService) {}

  @Get('product/:siteId/:productId')
  async getAll(
    @Param('siteId') siteId: string,
    @Param('productId') productId: string,
  ) {
    return await this.productStockService.getByProduct(+siteId, +productId);
  }

  @Post('check-stock/:siteId')
  async checkStock(
    @Param('siteId') siteId: string,
    @Body() { cartItems }: { cartItems: CartItem[] },
  ): Promise<CheckStockResponse> {
    return await this.productStockService.checkStock(+siteId, cartItems);
  }
}
