import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateOrderTable1747620556149 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}orders`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      this.TABLE_NAME,
      'lastNameKana',
      new TableColumn({
        name: 'lastNameKana',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
    );

    await queryRunner.changeColumn(
      this.TABLE_NAME,
      'firstNameKana',
      new TableColumn({
        name: 'firstNameKana',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
    );

    await queryRunner.addColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'shippingLastName',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'shippingFirstName',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'shippingLastNameKana',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'shippingFirstNameKana',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'shippingEmail',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'shippingPostalCode',
        type: 'varchar',
        length: '10',
        isNullable: true,
      }),
      new TableColumn({
        name: 'shippingPrefecture',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'shippingAddressLine1',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'shippingAddressLine2',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'shippingPhoneNumber',
        type: 'varchar',
        length: '20',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      this.TABLE_NAME,
      'lastNameKana',
      new TableColumn({
        name: 'lastNameKana',
        type: 'varchar',
        length: '255',
        isNullable: false,
      }),
    );

    await queryRunner.changeColumn(
      this.TABLE_NAME,
      'firstNameKana',
      new TableColumn({
        name: 'firstNameKana',
        type: 'varchar',
        length: '255',
        isNullable: false,
      }),
    );

    await queryRunner.dropColumns(this.TABLE_NAME, [
      'shippingLastName',
      'shippingFirstName',
      'shippingLastNameKana',
      'shippingFirstNameKana',
      'shippingEmail',
      'shippingPostalCode',
      'shippingPrefecture',
      'shippingAddressLine1',
      'shippingAddressLine2',
      'shippingPhoneNumber',
    ]);
  }
}
