import { SiteStatus } from './site.type';

export interface VersionHistoryDto {
  data: SiteVersionEntity[];
  total: number;
  page: number;
  pageSize: number;
}

export interface SiteVersionDto {
  id: number;
  siteId: number;
  projectId: number;
  projectFolderId: number;
  managementName: string;
  status: SiteStatus;
  url: string;
  title: string;
  description: string;
  isSearch: boolean;
  thumb: string;
  headCode: string;
  bodyCode: string;
  isArchived: boolean;
  versionCreatedAt: Date;
  versionName: string;
}

export interface SiteVersionEntity extends SiteVersionDto {
  createdAt: Date;
  updatedAt: Date;
}
