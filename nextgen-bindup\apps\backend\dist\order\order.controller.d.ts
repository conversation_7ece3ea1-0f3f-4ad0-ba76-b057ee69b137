import { OrderService } from './order.service';
import { GetOrdersQueryDto } from './dto/get-order.dto';
import { Response } from 'express';
export declare class OrderController {
    private readonly orderService;
    constructor(orderService: OrderService);
    getOrders(siteId: string, query: GetOrdersQueryDto): Promise<import("../common/paginated-response").PaginatedResponse<import("./entites/order.entity").OrderEntity>>;
    getOrderById(id: string): Promise<import("./entites/order.entity").OrderEntity>;
    markAsPaid(id: string): Promise<import("./entites/order.entity").OrderEntity>;
    downloadCsv(siteId: string, query: GetOrdersQueryDto, res: Response): Promise<void>;
}
