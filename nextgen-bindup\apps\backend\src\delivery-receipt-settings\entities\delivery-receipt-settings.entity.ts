import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('delivery_receipt_settings', { schema: process.env.DATABASE_SCHEMA })
export class DeliveryReceiptSettingEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @Column({
    name: 'header',
    type: 'text',
    nullable: true,
  })
  header: string;

  @Column({
    name: 'footer',
    type: 'text',
    nullable: true,
  })
  footer: string;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
