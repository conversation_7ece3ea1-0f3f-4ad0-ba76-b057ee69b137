export class CreateOrderItemDto {
  productId: number;
  quantity: number;
  attributes?: Record<string, string>;
}

export class CreateOrderDto {
  projectId: number;
  siteId: number;
  contact: Address;
  shipping: Address;
  additionalInformation?: string;
  orderItems: CreateOrderItemDto[];
}

export class Address {
  lastName: string;
  firstName: string;
  lastNameKana: string;
  firstNameKana: string;
  email: string;
  postalCode: string;
  prefecture: string;
  addressLine1: string;
  addressLine2: string;
  phoneNumber: string;
}
