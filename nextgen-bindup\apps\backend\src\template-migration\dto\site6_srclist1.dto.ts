export enum Srclist1_Category {
  FAVICON = 'fv',
  WEB_CLIP = 'wc',
  PAGE_AREA_BG_IMAGE = 'pg',
  BLOCK_BG_IMAGE = 'bd',
  SIGN = 'sign',
  SHIFT = 'shift',
  LINK = 'link',
  DOWNLOAD = 'download',
  VIDEO = 'movie',
  ID = 'id',
  LIVE = 'live',
}

export enum Srclist1_Area {
  ENTIRE_PAGE_ROW_1ST = 0,
  ENTIRE_PAGE_ROW_2ND = 1,
  HEADER = 2,
  BILLBOARD = 3,
  MAIN = 4,
  SITE_A = 5,
  SITE_B = 6,
  FOOTER = 7,
  CONTENT = 8,
}

export interface Site6_Srclist1 {
  tmpSiteId: number;
  srclistId: number;
  siteId: number;
  category: Srclist1_Category;
  bgRefId: number;
  areaNo: Srclist1_Area;
  blockdataId: number;
  resourceId: number;
  seq: number;
  filename: string;
  signXml: string; // XML - encoded
  signXmlJson: Srclist1_SignXml;

  imageDataThumbnail?: string;

  delFlg: number;
  insDate: string;
  updDate: string;
}

export interface Srclist1_SignXml {
  project: Srclist1_SignXml_Project;
}

export interface Srclist1_SignXml_Project {
  version: string;
  internal_version: string;
  common: Srclist1_SignXml_Project_Common;
  Layers: Srclist1_SignXml_Project_Layers;
}

export interface Srclist1_SignXml_Project_Common {
  formatIndex: string;
  color: string;
  transparency: string;
  compressionRate: string;
  stageheight: string;
  stagewidth: string;
}

export interface Srclist1_SignXml_Project_Layers {
  layer: Srclist1_SignXml_Project_Layer;
}

export interface Srclist1_SignXml_Project_Layer {
  properties: Srclist1_SignXml_Project_Layer_Props;
  type: string;
  uniqid: string;
}

export interface Srclist1_SignXml_Project_Layer_Props {
  geometry: string;
  blendMode: string;
  orgPath: string;
  shapeprop: Srclist1_SignXml_Project_Layer_Props_ShapeProp;
  data: Srclist1_SignXml_Project_Layer_Props_Data;
  imageporp: Srclist1_SignXml_Project_Layer_Props_ImagePorp;
}

export interface Srclist1_SignXml_Project_Layer_Props_ShapeProp {
  bkcolor: string;
  bkalpha: string;
  strokeThin: string;
  strokeColor: string;
  contentRectX: string;
  contentRectY: string;
  contentRectW: string;
  contentRectH: string;
}

export interface Srclist1_SignXml_Project_Layer_Props_Data {
  BitmapImage: Srclist1_SignXml_Project_Layer_Props_Data_BitmapImage;
  mask: Srclist1_SignXml_Project_Layer_Props_Data_Mask;
}

export interface Srclist1_SignXml_Project_Layer_Props_Data_BitmapImage {
  width: string;
  height: string;
  x: string;
  y: string;
  source: string;
}

export interface Srclist1_SignXml_Project_Layer_Props_Data_Mask {
  Rect: Srclist1_SignXml_Project_Layer_Props_Data_Mask_Rect;
}

export interface Srclist1_SignXml_Project_Layer_Props_Data_Mask_Rect {
  fill: Srclist1_SignXml_Project_Layer_Props_Data_Mask_Rect_Fill;
  width: string;
  height: string;
  x: string;
  y: string;
}

export interface Srclist1_SignXml_Project_Layer_Props_Data_Mask_Rect_Fill {
  SolidColor: Srclist1_SignXml_Project_Layer_Props_Data_Mask_Rect_Fill_SolidColor;
}

export interface Srclist1_SignXml_Project_Layer_Props_Data_Mask_Rect_Fill_SolidColor {
  alpha: string;
  color: string;
}

export interface Srclist1_SignXml_Project_Layer_Props_ImagePorp {
  imageScale: string;
}
