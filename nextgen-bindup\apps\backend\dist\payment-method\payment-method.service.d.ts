import { PaymentMethodEntity } from './entities/payment-method.entity';
import { Repository } from 'typeorm';
import { PaymentMethodResponseDto } from './dto/payment-method-response.dto';
import { ConfigService } from '@nestjs/config';
import { StripeInformation } from './dto/stripe-infomation.dto';
export declare class PaymentMethodService {
    private readonly configService;
    private stripe;
    readonly paymentMethodRepo: Repository<PaymentMethodEntity>;
    constructor(configService: ConfigService);
    getPaymentMethodBySiteId(siteId: number): Promise<PaymentMethodResponseDto>;
    updatePaymentMethod(siteId: number, paymentMethod: Partial<PaymentMethodEntity>): Promise<boolean>;
    enableStripePaymentGateway(siteId: number): Promise<{
        url: string;
    }>;
    checkStripeStatus(siteId: number): Promise<StripeInformation>;
    removeStripeAccount(siteId: number): Promise<boolean>;
}
