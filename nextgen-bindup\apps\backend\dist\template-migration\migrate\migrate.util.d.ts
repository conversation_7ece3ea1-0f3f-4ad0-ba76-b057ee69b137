import { Component } from '@nextgen-bindup/common/dto/component';
import { PropertyDto } from '@nextgen-bindup/common/dto/component-properties/general-prop.dto';
import { BlockData_LayoutOpt } from '../dto/site4_blockdata.dto';
import { LayoutFlexPropDto } from '@nextgen-bindup/common/dto/setting-properties/layout/layout-flex-prop.dto';
export declare const DEFAULT_TEMPLATE_COMPONENT: Record<string, Component>;
export declare const DEFAULT_FLEX_PROP: LayoutFlexPropDto;
export declare class MigrateUtil {
    static blockProps(): PropertyDto;
    static getColumns: (layoutOptID: BlockData_LayoutOpt) => 1 | 5 | 2 | 3 | 4;
}
