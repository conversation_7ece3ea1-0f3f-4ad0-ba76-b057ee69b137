import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ShopInformationSettingEntity } from './entities/shop-information-settings.entity';
import { ShopInformationSettingController } from './shop-information-settings.controller';
import { ShopInformationSettingService } from './shop-information-settings.service';
import { SiteModule } from 'src/site/site.module';
import { ShippingNoteSettingEntity } from '../shipping-note-settings/entities/shipping-note--settings.entity';
import { DeliveryReceiptSettingEntity } from '../delivery-receipt-settings/entities/delivery-receipt-settings.entity';
import { OrderCompletionSettingEntity } from '../order-complete-settings/entities/order-complete-settings.entity';
import { PaymentMethodEntity } from 'src/payment-method/entities/payment-method.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ShopInformationSettingEntity,
      ShippingNoteSettingEntity,
      PaymentMethodEntity,
      DeliveryReceiptSettingEntity,
      OrderCompletionSettingEntity,
    ]),
    SiteModule,
  ],
  controllers: [ShopInformationSettingController],
  providers: [ShopInformationSettingService],
  exports: [ShopInformationSettingService],
})
export class ShopInformationSettingModule {}
