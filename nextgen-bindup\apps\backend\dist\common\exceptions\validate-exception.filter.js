"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidateExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const validate_exception_1 = require("./validate.exception");
const logger_service_1 = require("../../logger/logger.service");
let ValidateExceptionFilter = class ValidateExceptionFilter {
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const status = exception.getStatus();
        if (response.locals.requestLog) {
            this.loggerService.info(response.locals.shop, exception.message, {
                meta: response.locals.requestLog,
            });
            response.locals.requestLog = undefined;
        }
        response.status(status).json({ data: exception.message });
    }
};
exports.ValidateExceptionFilter = ValidateExceptionFilter;
exports.ValidateExceptionFilter = ValidateExceptionFilter = __decorate([
    (0, common_1.Catch)(validate_exception_1.ValidateException),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], ValidateExceptionFilter);
//# sourceMappingURL=validate-exception.filter.js.map