import { PaymentService } from './payment.service';
export declare class PaymentController {
    private readonly paymentService;
    constructor(paymentService: PaymentService);
    getUserSubscription(userId: string): Promise<import("./dto/payment.dto").SubscriptionResponse>;
    getHistorySubscription(userId: string): Promise<import("./dto/payment.dto").SubscriptionResponse[]>;
    cancelSubscription(req: any): Promise<boolean>;
    getPlans(): Promise<import("./dto/payment.dto").PlanDto[]>;
    createCheckout(req: any): Promise<{
        url: string;
    }>;
    handleWebhook(req: any, res: any, signature: string): Promise<void>;
}
