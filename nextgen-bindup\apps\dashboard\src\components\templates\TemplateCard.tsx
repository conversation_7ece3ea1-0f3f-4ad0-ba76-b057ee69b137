import { type FC } from 'react';
import ImageIcon from '@mui/icons-material/Image';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import { Link } from '@tanstack/react-router';

type Props = {
  templateId: number;
  name: string;
};

const TemplateCard: FC<Props> = ({ templateId, name }) => (
  <Card sx={{ width: 360, height: 284 }}>
    <Link to="/templates/$templateId" params={{ templateId: `${templateId}` }}>
      <CardContent
        sx={{
          height: 240,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#0000008F',
          color: '#fff',
        }}
      >
        <ImageIcon sx={{ fontSize: 48 }} />
      </CardContent>
    </Link>
    <CardActions>
      <Typography>{name}</Typography>
    </CardActions>
  </Card>
);

export default TemplateCard;
