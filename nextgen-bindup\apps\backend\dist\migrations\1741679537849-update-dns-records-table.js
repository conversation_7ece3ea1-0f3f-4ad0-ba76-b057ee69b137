"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateDnsRecordsTable1741679537849 = void 0;
const typeorm_1 = require("typeorm");
class UpdateDnsRecordsTable1741679537849 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}dns_records`;
    }
    async up(queryRunner) {
        const column = new typeorm_1.TableColumn({
            name: 'siteId',
            type: 'integer',
            isNullable: false,
        });
        await queryRunner.addColumn(this.TABLE_NAME, column);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
    }
}
exports.UpdateDnsRecordsTable1741679537849 = UpdateDnsRecordsTable1741679537849;
//# sourceMappingURL=1741679537849-update-dns-records-table.js.map