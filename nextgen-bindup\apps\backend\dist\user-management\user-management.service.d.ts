import { IdentityService } from 'src/identity/identity.service';
import { InviteMemberReq } from './dto/invite-member.dto';
import { DataSource } from 'typeorm';
import { UpdateMemberOfTeamReq } from './dto/update-member-of-team.dto';
export declare class UserManagementService {
    private readonly identityService;
    private dataSource;
    constructor(identityService: IdentityService, dataSource: DataSource);
    inviteMemberToTeam(rootUserId: string, input: InviteMemberReq): Promise<void>;
    updateMemberOfTeam(rootUserId: string, input: UpdateMemberOfTeamReq): Promise<void>;
}
