"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PROP_TRANSFORM_DEFAULT_VALUE = void 0;
const PROP_TRANSFORM_DEFAULT_VALUE = (ts, prop) => ({
    move: {
        x: { value: '0', unit: 'px' },
        y: { value: '0', unit: 'px' },
    },
    expand: {
        x: { value: '0', unit: '%' },
    },
    rotate: 0,
    distort: {
        x: { value: '0', unit: '%' },
        y: { value: '0', unit: '%' },
    },
    origin: '中心',
    ts: ts,
    ...(prop || undefined),
});
exports.PROP_TRANSFORM_DEFAULT_VALUE = PROP_TRANSFORM_DEFAULT_VALUE;
//# sourceMappingURL=prop-transform-default-value.js.map