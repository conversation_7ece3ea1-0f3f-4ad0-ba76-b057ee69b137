import { SiteEntity } from './entities/site.entity';
import { DataSource, Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { ProjectService } from 'src/project/project.service';
import { CmsCollectionService } from 'src/cms-collection/cms-collection.service';
export declare class SiteService {
    private readonly configService;
    private readonly projectService;
    private readonly cmsCollectionService;
    private readonly dataSource;
    readonly siteRepo: Repository<SiteEntity>;
    constructor(configService: ConfigService, projectService: ProjectService, cmsCollectionService: CmsCollectionService, dataSource: DataSource);
    private initCollection;
    create(siteEntity: SiteEntity): Promise<SiteEntity>;
    update(siteId: number, siteData: Partial<SiteEntity>): Promise<SiteEntity>;
    getURLSite(_siteId: number): Promise<string>;
    clone(siteId: number): Promise<SiteEntity>;
    findById(siteId: number): Promise<SiteEntity>;
    findByProjectId(projectId: number): Promise<SiteEntity[]>;
    findByProjectFolderId(projectId: number, projectFolderId: number): Promise<SiteEntity[]>;
    findByIds(ids: number[]): Promise<SiteEntity[]>;
    delete(siteId: number): Promise<boolean>;
    updateArchive(siteId: number, archive: boolean): Promise<SiteEntity>;
}
