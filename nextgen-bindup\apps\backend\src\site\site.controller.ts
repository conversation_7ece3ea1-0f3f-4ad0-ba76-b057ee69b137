import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { SiteService } from './site.service';
import { SiteEntity } from './entities/site.entity';
import { AuthGuard } from 'src/auth/auth.guard';

@Controller('sites')
@UseGuards(AuthGuard)
export class SiteController {
  constructor(private readonly siteService: SiteService) {}

  @Post('create')
  async create(@Body() siteEntity: SiteEntity) {
    return await this.siteService.create(siteEntity);
  }

  @Put('update/:siteId')
  async update(
    @Param('siteId') siteId: string,
    @Body() siteData: Partial<SiteEntity>,
  ) {
    return await this.siteService.update(+siteId, siteData);
  }

  @Get('get-site-url/:siteId')
  async getUrl(@Param('siteId') siteId: string) {
    return await this.siteService.getURLSite(+siteId);
  }

  @Get('one/:siteId')
  async getById(@Param('siteId') siteId: string) {
    return await this.siteService.findById(+siteId);
  }

  @Get('project/:projectId')
  async getByProjectId(@Param('projectId') projectId: string) {
    return await this.siteService.findByProjectId(+projectId);
  }

  @Get('project-folder/:projectId/:projectFolderId')
  async getByProjectFolderId(
    @Param('projectId') projectId: string,
    @Param('projectFolderId') projectFolderId: string,
  ) {
    if (!projectFolderId)
      return await this.siteService.findByProjectId(+projectId);

    return await this.siteService.findByProjectFolderId(
      +projectId,
      +projectFolderId,
    );
  }

  @Post('many')
  async getByIds(@Body() data: { ids: number[] }) {
    return await this.siteService.findByIds(data.ids);
  }

  @Delete(':siteId')
  async delete(@Param('siteId') siteId: string) {
    await this.siteService.updateArchive(+siteId, true);
    return true;
  }

  @Post('clone/:siteId')
  async clone(@Param('siteId') siteId: string) {
    return await this.siteService.clone(+siteId);
  }
}
