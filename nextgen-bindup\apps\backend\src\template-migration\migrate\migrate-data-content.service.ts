import { NEW_TS } from 'src/utils/common.util';
import { Component } from '@nextgen-bindup/common/dto/component';
import { BlockData_Group } from 'src/template-migration/dto/blockdata-content.dto';
import { Site3_Block } from 'src/template-migration/dto/site3_block.dto';
import {
  BlockData_Info,
  BlockData_Layout,
  BlockData_LayoutOpt,
  Site4_BlockData,
} from 'src/template-migration/dto/site4_blockdata.dto';
import { MigrateUtil } from './migrate.util';
import { PropertyDto } from '@nextgen-bindup/common/dto/component-properties/general-prop.dto';
import { ComponentType } from 'src/page/types/component.type';
import {
  BackgroundListPropDto,
  BgImagePropDto,
} from '@nextgen-bindup/common/dto/setting-properties/background-prop.dto';
import { PROP_BACKGROUND_LIST_DEFAULT_VALUE } from 'src/page/utils/prop-background-default-value';

export class MigrateDataContentService {
  components: Record<string, Component>;
  block: Site3_Block;
  blockData: Site4_BlockData;
  dataGroup: BlockData_Group;
  ts: number;

  constructor(inp: { components: Record<string, Component> }) {
    this.components = inp.components;
  }

  migrateDataGroup(inp: {
    parentComponentId: string;
    block: Site3_Block;
    blockData: Site4_BlockData;
    dataGroup: BlockData_Group;
  }): Component {
    this.block = inp.block;
    this.blockData = inp.blockData;
    this.dataGroup = inp.dataGroup;
    this.ts = NEW_TS();

    const id: string = `group${this.block.areaId}_${this.block.blockId}_${this.dataGroup.index}`;
    const properties: PropertyDto = this.createGroupProp();

    const groupBlock: Component = {
      id: id,
      type: ComponentType.Block,
      name: id,
      parentId: inp.parentComponentId,
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: this.ts },
        phone: { ts: this.ts },
      },
      ts: this.ts,
    };
    this.components[groupBlock.id] = groupBlock;
    this.components[groupBlock.parentId].children.push(groupBlock.id);

    // for (const dataContent of this.dataGroup.contents) {
    //   this.createContentBlock({
    //     ts: ts,
    //     idIndex: idIndex++,
    //     blockData: blockData,
    //     parentId: groupBlock.id,
    //     dataContent: dataContent,
    //     extra: {
    //       areaId: inp.areaId,
    //     },
    //   });
    // }

    return groupBlock;
  }

  // =========================================
  private createGroupProp(): PropertyDto {
    let properties: PropertyDto = MigrateUtil.blockProps();

    const layoutID: BlockData_Layout =
      this.blockData.blockdataInfoJson.blockdata_layoutID;

    switch (layoutID) {
      case BlockData_Layout.PLAIN:
        properties = this.createGroupPropPlain(properties);
        break;

      case BlockData_Layout.ASYMM:
        properties = this.createGroupPropAsymm(properties);
        break;

      case BlockData_Layout.TABLE:
        properties = this.createGroupPropTable(properties);
        break;

      case BlockData_Layout.ALBUM:
        properties = this.createGroupPropAlbum(properties);
        break;

      case BlockData_Layout.TAB:
        properties = this.createGroupPropTab(properties);
        break;

      case BlockData_Layout.ACCORDION:
        properties = this.createGroupPropAccordion(properties);
        break;
    }

    return properties;
  }

  private createGroupPropPlain(properties: PropertyDto): PropertyDto {
    return properties;
  }

  private createGroupPropAsymm(properties: PropertyDto): PropertyDto {
    const layoutOptID: BlockData_LayoutOpt =
      this.blockData.blockdataInfoJson.blockdata_layoutOptID;

    properties.flexItem = {
      flexGrow:
        layoutOptID === BlockData_LayoutOpt.RIGHT_WIDEL &&
        this.dataGroup.index === 2
          ? '2'
          : '1',
      ts: this.ts,
    };
    return properties;
  }

  private createGroupPropTable(properties: PropertyDto): PropertyDto {
    const infoJson: BlockData_Info = this.blockData.blockdataInfoJson;
    if (infoJson.blockdata_skinNo !== '1') return properties;

    // background
    const bgProps: BackgroundListPropDto = PROP_BACKGROUND_LIST_DEFAULT_VALUE(
      this.ts,
    );

    bgProps.list.push({
      type: 'image',
      url: 'https://tuekgiwcwyuckzgwxejb.supabase.co/storage/v1/object/public/bind/theme/default08/blockskin/skin-3/index_bg.gif',
      position: {
        x: { value: '0', unit: 'px' },
        y: { value: '0', unit: 'px' },
        offsetX: { value: '0', unit: 'px' },
        offsetY: { value: '0', unit: 'px' },
      },
      repeat: 'repeat',
      visibility: true,
    } as BgImagePropDto);

    // border
    properties.border.top = {
      color: '#bbb',
      width: { value: '1', unit: 'px' },
      borderStyle: 'solid',
    };

    const columns: number = MigrateUtil.getColumns(
      infoJson.blockdata_layoutOptID,
    );
    if (this.dataGroup.index % columns !== 1) {
      properties.border.isDetail = true;
      properties.border.right = {
        color: '#bbb',
        width: { value: '1', unit: 'px' },
        borderStyle: 'solid',
      };
      properties.border.bottom = {
        color: '#bbb',
        width: { value: '1', unit: 'px' },
        borderStyle: 'solid',
      };
    }

    // padding
    properties.marginPadding.padding = {
      ts: this.ts,
      top: {
        unit: 'px',
        value: '10',
      },
      left: {
        unit: 'px',
        value: '10',
      },
      right: {
        unit: 'px',
        value: '10',
      },
      bottom: {
        unit: 'px',
        value: '0',
      },
      isDetail: true,
    };

    return properties;
  }

  private createGroupPropAlbum(properties: PropertyDto): PropertyDto {
    return properties;
  }

  private createGroupPropTab(properties: PropertyDto): PropertyDto {
    return properties;
  }

  private createGroupPropAccordion(properties: PropertyDto): PropertyDto {
    return properties;
  }
}
