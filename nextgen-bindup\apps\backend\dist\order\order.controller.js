"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderController = void 0;
const common_1 = require("@nestjs/common");
const order_service_1 = require("./order.service");
const get_order_dto_1 = require("./dto/get-order.dto");
const auth_guard_1 = require("../auth/auth.guard");
let OrderController = class OrderController {
    constructor(orderService) {
        this.orderService = orderService;
    }
    async getOrders(siteId, query) {
        return await this.orderService.getOrders(siteId, query);
    }
    async getOrderById(id) {
        return await this.orderService.findOrderById(+id);
    }
    async markAsPaid(id) {
        return await this.orderService.markAsPaid(+id);
    }
    async downloadCsv(siteId, query, res) {
        const csvStream = await this.orderService.downloadCSV(siteId, query);
        res.header('Content-Type', 'text/csv');
        res.header('Content-Disposition', `attachment; filename="orders_${siteId}_${new Date().toISOString().slice(0, 10)}.csv"`);
        res.header('Cache-Control', 'no-cache');
        csvStream.pipe(res);
    }
};
exports.OrderController = OrderController;
__decorate([
    (0, common_1.Get)('search/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, get_order_dto_1.GetOrdersQueryDto]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "getOrders", null);
__decorate([
    (0, common_1.Get)('one/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "getOrderById", null);
__decorate([
    (0, common_1.Post)('mark-as-paid/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "markAsPaid", null);
__decorate([
    (0, common_1.Get)('download/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, get_order_dto_1.GetOrdersQueryDto, Object]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "downloadCsv", null);
exports.OrderController = OrderController = __decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Controller)('order'),
    __metadata("design:paramtypes", [order_service_1.OrderService])
], OrderController);
//# sourceMappingURL=order.controller.js.map