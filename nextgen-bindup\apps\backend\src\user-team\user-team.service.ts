import { Injectable } from '@nestjs/common';
import { UserTeamEntity } from './entities/user-team.entity';
import { DataSource, Repository } from 'typeorm';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { UserTeamDto } from './dto/user-team.dto';

@Injectable()
export class UserTeamService {
  @InjectRepository(UserTeamEntity)
  readonly userTeamRepo: Repository<UserTeamEntity>;

  constructor(@InjectDataSource() private dataSource: DataSource) {}

  async getAllByRootUserId(rootUserId: string): Promise<UserTeamEntity[]> {
    return await this.userTeamRepo.find({ where: { rootUserId } });
  }

  async getAllByTeamId(
    rootUserId: string,
    teamId: number,
  ): Promise<UserTeamEntity[]> {
    return await this.userTeamRepo.find({
      where: { rootUserId: rootUserId, teamId: teamId },
    });
  }

  async getMemberInfoByTeamId(
    rootUserId: string,
    teamId: number,
  ): Promise<UserTeamDto[]> {
    const dataRaw = await this.dataSource.manager.query<UserTeamDto[]>(
      `
        SELECT
          ut.id AS id,
          u.userid AS "userId",
          u.email AS email,
          u.name AS fullname,
          t.name AS "teamName",
          t.id AS "teamId",
          ui."firstNameHira" AS "firstNameHira",
          ui."lastNameHira" AS "lastNameHira",
          ut."isAdmin" AS "isAdmin"
        FROM user_team ut
          LEFT JOIN users u ON CAST(u.userid AS TEXT) = ut."userId"
          LEFT JOIN user_info ui ON ui."userId" = ut."userId"
          LEFT JOIN teams t ON ut."teamId" = t.id
        WHERE
          ut."rootUserId" = $1
          AND ut."teamId" = $2
      `,
      [rootUserId, teamId],
    );

    for (const data of dataRaw) {
      let fullname = data.firstNameHira || '';
      if (data.lastNameHira)
        fullname += (fullname ? ' ' : '') + data.lastNameHira;
      if (!fullname) fullname = data.fullname || '';

      data.fullname = fullname;
    }

    return dataRaw;
  }

  async getTeamsOfMember(rootUserId: string): Promise<UserTeamDto[]> {
    const dataRaw = await this.dataSource.manager.query<UserTeamDto[]>(
      `
      SELECT
        u.userid AS "userId",
        u.email AS email,
        u.name AS fullname,
        t.name AS "teamName",
        t.id AS "teamId",
        ui."firstNameHira" AS "firstNameHira",
        ui."lastNameHira" AS "lastNameHira",
        ut."isAdmin" AS "isAdmin"
      FROM user_team ut
        LEFT JOIN users u ON CAST(u.userid AS TEXT) = ut."userId"
        LEFT JOIN user_info ui ON ui."userId" = ut."userId"
        LEFT JOIN teams t ON ut."teamId" = t.id
      WHERE
        ut."rootUserId" = $1
    `,
      [rootUserId],
    );

    const loginUserRaw = await this.dataSource.manager.query<UserTeamDto[]>(
      `
      SELECT
        u.userid AS "userId",
        u.email AS email,
        u.name AS fullname,
        null AS "teamName",
        null AS "teamId",
        ui."firstNameHira" AS "firstNameHira",
        ui."lastNameHira" AS "lastNameHira",
        true AS "isAdmin"
      FROM users u
        LEFT JOIN user_info ui ON ui."userId" = CAST(u.userid AS TEXT)
      WHERE
        CAST(u.userid AS TEXT) = $1
    `,
      [rootUserId],
    );

    if (loginUserRaw.length > 0) {
      dataRaw.unshift(loginUserRaw[0]);
    }

    const result: Record<string, UserTeamDto> = {};
    for (const data of dataRaw) {
      let fullname = data.firstNameHira || '';
      if (data.lastNameHira)
        fullname += (fullname ? ' ' : '') + data.lastNameHira;
      if (!fullname) fullname = data.fullname || '';

      const item: UserTeamDto = result[data.userId] || {
        userId: data.userId,
        email: data.email,
        teamName: '',
        fullname: fullname,
        isAdmin: data.isAdmin || false,
      };

      if (data.teamName) {
        item.teamName += (item.teamName ? ', ' : '') + data.teamName;
      }
      result[data.userId] = item;
    }

    return Object.values(result);
  }
}
