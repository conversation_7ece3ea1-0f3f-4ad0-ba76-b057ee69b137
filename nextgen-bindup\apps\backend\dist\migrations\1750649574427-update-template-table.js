"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateTemplatesTable1750649574427 = void 0;
const typeorm_1 = require("typeorm");
class UpdateTemplatesTable1750649574427 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}templates`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'tmpSiteId',
            type: 'varchar',
            length: '250',
            isNullable: false,
            default: `''`,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'titleDetail',
            type: 'varchar',
            length: '250',
            isNullable: true,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'seq',
            type: 'integer',
            isNullable: false,
            default: '1',
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'smartblock',
            type: 'boolean',
            isNullable: false,
            default: false,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'tmpSiteId');
        await queryRunner.dropColumn(this.TABLE_NAME, 'titleDetail');
        await queryRunner.dropColumn(this.TABLE_NAME, 'seq');
        await queryRunner.dropColumn(this.TABLE_NAME, 'smartblock');
    }
}
exports.UpdateTemplatesTable1750649574427 = UpdateTemplatesTable1750649574427;
//# sourceMappingURL=1750649574427-update-template-table.js.map