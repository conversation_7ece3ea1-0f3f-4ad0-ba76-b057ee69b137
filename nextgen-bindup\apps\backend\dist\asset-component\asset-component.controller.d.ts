import { AssetComponentService } from './asset-component.service';
import { AssetComponent } from './entities/asset-component.entity';
export declare class AssetComponentController {
    private readonly assetComponentService;
    constructor(assetComponentService: AssetComponentService);
    create(assetEntity: AssetComponent): Promise<AssetComponent>;
    update(assetComponentId: string, data: Partial<AssetComponent>): Promise<AssetComponent>;
    getById(assetComponentId: string): Promise<AssetComponent>;
    getByProjectId(projectId: string): Promise<AssetComponent[]>;
    getBySiteId(projectId: string, siteId: string): Promise<AssetComponent[]>;
    delete(assetComponentId: string): Promise<boolean>;
}
