"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePageEditSessionTable1740362608459 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePageEditSessionTable1740362608459 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}page_edit_sessions`;
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME} DROP CONSTRAINT unique_user_page_session;`);
        await queryRunner.addColumn(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`, new typeorm_1.TableColumn({
            name: 'siteId',
            type: 'integer',
            isNullable: false,
        }));
        await queryRunner.createUniqueConstraint(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`, new typeorm_1.TableUnique({
            name: 'unique_user_site_session',
            columnNames: ['userId', 'pageId', 'siteId', 'sessionId'],
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropUniqueConstraint(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`, 'unique_user_site_session');
        await queryRunner.dropColumn(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`, 'siteId');
        await queryRunner.query(`ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME} ADD CONSTRAINT unique_user_page_session UNIQUE ("userId", "pageId", "siteId", "sessionId");`);
    }
}
exports.UpdatePageEditSessionTable1740362608459 = UpdatePageEditSessionTable1740362608459;
//# sourceMappingURL=1740362608459-update-page-edit-session-table.js.map