"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SiteGuard = void 0;
const common_1 = require("@nestjs/common");
const site_auth_service_1 = require("./site-auth.service");
let SiteGuard = class SiteGuard {
    constructor(siteAuthService) {
        this.siteAuthService = siteAuthService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const token = request.headers.authorization?.split(' ')[1];
        if (!token) {
            throw new common_1.UnauthorizedException('No token provided');
        }
        try {
            const { siteId } = await this.siteAuthService.validateTokenAndGetSite(token);
            request.site = {
                siteId: siteId,
            };
            return true;
        }
        catch (error) {
            throw new common_1.UnauthorizedException(error.message);
        }
    }
};
exports.SiteGuard = SiteGuard;
exports.SiteGuard = SiteGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [site_auth_service_1.SiteAuthService])
], SiteGuard);
//# sourceMappingURL=site.guard.js.map