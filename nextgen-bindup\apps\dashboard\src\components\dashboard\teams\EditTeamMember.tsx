import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  OutlinedInput,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { styled } from '@mui/material/styles';
import { getErrMsg } from '@nextgen-bindup/common/utility';
import { useAuth } from '../../../auth';
import { TeamDto } from '../../../dto/team.type';
import { UserTeamDto } from '../../../dto/user-team.type';
import { teamService } from '../../../services/team.service';
import { userTeamService } from '../../../services/user-team.service';

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(0),
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
}));

export const EditTeamMember: FC<{
  teamId: number;
  onUpdate: (status: string) => void;
}> = ({ teamId, onUpdate }) => {
  const { t } = useTranslation();
  const { session } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [errorField, setErrorField] = useState<{
    field: string;
    msg: string;
  } | null>(null);
  const [team, setTeam] = useState<TeamDto>({
    id: 0,
    rootUserId: '',
    name: '',
  });

  const [teamMembers, setTeamMembers] = useState<UserTeamDto[]>([]);
  const [email, setEmail] = useState<string>('');

  useEffect(() => {
    initData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const initData = async () => {
    setIsLoading(true);
    try {
      const rootUserId: string = session?.user.userId || '';
      const team: TeamDto = await teamService.findById(teamId);
      if (!team) {
        setErrorField({
          field: 'body',
          msg: t('team.error.team_not_found'),
        });
        return;
      }

      const teamMembers: UserTeamDto[] = await userTeamService.getMembersOfTeam(
        rootUserId,
        teamId,
      );

      setTeam(team);
      setTeamMembers(teamMembers);
    } catch (e) {
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  };

  const onRemoveEmail = (id: number) => {
    if (isLoading) return;
    const index = teamMembers.findIndex(item => item.id === id);
    if (index === -1) return;

    const newTemMembers = [...teamMembers];
    if (id < 0) {
      newTemMembers.splice(index, 1);
    } else {
      newTemMembers[index].isDeleted = true;
      newTemMembers[index].isAdmin = false;
    }
    setTeamMembers(newTemMembers);
  };

  const onAddEmail = () => {
    if (isLoading) return;

    if (!email.trim()) {
      setErrorField({
        field: 'email',
        msg: t('member.invite_member.error.email_required'),
      });
      return;
    }

    const isEmail = email
      .trim()
      .toLowerCase()
      .match(
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      );

    if (!isEmail) {
      setErrorField({
        field: 'email',
        msg: t('member.invite_member.error.email_invalid'),
      });
      return;
    }

    const newTemMembers = [...teamMembers];
    const memberIndex = newTemMembers.findIndex(
      item => item.email.toLowerCase() === email.trim().toLowerCase(),
    );

    if (memberIndex !== -1 && !newTemMembers[memberIndex].isDeleted) {
      setErrorField({
        field: 'email',
        msg: t('member.invite_member.error.email_duplicate'),
      });
      return;
    }

    if (memberIndex >= 0) {
      newTemMembers[memberIndex].isDeleted = false;
    } else {
      newTemMembers.push({
        id: -1 * new Date().getTime(),
        userId: '',
        email: email.trim(),
        teamName: '',
        fullname: email.trim(),
        isAdmin: false,
      });
    }

    setTeamMembers(newTemMembers);
    setEmail('');
    setErrorField(null);
  };

  const onSetAdmin = (id: number) => {
    if (isLoading) return;

    const newTemMembers = [...teamMembers];

    const memberIndex = newTemMembers.findIndex(item => item.id === id);
    if (memberIndex === -1) return;

    const isAdminIndex = newTemMembers.findIndex(item => item.isAdmin === true);
    if (isAdminIndex >= 0) newTemMembers[isAdminIndex].isAdmin = false;

    newTemMembers[memberIndex].isAdmin = true;

    setTeamMembers(newTemMembers);
  };

  const onSave = async () => {
    if (isLoading) return;

    setIsLoading(true);
    setErrorField(null);

    try {
      await userTeamService.updateMemberOfTeam({
        teamId: teamId,
        members: teamMembers,
      });

      onUpdate('invite');
    } catch (e) {
      setErrorField({
        field: 'body',
        msg: t(getErrMsg(e)),
      });
      console.log(e);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <BootstrapDialog open fullWidth>
      <DialogTitle sx={{ m: 0, p: 1 }} id="customized-dialog-title">
        {team?.name || t('team.edit_member.title')}
      </DialogTitle>

      <DialogContent dividers>
        <Box>
          <Stack spacing={2}>
            <Box sx={{ padding: '1rem 1.5rem 0 1.5rem' }}>
              <Stack spacing={2}>
                <Table aria-label="team_member table">
                  <TableHead sx={{ backgroundColor: '#f6f6f6' }}>
                    <TableRow>
                      <TableCell>{t('team.name')}</TableCell>
                      <TableCell>{t('team.administrator')}</TableCell>
                      <TableCell>{t('common.action')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {teamMembers.map(member =>
                      member.isDeleted ? null : (
                        <TableRow
                          key={member.id}
                          sx={{
                            '&:last-child td, &:last-child th': { border: 0 },
                          }}
                        >
                          <TableCell scope="row" sx={{ padding: '0 10px' }}>
                            <Typography
                              component={'span'}
                              color={
                                (member.id || 0) < 0 ? 'warning' : 'textPrimary'
                              }
                            >
                              {member.fullname === member.email ? (
                                member.email
                              ) : (
                                <>
                                  <span style={{ fontWeight: 'bold' }}>
                                    {member.fullname}
                                  </span>
                                  <br />
                                  {member.email}
                                </>
                              )}
                            </Typography>
                          </TableCell>
                          <TableCell sx={{ padding: '0 10px' }}>
                            {member.isAdmin ? (
                              <IconButton
                                color="info"
                                sx={{ pointerEvents: 'none' }}
                              >
                                <AdminPanelSettingsIcon />
                              </IconButton>
                            ) : (
                              <IconButton
                                title={t('team.edit_member.set_admin')}
                                onClick={() => {
                                  onSetAdmin(member.id || 0);
                                }}
                              >
                                <AdminPanelSettingsIcon />
                              </IconButton>
                            )}
                          </TableCell>
                          <TableCell sx={{ padding: '0 10px' }}>
                            <Stack direction="row" spacing={1}>
                              <IconButton
                                color="error"
                                title={t('team.edit_member.remove_member')}
                                onClick={() => {
                                  onRemoveEmail(member.id || 0);
                                }}
                              >
                                <CloseIcon />
                              </IconButton>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ),
                    )}
                  </TableBody>
                </Table>
              </Stack>
            </Box>

            <Box
              sx={{
                padding: '0 1.5rem 1rem 1.5rem',
                display: isLoading ? 'none' : '',
              }}
            >
              <FormControl variant="outlined" size="small" fullWidth>
                <InputLabel htmlFor="email">
                  {t('member.invite_member.email')}
                </InputLabel>
                <OutlinedInput
                  id="email"
                  type="email"
                  value={email}
                  onChange={e => {
                    setEmail(e.target.value);
                    setErrorField(null);
                  }}
                  onKeyUp={e => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      onAddEmail();
                    }
                  }}
                  error={errorField?.field === `email`}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        onClick={onAddEmail}
                        edge="end"
                        color="success"
                      >
                        <AddCircleIcon />
                      </IconButton>
                    </InputAdornment>
                  }
                  label={t('member.invite_member.email')}
                />

                {errorField?.field === `email` ? (
                  <Typography
                    variant="body1"
                    color="error"
                    sx={{ paddingLeft: '5px', marginTop: '5px' }}
                  >
                    {errorField.msg}
                  </Typography>
                ) : null}
              </FormControl>

              {errorField?.field === `body` ? (
                <Typography
                  variant="body1"
                  color="error"
                  sx={{ paddingLeft: '5px', marginTop: '5px' }}
                >
                  {errorField.msg}
                </Typography>
              ) : null}
            </Box>
          </Stack>
        </Box>
      </DialogContent>
      <DialogActions>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <Button
            onClick={() => {
              onUpdate('cancel');
            }}
            color="inherit"
            variant="outlined"
            size="small"
            loading={isLoading}
          >
            {t('common.cancel')}
          </Button>

          <Button
            autoFocus
            onClick={onSave}
            color="success"
            variant="contained"
            size="small"
            loading={isLoading}
          >
            {t('common.btn_save')}
          </Button>
        </Box>
      </DialogActions>
    </BootstrapDialog>
  );
};
