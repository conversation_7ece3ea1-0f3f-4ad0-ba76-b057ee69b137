import { Module } from '@nestjs/common';
import { ProjectFoldersController } from './project-folders.controller';
import { ProjectFoldersService } from './project-folders.service';
import { ProjectFolderEntity } from './entities/project-folders.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([ProjectFolderEntity])],
  controllers: [ProjectFoldersController],
  providers: [ProjectFoldersService],
  exports: [ProjectFoldersService],
})
export class ProjectFoldersModule {}
