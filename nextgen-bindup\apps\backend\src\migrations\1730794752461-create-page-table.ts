import { PageStatus, PageType } from 'src/page/types/page.type';
import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreatePageTable1730794752461 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}pages`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'type',
            type: 'varchar',
            length: '10',
            isNullable: false,
            default: `'${PageType.PAGE}'`,
          },
          {
            name: 'parentId',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'projectId',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'components',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'ts',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'smallint',
            isNullable: false,
            default: `'${PageStatus.DRAFT}'`,
          },
          {
            name: 'url',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isSearch',
            type: 'boolean',
            isNullable: true,
          },
          {
            name: 'thumb',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'headCode',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'bodyCode',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isPrivate',
            type: 'boolean',
            isNullable: false,
            default: 'false',
          },
          {
            name: 'isHome',
            type: 'boolean',
            isNullable: false,
            default: 'false',
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );

    //alter publication supabase_realtime add table this.TABLE_NAME;
    await queryRunner.query(
      `alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );

    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
