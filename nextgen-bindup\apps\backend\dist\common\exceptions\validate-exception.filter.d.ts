import { ArgumentsHost, ExceptionFilter } from '@nestjs/common';
import { ValidateException } from './validate.exception';
import { LoggerService } from '../../logger/logger.service';
export declare class ValidateExceptionFilter implements ExceptionFilter {
    private readonly loggerService;
    constructor(loggerService: LoggerService);
    catch(exception: ValidateException, host: ArgumentsHost): void;
}
