"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsCollectionTable1743388643398 = void 0;
class UpdateCmsCollectionTable1743388643398 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}cms_collection`;
    }
    async up(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'title');
    }
    async down(queryRunner) {
        console.log(!!queryRunner);
    }
}
exports.UpdateCmsCollectionTable1743388643398 = UpdateCmsCollectionTable1743388643398;
//# sourceMappingURL=1743388643398-update-cms_collection-table.js.map