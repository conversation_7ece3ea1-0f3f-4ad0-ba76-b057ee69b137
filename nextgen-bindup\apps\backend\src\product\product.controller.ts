import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { ProductService } from './product.service';
import { ProductEntity } from './entities/product.entity';
import { GetProductsQueryDto } from './dto/get-product.dto';
import { ensureFolderExists } from 'src/utils/file.util';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { Response } from 'express';

@Controller('product')
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Get('template')
  async downloadTemplate(@Res() res: Response) {
    const csvStream = await this.productService.getCSVTemplate();
    res.header('Content-Type', 'text/csv');
    res.header(
      'Content-Disposition',
      'attachment; filename="product_template.csv"',
    );
    res.header('Cache-Control', 'no-cache');
    csvStream.pipe(res);
  }

  @Post('upload/:siteId')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: (req, file, cb) => {
          ensureFolderExists('./upload');
          cb(null, './upload');
        },
        filename: (req, file, callback) => {
          const uniqueSuffix =
            Date.now() + '-' + Math.round(Math.random() * 1e9);
          callback(null, `${uniqueSuffix}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, callback) => {
        if (file.mimetype !== 'text/csv') {
          return callback(new BadRequestException('Invalid file type'), false);
        }
        callback(null, true);
      },
    }),
  )
  async uploadCSV(
    @Param('siteId') siteId: string,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return await this.productService.uploadCSV(+siteId, file);
  }

  @Get('download/:siteId')
  async downloadCsv(
    @Param('siteId') siteId: string,
    @Query() query: GetProductsQueryDto,
    @Res() res: Response,
  ) {
    const csvStream = await this.productService.downloadCSV(+siteId, query);
    res.header('Content-Type', 'text/csv');
    res.header(
      'Content-Disposition',
      `attachment; filename="products_${siteId}_${new Date().toISOString().slice(0, 10)}.csv"`,
    );
    res.header('Cache-Control', 'no-cache');
    csvStream.pipe(res);
  }

  @Get('all/:siteId')
  async getAll(@Param('siteId') siteId: number) {
    console.log('siteId', siteId);
    return await this.productService.getAllProducts(siteId);
  }

  @Get('search/:siteId')
  async getList(
    @Param('siteId') siteId: number,
    @Query() query: GetProductsQueryDto,
  ) {
    return await this.productService.searchProducts(
      siteId,
      query.page,
      query.limit,
      query.search,
      query.productType,
      query.isOrderable,
    );
  }

  @Post('create')
  async create(@Body() productEntity: ProductEntity) {
    return await this.productService.create(productEntity);
  }

  @Put('update/:id')
  async update(@Param('id') id: string, @Body() data: Partial<ProductEntity>) {
    return await this.productService.update(+id, data);
  }

  @Get('one/:id')
  async getById(@Param('id') id: string) {
    return await this.productService.findById(+id);
  }

  @Post('duplicate/:id')
  async duplicate(@Param('id') id: string) {
    return await this.productService.duplicate(+id);
  }

  @Delete(':id')
  async delete(@Param('id') id: string) {
    return await this.productService.delete(+id);
  }
}
