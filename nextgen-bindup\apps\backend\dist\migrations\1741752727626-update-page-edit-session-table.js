"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePageEditSessionTable1741752727626 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePageEditSessionTable1741752727626 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}page_edit_sessions`;
        this.CONSTRAINT_NAME = 'unique_user_site_session';
    }
    async up(queryRunner) {
        await queryRunner.query(`
        ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}
        DROP CONSTRAINT IF EXISTS ${this.CONSTRAINT_NAME};
      `);
        await queryRunner.query(`DELETE FROM ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
        await queryRunner.changeColumn(this.TABLE_NAME, 'userId', new typeorm_1.TableColumn({
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: false,
        }));
        await queryRunner.createUniqueConstraint(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`, new typeorm_1.TableUnique({
            name: this.CONSTRAINT_NAME,
            columnNames: ['userId', 'pageId', 'siteId', 'sessionId'],
        }));
    }
    async down(queryRunner) {
        await queryRunner.query(`
        ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}
        DROP CONSTRAINT IF EXISTS ${this.CONSTRAINT_NAME};
      `);
        await queryRunner.changeColumn(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`, 'userId', new typeorm_1.TableColumn({
            name: 'userId',
            type: 'integer',
            isNullable: false,
        }));
        await queryRunner.createUniqueConstraint(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`, new typeorm_1.TableUnique({
            name: this.CONSTRAINT_NAME,
            columnNames: ['userId', 'pageId', 'siteId', 'sessionId'],
        }));
    }
}
exports.UpdatePageEditSessionTable1741752727626 = UpdatePageEditSessionTable1741752727626;
//# sourceMappingURL=1741752727626-update-page-edit-session-table.js.map