"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionEntity = void 0;
const typeorm_1 = require("typeorm");
const payment_dto_1 = require("../dto/payment.dto");
let SubscriptionEntity = class SubscriptionEntity {
};
exports.SubscriptionEntity = SubscriptionEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], SubscriptionEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'userId',
        type: 'varchar',
        length: 36,
        nullable: false,
    }),
    __metadata("design:type", String)
], SubscriptionEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'planId',
        type: 'varchar',
        length: 36,
        nullable: false,
    }),
    __metadata("design:type", String)
], SubscriptionEntity.prototype, "planId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'stripeSubscriptionId',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], SubscriptionEntity.prototype, "stripeSubscriptionId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'status',
        type: 'varchar',
        length: 50,
        nullable: false,
    }),
    __metadata("design:type", String)
], SubscriptionEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'currentPeriodStart',
        type: 'timestamptz',
        nullable: false,
    }),
    __metadata("design:type", Date)
], SubscriptionEntity.prototype, "currentPeriodStart", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'currentPeriodEnd',
        type: 'timestamptz',
        nullable: false,
    }),
    __metadata("design:type", Date)
], SubscriptionEntity.prototype, "currentPeriodEnd", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'cancelAt',
        type: 'timestamptz',
        nullable: true,
    }),
    __metadata("design:type", Date)
], SubscriptionEntity.prototype, "cancelAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], SubscriptionEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], SubscriptionEntity.prototype, "updatedAt", void 0);
exports.SubscriptionEntity = SubscriptionEntity = __decorate([
    (0, typeorm_1.Entity)('subscriptions', { schema: process.env.DATABASE_SCHEMA })
], SubscriptionEntity);
//# sourceMappingURL=subscription.entity.js.map