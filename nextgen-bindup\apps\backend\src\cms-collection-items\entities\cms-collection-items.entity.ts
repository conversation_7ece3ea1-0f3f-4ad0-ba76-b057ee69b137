import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CollectionItemStatus } from '../types/cms-collection-item.type';
import { CmsCollectionItemData } from '../dto/cms-collection-item-data.dto';

@Entity('cms_collection_items', { schema: process.env.DATABASE_SCHEMA })
export class CmsCollectionItemEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'cmsCollectionId',
    type: 'integer',
    nullable: false,
  })
  cmsCollectionId: number;

  @Column({
    name: 'title',
    type: 'varchar',
    length: 1000,
    nullable: false,
  })
  title: string;

  @Column({
    name: 'slug',
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  slug: string;

  // { [structId: number]: CmsCollectionItemData }
  @Column({
    name: 'data',
    type: 'jsonb',
    nullable: true,
  })
  data: Record<number, CmsCollectionItemData>;

  @Column({
    name: 'status',
    type: 'varchar',
    length: 15,
    nullable: false,
    default: 'draft',
  })
  status: CollectionItemStatus = 'draft';

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @Column({
    name: 'rootUserId',
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  rootUserId: string;

  @Column({
    name: 'userId',
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  userId: string;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
