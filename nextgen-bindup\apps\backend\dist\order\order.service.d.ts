import { OrderEntity } from './entites/order.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { OrderStatus } from './enum/order.enum';
import { PaginatedResponse } from 'src/common/paginated-response';
import { PaymentMethodType } from './enum/payment-method-type.enum';
import { ProductService } from 'src/product/product.service';
import { ShippingNoteSettingService } from 'src/shipping-note-settings/shipping-note-settings.service';
import { ShopInformationSettingService } from 'src/shop-information-settings/shop-information-settings.service';
import { GetOrdersQueryDto } from './dto/get-order.dto';
import { CsvService } from '../product/csv.service';
import { Readable } from 'stream';
export declare class OrderService {
    private readonly productService;
    private readonly shippingNoteSettingService;
    private readonly shopInformationSettingService;
    private readonly csvService;
    private orderRepository;
    private orderItemRepository;
    constructor(productService: ProductService, shippingNoteSettingService: ShippingNoteSettingService, shopInformationSettingService: ShopInformationSettingService, csvService: CsvService);
    getOrders(siteId: string, query: GetOrdersQueryDto): Promise<PaginatedResponse<OrderEntity>>;
    createOrder(dto: CreateOrderDto, paymentMethodType: PaymentMethodType, orderStatus: OrderStatus): Promise<OrderEntity>;
    updateOrder(orderId: number, orderData: Partial<OrderEntity>): Promise<OrderEntity>;
    findOrderById(orderId: number): Promise<OrderEntity>;
    deleteOrder(orderId: number): Promise<boolean>;
    getWaitingPaymentOrdersInLast2Days(): Promise<OrderEntity[]>;
    downloadCSV(siteId: string, query?: GetOrdersQueryDto): Promise<Readable>;
    private formatProductOptions;
    markAsPaid(orderId: number): Promise<OrderEntity>;
}
