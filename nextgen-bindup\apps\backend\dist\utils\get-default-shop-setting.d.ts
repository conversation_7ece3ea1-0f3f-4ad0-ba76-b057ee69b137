export declare const getDefaultSetting: (shopName: string, email: string) => {
    shippingNote: {
        note: string;
    };
    paymentMethod: {
        bankTransfer: {
            isEnabled: boolean;
            bankAccount: string;
            description: string;
        };
        postalTransfer: {
            isEnabled: boolean;
            bankAccount: string;
            description: string;
        };
        stripePaymentGateway: {
            isEnabled: boolean;
            description: string;
            stripeAccountId: string;
        };
        cashOnDelivery: {
            isEnabled: boolean;
            description: string;
            fee: {
                fromAmount: number;
                codFee: number;
            }[];
        };
    };
    delivery: {
        headerText: string;
        footerText: string;
    };
    orderCompletion: {
        displayText: string;
        emailSubject: string;
        emailHeader: string;
        emailFooter: string;
    };
};
