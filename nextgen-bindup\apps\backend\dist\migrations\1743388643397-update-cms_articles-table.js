"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsArticleTable1743388643397 = void 0;
const typeorm_1 = require("typeorm");
class UpdateCmsArticleTable1743388643397 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}cms_articles`;
    }
    async up(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'name');
        const title = new typeorm_1.TableColumn({
            name: 'title',
            type: 'varchar',
            length: '1000',
            isNullable: false,
        });
        await queryRunner.addColumn(this.TABLE_NAME, title);
        const slug = new typeorm_1.TableColumn({
            name: 'slug',
            type: 'varchar',
            length: '1000',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, slug);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'title');
        await queryRunner.dropColumn(this.TABLE_NAME, 'slug');
    }
}
exports.UpdateCmsArticleTable1743388643397 = UpdateCmsArticleTable1743388643397;
//# sourceMappingURL=1743388643397-update-cms_articles-table.js.map