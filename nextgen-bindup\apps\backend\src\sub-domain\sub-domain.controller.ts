import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { SubDomainService } from './sub-domain.service';
import { SubDomainEntity } from './entities/sub-domain.entity';

@Controller('sub-domain')
@UseGuards(AuthGuard)
export class SubDomainController {
  constructor(private readonly subDomainService: SubDomainService) {}

  @Post('create')
  async create(@Body() data: SubDomainEntity) {
    return await this.subDomainService.create(data);
  }

  @Put('update/:id')
  async update(
    @Param('id') id: string,
    @Body() data: Partial<SubDomainEntity>,
  ) {
    return await this.subDomainService.update(+id, data);
  }

  @Delete('delete/:id')
  async delete(@Param('id') id: string) {
    return await this.subDomainService.delete(+id);
  }

  @Get('site/:siteId')
  async findBySiteId(@Param('siteId') siteId: string) {
    return await this.subDomainService.findBySiteId(+siteId);
  }

  @Get('project/:projectId')
  async findByProjectId(@Param('projectId') projectId: string) {
    return await this.subDomainService.findByProjectId(+projectId);
  }
}
