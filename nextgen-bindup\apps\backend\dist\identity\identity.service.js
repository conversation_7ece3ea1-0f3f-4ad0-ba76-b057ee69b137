"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdentityService = void 0;
const common_1 = require("@nestjs/common");
const supabase_js_1 = require("@supabase/supabase-js");
const app_exception_1 = require("../common/exceptions/app.exception");
let IdentityService = class IdentityService {
    constructor(supabase) {
        this.supabase = supabase;
    }
    async createIdentity(input) {
        const res = await this.supabase.auth.signUp({
            email: input.email,
            password: input.password,
        });
        if (res.error)
            throw new app_exception_1.AppException(`api.error.${res.error.code}`);
        if (!res?.data?.user)
            throw new app_exception_1.AppException('api.error.unknown_error');
        if (!res?.data?.session)
            throw new app_exception_1.AppException('api.error.user_is_existed');
        const result = {
            accessToken: res.data?.session?.access_token,
            user: {
                userId: res.data.user.id,
                email: res.data.user.email,
            },
        };
        return result;
    }
    async signInWithPassword(email, password) {
        const res = await this.supabase.auth.signInWithPassword({
            email,
            password,
        });
        if (res.error)
            throw new app_exception_1.AppException(`api.error.${res.error.code}`);
        if (!res?.data?.user)
            throw new app_exception_1.AppException('api.error.unknown_error');
        if (!res?.data?.session)
            throw new app_exception_1.AppException('api.error.unknown_error');
        const result = {
            accessToken: res.data?.session?.access_token,
            user: {
                userId: res.data.user.id,
                email: res.data.user.email,
            },
        };
        return result;
    }
    async forgotPasswordForEmail(email, redirectTo) {
        const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
            redirectTo: `${redirectTo}?email=${encodeURIComponent(email)}`,
        });
        if (error)
            throw new app_exception_1.AppException(`api.error.${error.code}`);
    }
    async changePassword(email, password) {
        const res = await this.supabase.auth.updateUser({
            email,
            password,
        });
        if (res.error)
            throw new app_exception_1.AppException(`api.error.${res.error.code}`);
        if (res.error)
            throw new app_exception_1.AppException(res.error.message);
        if (!res?.data?.user)
            throw new app_exception_1.AppException('api.error.unknown_error');
    }
};
exports.IdentityService = IdentityService;
exports.IdentityService = IdentityService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('SUPABASE_CLIENT')),
    __metadata("design:paramtypes", [supabase_js_1.SupabaseClient])
], IdentityService);
//# sourceMappingURL=identity.service.js.map