import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { FileModule } from './file/file.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SupabaseModule } from './supabase/supabase.module';
import { UserModule } from './user/user.module';
import { ProjectModule } from './project/project.module';
import { PageModule } from './page/page.module';
import { SUPABASE_KEY, SUPABASE_URL } from './supabase/supabase.constant';
import { StorageModule } from './storage/storage.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoggerModule } from './logger/logger.module';
import { PublishModule } from './publish/publish.module';
import { StyleModule } from './style/style.module';
import { AssetsModule } from './asset/asset.module';
import { ContentModule } from './asset-component/asset-component.module';
import { PageEditSessionModule } from './page-edit-session/page-edit-session.module';
import { ScheduleModule } from '@nestjs/schedule';
import { SiteModule } from './site/site.module';
import { VersionHistoryModule } from './version-history/version-history.module';
import { AuthModule } from './auth/auth.module';
import { UserInfoModule } from './user-info/user-info.module';
import { TeamModule } from './team/team.module';
import { UserTeamModule } from './user-team/user-team.module';
import { IdentityModule } from './identity/identity.module';
import { UserManagementModule } from './user-management/user-management.module';
import { ProjectFoldersModule } from './project-folders/project-folders.module';
import { NameServersModule } from './name-servers/name-servers.module';
import { DnsRecordModule } from './dns-record/dns-record.module';
import { SubDomainModule } from './sub-domain/sub-domain.module';
import { FontsetModule } from './fontset/fontset.module';
import { PaymentModule } from './payment/payment.module';
import { CmsCollectionModule } from './cms-collection/cms-collection.module';
import { ProductModule } from './product/product.module';
import { OrderModule } from './order/order.module';
import { CmsCollectionItemsModule } from './cms-collection-items/cms-collection-items.module';
import { MailModule } from './mail/mail.module';
import { ProductStocksModule } from './product-stocks/product-stocks.module';
import { OrderCompletionSettingModule } from './order-complete-settings/order-complete-settings.module';
import { ShopInformationSettingModule } from './shop-information-settings/shop-information-settings.module';
import { ShippingNoteSettingModule } from './shipping-note-settings/shipping-note-settings.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { PaymentMethodModule } from './payment-method/payment-method.module';
import { ProxyModule } from './proxy/proxy.module';
import { DeliveryReceiptSettingModule } from './delivery-receipt-settings/delivery-receipt-settings.module';
import { TemplateMigrationModule } from './template-migration/template-migration.module';

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
    FileModule,
    LoggerModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    SupabaseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        url: configService.get<string>(SUPABASE_URL),
        key: configService.get<string>(SUPABASE_KEY),
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        type: configService.get<string>('DATABASE_TYPE') as any,
        host: configService.get<string>('DATABASE_HOST'),
        port: parseInt(configService.get<string>('DATABASE_PORT')),
        database: configService.get<string>('DATABASE_NAME'),
        username: configService.get<string>('DATABASE_USER'),
        password: configService.get<string>('DATABASE_PASSWORD'),
        entities: ['dist/**/*.entity.{ts,js}'],
        migrations: [
          configService.get<string>('NODE_ENV') === 'production'
            ? 'dist/migrations/*.{ts,js}'
            : 'dist/migrations/*.{ts,js}',
        ],
        migrationsTableName: `${configService.get<string>('ENTITY_PREFIX')}typeorm_migrations`,
        logger: (configService.get<string>('NODE_ENV') === 'production'
          ? 'simple-console'
          : 'advanced-console') as any,
        logging: false,
        entityPrefix: configService.get<string>('ENTITY_PREFIX') || '',
        migrationsRun:
          configService.get<string>('DATABASE_MIGRATIONS_RUN') === 'true',
      }),
      inject: [ConfigService],
    }),
    UserModule,
    ProjectModule,
    PageModule,
    StorageModule,
    PublishModule,
    StyleModule,
    AssetsModule,
    ContentModule,
    PageEditSessionModule,
    SiteModule,
    VersionHistoryModule,
    AuthModule,
    UserInfoModule,
    TeamModule,
    UserTeamModule,
    IdentityModule,
    UserManagementModule,
    ProjectFoldersModule,
    NameServersModule,
    DnsRecordModule,
    SubDomainModule,
    FontsetModule,
    PaymentModule,
    CmsCollectionModule,
    ProductModule,
    OrderModule,
    CmsCollectionItemsModule,
    MailModule,
    ProductStocksModule,
    OrderCompletionSettingModule,
    ShopInformationSettingModule,
    ShippingNoteSettingModule,
    PaymentMethodModule,
    ProxyModule,
    DeliveryReceiptSettingModule,
    TemplateMigrationModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
