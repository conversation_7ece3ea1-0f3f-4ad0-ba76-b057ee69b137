import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdatePagesTable1739757489363 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}pages`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const userIdColumn: TableColumn = new TableColumn({
      name: 'userId',
      type: 'varchar',
      length: '36',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, userIdColumn);

    const siteIdColumn: TableColumn = new TableColumn({
      name: 'siteId',
      type: 'integer',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, siteIdColumn);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'userId');
    await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
  }
}
