import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateShippingNoteTable1748401492926
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}shipping_note_settings`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isGenerated: true,
            generationStrategy: 'increment',
            isPrimary: true,
          },
          {
            name: 'siteId',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'shippingFee',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'shippingFeeDetail',
            type: 'jsonb',
            isNullable: false,
          },
          {
            name: 'isFreeShippingCondition',
            type: 'boolean',
            isNullable: false,
          },
          {
            name: 'freeShippingCondition',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'note',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
