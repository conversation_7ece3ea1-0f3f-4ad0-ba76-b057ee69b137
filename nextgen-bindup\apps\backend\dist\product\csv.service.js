"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CsvService = void 0;
const common_1 = require("@nestjs/common");
const fs = require("fs");
const csv = require("csv-parser");
const csv_stringify_1 = require("csv-stringify");
const stream_1 = require("stream");
let CsvService = class CsvService {
    async parseCsv(filePath, expectedColumns) {
        return new Promise((resolve, reject) => {
            const results = [];
            let headersChecked = false;
            fs.createReadStream(filePath)
                .pipe(csv())
                .on('headers', (headers) => {
                if (headers.length !== expectedColumns.length) {
                    return reject(new common_1.BadRequestException(`Invalid number of columns. Expected ${expectedColumns.length} but got ${headers.length}.`));
                }
                for (let i = 0; i < expectedColumns.length; i++) {
                    if (headers[i] !== expectedColumns[i]) {
                        return reject(new common_1.BadRequestException(`Column mismatch at position ${i + 1}. Expected "${expectedColumns[i]}" but got "${headers[i]}".`));
                    }
                }
                headersChecked = true;
            })
                .on('data', data => {
                if (headersChecked) {
                    results.push(data);
                }
            })
                .on('end', () => {
                if (!headersChecked) {
                    return reject(new common_1.BadRequestException('CSV file is empty or missing headers.'));
                }
                resolve(results);
            })
                .on('error', error => {
                reject(error);
            });
        });
    }
    async exportToCsv(data, headers) {
        const columns = headers
            ? headers.map(header => ({ key: header, header: header }))
            : data.length > 0
                ? Object.keys(data[0]).map(key => ({ key, header: key }))
                : [];
        const stringifier = (0, csv_stringify_1.stringify)({
            header: true,
            columns: columns,
        });
        const readableStream = new stream_1.Readable({
            read() { },
        });
        data.forEach(row => {
            stringifier.write(row);
        });
        stringifier.end();
        stringifier.on('data', chunk => {
            readableStream.push(chunk);
        });
        stringifier.on('end', () => {
            readableStream.push(null);
        });
        stringifier.on('error', err => {
            readableStream.destroy(err);
        });
        return readableStream;
    }
};
exports.CsvService = CsvService;
exports.CsvService = CsvService = __decorate([
    (0, common_1.Injectable)()
], CsvService);
//# sourceMappingURL=csv.service.js.map