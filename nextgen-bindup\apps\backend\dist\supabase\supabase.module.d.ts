import { DynamicModule } from '@nestjs/common';
export interface SupabaseModuleOptions {
    url: string;
    key: string;
}
export interface SupabaseModuleAsyncOptions {
    useFactory: (...args: any[]) => Promise<SupabaseModuleOptions> | SupabaseModuleOptions;
    inject?: any[];
}
export declare class SupabaseModule {
    static forRoot(options: SupabaseModuleOptions): DynamicModule;
    static forRootAsync(options: SupabaseModuleAsyncOptions): DynamicModule;
    static forFeature(tableName: string): DynamicModule;
}
