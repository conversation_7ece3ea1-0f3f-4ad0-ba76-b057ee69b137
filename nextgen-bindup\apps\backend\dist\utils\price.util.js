"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDisplayPrice = exports.formatDisplayPrice = exports.formatPriceWithTax = exports.calculatePriceWithTax = exports.getShippingFee = exports.calculateFees = void 0;
const calculateFees = (orderItems, shippingNoteSetting, platformFeeRate, paymentGatewayFeeRate, paymentMethodType, shippingPrefecture) => {
    const subtotal = orderItems.reduce((total, item) => total + Number(item.displayPrice) * Number(item.quantity), 0);
    let shippingFee = 0;
    const isOnlyDigitalProducts = orderItems.every(item => item.productType === "DIGITAL");
    if (!isOnlyDigitalProducts) {
        const individualShippingChargesTotal = orderItems.reduce((total, item) => {
            if (item.individualShippingCharges &&
                item.individualShippingCharges > 0 &&
                item.productType === "PRODUCT") {
                return total + Number(item.individualShippingCharges);
            }
            return total;
        }, 0);
        const hasItemsWithoutIndividualCharges = orderItems.some(item => !item.individualShippingCharges || item.individualShippingCharges === 0);
        let commonShippingFee = 0;
        if (hasItemsWithoutIndividualCharges) {
            if (!(shippingNoteSetting.isFreeShippingCondition &&
                subtotal > shippingNoteSetting.freeShippingCondition)) {
                const shippingFeeDetail = shippingNoteSetting.shippingFeeDetail;
                commonShippingFee = Number(shippingFeeDetail[shippingPrefecture] || 0);
            }
        }
        shippingFee =
            Number(individualShippingChargesTotal) + Number(commonShippingFee);
    }
    const total = subtotal + shippingFee;
    let platformFee = 0;
    let paymentGatewayFee = 0;
    let shopNetPayout = total;
    if (paymentMethodType === "CREDIT_CARD") {
        platformFee = Math.ceil(total * platformFeeRate);
        paymentGatewayFee = Math.ceil(total * paymentGatewayFeeRate);
        shopNetPayout = total - platformFee - paymentGatewayFee;
    }
    return {
        subtotal,
        shippingFee,
        total,
        platformFee,
        paymentGatewayFee,
        shopNetPayout,
    };
};
exports.calculateFees = calculateFees;
const getShippingFee = (showShippingAddress, shippingPrefecture, orderItems, shippingNoteSetting) => {
    if (!showShippingAddress || !shippingNoteSetting) {
        return 0;
    }
    if (!shippingPrefecture || !orderItems.length) {
        return 0;
    }
    const individualShippingChargesTotal = orderItems.reduce((total, item) => {
        if (item.individualShippingCharges && item.individualShippingCharges > 0) {
            return total + Number(item.individualShippingCharges);
        }
        return total;
    }, 0);
    const hasItemsWithoutIndividualCharges = orderItems.some(item => !item.individualShippingCharges ||
        Number(item.individualShippingCharges) === 0);
    let commonShippingFee = 0;
    if (hasItemsWithoutIndividualCharges) {
        if (shippingNoteSetting.isFreeShippingCondition) {
            const totalPrice = orderItems.reduce((total, item) => total + Number(item.displayPrice) * Number(item.quantity), 0);
            if (totalPrice <= shippingNoteSetting.freeShippingCondition) {
                const shippingFeeDetail = shippingNoteSetting.shippingFeeDetail;
                commonShippingFee = Number(shippingFeeDetail[shippingPrefecture] || 0);
            }
        }
        else {
            const shippingFeeDetail = shippingNoteSetting.shippingFeeDetail;
            commonShippingFee = Number(shippingFeeDetail[shippingPrefecture] || 0);
        }
    }
    return individualShippingChargesTotal + commonShippingFee;
};
exports.getShippingFee = getShippingFee;
const calculatePriceWithTax = (price, taxMode, taxRate, taxRegulation) => {
    if (!taxMode || !taxRate || !taxRegulation) {
        return price;
    }
    if (taxMode === "INCLUSIVE") {
        return price;
    }
    else {
        const taxRateDecimal = taxRate / 100;
        if (taxRegulation === "TRUNCATE") {
            return Math.trunc(price * (1 + taxRateDecimal));
        }
        else if (taxRegulation === "ROUND_UP") {
            return Math.ceil(price * (1 + taxRateDecimal));
        }
        else if (taxRegulation === "ROUND_DOWN") {
            return Math.floor(price * (1 + taxRateDecimal));
        }
        return Math.round(price * (1 + taxRateDecimal));
    }
};
exports.calculatePriceWithTax = calculatePriceWithTax;
const formatPriceWithTax = (price) => {
    return `¥ ${price.toLocaleString()}（税込）`;
};
exports.formatPriceWithTax = formatPriceWithTax;
const formatDisplayPrice = (price, salePrice) => {
    const formattedPrice = (0, exports.formatPriceWithTax)(price);
    if (salePrice) {
        const formattedSalePrice = (0, exports.formatPriceWithTax)(salePrice);
        return `${formattedPrice} → ${formattedSalePrice}`;
    }
    return formattedPrice;
};
exports.formatDisplayPrice = formatDisplayPrice;
const getDisplayPrice = (product, shopInformation) => {
    const price = Number(product.sale) > 0 ? Number(product.sale) : Number(product.price);
    if (!shopInformation) {
        return price;
    }
    return (0, exports.calculatePriceWithTax)(price, shopInformation.taxMode, shopInformation.taxRate, shopInformation.taxRegulation);
};
exports.getDisplayPrice = getDisplayPrice;
//# sourceMappingURL=price.util.js.map