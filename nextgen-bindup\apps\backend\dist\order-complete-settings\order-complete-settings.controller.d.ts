import { OrderCompletionSettingService } from './order-complete-settings.service';
import { OrderCompletionSettingEntity } from './entities/order-complete-settings.entity';
export declare class OrderCompletionSettingController {
    private readonly orderCompletionSettingService;
    constructor(orderCompletionSettingService: OrderCompletionSettingService);
    create(orderCompletionSettingEntity: OrderCompletionSettingEntity): Promise<OrderCompletionSettingEntity>;
    update(id: string, data: Partial<OrderCompletionSettingEntity>): Promise<OrderCompletionSettingEntity>;
    getOneBySiteId(siteId: string): Promise<OrderCompletionSettingEntity>;
}
