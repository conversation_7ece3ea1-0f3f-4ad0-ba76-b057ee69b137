import { CmsCollectionService } from './cms-collection.service';
import { CmsCollectionEntity } from './entities/cms-collection.entity';
export declare class CmsCollectionController {
    private readonly collectionService;
    constructor(collectionService: CmsCollectionService);
    create(assetEntity: CmsCollectionEntity): Promise<CmsCollectionEntity>;
    update(id: string, data: Partial<CmsCollectionEntity>): Promise<CmsCollectionEntity>;
    getById(id: string): Promise<CmsCollectionEntity>;
    getBySiteId(siteId: string): Promise<CmsCollectionEntity[]>;
    getCollectionWithProductBySiteId(siteId: string): Promise<CmsCollectionEntity[]>;
    delete(id: string): Promise<boolean>;
}
