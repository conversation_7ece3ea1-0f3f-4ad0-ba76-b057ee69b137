"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDeliveryReceiptSettingsTbale1748508213962 = void 0;
const typeorm_1 = require("typeorm");
class CreateDeliveryReceiptSettingsTbale1748508213962 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}delivery_receipt_settings`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'siteId',
                    type: 'int',
                    isUnique: true,
                    isNullable: false,
                },
                {
                    name: 'header',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'footer',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateDeliveryReceiptSettingsTbale1748508213962 = CreateDeliveryReceiptSettingsTbale1748508213962;
//# sourceMappingURL=1748508213962-create-delivery-receipt-settings-table.js.map