"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FontsetService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const fontset_entity_1 = require("./entities/fontset.entity");
const typeorm_2 = require("typeorm");
const project_service_1 = require("../project/project.service");
const app_exception_1 = require("../common/exceptions/app.exception");
let FontsetService = class FontsetService {
    constructor(projectService) {
        this.projectService = projectService;
    }
    async create(fontsetEntity) {
        const project = await this.projectService.findById(fontsetEntity.projectId);
        if (!project)
            throw new app_exception_1.AppException('error.project_not_found');
        const now = new Date();
        const fontset = new fontset_entity_1.FontsetEntity();
        fontset.projectId = fontsetEntity.projectId;
        fontset.siteId = fontsetEntity.siteId;
        fontset.name = fontsetEntity.name;
        fontset.fonts = fontsetEntity.fonts;
        fontset.createdAt = now;
        fontset.updatedAt = now;
        return await this.fontsetRepo.save(fontset);
    }
    async update(id, fontsetData) {
        const fontset = await this.fontsetRepo.findOneBy({ id: id });
        if (!fontset)
            throw new app_exception_1.AppException('error.fontset_not_found');
        delete fontsetData.id;
        fontsetData.updatedAt = new Date();
        await this.fontsetRepo.update(id, fontsetData);
        return { ...fontset, ...fontsetData };
    }
    async findById(id) {
        return await this.fontsetRepo.findOneBy({ id });
    }
    async findByProjectId(projectId) {
        return await this.fontsetRepo.findBy({ projectId });
    }
    async findBySiteId(projectId, siteId) {
        return await this.fontsetRepo.findBy({
            projectId: projectId,
            siteId: siteId,
        });
    }
    async delete(id) {
        const fontset = await this.fontsetRepo.findOneBy({ id });
        if (!fontset)
            throw new app_exception_1.AppException('error.fontset_not_found');
        await this.fontsetRepo.delete(fontset.id);
        return true;
    }
};
exports.FontsetService = FontsetService;
__decorate([
    (0, typeorm_1.InjectRepository)(fontset_entity_1.FontsetEntity),
    __metadata("design:type", typeorm_2.Repository)
], FontsetService.prototype, "fontsetRepo", void 0);
exports.FontsetService = FontsetService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [project_service_1.ProjectService])
], FontsetService);
//# sourceMappingURL=fontset.service.js.map