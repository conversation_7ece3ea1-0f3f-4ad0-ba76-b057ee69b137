import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class NewMigraton1745381627020 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}user_info`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const stripeAccountId = new TableColumn({
      name: 'stripeAccountId',
      type: 'varchar',
      length: '250',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, stripeAccountId);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'stripeAccountId');
  }
}
