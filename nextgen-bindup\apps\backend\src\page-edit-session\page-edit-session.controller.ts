import { Controller } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { PageEditSessionService } from './page-edit-session.service';

@Controller('page-edit-session')
export class PageEditSessionController {
  constructor(
    private readonly pageEditSessionService: PageEditSessionService,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  handleCron() {
    this.pageEditSessionService.cronJobDeletePageEditSession();
  }
}
