"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyDocument)\n/* harmony export */ });\n/* harmony import */ var _emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/react/jsx-dev-runtime */ \"@emotion/react/jsx-dev-runtime\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"../../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @emotion/cache */ \"@emotion/cache\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @emotion/react */ \"@emotion/react\");\n/* harmony import */ var _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @emotion/server/create-instance */ \"@emotion/server/create-instance\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__, _emotion_cache__WEBPACK_IMPORTED_MODULE_3__, _emotion_react__WEBPACK_IMPORTED_MODULE_4__, _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_5__]);\n([_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__, _emotion_cache__WEBPACK_IMPORTED_MODULE_3__, _emotion_react__WEBPACK_IMPORTED_MODULE_4__, _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst createEmotionCache = ()=>{\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n        key: 'css'\n    });\n};\nclass MyDocument extends (next_document__WEBPACK_IMPORTED_MODULE_1___default()) {\n    render() {\n        return /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n            children: [\n                /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                    children: /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://unpkg.com/modern-css-reset/dist/reset.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                            fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                            fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this);\n    }\n}\nMyDocument.getInitialProps = async (ctx)=>{\n    const originalRenderPage = ctx.renderPage;\n    const cache = createEmotionCache();\n    const { extractCriticalToChunks } = (0,_emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(cache);\n    ctx.renderPage = ()=>originalRenderPage({\n            enhanceApp: (App)=>(props)=>/*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emotion_react__WEBPACK_IMPORTED_MODULE_4__.CacheProvider, {\n                        value: cache,\n                        children: /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(App, {\n                            ...props\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined)\n        });\n    const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_1___default().getInitialProps(ctx);\n    const emotionStyles = extractCriticalToChunks(initialProps.html);\n    const emotionStyleTags = emotionStyles.styles.map((style)=>/*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n            \"data-emotion\": `${style.key} ${style.ids.join(' ')}`,\n            dangerouslySetInnerHTML: {\n                __html: style.css\n            }\n        }, style.key, false, {\n            fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n            lineNumber: 54,\n            columnNumber: 5\n        }, undefined));\n    return {\n        ...initialProps,\n        styles: [\n            ...react__WEBPACK_IMPORTED_MODULE_2___default().Children.toArray(initialProps.styles),\n            ...emotionStyleTags\n        ]\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2RvY3VtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBTXVCO0FBQ0c7QUFDZTtBQUNNO0FBQ21CO0FBRWxFLE1BQU1TLHFCQUFxQjtJQUN6QixPQUFPSCwwREFBV0EsQ0FBQztRQUFFSSxLQUFLO0lBQU07QUFDbEM7QUFFZSxNQUFNQyxtQkFBbUJYLHNEQUFRQTtJQUM5Q1ksU0FBUztRQUNQLHFCQUNFLHVFQUFDWCwrQ0FBSUE7OzhCQUNILHVFQUFDQywrQ0FBSUE7OEJBQ0gscUZBQUNXO3dCQUNDQyxLQUFJO3dCQUNKQyxNQUFLOzs7Ozs7Ozs7Ozs4QkFHVCx1RUFBQ0M7O3NDQUNDLHVFQUFDYiwrQ0FBSUE7Ozs7O3NDQUNMLHVFQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSW5CO0FBQ0Y7QUFFQU8sV0FBV00sZUFBZSxHQUFHLE9BQU9DO0lBQ2xDLE1BQU1DLHFCQUFxQkQsSUFBSUUsVUFBVTtJQUV6QyxNQUFNQyxRQUFRWjtJQUNkLE1BQU0sRUFBRWEsdUJBQXVCLEVBQUUsR0FBR2QsMkVBQW1CQSxDQUFDYTtJQUV4REgsSUFBSUUsVUFBVSxHQUFHLElBQ2ZELG1CQUFtQjtZQUNqQkksWUFBWUMsQ0FBQUEsTUFBT0MsQ0FBQUEsc0JBQ2pCLHVFQUFDbEIseURBQWFBO3dCQUFDbUIsT0FBT0w7a0NBQ3BCLHFGQUFDRzs0QkFBSyxHQUFHQyxLQUFLOzs7Ozs7Ozs7OztRQUdwQjtJQUVGLE1BQU1FLGVBQWUsTUFBTTNCLG9FQUF3QixDQUFDa0I7SUFDcEQsTUFBTVUsZ0JBQWdCTix3QkFBd0JLLGFBQWFFLElBQUk7SUFDL0QsTUFBTUMsbUJBQW1CRixjQUFjRyxNQUFNLENBQUNDLEdBQUcsQ0FBQ0MsQ0FBQUEsc0JBQ2hELHVFQUFDQTtZQUVDQyxnQkFBYyxHQUFHRCxNQUFNdkIsR0FBRyxDQUFDLENBQUMsRUFBRXVCLE1BQU1FLEdBQUcsQ0FBQ0MsSUFBSSxDQUFDLE1BQU07WUFDbkRDLHlCQUF5QjtnQkFBRUMsUUFBUUwsTUFBTU0sR0FBRztZQUFDO1dBRnhDTixNQUFNdkIsR0FBRzs7Ozs7SUFNbEIsT0FBTztRQUNMLEdBQUdpQixZQUFZO1FBQ2ZJLFFBQVE7ZUFDSDFCLHFEQUFjLENBQUNvQyxPQUFPLENBQUNkLGFBQWFJLE1BQU07ZUFDMUNEO1NBQ0o7SUFDSDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG5leHRnZW4tYmluZHVwL2xpdmUtZWRpdG9yLy4vc3JjL3BhZ2VzL19kb2N1bWVudC50c3g/MTg4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgRG9jdW1lbnQsIHtcclxuICBIdG1sLFxyXG4gIEhlYWQsXHJcbiAgTWFpbixcclxuICBOZXh0U2NyaXB0LFxyXG4gIERvY3VtZW50Q29udGV4dCxcclxufSBmcm9tICduZXh0L2RvY3VtZW50JztcclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IGNyZWF0ZUNhY2hlIGZyb20gJ0BlbW90aW9uL2NhY2hlJztcclxuaW1wb3J0IHsgQ2FjaGVQcm92aWRlciB9IGZyb20gJ0BlbW90aW9uL3JlYWN0JztcclxuaW1wb3J0IGNyZWF0ZUVtb3Rpb25TZXJ2ZXIgZnJvbSAnQGVtb3Rpb24vc2VydmVyL2NyZWF0ZS1pbnN0YW5jZSc7XHJcblxyXG5jb25zdCBjcmVhdGVFbW90aW9uQ2FjaGUgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIGNyZWF0ZUNhY2hlKHsga2V5OiAnY3NzJyB9KTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIE15RG9jdW1lbnQgZXh0ZW5kcyBEb2N1bWVudCB7XHJcbiAgcmVuZGVyKCkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPEh0bWw+XHJcbiAgICAgICAgPEhlYWQ+XHJcbiAgICAgICAgICA8bGlua1xyXG4gICAgICAgICAgICByZWw9XCJzdHlsZXNoZWV0XCJcclxuICAgICAgICAgICAgaHJlZj1cImh0dHBzOi8vdW5wa2cuY29tL21vZGVybi1jc3MtcmVzZXQvZGlzdC9yZXNldC5taW4uY3NzXCJcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9IZWFkPlxyXG4gICAgICAgIDxib2R5PlxyXG4gICAgICAgICAgPE1haW4gLz5cclxuICAgICAgICAgIDxOZXh0U2NyaXB0IC8+XHJcbiAgICAgICAgPC9ib2R5PlxyXG4gICAgICA8L0h0bWw+XHJcbiAgICApO1xyXG4gIH1cclxufVxyXG5cclxuTXlEb2N1bWVudC5nZXRJbml0aWFsUHJvcHMgPSBhc3luYyAoY3R4OiBEb2N1bWVudENvbnRleHQpID0+IHtcclxuICBjb25zdCBvcmlnaW5hbFJlbmRlclBhZ2UgPSBjdHgucmVuZGVyUGFnZTtcclxuXHJcbiAgY29uc3QgY2FjaGUgPSBjcmVhdGVFbW90aW9uQ2FjaGUoKTtcclxuICBjb25zdCB7IGV4dHJhY3RDcml0aWNhbFRvQ2h1bmtzIH0gPSBjcmVhdGVFbW90aW9uU2VydmVyKGNhY2hlKTtcclxuXHJcbiAgY3R4LnJlbmRlclBhZ2UgPSAoKSA9PlxyXG4gICAgb3JpZ2luYWxSZW5kZXJQYWdlKHtcclxuICAgICAgZW5oYW5jZUFwcDogQXBwID0+IHByb3BzID0+IChcclxuICAgICAgICA8Q2FjaGVQcm92aWRlciB2YWx1ZT17Y2FjaGV9PlxyXG4gICAgICAgICAgPEFwcCB7Li4ucHJvcHN9IC8+XHJcbiAgICAgICAgPC9DYWNoZVByb3ZpZGVyPlxyXG4gICAgICApLFxyXG4gICAgfSk7XHJcblxyXG4gIGNvbnN0IGluaXRpYWxQcm9wcyA9IGF3YWl0IERvY3VtZW50LmdldEluaXRpYWxQcm9wcyhjdHgpO1xyXG4gIGNvbnN0IGVtb3Rpb25TdHlsZXMgPSBleHRyYWN0Q3JpdGljYWxUb0NodW5rcyhpbml0aWFsUHJvcHMuaHRtbCk7XHJcbiAgY29uc3QgZW1vdGlvblN0eWxlVGFncyA9IGVtb3Rpb25TdHlsZXMuc3R5bGVzLm1hcChzdHlsZSA9PiAoXHJcbiAgICA8c3R5bGVcclxuICAgICAga2V5PXtzdHlsZS5rZXl9XHJcbiAgICAgIGRhdGEtZW1vdGlvbj17YCR7c3R5bGUua2V5fSAke3N0eWxlLmlkcy5qb2luKCcgJyl9YH1cclxuICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3sgX19odG1sOiBzdHlsZS5jc3MgfX1cclxuICAgIC8+XHJcbiAgKSk7XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICAuLi5pbml0aWFsUHJvcHMsXHJcbiAgICBzdHlsZXM6IFtcclxuICAgICAgLi4uUmVhY3QuQ2hpbGRyZW4udG9BcnJheShpbml0aWFsUHJvcHMuc3R5bGVzKSxcclxuICAgICAgLi4uZW1vdGlvblN0eWxlVGFncyxcclxuICAgIF0sXHJcbiAgfTtcclxufTtcclxuIl0sIm5hbWVzIjpbIkRvY3VtZW50IiwiSHRtbCIsIkhlYWQiLCJNYWluIiwiTmV4dFNjcmlwdCIsIlJlYWN0IiwiY3JlYXRlQ2FjaGUiLCJDYWNoZVByb3ZpZGVyIiwiY3JlYXRlRW1vdGlvblNlcnZlciIsImNyZWF0ZUVtb3Rpb25DYWNoZSIsImtleSIsIk15RG9jdW1lbnQiLCJyZW5kZXIiLCJsaW5rIiwicmVsIiwiaHJlZiIsImJvZHkiLCJnZXRJbml0aWFsUHJvcHMiLCJjdHgiLCJvcmlnaW5hbFJlbmRlclBhZ2UiLCJyZW5kZXJQYWdlIiwiY2FjaGUiLCJleHRyYWN0Q3JpdGljYWxUb0NodW5rcyIsImVuaGFuY2VBcHAiLCJBcHAiLCJwcm9wcyIsInZhbHVlIiwiaW5pdGlhbFByb3BzIiwiZW1vdGlvblN0eWxlcyIsImh0bWwiLCJlbW90aW9uU3R5bGVUYWdzIiwic3R5bGVzIiwibWFwIiwic3R5bGUiLCJkYXRhLWVtb3Rpb24iLCJpZHMiLCJqb2luIiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiLCJjc3MiLCJDaGlsZHJlbiIsInRvQXJyYXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "@emotion/cache":
/*!*********************************!*\
  !*** external "@emotion/cache" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/cache");;

/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/react");;

/***/ }),

/***/ "@emotion/react/jsx-dev-runtime":
/*!*************************************************!*\
  !*** external "@emotion/react/jsx-dev-runtime" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = import("@emotion/react/jsx-dev-runtime");;

/***/ }),

/***/ "@emotion/server/create-instance":
/*!**************************************************!*\
  !*** external "@emotion/server/create-instance" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = import("@emotion/server/create-instance");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./src/pages/_document.tsx")));
module.exports = __webpack_exports__;

})();