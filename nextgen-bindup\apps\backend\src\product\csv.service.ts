import { BadRequestException, Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as csv from 'csv-parser';
import { stringify } from 'csv-stringify';
import { Readable } from 'stream';
@Injectable()
export class CsvService {
  async parseCsv(filePath: string, expectedColumns: string[]): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      let headersChecked = false;

      fs.createReadStream(filePath)
        .pipe(csv())
        .on('headers', (headers: string[]) => {
          if (headers.length !== expectedColumns.length) {
            return reject(
              new BadRequestException(
                `Invalid number of columns. Expected ${expectedColumns.length} but got ${headers.length}.`,
              ),
            );
          }
          for (let i = 0; i < expectedColumns.length; i++) {
            if (headers[i] !== expectedColumns[i]) {
              return reject(
                new BadRequestException(
                  `Column mismatch at position ${i + 1}. Expected "${expectedColumns[i]}" but got "${headers[i]}".`,
                ),
              );
            }
          }
          headersChecked = true;
        })
        .on('data', data => {
          if (headersChecked) {
            results.push(data);
          }
        })
        .on('end', () => {
          if (!headersChecked) {
            return reject(
              new BadRequestException('CSV file is empty or missing headers.'),
            );
          }
          resolve(results);
        })
        .on('error', error => {
          reject(error);
        });
    });
  }

  async exportToCsv<T extends Record<string, any>>(
    data: T[],
    headers?: string[],
  ): Promise<Readable> {
    const columns = headers
      ? headers.map(header => ({ key: header, header: header }))
      : data.length > 0
        ? Object.keys(data[0]).map(key => ({ key, header: key }))
        : [];

    const stringifier = stringify({
      header: true,
      columns: columns,
    });

    const readableStream = new Readable({
      read() {},
    });

    data.forEach(row => {
      stringifier.write(row);
    });
    stringifier.end();

    stringifier.on('data', chunk => {
      readableStream.push(chunk);
    });

    stringifier.on('end', () => {
      readableStream.push(null);
    });

    stringifier.on('error', err => {
      readableStream.destroy(err);
    });

    return readableStream;
  }
}
