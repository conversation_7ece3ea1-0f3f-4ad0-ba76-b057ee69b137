import { forwardRef, Module } from '@nestjs/common';
import { PublishService } from './publish.service';
import { PublishController } from './publish.controller';
import { PageModule } from 'src/page/page.module';
import { FtpService } from './ftp.service';
import { PaymentModule } from 'src/payment/payment.module';
import { CmsCollectionItemsModule } from 'src/cms-collection-items/cms-collection-items.module';
import { ProxyModule } from 'src/proxy/proxy.module';

@Module({
  providers: [PublishService, FtpService],
  imports: [
    forwardRef(() => PageModule),
    PaymentModule,
    CmsCollectionItemsModule,
    ProxyModule,
  ],
  controllers: [PublishController],
  exports: [PublishService],
})
export class PublishModule {}
