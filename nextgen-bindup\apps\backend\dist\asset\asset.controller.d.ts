import { AssetService } from './asset.service';
import { AssetEntity } from './entities/asset.entity';
export declare class AssetController {
    private readonly assetService;
    constructor(assetService: AssetService);
    create(assetEntity: AssetEntity): Promise<AssetEntity>;
    update(assetId: string, data: Partial<AssetEntity>): Promise<AssetEntity>;
    getById(assetId: string): Promise<AssetEntity>;
    getByProjectId(projectId: string): Promise<AssetEntity[]>;
    delete(assetId: string): Promise<boolean>;
    findByUrl(url: string): Promise<AssetEntity>;
}
