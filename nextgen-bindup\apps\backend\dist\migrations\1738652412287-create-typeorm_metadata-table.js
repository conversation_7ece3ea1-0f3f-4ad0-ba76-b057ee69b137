"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTypeOrmMetadataTable1738652412287 = void 0;
const typeorm_1 = require("typeorm");
class CreateTypeOrmMetadataTable1738652412287 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}typeorm_metadata`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'type',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'database',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'schema',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'table',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'value',
                    type: 'text',
                    isNullable: true,
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateTypeOrmMetadataTable1738652412287 = CreateTypeOrmMetadataTable1738652412287;
//# sourceMappingURL=1738652412287-create-typeorm_metadata-table.js.map