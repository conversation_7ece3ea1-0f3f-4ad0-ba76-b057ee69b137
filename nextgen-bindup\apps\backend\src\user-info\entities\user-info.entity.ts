import { Column, CreateDateColumn, Entity, UpdateDateColumn } from 'typeorm';
import {
  CountryCode,
  Industry,
  JobType,
  Occupation,
  Sex,
  UserType,
} from '../enum/user-info.enum';

@Entity('user_info', { schema: process.env.DATABASE_SCHEMA })
export class UserInfoEntity {
  @Column({
    name: 'userId',
    type: 'varchar',
    length: 36,
    primary: true,
    nullable: false,
  })
  userId: string;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string;

  @Column({
    name: 'type',
    type: 'integer',
    nullable: false,
    default: () => `${UserType.INDIVIDUAL}`,
  })
  type: UserType;

  @Column({
    name: 'avatar',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  avatar: string;

  @Column({
    name: 'company',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  company: string;

  @Column({
    name: 'lastNameHira',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  lastNameHira: string;

  @Column({
    name: 'firstNameHira',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  firstNameHira: string;

  @Column({
    name: 'sex',
    type: 'integer',
    nullable: false,
  })
  sex: Sex;

  @Column({
    name: 'countryCode',
    type: 'integer',
    nullable: false,
  })
  countryCode: CountryCode;

  @Column({
    name: 'postalCode',
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  postalCode: string;

  @Column({
    name: 'prefecture',
    type: 'integer',
    nullable: true,
  })
  prefecture: number;

  @Column({
    name: 'municipalities',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  municipalities: string;

  @Column({
    name: 'street',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  street: string;

  @Column({
    name: 'building',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  building: string;

  @Column({
    name: 'phone',
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  phone: string;

  @Column({
    name: 'fax',
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  fax: string;

  @Column({
    name: 'occupation',
    type: 'integer',
    nullable: true,
  })
  occupation: Occupation;

  @Column({
    name: 'industry',
    type: 'integer',
    nullable: true,
  })
  industry: Industry;

  @Column({
    name: 'jobType',
    type: 'integer',
    nullable: true,
  })
  jobType: JobType;

  @Column({
    name: 'birthday',
    type: 'date',
    nullable: true,
  })
  birthday: Date;

  @Column({
    name: 'receiveNewsletter',
    type: 'boolean',
    nullable: true,
  })
  receiveNewsletter: boolean;

  @Column({
    name: 'receiveSupport',
    type: 'boolean',
    nullable: true,
  })
  receiveSupport: boolean;

  @Column({
    name: 'receiveDirectMail',
    type: 'boolean',
    nullable: true,
  })
  receiveDirectMail: boolean;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;

  @Column({
    name: 'recentlySite',
    type: 'jsonb',
    nullable: true,
  })
  recentlySite: number[];

  @Column({
    name: 'stripeCustomerId',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  stripeCustomerId: string;

  @Column({
    name: 'activeSubscription',
    type: 'boolean',
    nullable: true,
  })
  activeSubscription: boolean;

  @Column({
    name: 'stripeAccountId',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  stripeAccountId: string;
}
