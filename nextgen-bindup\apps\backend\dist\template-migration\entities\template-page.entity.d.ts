import { PageStatus, PageType } from 'src/page/types/page.type';
export declare class TemplatePageEntity {
    id: number;
    templateId: number;
    templateSiteId: number;
    templatePageId: number;
    type: PageType;
    parentId: number;
    components: any;
    name: string;
    ts: number;
    status: PageStatus;
    url: string;
    title: string;
    description: string;
    isSearch: boolean;
    thumb: string;
    headCode: string;
    bodyCode: string;
    isPrivate: boolean;
    isHome: boolean;
    children: number[];
    userId: string;
    isDeleted: boolean;
    createdAt: Date;
    updatedAt: Date;
}
