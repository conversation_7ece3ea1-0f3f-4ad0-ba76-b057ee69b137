"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTemplateSitesTable1750649574425 = void 0;
const site_type_1 = require("../site/types/site.type");
const typeorm_1 = require("typeorm");
class CreateTemplateSitesTable1750649574425 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}template_sites`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'templateId',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'managementName',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'status',
                    type: 'smallint',
                    isNullable: false,
                    default: `'${site_type_1.SiteStatus.DRAFT}'`,
                },
                {
                    name: 'url',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'title',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'description',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'isSearch',
                    type: 'boolean',
                    isNullable: true,
                },
                {
                    name: 'thumb',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'headCode',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'bodyCode',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'isArchived',
                    type: 'boolean',
                    isNullable: true,
                },
                {
                    name: 'userId',
                    type: 'varchar',
                    length: '36',
                    isNullable: false,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateTemplateSitesTable1750649574425 = CreateTemplateSitesTable1750649574425;
//# sourceMappingURL=1750649574425-create-template-site-table.js.map