"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentModule = void 0;
const common_1 = require("@nestjs/common");
const payment_controller_1 = require("./payment.controller");
const payment_service_1 = require("./payment.service");
const plan_entity_1 = require("./entities/plan.entity");
const typeorm_1 = require("@nestjs/typeorm");
const subscription_entity_1 = require("./entities/subscription.entity");
const user_info_entity_1 = require("../user-info/entities/user-info.entity");
const user_payment_controller_1 = require("./user-payment.controller");
const user_payment_service_1 = require("./user-payment.service");
const product_module_1 = require("../product/product.module");
const order_module_1 = require("../order/order.module");
const site_module_1 = require("../site/site.module");
const payment_method_module_1 = require("../payment-method/payment-method.module");
const shop_information_settings_module_1 = require("../shop-information-settings/shop-information-settings.module");
const order_complete_settings_module_1 = require("../order-complete-settings/order-complete-settings.module");
let PaymentModule = class PaymentModule {
};
exports.PaymentModule = PaymentModule;
exports.PaymentModule = PaymentModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([plan_entity_1.PlanEntity]),
            typeorm_1.TypeOrmModule.forFeature([subscription_entity_1.SubscriptionEntity]),
            typeorm_1.TypeOrmModule.forFeature([user_info_entity_1.UserInfoEntity]),
            product_module_1.ProductModule,
            order_module_1.OrderModule,
            site_module_1.SiteModule,
            payment_method_module_1.PaymentMethodModule,
            shop_information_settings_module_1.ShopInformationSettingModule,
            order_complete_settings_module_1.OrderCompletionSettingModule,
        ],
        controllers: [payment_controller_1.PaymentController, user_payment_controller_1.UserPaymentController],
        providers: [payment_service_1.PaymentService, user_payment_service_1.UserPaymentService],
        exports: [payment_service_1.PaymentService, user_payment_service_1.UserPaymentService],
    })
], PaymentModule);
//# sourceMappingURL=payment.module.js.map