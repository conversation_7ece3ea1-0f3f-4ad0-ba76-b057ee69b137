"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FontsetModule = void 0;
const common_1 = require("@nestjs/common");
const fontset_controller_1 = require("./fontset.controller");
const fontset_service_1 = require("./fontset.service");
const fontset_entity_1 = require("./entities/fontset.entity");
const typeorm_1 = require("@nestjs/typeorm");
const project_module_1 = require("../project/project.module");
let FontsetModule = class FontsetModule {
};
exports.FontsetModule = FontsetModule;
exports.FontsetModule = FontsetModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([fontset_entity_1.FontsetEntity]), project_module_1.ProjectModule],
        controllers: [fontset_controller_1.FontsetController],
        providers: [fontset_service_1.FontsetService],
        exports: [fontset_service_1.FontsetService],
    })
], FontsetModule);
//# sourceMappingURL=fontset.module.js.map