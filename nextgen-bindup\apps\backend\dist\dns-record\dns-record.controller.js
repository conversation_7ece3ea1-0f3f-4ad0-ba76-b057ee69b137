"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DnsRecordController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../auth/auth.guard");
const dns_record_service_1 = require("./dns-record.service");
const dns_record_entity_1 = require("./entities/dns-record.entity");
let DnsRecordController = class DnsRecordController {
    constructor(dnsRecordService) {
        this.dnsRecordService = dnsRecordService;
    }
    async create(data) {
        return await this.dnsRecordService.create(data);
    }
    async update(id, data) {
        return await this.dnsRecordService.update(+id, data);
    }
    async delete(id) {
        return await this.dnsRecordService.delete(+id);
    }
    async findBySiteId(siteId) {
        return await this.dnsRecordService.findBySiteId(+siteId);
    }
    async findByProjectId(projectId) {
        return await this.dnsRecordService.findByProjectId(+projectId);
    }
};
exports.DnsRecordController = DnsRecordController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dns_record_entity_1.DnsRecordEntity]),
    __metadata("design:returntype", Promise)
], DnsRecordController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DnsRecordController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DnsRecordController.prototype, "delete", null);
__decorate([
    (0, common_1.Get)('site/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DnsRecordController.prototype, "findBySiteId", null);
__decorate([
    (0, common_1.Get)('project/:projectId'),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DnsRecordController.prototype, "findByProjectId", null);
exports.DnsRecordController = DnsRecordController = __decorate([
    (0, common_1.Controller)('dns-record'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [dns_record_service_1.DnsRecordService])
], DnsRecordController);
//# sourceMappingURL=dns-record.controller.js.map