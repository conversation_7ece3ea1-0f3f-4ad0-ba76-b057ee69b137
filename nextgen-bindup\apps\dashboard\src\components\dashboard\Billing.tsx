import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import Paper from '@mui/material/Paper';
import { useAuth } from '../../auth';
import { PlanEntity } from '../../dto/plan.type';
import { Subscription, SubscriptionStatus } from '../../dto/subscription.type';
import { paymentService } from '../../services/payment-service';
import ConfirmationDialog from '../common/ConfirmmationDialog';

export const Billing: FC = () => {
  const { t } = useTranslation();

  const { session } = useAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [openConfirmCancelDialog, setOpenConfirmCancelDialog] = useState(false);
  const fetchSubscription = async () => {
    try {
      const response = await paymentService.getUserSubscription(
        session?.user.userId as string,
      );
      setSubscription(response);
    } catch (error) {
      console.error('Error fetching subscription:', error);
    }
  };

  useEffect(() => {
    fetchSubscription();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session]);

  const handleCancelSubscription = async () => {
    try {
      await paymentService.cancelSubscription(session?.user.userId as string);
      fetchSubscription();
    } catch (error) {
      console.error('Error cancelling subscription:', error);
    }
  };

  return (
    <>
      <YourSubscription
        subscription={subscription}
        handleCancelSubscription={() => {
          setOpenConfirmCancelDialog(true);
        }}
      />
      <HistorySubscription />
      <Plans subscription={subscription} />
      <ConfirmationDialog
        open={openConfirmCancelDialog}
        title={t('billing.confirm_cancel_subscription.title')}
        message={t('billing.confirm_cancel_subscription.message')}
        confirmColor="primary"
        confirmLabel={t('billing.cancel_subscription')}
        onConfirm={async () => {
          try {
            await handleCancelSubscription();
            setOpenConfirmCancelDialog(false);
          } catch {
            // TODO: alert error
          }
        }}
        onCancel={() => {
          setOpenConfirmCancelDialog(false);
        }}
      />
    </>
  );
};
interface YourSubscriptionProps {
  subscription: Subscription | null;
  handleCancelSubscription: () => void;
}
export const YourSubscription: FC<YourSubscriptionProps> = ({
  subscription,
  handleCancelSubscription,
}) => {
  const { t } = useTranslation();

  return (
    <div className="p-4 border rounded-xl">
      <h2 className="text-xl font-semibold">
        {t('billing.your_subscription')}
      </h2>
      {subscription ? (
        <div
          style={{
            border: '2px solid #ccc',
            borderRadius: '8px',
            padding: '16px',
          }}
        >
          <Stack direction="row" spacing={2} alignItems="center">
            {subscription.planImageUrl && (
              <Box>
                <CardMedia
                  component="img"
                  height="140"
                  width={'100%'}
                  image={subscription.planImageUrl}
                  alt="green iguana"
                />
              </Box>
            )}

            <Box>
              <p>
                {t('billing.status')}:{' '}
                {subscription.status === SubscriptionStatus.Active ? (
                  <Chip label="success" color="success" />
                ) : (
                  <Chip label={subscription.status} color="error" />
                )}
              </p>
              <p>
                {t('billing.plan')}: {subscription.planName}
              </p>
              <p>
                {t('billing.price')}: ${subscription.planPrice}{' '}
                {subscription.planCurrency.toUpperCase()}
              </p>
              <p>
                {t('billing.current_period')}:{' '}
                {new Date(subscription.currentPeriodStart).toLocaleDateString()}{' '}
                - {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
              </p>
              <Button
                variant="contained"
                disabled={!!subscription.cancelAt}
                onClick={() => handleCancelSubscription()}
              >
                {t('billing.cancel_subscription')}
              </Button>
            </Box>
          </Stack>
        </div>
      ) : (
        <p>{t('billing.not_active_subscription')}</p>
      )}
    </div>
  );
};

export const HistorySubscription: FC = () => {
  const [history, setHistory] = useState<Subscription[]>([]);
  const { session } = useAuth();
  const { t } = useTranslation();

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        setLoading(true);
        const response = await paymentService.getHistorySubscription(
          session?.user.userId as string,
        );
        setHistory(response);
      } catch (error) {
        console.error('Error fetching subscription history:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchHistory();
  }, [session]);

  if (loading) return <p>{t('billing.loading_message')}</p>;

  return (
    <div className="p-4 border rounded-xl">
      <h2 className="text-xl font-semibold">
        {t('billing.subscription_history')}
      </h2>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell component="th" scope="row">
                {t('billing.plan_name')}
              </TableCell>
              <TableCell component="th" scope="row">
                {t('billing.price')}
              </TableCell>
              <TableCell component="th" scope="row">
                {t('billing.period_start')}
              </TableCell>
              <TableCell component="th" scope="row">
                {t('billing.period_end')}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {history.map(row => (
              <TableRow
                key={row.stripeSubscriptionId}
                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
              >
                <TableCell component="th" scope="row">
                  {row.planName}
                </TableCell>
                <TableCell component="th" scope="row">
                  {`${row.planPrice} ${row.planCurrency.toUpperCase()}`}
                </TableCell>
                <TableCell component="th" scope="row">
                  {new Date(row.currentPeriodStart).toLocaleDateString()}
                </TableCell>
                <TableCell component="th" scope="row">
                  {new Date(row.currentPeriodEnd).toLocaleDateString()}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

interface PlansProps {
  subscription: Subscription | null;
}
export const Plans: FC<PlansProps> = ({ subscription }) => {
  const [plans, setPlans] = useState<PlanEntity[]>([]);
  const { session } = useAuth();
  const isEnableButtonBuy =
    !subscription || subscription.currentPeriodEnd < new Date();
  const [loading, setLoading] = useState(true);
  const { t } = useTranslation();

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        const response = await paymentService.getPlans();
        setPlans(response);
      } catch (error) {
        console.error('Error fetching plans:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchPlans();
  }, [session]);

  const handleSubscribe = async (planId: string) => {
    try {
      const response = await paymentService.createCheckoutSession(
        session?.user.userId as string,
        planId,
      );
      window.location.href = response.url;
    } catch (error) {
      console.error('Error subscribing to plan:', error);
    }
  };

  if (loading) return <p>{t('billing.loading_plan_message')}</p>;

  return (
    <div>
      <h2 className="text-xl font-semibold">{t('billing.plans')}</h2>
      <div
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'space-around',
        }}
      >
        {plans.map((plan: PlanEntity) => (
          <Card
            key={`plan-${plan.id}`}
            style={{
              width: '300px',
              padding: '16px',
              margin: '16px',
              display: 'inline-block',
              backgroundColor: '#f9f9f9',
              borderRadius: '8px',
            }}
          >
            <CardMedia
              component="img"
              height="140"
              width={'100%'}
              image={plan.imageUrl}
              alt="green iguana"
            />
            <CardContent>
              <h2 className="text-xl font-semibold">{plan.name}</h2>
              <p className="text-gray-600">{plan.description}</p>
              <p className="text-lg font-bold mt-2">
                ${plan.price} {plan.currency.toUpperCase()}
              </p>
              <p>{plan.description}</p>
              <Button
                disabled={!isEnableButtonBuy}
                variant="contained"
                className="mt-4 w-full"
                onClick={() => {
                  handleSubscribe(plan.id);
                }}
              >
                {t('billing.subscribe')}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
