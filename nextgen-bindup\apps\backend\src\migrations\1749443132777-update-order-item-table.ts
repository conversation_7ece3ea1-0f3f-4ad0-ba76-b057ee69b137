import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateOrderItemTable1749443132777 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}order_items`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'individualShippingCharges',
        type: 'bigint',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'individualShippingCharges');
  }
}
