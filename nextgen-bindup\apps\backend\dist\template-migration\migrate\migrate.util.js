"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrateUtil = exports.DEFAULT_FLEX_PROP = exports.DEFAULT_TEMPLATE_COMPONENT = void 0;
const component_type_1 = require("../../page/types/component.type");
const prop_action_default_value_1 = require("../../page/utils/prop-action-default-value");
const prop_spacing_default_value_1 = require("../../page/utils/prop-spacing-default-value");
const prop_size_default_value_1 = require("../../page/utils/prop-size-default-value");
const prop_border_default_value_1 = require("../../page/utils/prop-border-default-value");
const prop_position_default_value_1 = require("../../page/utils/prop-position-default-value");
const prop_effect_default_value_1 = require("../../page/utils/prop-effect-default-value");
const prop_filter_default_value_1 = require("../../page/utils/prop-filter-default-value");
const prop_datasource_default_value_1 = require("../../page/utils/prop-datasource-default-value");
const site4_blockdata_dto_1 = require("../dto/site4_blockdata.dto");
const prop_background_default_value_1 = require("../../page/utils/prop-background-default-value");
exports.DEFAULT_TEMPLATE_COMPONENT = {
    __main__: {
        id: '__main__',
        ts: 1,
        name: 'Main',
        type: component_type_1.ComponentType.Main,
        children: [],
        parentId: '__page__',
        breakpoint: {
            phone: { ts: 1 },
            tablet: { ts: 1 },
        },
        properties: {
            ts: 1,
            size: {
                ts: 1,
                width: { unit: '%', value: '100' },
                height: { unit: 'auto', value: '' },
                maxWidth: { unit: 'auto', value: '' },
                minWidth: { unit: 'auto', value: '' },
                overflow: 'unset',
                maxHeight: { unit: 'auto', value: '' },
                minHeight: { unit: '%', value: '100' },
            },
            border: {
                ts: 1,
                top: {
                    color: '#000',
                    width: { unit: 'px', value: '0' },
                    borderStyle: 'none',
                },
                left: {
                    color: '#000',
                    width: { unit: 'px', value: '0' },
                    borderStyle: 'none',
                },
                right: {
                    color: '#000',
                    width: { unit: 'px', value: '0' },
                    borderStyle: 'none',
                },
                bottom: {
                    color: '#000',
                    width: { unit: 'px', value: '0' },
                    borderStyle: 'none',
                },
                isDetail: false,
                radiusTopLeft: {
                    width: { unit: 'px', value: '0' },
                    height: { unit: 'px', value: '0' },
                    isDetail: false,
                },
                radiusTopRight: {
                    width: { unit: 'px', value: '0' },
                    height: { unit: 'px', value: '0' },
                    isDetail: false,
                },
                radiusBottomLeft: {
                    width: { unit: 'px', value: '0' },
                    height: { unit: 'px', value: '0' },
                    isDetail: false,
                },
                radiusBottomRight: {
                    width: { unit: 'px', value: '0' },
                    height: { unit: 'px', value: '0' },
                    isDetail: false,
                },
            },
            filter: { ts: 1, applyTo: 'global' },
            actions: { ts: 1, list: [] },
            effects: { ts: 1, list: [] },
            position: { ts: 1, position: 'relative' },
            backgrounds: { ts: 1, list: [] },
            marginPadding: {
                ts: 1,
                margin: {
                    ts: 0,
                    top: { unit: 'px', value: '0' },
                    left: { unit: 'px', value: '0' },
                    right: { unit: 'px', value: '0' },
                    bottom: { unit: 'px', value: '0' },
                    isDetail: false,
                },
                padding: {
                    ts: 0,
                    top: { unit: 'px', value: '0' },
                    left: { unit: 'px', value: '0' },
                    right: { unit: 'px', value: '0' },
                    bottom: { unit: 'px', value: '0' },
                    isDetail: false,
                },
            },
        },
    },
    __page__: {
        id: '__page__',
        ts: 1,
        name: 'Page',
        type: component_type_1.ComponentType.Page,
        children: ['__main__'],
        breakpoint: {
            phone: { ts: 1 },
            tablet: { ts: 1 },
        },
        properties: {
            ts: 1,
            size: {
                ts: 1,
                width: { unit: '%', value: '100' },
                height: { unit: 'auto', value: '' },
                maxWidth: { unit: 'auto', value: '' },
                minWidth: { unit: 'auto', value: '' },
                overflow: 'unset',
                maxHeight: { unit: 'auto', value: '' },
                minHeight: { unit: 'dvh', value: '100' },
            },
            border: {
                ts: 1,
                top: {
                    color: '#000',
                    width: { unit: 'px', value: '0' },
                    borderStyle: 'none',
                },
                left: {
                    color: '#000',
                    width: { unit: 'px', value: '0' },
                    borderStyle: 'none',
                },
                right: {
                    color: '#000',
                    width: { unit: 'px', value: '0' },
                    borderStyle: 'none',
                },
                bottom: {
                    color: '#000',
                    width: { unit: 'px', value: '0' },
                    borderStyle: 'none',
                },
                isDetail: false,
                radiusTopLeft: {
                    width: { unit: 'px', value: '0' },
                    height: { unit: 'px', value: '0' },
                    isDetail: false,
                },
                radiusTopRight: {
                    width: { unit: 'px', value: '0' },
                    height: { unit: 'px', value: '0' },
                    isDetail: false,
                },
                radiusBottomLeft: {
                    width: { unit: 'px', value: '0' },
                    height: { unit: 'px', value: '0' },
                    isDetail: false,
                },
                radiusBottomRight: {
                    width: { unit: 'px', value: '0' },
                    height: { unit: 'px', value: '0' },
                    isDetail: false,
                },
            },
            filter: { ts: 1, applyTo: 'global' },
            actions: { ts: 1, list: [] },
            effects: { ts: 1, list: [] },
            position: { ts: 1, position: 'relative' },
            backgrounds: { ts: 1, list: [] },
            marginPadding: {
                ts: 1,
                margin: {
                    ts: 0,
                    top: { unit: 'px', value: '0' },
                    left: { unit: 'px', value: '0' },
                    right: { unit: 'px', value: '0' },
                    bottom: { unit: 'px', value: '0' },
                    isDetail: false,
                },
                padding: {
                    ts: 0,
                    top: { unit: 'px', value: '0' },
                    left: { unit: 'px', value: '0' },
                    right: { unit: 'px', value: '0' },
                    bottom: { unit: 'px', value: '0' },
                    isDetail: false,
                },
            },
        },
    },
};
exports.DEFAULT_FLEX_PROP = {
    flexDirection: 'row',
    verSpacing: { value: '0', unit: 'px' },
    hozSpacing: { value: '0', unit: 'px' },
    justifyContent: '',
    alignContent: '',
    alignItems: '',
    flexGrow: '0',
    flexShrink: '0',
    flexWrap: 'nowrap',
};
class MigrateUtil {
    static blockProps() {
        const ts = 1;
        const properties = {
            ts: ts,
            actions: (0, prop_action_default_value_1.PROP_ACTION_DEFAULT_VALUE)(ts),
            marginPadding: (0, prop_spacing_default_value_1.PROP_SPACING_DEFAULT_VALUE)(ts),
            size: (0, prop_size_default_value_1.PROP_SIZE_DEFAULT_VALUE)(ts),
            border: (0, prop_border_default_value_1.PROP_BORDER_DEFAULT_VALUE)(ts),
            position: (0, prop_position_default_value_1.PROP_POSITION_DEFAULT_VALUE)(ts),
            backgrounds: (0, prop_background_default_value_1.PROP_BACKGROUND_LIST_DEFAULT_VALUE)(ts),
            effects: (0, prop_effect_default_value_1.PROP_EFFECT_LIST_DEFAULT_VALUE)(ts),
            filter: (0, prop_filter_default_value_1.PROP_FILTER_DEFAULT_VALUE)(ts),
            datasource: (0, prop_datasource_default_value_1.PROP_DATASOURCE_DEFAULT_VALUE)(ts),
        };
        return properties;
    }
}
exports.MigrateUtil = MigrateUtil;
MigrateUtil.getColumns = (layoutOptID) => {
    switch (layoutOptID) {
        case site4_blockdata_dto_1.BlockData_LayoutOpt.STEP_1:
            return 1;
        case site4_blockdata_dto_1.BlockData_LayoutOpt.STEP_2:
            return 2;
        case site4_blockdata_dto_1.BlockData_LayoutOpt.STEP_3:
            return 3;
        case site4_blockdata_dto_1.BlockData_LayoutOpt.STEP_4:
            return 4;
        case site4_blockdata_dto_1.BlockData_LayoutOpt.STEP_5:
            return 5;
        default:
            return 1;
    }
};
//# sourceMappingURL=migrate.util.js.map