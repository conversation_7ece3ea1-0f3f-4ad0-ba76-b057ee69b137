"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublishController = void 0;
const common_1 = require("@nestjs/common");
const publish_service_1 = require("./publish.service");
let PublishController = class PublishController {
    constructor(publishService) {
        this.publishService = publishService;
    }
    async publish(projectId, siteId, req) {
        req.pageIds = [];
        return await this.publishService.publish(+projectId, +siteId, req);
    }
};
exports.PublishController = PublishController;
__decorate([
    (0, common_1.Post)(':projectId/:siteId'),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Param)('siteId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], PublishController.prototype, "publish", null);
exports.PublishController = PublishController = __decorate([
    (0, common_1.Controller)('publish'),
    __metadata("design:paramtypes", [publish_service_1.PublishService])
], PublishController);
//# sourceMappingURL=publish.controller.js.map