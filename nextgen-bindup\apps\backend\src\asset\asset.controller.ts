import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { AssetService } from './asset.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { AssetEntity } from './entities/asset.entity';

@Controller('assets')
@UseGuards(AuthGuard)
export class AssetController {
  constructor(private readonly assetService: AssetService) {}

  @Post('create')
  async create(@Body() assetEntity: AssetEntity) {
    return await this.assetService.create(assetEntity);
  }

  @Put('update/:assetId')
  async update(
    @Param('assetId') assetId: string,
    @Body() data: Partial<AssetEntity>,
  ) {
    return await this.assetService.update(+assetId, data);
  }

  @Get('one/:assetId')
  async getById(@Param('assetId') assetId: string) {
    return await this.assetService.findById(+assetId);
  }

  @Get('project/:projectId')
  async getByProjectId(@Param('projectId') projectId: string) {
    return await this.assetService.findByProjectId(+projectId);
  }

  @Delete(':assetId')
  async delete(@Param('assetId') assetId: string) {
    return await this.assetService.delete(+assetId);
  }

  @Get('url/:url')
  async findByUrl(@Param('url') url: string) {
    return await this.assetService.findByUrl(url);
  }
}
