import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { NameServersService } from './name-servers.service';
import { NameServerEntity } from './entities/name-server.entity';

@Controller('name-servers')
@UseGuards(AuthGuard)
export class NameServersController {
  constructor(private readonly nameServersService: NameServersService) {}

  @Post('create')
  async create(@Body() data: NameServerEntity) {
    return await this.nameServersService.create(data);
  }

  @Put('update/:id')
  async update(
    @Param('id') id: string,
    @Body() data: Partial<NameServerEntity>,
  ) {
    return await this.nameServersService.update(+id, data);
  }

  @Delete('delete/:id')
  async delete(@Param('id') id: string) {
    return await this.nameServersService.delete(+id);
  }

  @Get('site/:siteId')
  async findBySiteId(@Param('siteId') siteId: string) {
    return await this.nameServersService.findBySiteId(+siteId);
  }

  @Get('project/:projectId')
  async findByProjectId(@Param('projectId') projectId: string) {
    return await this.nameServersService.findByProjectId(+projectId);
  }
}
