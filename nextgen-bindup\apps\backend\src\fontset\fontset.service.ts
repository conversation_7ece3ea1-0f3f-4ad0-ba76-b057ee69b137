import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FontsetEntity } from './entities/fontset.entity';
import { Repository } from 'typeorm';
import { ProjectService } from 'src/project/project.service';
import { AppException } from 'src/common/exceptions/app.exception';

@Injectable()
export class FontsetService {
  @InjectRepository(FontsetEntity)
  readonly fontsetRepo: Repository<FontsetEntity>;

  constructor(private readonly projectService: ProjectService) {}

  async create(fontsetEntity: FontsetEntity): Promise<FontsetEntity> {
    const project = await this.projectService.findById(fontsetEntity.projectId);
    if (!project) throw new AppException('error.project_not_found');

    const now: Date = new Date();
    const fontset = new FontsetEntity();
    fontset.projectId = fontsetEntity.projectId;
    fontset.siteId = fontsetEntity.siteId;
    fontset.name = fontsetEntity.name;
    fontset.fonts = fontsetEntity.fonts;
    fontset.createdAt = now;
    fontset.updatedAt = now;
    return await this.fontsetRepo.save(fontset);
  }

  async update(
    id: number,
    fontsetData: Partial<FontsetEntity>,
  ): Promise<FontsetEntity> {
    const fontset = await this.fontsetRepo.findOneBy({ id: id });
    if (!fontset) throw new AppException('error.fontset_not_found');

    delete fontsetData.id;
    fontsetData.updatedAt = new Date();
    await this.fontsetRepo.update(id, fontsetData);
    return { ...fontset, ...fontsetData };
  }

  async findById(id: number): Promise<FontsetEntity> {
    return await this.fontsetRepo.findOneBy({ id });
  }

  async findByProjectId(projectId: number): Promise<FontsetEntity[]> {
    return await this.fontsetRepo.findBy({ projectId });
  }

  async findBySiteId(
    projectId: number,
    siteId: number,
  ): Promise<FontsetEntity[]> {
    return await this.fontsetRepo.findBy({
      projectId: projectId,
      siteId: siteId,
    });
  }

  async delete(id: number): Promise<boolean> {
    const fontset: FontsetEntity = await this.fontsetRepo.findOneBy({ id });
    if (!fontset) throw new AppException('error.fontset_not_found');

    await this.fontsetRepo.delete(fontset.id);
    return true;
  }
}
