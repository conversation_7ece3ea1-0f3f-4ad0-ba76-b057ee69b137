"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderTable1747620556149 = void 0;
const typeorm_1 = require("typeorm");
class UpdateOrderTable1747620556149 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}orders`;
    }
    async up(queryRunner) {
        await queryRunner.changeColumn(this.TABLE_NAME, 'lastNameKana', new typeorm_1.TableColumn({
            name: 'lastNameKana',
            type: 'varchar',
            length: '255',
            isNullable: true,
        }));
        await queryRunner.changeColumn(this.TABLE_NAME, 'firstNameKana', new typeorm_1.TableColumn({
            name: 'firstNameKana',
            type: 'varchar',
            length: '255',
            isNullable: true,
        }));
        await queryRunner.addColumns(this.TABLE_NAME, [
            new typeorm_1.TableColumn({
                name: 'shippingLastName',
                type: 'varchar',
                length: '255',
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'shippingFirstName',
                type: 'varchar',
                length: '255',
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'shippingLastNameKana',
                type: 'varchar',
                length: '255',
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'shippingFirstNameKana',
                type: 'varchar',
                length: '255',
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'shippingEmail',
                type: 'varchar',
                length: '255',
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'shippingPostalCode',
                type: 'varchar',
                length: '10',
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'shippingPrefecture',
                type: 'varchar',
                length: '255',
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'shippingAddressLine1',
                type: 'varchar',
                length: '255',
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'shippingAddressLine2',
                type: 'varchar',
                length: '255',
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'shippingPhoneNumber',
                type: 'varchar',
                length: '20',
                isNullable: true,
            }),
        ]);
    }
    async down(queryRunner) {
        await queryRunner.changeColumn(this.TABLE_NAME, 'lastNameKana', new typeorm_1.TableColumn({
            name: 'lastNameKana',
            type: 'varchar',
            length: '255',
            isNullable: false,
        }));
        await queryRunner.changeColumn(this.TABLE_NAME, 'firstNameKana', new typeorm_1.TableColumn({
            name: 'firstNameKana',
            type: 'varchar',
            length: '255',
            isNullable: false,
        }));
        await queryRunner.dropColumns(this.TABLE_NAME, [
            'shippingLastName',
            'shippingFirstName',
            'shippingLastNameKana',
            'shippingFirstNameKana',
            'shippingEmail',
            'shippingPostalCode',
            'shippingPrefecture',
            'shippingAddressLine1',
            'shippingAddressLine2',
            'shippingPhoneNumber',
        ]);
    }
}
exports.UpdateOrderTable1747620556149 = UpdateOrderTable1747620556149;
//# sourceMappingURL=1747620556149-update-order-table.js.map