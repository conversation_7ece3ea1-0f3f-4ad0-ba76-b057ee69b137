import { ShippingNoteSettingService } from 'src/shipping-note-settings/shipping-note-settings.service';
import { ShopInformationSettingService } from 'src/shop-information-settings/shop-information-settings.service';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { OrderService } from 'src/order/order.service';
import { ProductStocksService } from 'src/product-stocks/product-stocks.service';
import { CartItem } from 'src/product-stocks/dto/product-stock.dto';
import { UserPaymentService } from 'src/payment/user-payment.service';
import { CreateCheckoutSession } from 'src/payment/dto/create-checkout-session.dto';
import { PaymentMethodType } from 'src/order/enum/payment-method-type.enum';
import { Request } from 'express';
import { SiteService } from 'src/site/site.service';
import { OrderCompletionSettingService } from 'src/order-complete-settings/order-complete-settings.service';
export declare class ProxyController {
    private readonly shippingNoteSettingService;
    private readonly shopInformationSettingService;
    private readonly paymentMethodService;
    private readonly orderService;
    private readonly productStockService;
    private readonly userPaymentService;
    private readonly siteService;
    private readonly orderCompletionSettingService;
    constructor(shippingNoteSettingService: ShippingNoteSettingService, shopInformationSettingService: ShopInformationSettingService, paymentMethodService: PaymentMethodService, orderService: OrderService, productStockService: ProductStocksService, userPaymentService: UserPaymentService, siteService: SiteService, orderCompletionSettingService: OrderCompletionSettingService);
    checkStock(siteId: number, { cartItems }: {
        cartItems: CartItem[];
    }): Promise<import("src/product-stocks/dto/product-stock.dto").CheckStockResponse>;
    getShopInformation(siteId: number): Promise<import("../shop-information-settings/entities/shop-information-settings.entity").ShopInformationSettingEntity>;
    getShippingNoteSetting(siteId: number): Promise<import("../shipping-note-settings/entities/shipping-note--settings.entity").ShippingNoteSettingEntity>;
    getPaymentMethods(siteId: number): Promise<import("../payment-method/dto/payment-method-response.dto").PaymentMethodResponseDto>;
    getOrderCompletionSettings(siteId: number): Promise<import("../order-complete-settings/entities/order-complete-settings.entity").OrderCompletionSettingEntity>;
    getOrderDetail(orderId: string): Promise<import("../order/entites/order.entity").OrderEntity>;
    checkout(siteId: number, req: Request, body: CreateCheckoutSession): Promise<{
        success: boolean;
        paymentMethodType: PaymentMethodType;
        url: string;
        orderId?: undefined;
    } | {
        success: boolean;
        paymentMethodType: PaymentMethodType;
        orderId: number;
        url?: undefined;
    }>;
}
