import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateOrderItemTable1748481763986 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}order_items`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'displayPrice',
        type: 'bigint',
        isNullable: false,
      }),
    );

    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'salePrice',
        type: 'bigint',
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'productType',
        type: 'varchar',
        length: '10',
        isNullable: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // remove displayPrice column
    await queryRunner.dropColumn(this.TABLE_NAME, 'displayPrice');
    // remove salePrice column
    await queryRunner.dropColumn(this.TABLE_NAME, 'salePrice');
    // remove productType column
    await queryRunner.dropColumn(this.TABLE_NAME, 'productType');
  }
}
