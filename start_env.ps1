# NextGen BindUp - Complete Environment Setup and Start Script
# This single script handles everything: setup, dependency installation, and service startup

param(
    [switch]$Help,            # Show help
    [switch]$CheckOnly,       # Only check service status
    [switch]$StopOnly,        # Only stop services
    [switch]$Verbose          # Verbose output
)

# Color functions for better output
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Header { param($Message) Write-Host "`n$Message" -ForegroundColor Green; Write-Host ("=" * $Message.Length) -ForegroundColor Green }

# Configuration
$ProjectRoot = "../nextgen-bindup"
$Services = @(
    @{Name="Backend"; Path="$ProjectRoot/apps/backend"; Port=4000; URL="http://localhost:4000"; EnvFile="backend.env"},
    @{Name="Live Editor"; Path="$ProjectRoot/apps/live-editor"; Port=3000; URL="http://localhost:3000"; EnvFile="live-editor.env"},
    @{Name="Design Editor"; Path="$ProjectRoot/apps/design-editor"; Port=5173; URL="http://localhost:5173"; EnvFile="design-editor.env"},
    @{Name="Dashboard"; Path="$ProjectRoot/apps/dashboard"; Port=5174; URL="http://localhost:5174"; EnvFile="dashboard.env"},
    @{Name="SSG"; Path="$ProjectRoot/apps/ssg"; Port=4321; URL="http://localhost:4321"; EnvFile=""}
)

function Show-Help {
    Write-Header "NextGen BindUp - Environment Setup and Start Script"
    Write-Host ""
    Write-Info "USAGE:"
    Write-Host "  .\start_env.ps1                # Setup and start everything (default)"
    Write-Host "  .\start_env.ps1 -CheckOnly     # Only check service status"
    Write-Host "  .\start_env.ps1 -StopOnly      # Only stop all services"
    Write-Host "  .\start_env.ps1 -Help          # Show this help"
    Write-Host "  .\start_env.ps1 -Verbose       # Enable verbose output"
    Write-Host ""
    Write-Info "DEFAULT BEHAVIOR (no parameters):"
    Write-Host "  1. Check prerequisites (Node.js, Yarn)"
    Write-Host "  2. Create directories and environment files"
    Write-Host "  3. Install all dependencies"
    Write-Host "  4. Start all services"
    Write-Host ""
    Write-Info "SERVICE URLS (after starting):"
    foreach ($service in $Services) {
        Write-Host "  $($service.Name): $($service.URL)" -ForegroundColor White
    }
    Write-Host ""
}

function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check Node.js
    try {
        $nodeVersion = node --version
        Write-Success "✅ Node.js is installed (version: $nodeVersion)"
    } catch {
        Write-Error "❌ Node.js is not installed. Please install Node.js first:"
        Write-Info "   https://nodejs.org/"
        return $false
    }
    
    # Check Yarn
    try {
        $yarnVersion = yarn --version
        Write-Success "✅ Yarn is installed (version: $yarnVersion)"
    } catch {
        Write-Error "❌ Yarn is not installed. Please install Yarn first:"
        Write-Info "   https://yarnpkg.com/getting-started/install"
        return $false
    }
    
    # Check project directory
    if (!(Test-Path $ProjectRoot)) {
        Write-Error "❌ Project directory not found: $ProjectRoot"
        return $false
    }
    Write-Success "✅ Project directory found"
    
    return $true
}

function Setup-Environment {
    Write-Header "Setting up NextGen BindUp Environment"
    
    # Create test directories
    Write-Info "Creating test directories..."
    $directories = @("test-data", "test-results", "test-configs")
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Success "Created $dir directory"
        } else {
            if ($Verbose) { Write-Info "$dir directory already exists" }
        }
    }
    
    # Create environment files
    Create-EnvironmentFiles
    
    # Install test dependencies if package.json exists
    if (Test-Path "package.json") {
        Write-Info "Installing test dependencies..."
        yarn install | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "✅ Test dependencies installed"
        } else {
            Write-Warning "⚠️  Test dependencies installation had issues (continuing anyway)"
        }
    }
    
    # Install project dependencies
    Install-ProjectDependencies
}

function Create-EnvironmentFiles {
    Write-Info "Creating environment configuration files..."
    
    # Live-editor environment
    $liveEditorEnv = @(
        "NEXT_PUBLIC_SUPABASE_URL=https://qxgdiywidkdhfwnqhaxv.supabase.co",
        "NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.akAFcPxyaN4dQEP0ybm2b-UmcWUpv4JkONYDMb2GKRw",
        "NEXT_API_URL=http://localhost:4000"
    )
    Create-EnvFile -Path "test-configs/live-editor.env" -Variables $liveEditorEnv
    
    # Design-editor environment
    $designEditorEnv = @(
        "VITE_SUPABASE_URL=https://qxgdiywidkdhfwnqhaxv.supabase.co",
        "VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.akAFcPxyaN4dQEP0ybm2b-UmcWUpv4JkONYDMb2GKRw",
        "VITE_FONTS_API_KEY=AIzaSyBfadDQvcYbblL_YRVKUKpEgK11E-aUVWU",
        "VITE_API_URL=http://localhost:4000",
        "VITE_USING_API=0"
    )
    Create-EnvFile -Path "test-configs/design-editor.env" -Variables $designEditorEnv
    
    # Backend environment
    $backendEnv = @(
        "APP_JWT=5VuiEbDh4RsOaQVLQvcAgwlgc0BqCqhP",
        "CLOUDFLARE_API_TOKEN=****************************************",
        "CLOUDFLARE_ACCOUNT_ID=9fbbaf4a5d38283722a157b10c2ed59a",
        "SUPABASE_URL=https://qxgdiywidkdhfwnqhaxv.supabase.co",
        "SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.akAFcPxyaN4dQEP0ybm2b-UmcWUpv4JkONYDMb2GKRw"
    )
    Create-EnvFile -Path "test-configs/backend.env" -Variables $backendEnv
    
    # Dashboard environment
    $dashboardEnv = @(
        "VITE_API_URL=http://localhost:4000",
        "VITE_SUPABASE_URL=https://qxgdiywidkdhfwnqhaxv.supabase.co",
        "VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.akAFcPxyaN4dQEP0ybm2b-UmcWUpv4JkONYDMb2GKRw"
    )
    Create-EnvFile -Path "test-configs/dashboard.env" -Variables $dashboardEnv
}

function Create-EnvFile {
    param(
        [string]$Path,
        [string[]]$Variables
    )
    
    $directory = Split-Path $Path -Parent
    if (!(Test-Path $directory)) {
        New-Item -ItemType Directory -Path $directory -Force | Out-Null
    }
    
    if (!(Test-Path $Path)) {
        $Variables | Out-File -FilePath $Path -Encoding UTF8
        Write-Success "Created environment file: $Path"
    } else {
        if ($Verbose) { Write-Info "Environment file already exists: $Path" }
    }
}

function Install-ProjectDependencies {
    Write-Info "Installing project dependencies..."
    
    # Install root dependencies
    if (Test-Path $ProjectRoot) {
        Push-Location $ProjectRoot
        Write-Info "Installing root dependencies..."
        yarn install
        if ($LASTEXITCODE -eq 0) {
            Write-Success "✅ Root dependencies installed"
        } else {
            Write-Error "❌ Failed to install root dependencies"
            Pop-Location
            exit 1
        }
        Pop-Location
    }
    
    # Install app dependencies
    foreach ($service in $Services) {
        if (Test-Path $service.Path) {
            Write-Info "Installing dependencies for $($service.Name)..."
            Push-Location $service.Path
            yarn install
            if ($LASTEXITCODE -eq 0) {
                Write-Success "✅ $($service.Name) dependencies installed"
            } else {
                Write-Warning "⚠️  $($service.Name) dependencies had issues (will retry during startup)"
            }
            Pop-Location
        } else {
            Write-Warning "⚠️  $($service.Name) directory not found, skipping..."
        }
    }
}

function Copy-EnvironmentFiles {
    Write-Info "Copying environment files to project locations..."
    
    foreach ($service in $Services) {
        if ($service.EnvFile -and (Test-Path $service.Path)) {
            $sourceEnv = "test-configs/$($service.EnvFile)"
            $targetEnv = "$($service.Path)/.env"
            
            if (Test-Path $sourceEnv) {
                Copy-Item $sourceEnv $targetEnv -Force
                Write-Success "Copied environment for $($service.Name)"
            }
        }
    }
}

function Start-Services {
    Write-Header "Starting NextGen BindUp Services"
    
    Copy-EnvironmentFiles
    
    Write-Info "Starting services (dependencies will be installed automatically if needed)..."
    
    # Start backend first
    $backendService = $Services | Where-Object { $_.Name -eq "Backend" }
    if ($backendService -and (Test-Path $backendService.Path)) {
        Write-Info "Starting $($backendService.Name)..."
        $startCommand = "cd $($backendService.Path); if (!(Test-Path 'node_modules')) { Write-Host 'Installing dependencies...'; yarn install }; yarn dev"
        Start-Process powershell -ArgumentList "-NoExit", "-Command", $startCommand -WindowStyle Normal
        Start-Sleep -Seconds 3
    }
    
    # Start other services
    foreach ($service in $Services) {
        if ($service.Name -ne "Backend" -and (Test-Path $service.Path)) {
            Write-Info "Starting $($service.Name)..."
            $startCommand = "cd $($service.Path); if (!(Test-Path 'node_modules')) { Write-Host 'Installing dependencies...'; yarn install }; yarn dev"
            Start-Process powershell -ArgumentList "-NoExit", "-Command", $startCommand -WindowStyle Normal
            Start-Sleep -Seconds 1
        }
    }
    
    Write-Success "`n🎉 All services started!"
    Write-Info "Service URLs:"
    foreach ($service in $Services) {
        Write-Host "   $($service.Name): $($service.URL)" -ForegroundColor White
    }
    Write-Host ""
    Write-Success "✅ Environment is ready for development!"
}

function Check-Services {
    Write-Header "Checking Service Status"
    
    $allRunning = $true
    foreach ($service in $Services) {
        try {
            $response = Invoke-WebRequest -Uri $service.URL -TimeoutSec 3 -ErrorAction Stop
            Write-Success "✅ $($service.Name) is running on $($service.URL)"
        } catch {
            Write-Error "❌ $($service.Name) is not running on $($service.URL)"
            $allRunning = $false
        }
    }
    
    if ($allRunning) {
        Write-Success "`n🎉 All services are running!"
    } else {
        Write-Warning "`n⚠️  Some services are not running. Run .\start_env.ps1 to start them."
    }
    
    return $allRunning
}

function Stop-Services {
    Write-Header "Stopping All Services"
    
    foreach ($service in $Services) {
        try {
            $processes = Get-NetTCPConnection -LocalPort $service.Port -ErrorAction SilentlyContinue | 
                         Select-Object -ExpandProperty OwningProcess | 
                         Get-Process -Id { $_ } -ErrorAction SilentlyContinue
            
            if ($processes) {
                foreach ($process in $processes) {
                    Write-Info "Stopping $($service.Name) (PID: $($process.Id))..."
                    Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
                    Write-Success "✅ Stopped $($service.Name)"
                }
            }
        } catch {
            # Ignore errors when stopping
        }
    }
    
    Write-Success "🛑 All services stopped!"
}

# Main execution
try {
    if ($Help) {
        Show-Help
    } elseif ($CheckOnly) {
        Check-Services
    } elseif ($StopOnly) {
        Stop-Services
    } else {
        # Default behavior: Setup and Start everything
        Write-Header "NextGen BindUp - Complete Environment Setup and Start"
        
        if (!(Test-Prerequisites)) {
            exit 1
        }
        
        Setup-Environment
        Start-Services
        
        Write-Host ""
        Write-Info "💡 Use the following commands:"
        Write-Host "   .\start_env.ps1 -CheckOnly    # Check service status"
        Write-Host "   .\start_env.ps1 -StopOnly     # Stop all services"
        Write-Host "   .\start_env.ps1 -Help         # Show help"
    }
} catch {
    Write-Error "An error occurred: $($_.Exception.Message)"
    Write-Info "Use '.\start_env.ps1 -Help' for usage information."
    exit 1
}
