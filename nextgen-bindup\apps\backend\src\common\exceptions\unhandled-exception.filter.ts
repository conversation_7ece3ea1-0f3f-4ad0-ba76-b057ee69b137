import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import { HttpArgumentsHost } from '@nestjs/common/interfaces';
import { LoggerService } from '../../logger/logger.service';

@Catch()
export class UnhandledExceptionFilter implements ExceptionFilter {
  constructor(private readonly loggerService: LoggerService) {}

  catch(exception: any, host: ArgumentsHost) {
    try {
      const ctx: HttpArgumentsHost = host.switchToHttp();
      const response: Response = ctx.getResponse<Response>();
      const status: number =
        exception instanceof HttpException
          ? exception.getStatus()
          : HttpStatus.BAD_REQUEST;

      if (response.locals.requestLog) {
        this.loggerService.error(response.locals.shop, exception.message, {
          meta: response.locals.requestLog,
        });
        response.locals.requestLog = undefined;
      } else {
        this.loggerService.error(response.locals.shop, exception.message);
      }

      let responseMsg: string = 'api.error.bad_bequest';
      if (
        exception.type === 'ValidateException' ||
        exception.type === 'AppException'
      ) {
        responseMsg = exception.message;
      }

      console.error('Unhandled exception:', exception);
      response.status(status).json({ data: responseMsg });
    } catch {}
  }
}
