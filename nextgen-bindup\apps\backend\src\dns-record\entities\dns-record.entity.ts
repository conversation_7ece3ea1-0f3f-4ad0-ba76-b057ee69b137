import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('dns_records', { schema: process.env.DATABASE_SCHEMA })
export class DnsRecordEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'projectId',
    type: 'integer',
    nullable: false,
  })
  projectId: number;

  @Column({
    name: 'type',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  type: string;

  @Column({
    name: 'subdomain',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  subdomain: string;

  @Column({
    name: 'value',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  value: string;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  name: string;

  @Column({
    name: 'ttl',
    type: 'integer',
    nullable: true,
  })
  ttl: number;

  @Column({
    name: 'status',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  status: string;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
