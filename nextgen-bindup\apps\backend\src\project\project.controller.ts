import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { ProjectService } from './project.service';
import { CreateProjectReq } from './dto/project.dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { JwtPayloadDto } from 'src/auth/dto/auth.dto';
import { ExtractUser } from 'src/auth/user.decorator';
import { ProjectEntity } from './entities/project.entity';

@Controller('projects')
@UseGuards(AuthGuard)
export class ProjectController {
  constructor(private readonly projectService: ProjectService) {}

  @Post('create')
  async createProject(
    @ExtractUser() user: JwtPayloadDto,
    @Body() projectData: CreateProjectReq,
  ) {
    return await this.projectService.createProject(user.userId, projectData);
  }

  @Put('update/:id')
  async updateProject(
    @Param('id') projectId: string,
    @Body() updateData: Partial<ProjectEntity>,
  ) {
    return await this.projectService.updateProject(+projectId, updateData);
  }

  @Delete('delete/:id')
  async deleteProject(@Param('id') projectId: string) {
    return await this.projectService.deleteProject(+projectId);
  }

  @Get('me')
  async getProjectsByUser(@ExtractUser() user: JwtPayloadDto) {
    return await this.projectService.getProjectsByUser(user.userId);
  }

  @Get('one/:id')
  async getProjectById(@Param('id') id: string) {
    return await this.projectService.findById(+id);
  }
}
