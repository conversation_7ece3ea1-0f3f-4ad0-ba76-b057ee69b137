"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUpdateProductTable1746783765335 = void 0;
const typeorm_1 = require("typeorm");
class CreateUpdateProductTable1746783765335 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}products`;
    }
    async up(queryRunner) {
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_product_siteId_code',
            columnNames: ['siteId', 'code'],
            isUnique: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_product_siteId_code');
    }
}
exports.CreateUpdateProductTable1746783765335 = CreateUpdateProductTable1746783765335;
//# sourceMappingURL=1746783765335-update-product-table.js.map