import { OrderStatus } from '../enum/order.enum';
import { PaymentMethodType } from '../enum/payment-method-type.enum';
export declare class GetOrdersQueryDto {
    page: number;
    limit: number;
    search?: string;
    email?: string;
    phoneNumber?: string;
    orderStatus?: OrderStatus;
    paymentMethodType?: PaymentMethodType;
    startDate?: string;
    endDate?: string;
    additionalInformation?: string;
}
