"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductStocksModule = void 0;
const common_1 = require("@nestjs/common");
const product_stocks_controller_1 = require("./product-stocks.controller");
const product_stocks_service_1 = require("./product-stocks.service");
const product_stock_entity_1 = require("./entities/product-stock.entity");
const typeorm_1 = require("@nestjs/typeorm");
const product_module_1 = require("../product/product.module");
let ProductStocksModule = class ProductStocksModule {
};
exports.ProductStocksModule = ProductStocksModule;
exports.ProductStocksModule = ProductStocksModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([product_stock_entity_1.ProductStockEntity]),
            (0, common_1.forwardRef)(() => product_module_1.ProductModule),
        ],
        controllers: [product_stocks_controller_1.ProductStocksController],
        providers: [product_stocks_service_1.ProductStocksService],
        exports: [product_stocks_service_1.ProductStocksService],
    })
], ProductStocksModule);
//# sourceMappingURL=product-stocks.module.js.map