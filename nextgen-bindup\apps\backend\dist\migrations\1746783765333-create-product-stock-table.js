"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateProductStockTable1746783765333 = void 0;
const typeorm_1 = require("typeorm");
class CreateProductStockTable1746783765333 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}product_stocks`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'siteId',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'productId',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'x',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'y',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'quantity',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_product_stocks_siteid',
            columnNames: ['siteId'],
            isUnique: false,
        }));
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_product_stocks_productid',
            columnNames: ['productId'],
            isUnique: false,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_product_stocks_siteid');
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_product_stocks_productid');
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateProductStockTable1746783765333 = CreateProductStockTable1746783765333;
//# sourceMappingURL=1746783765333-create-product-stock-table.js.map