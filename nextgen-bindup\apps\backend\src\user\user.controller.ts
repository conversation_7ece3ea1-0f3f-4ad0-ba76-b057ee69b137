import { Controller, Get, UseGuards } from '@nestjs/common';
import { UserService } from './user.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { JwtPayloadDto } from 'src/auth/dto/auth.dto';
import { ExtractUser } from 'src/auth/user.decorator';

@Controller('users')
@UseGuards(AuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('me')
  async findByUserId(@ExtractUser() user: JwtPayloadDto) {
    return await this.userService.findByUserId(user.userId);
  }

  @Get('email/:email')
  async findByEmail(email: string) {
    return await this.userService.findByEmail(email);
  }
}
