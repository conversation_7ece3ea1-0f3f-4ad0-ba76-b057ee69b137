"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOrderTable1745478879555 = void 0;
const typeorm_1 = require("typeorm");
class CreateOrderTable1745478879555 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}orders`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'lastName',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'firstName',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'lastNameKana',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'first<PERSON><PERSON><PERSON><PERSON>',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'email',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'postalCode',
                    type: 'varchar',
                    length: '10',
                    isNullable: false,
                },
                {
                    name: 'prefecture',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'addressLine1',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'addressLine2',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'phoneNumber',
                    type: 'varchar',
                    length: '20',
                    isNullable: true,
                },
                {
                    name: 'shippingLastName',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'shippingFirstName',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'shippingLastNameKana',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'shippingFirstNameKana',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'shippingEmail',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'shippingPostalCode',
                    type: 'varchar',
                    length: '10',
                    isNullable: false,
                },
                {
                    name: 'shippingPrefecture',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'shippingAddressLine1',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'shippingAddressLine2',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'shippingPhoneNumber',
                    type: 'varchar',
                    length: '20',
                    isNullable: true,
                },
                {
                    name: 'totalAmount',
                    type: 'bigint',
                    isNullable: false,
                },
                {
                    name: 'platformFee',
                    type: 'bigint',
                    isNullable: true,
                },
                {
                    name: 'stripeFee',
                    type: 'bigint',
                    isNullable: true,
                },
                {
                    name: 'netAmount',
                    type: 'bigint',
                    isNullable: true,
                },
                {
                    name: 'orderStatus',
                    type: 'varchar',
                    length: '50',
                    isNullable: false,
                },
                {
                    name: 'checkoutSessionUrl',
                    type: 'varchar',
                    length: '2048',
                    isNullable: true,
                },
                {
                    name: 'checkoutSessionId',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'additionalInformation',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false,
                },
                {
                    name: 'updatedAt',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false,
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(this.TABLE_NAME);
    }
}
exports.CreateOrderTable1745478879555 = CreateOrderTable1745478879555;
//# sourceMappingURL=1745478879555-create-order-table.js.map