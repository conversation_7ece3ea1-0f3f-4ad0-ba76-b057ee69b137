"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePageVersion1739859988328 = void 0;
const page_type_1 = require("../page/types/page.type");
const typeorm_1 = require("typeorm");
class CreatePageVersion1739859988328 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}page_versions`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'siteVersionId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'siteId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'pageId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'type',
                    type: 'varchar',
                    length: '10',
                    isNullable: false,
                    default: `'${page_type_1.PageType.PAGE}'`,
                },
                {
                    name: 'parentId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'projectId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'components',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'ts',
                    type: 'bigint',
                    isNullable: true,
                },
                {
                    name: 'status',
                    type: 'smallint',
                    isNullable: false,
                    default: `'${page_type_1.PageStatus.DRAFT}'`,
                },
                {
                    name: 'url',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'title',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'description',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'isSearch',
                    type: 'boolean',
                    isNullable: true,
                },
                {
                    name: 'thumb',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'headCode',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'bodyCode',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'isPrivate',
                    type: 'boolean',
                    isNullable: false,
                    default: 'false',
                },
                {
                    name: 'isHome',
                    type: 'boolean',
                    isNullable: false,
                    default: 'false',
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'versionCreatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'children',
                    type: 'jsonb',
                    isNullable: true,
                    isArray: true,
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreatePageVersion1739859988328 = CreatePageVersion1739859988328;
//# sourceMappingURL=1739859988328-create-page-version-table.js.map