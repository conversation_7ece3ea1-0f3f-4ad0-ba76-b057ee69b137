import { ShippingNoteSettingService } from './shipping-note-settings.service';
import { ShippingNoteSettingEntity } from './entities/shipping-note--settings.entity';
export declare class ShippingNoteSettingController {
    private readonly shippingNoteSettingService;
    constructor(shippingNoteSettingService: ShippingNoteSettingService);
    create(shippingNoteSettingEntity: ShippingNoteSettingEntity): Promise<ShippingNoteSettingEntity>;
    update(id: string, data: Partial<ShippingNoteSettingEntity>): Promise<ShippingNoteSettingEntity>;
    getOneBySiteId(siteId: string): Promise<ShippingNoteSettingEntity>;
}
