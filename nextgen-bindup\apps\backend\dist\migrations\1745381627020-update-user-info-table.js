"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewMigraton************* = void 0;
const typeorm_1 = require("typeorm");
class NewMigraton************* {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}user_info`;
    }
    async up(queryRunner) {
        const stripeAccountId = new typeorm_1.TableColumn({
            name: 'stripeAccountId',
            type: 'varchar',
            length: '250',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, stripeAccountId);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'stripeAccountId');
    }
}
exports.NewMigraton************* = NewMigraton*************;
//# sourceMappingURL=*************-update-user-info-table.js.map