"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteFolderRecursive = exports.copyFile = exports.ensureDirectories = exports.ensureFolderExists = void 0;
const fs = require("fs");
const path = require("path");
const ensureFolderExists = (folderPath) => {
    if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
    }
};
exports.ensureFolderExists = ensureFolderExists;
const ensureDirectories = (basePath, subPath) => {
    const fullPath = path.join(basePath, subPath);
    fs.mkdirSync(fullPath, { recursive: true });
};
exports.ensureDirectories = ensureDirectories;
const copyFile = (source, destination) => {
    const destDir = path.dirname(destination);
    if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
    }
    try {
        fs.copyFileSync(source, destination);
    }
    catch (e) {
        console.error(`Error copying file: ${e}`);
    }
};
exports.copyFile = copyFile;
const deleteFolderRecursive = folderPath => {
    if (fs.existsSync(folderPath)) {
        fs.readdirSync(folderPath).forEach(file => {
            const curPath = path.join(folderPath, file);
            if (fs.lstatSync(curPath).isDirectory()) {
                (0, exports.deleteFolderRecursive)(curPath);
            }
            else {
                fs.unlinkSync(curPath);
            }
        });
        fs.rmdirSync(folderPath);
    }
};
exports.deleteFolderRecursive = deleteFolderRecursive;
//# sourceMappingURL=file.util.js.map