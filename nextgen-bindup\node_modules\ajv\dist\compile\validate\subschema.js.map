{"version": 3, "file": "subschema.js", "sourceRoot": "", "sources": ["../../../lib/compile/validate/subschema.ts"], "names": [], "mappings": ";;;AAEA,wCAA0D;AAC1D,kCAA0D;AA6C1D,SAAgB,YAAY,CAC1B,EAAgB,EAChB,EAAC,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,EAAgB;IAErF,IAAI,OAAO,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QAClD,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAA;IACzE,CAAC;IAED,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QAC1B,MAAM,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAC9B,OAAO,UAAU,KAAK,SAAS;YAC7B,CAAC,CAAC;gBACE,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,IAAA,WAAC,EAAA,GAAG,EAAE,CAAC,UAAU,GAAG,IAAA,qBAAW,EAAC,OAAO,CAAC,EAAE;gBACtD,aAAa,EAAE,GAAG,EAAE,CAAC,aAAa,IAAI,OAAO,EAAE;aAChD;YACH,CAAC,CAAC;gBACE,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC;gBACvB,UAAU,EAAE,IAAA,WAAC,EAAA,GAAG,EAAE,CAAC,UAAU,GAAG,IAAA,qBAAW,EAAC,OAAO,CAAC,GAAG,IAAA,qBAAW,EAAC,UAAU,CAAC,EAAE;gBAChF,aAAa,EAAE,GAAG,EAAE,CAAC,aAAa,IAAI,OAAO,IAAI,IAAA,qBAAc,EAAC,UAAU,CAAC,EAAE;aAC9E,CAAA;IACP,CAAC;IAED,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACzB,IAAI,UAAU,KAAK,SAAS,IAAI,aAAa,KAAK,SAAS,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC1F,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAA;QAChG,CAAC;QACD,OAAO;YACL,MAAM;YACN,UAAU;YACV,YAAY;YACZ,aAAa;SACd,CAAA;IACH,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAA;AAChE,CAAC;AApCD,oCAoCC;AAED,SAAgB,mBAAmB,CACjC,SAA2B,EAC3B,EAAgB,EAChB,EAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAgB;IAE9E,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QACjD,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAA;IACxE,CAAC;IAED,MAAM,EAAC,GAAG,EAAC,GAAG,EAAE,CAAA;IAEhB,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QAC3B,MAAM,EAAC,SAAS,EAAE,WAAW,EAAE,IAAI,EAAC,GAAG,EAAE,CAAA;QACzC,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,IAAA,WAAC,EAAA,GAAG,EAAE,CAAC,IAAI,GAAG,IAAA,qBAAW,EAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QAC7E,gBAAgB,CAAC,QAAQ,CAAC,CAAA;QAC1B,SAAS,CAAC,SAAS,GAAG,IAAA,aAAG,EAAA,GAAG,SAAS,GAAG,IAAA,mBAAY,EAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAA;QAC/F,SAAS,CAAC,kBAAkB,GAAG,IAAA,WAAC,EAAA,GAAG,QAAQ,EAAE,CAAA;QAC7C,SAAS,CAAC,WAAW,GAAG,CAAC,GAAG,WAAW,EAAE,SAAS,CAAC,kBAAkB,CAAC,CAAA;IACxE,CAAC;IAED,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,YAAY,cAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA,CAAC,4BAA4B;QACvG,gBAAgB,CAAC,QAAQ,CAAC,CAAA;QAC1B,IAAI,YAAY,KAAK,SAAS;YAAE,SAAS,CAAC,YAAY,GAAG,YAAY,CAAA;QACrE,2GAA2G;IAC7G,CAAC;IAED,IAAI,SAAS;QAAE,SAAS,CAAC,SAAS,GAAG,SAAS,CAAA;IAE9C,SAAS,gBAAgB,CAAC,SAAe;QACvC,SAAS,CAAC,IAAI,GAAG,SAAS,CAAA;QAC1B,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS,GAAG,CAAC,CAAA;QACtC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAA;QACxB,EAAE,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAU,CAAA;QACxC,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,CAAA;QAC9B,SAAS,CAAC,SAAS,GAAG,CAAC,GAAG,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;IACpD,CAAC;AACH,CAAC;AArCD,kDAqCC;AAED,SAAgB,mBAAmB,CACjC,SAA2B,EAC3B,EAAC,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAgB;IAEtF,IAAI,aAAa,KAAK,SAAS;QAAE,SAAS,CAAC,aAAa,GAAG,aAAa,CAAA;IACxE,IAAI,YAAY,KAAK,SAAS;QAAE,SAAS,CAAC,YAAY,GAAG,YAAY,CAAA;IACrE,IAAI,SAAS,KAAK,SAAS;QAAE,SAAS,CAAC,SAAS,GAAG,SAAS,CAAA;IAC5D,SAAS,CAAC,gBAAgB,GAAG,gBAAgB,CAAA,CAAC,gBAAgB;IAC9D,SAAS,CAAC,WAAW,GAAG,WAAW,CAAA,CAAC,gBAAgB;AACtD,CAAC;AATD,kDASC"}