"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserInfoController = void 0;
const common_1 = require("@nestjs/common");
const user_info_service_1 = require("./user-info.service");
const auth_guard_1 = require("../auth/auth.guard");
const user_decorator_1 = require("../auth/user.decorator");
let UserInfoController = class UserInfoController {
    constructor(userInfoService) {
        this.userInfoService = userInfoService;
    }
    async updateRecently(user, siteId) {
        return await this.userInfoService.updateRecently(user.userId, +siteId);
    }
    async me(user) {
        return await this.userInfoService.findByUserId(user.userId);
    }
    async update(user, data) {
        return await this.userInfoService.update(user.userId, data);
    }
};
exports.UserInfoController = UserInfoController;
__decorate([
    (0, common_1.Put)('recently/:siteId'),
    __param(0, (0, user_decorator_1.ExtractUser)()),
    __param(1, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], UserInfoController.prototype, "updateRecently", null);
__decorate([
    (0, common_1.Get)('me'),
    __param(0, (0, user_decorator_1.ExtractUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserInfoController.prototype, "me", null);
__decorate([
    (0, common_1.Put)('update'),
    __param(0, (0, user_decorator_1.ExtractUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserInfoController.prototype, "update", null);
exports.UserInfoController = UserInfoController = __decorate([
    (0, common_1.Controller)('user-info'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [user_info_service_1.UserInfoService])
], UserInfoController);
//# sourceMappingURL=user-info.controller.js.map