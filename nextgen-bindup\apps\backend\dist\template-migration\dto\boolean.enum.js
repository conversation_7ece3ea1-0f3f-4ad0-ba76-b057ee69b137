"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Boolean3 = exports.Boolean2 = exports.Boolean1 = void 0;
var Boolean1;
(function (Boolean1) {
    Boolean1["TRUE"] = "True";
    Boolean1["FALSE"] = "False";
})(Boolean1 || (exports.Boolean1 = Boolean1 = {}));
var Boolean2;
(function (Boolean2) {
    Boolean2["TRUE"] = "1";
    Boolean2["FALSE"] = "0";
})(Boolean2 || (exports.Boolean2 = Boolean2 = {}));
var Boolean3;
(function (Boolean3) {
    Boolean3["TRUE"] = "true";
    Boolean3["FALSE"] = "false";
})(Boolean3 || (exports.Boolean3 = Boolean3 = {}));
//# sourceMappingURL=boolean.enum.js.map