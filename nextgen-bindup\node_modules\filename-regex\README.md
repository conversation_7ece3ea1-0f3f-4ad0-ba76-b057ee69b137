# filename-regex [![NPM version](https://img.shields.io/npm/v/filename-regex.svg?style=flat)](https://www.npmjs.com/package/filename-regex) [![NPM monthly downloads](https://img.shields.io/npm/dm/filename-regex.svg?style=flat)](https://npmjs.org/package/filename-regex)  [![NPM total downloads](https://img.shields.io/npm/dt/filename-regex.svg?style=flat)](https://npmjs.org/package/filename-regex) [![Linux Build Status](https://img.shields.io/travis/regexhq/filename-regex.svg?style=flat&label=Travis)](https://travis-ci.org/regexhq/filename-regex)

> Regular expression for matching file names, with or without extension.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save filename-regex
```

## Usage

```js
var regex = require('filename-regex');

'a/b/c/d.min.js'.match(regex());
//=> match[0] = 'd.min.js'

'a/b/c/.dotfile'.match(regex());
//=> match[0] = '.dotfile'
```

## About

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.4.3, on April 28, 2017._