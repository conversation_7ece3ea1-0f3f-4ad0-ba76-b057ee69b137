"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderItemTable1750649574422 = void 0;
const typeorm_1 = require("typeorm");
class UpdateOrderItemTable1750649574422 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}order_items`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'image',
            type: 'varchar',
            length: '250',
            isNullable: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'image');
    }
}
exports.UpdateOrderItemTable1750649574422 = UpdateOrderItemTable1750649574422;
//# sourceMappingURL=1750649574422-update-order-item-table.js.map