import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateOrderTable1748587188024 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}orders`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const columns = await queryRunner.getTable(this.TABLE_NAME);
    const notExistShippingFee = !columns?.columns.some(
      col => col.name === 'shippingFee',
    );
    if (notExistShippingFee) {
      await queryRunner.addColumn(
        this.TABLE_NAME,
        new TableColumn({
          name: 'shippingFee',
          type: 'bigint',
          isNullable: true,
        }),
      );
      return;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'shippingFee');
  }
}
