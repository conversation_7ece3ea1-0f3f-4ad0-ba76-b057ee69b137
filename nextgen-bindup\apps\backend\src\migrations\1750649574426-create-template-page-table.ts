import { PageStatus, PageType } from 'src/page/types/page.type';
import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateTemplatePagesTable1750649574426
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}template_pages`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isGenerated: true,
            generationStrategy: 'increment',
            isPrimary: true,
          },
          {
            name: 'templateSiteId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'type',
            type: 'varchar',
            length: '10',
            isNullable: false,
            default: `'${PageType.PAGE}'`,
          },
          {
            name: 'parentId',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'components',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'ts',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'smallint',
            isNullable: false,
            default: `'${PageStatus.DRAFT}'`,
          },
          {
            name: 'url',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'description',
            type: 'text',
          },
          {
            name: 'isSearch',
            type: 'boolean',
          },
          {
            name: 'thumb',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'headCode',
            type: 'text',
          },
          {
            name: 'bodyCode',
            type: 'text',
          },
          {
            name: 'isPrivate',
            type: 'boolean',
            isNullable: false,
            default: 'false',
          },
          {
            name: 'isHome',
            type: 'boolean',
            isNullable: false,
            default: 'false',
          },
          {
            name: 'children',
            type: 'jsonb',
            isNullable: true,
            isArray: true,
          },
          {
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'isDeleted',
            type: 'boolean',
            isNullable: true,
            default: 'false',
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
