import { DeliveryReceiptSettingEntity } from './entities/delivery-receipt-settings.entity';

export const getDefaultDeliveryReceiptSettings = (
  shopName: string,
  email: string,
): Partial<DeliveryReceiptSettingEntity> => {
  const defaultSettings: Partial<DeliveryReceiptSettingEntity> = {
    header: `
このたびは${shopName}・オンラインストアをご利用いただき、まことにありがとうございます。
なにかございましたらお気軽に${email}までご意見やご要望をお寄せください。
またのご利用を心からお待ちしております。
以下のとおり納品申し上げます。
      `,
    footer: `
${shopName}
〒000-0000 東京都○○区○○1-1-1
MAIL：${email}   
      `,
  };
  return defaultSettings;
};
