import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { UserTeamService } from './user-team.service';

@Controller('user-team')
@UseGuards(AuthGuard)
export class UserTeamController {
  constructor(private readonly userTeamService: UserTeamService) {}

  @Get('by-root/:rootUserId')
  async getAllByRootUserId(@Param('rootUserId') rootUserId: string) {
    return await this.userTeamService.getAllByRootUserId(rootUserId);
  }

  @Get('by-team/:rootUserId/:teamId')
  async getAllByTeamId(
    @Param('rootUserId') rootUserId: string,
    @Param('teamId') teamId: string,
  ) {
    return await this.userTeamService.getAllByTeamId(rootUserId, +teamId);
  }

  @Get('member-info-by-team/:rootUserId/:teamId')
  async getMemberInfoByTeamId(
    @Param('rootUserId') rootUserId: string,
    @Param('teamId') teamId: string,
  ) {
    return await this.userTeamService.getMemberInfoByTeamId(
      rootUserId,
      +teamId,
    );
  }

  @Get('teams-by-member/:rootUserId')
  async getTeamsOfMember(@Param('rootUserId') rootUserId: string) {
    return await this.userTeamService.getTeamsOfMember(rootUserId);
  }
}
