import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateCmsArticleTable1743388643397 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}cms_articles`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'name');

    const title: TableColumn = new TableColumn({
      name: 'title',
      type: 'varchar',
      length: '1000',
      isNullable: false,
    });
    await queryRunner.addColumn(this.TABLE_NAME, title);

    const slug: TableColumn = new TableColumn({
      name: 'slug',
      type: 'varchar',
      length: '1000',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, slug);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'title');
    await queryRunner.dropColumn(this.TABLE_NAME, 'slug');
  }
}
