"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublishService = void 0;
const common_1 = require("@nestjs/common");
const fs = require("fs");
const util_1 = require("util");
const path = require("path");
const child_process_1 = require("child_process");
const page_service_1 = require("../page/page.service");
const ftp_service_1 = require("./ftp.service");
const page_type_1 = require("../page/types/page.type");
const user_payment_service_1 = require("../payment/user-payment.service");
const uuid_1 = require("uuid");
const cms_collection_items_service_1 = require("../cms-collection-items/cms-collection-items.service");
const site_auth_service_1 = require("../proxy/site-auth.service");
const execPromise = (0, util_1.promisify)(child_process_1.exec);
let PublishService = class PublishService {
    constructor(pageService, ftpService, userPaymentService, cmsCollectionItemsService, siteAuthService) {
        this.pageService = pageService;
        this.ftpService = ftpService;
        this.userPaymentService = userPaymentService;
        this.cmsCollectionItemsService = cmsCollectionItemsService;
        this.siteAuthService = siteAuthService;
        this.baseDir = path.resolve(__dirname, '..', '..', '..', '..', 'apps', 'ssg');
    }
    async publish(projectId, siteId, req) {
        const buildDir = await this.buildStaticSite(projectId, siteId, req);
        if (buildDir) {
            await this.ftpService.uploadFTP(buildDir, '/');
            fs.rmSync(buildDir, { recursive: true, force: true });
            console.log('Published successfully');
            return true;
        }
        return false;
    }
    async buildStaticSite(projectId, siteId, req) {
        const buildOutput = `out/${(0, uuid_1.v4)()}`;
        const fullBuildOutput = path.join(this.baseDir, buildOutput);
        console.log('fullBuildOutput:', fullBuildOutput);
        const [allPages, collectionItems] = await Promise.all([
            this.pageService.getBySiteIdForRender(siteId),
            this.cmsCollectionItemsService.findBySiteId(siteId),
        ]);
        const pagesStructure = this.buildStructure(allPages, collectionItems);
        let pageIds = '';
        if (req.pageIds)
            pageIds = req.pageIds.join(',');
        const siteToken = await this.siteAuthService.generateToken(`${siteId}`);
        const { stdout, stderr } = await execPromise(`cd ../ssg && yarn build --outDir ${buildOutput}`, {
            env: {
                ...process.env,
                NEXT_PUBLIC_SUPABASE_URL: process.env.SUPABASE_URL,
                NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.SUPABASE_KEY,
                NEXT_PUBLIC_PROJECT_ID: `${projectId}`,
                NEXT_PUBLIC_SITE_ID: `${siteId}`,
                NEXT_PUBLIC_PAGE_ID: pageIds,
                NEXT_PUBLIC_SITE_TOKEN: siteToken,
            },
        });
        console.log(stdout);
        if (stderr) {
            throw new Error(stderr);
        }
        if (!fs.existsSync(fullBuildOutput)) {
            console.log(`NOT FOUND: ${fullBuildOutput}`);
            return null;
        }
        const refactorOutput = `${fullBuildOutput}_upload`;
        fs.mkdirSync(refactorOutput);
        for (const page of pagesStructure) {
            const newPath = this.createPath(refactorOutput, page.paths);
            this.moveFile(fullBuildOutput, page, newPath);
        }
        const fixFolders = [
            '_astro',
            'cart',
            'checkout',
            'order-complete',
        ];
        for (const folder of fixFolders) {
            fs.cpSync(`${fullBuildOutput}/${folder}`, `${refactorOutput}/${folder}`, {
                recursive: true,
            });
        }
        const fixFiles = ['favicon.ico'];
        for (const file of fixFiles) {
            fs.copyFileSync(`${fullBuildOutput}/${file}`, `${refactorOutput}/${file}`, fs.constants.COPYFILE_EXCL);
        }
        fs.rmSync(fullBuildOutput, { recursive: true, force: true });
        return refactorOutput;
    }
    buildStructure(allPages, collectionItems) {
        const result = [];
        for (const page of allPages) {
            if (page.type === page_type_1.PageType.ROOT)
                continue;
            page.paths = [];
            page.ssgID = `${page.id}`;
            if (page.type === page_type_1.PageType.BLOG || page.type === page_type_1.PageType.DIRECTORY) {
                page.paths.push(page.url || this.sanitizedUrl(page.name));
            }
            let parent = null;
            if (page.parentId)
                parent = allPages.find(p => p.id === page.parentId);
            if (parent && parent.type === page_type_1.PageType.ROOT)
                parent = null;
            while (parent) {
                if (parent.paths && parent.paths.length) {
                    page.paths.unshift(...parent.paths);
                    break;
                }
                else {
                    page.paths.unshift(parent.url || this.sanitizedUrl(parent.name));
                }
                if (parent.parentId)
                    parent = allPages.find(p => p.id === parent.parentId);
                if (parent && parent.type === page_type_1.PageType.ROOT)
                    parent = null;
            }
            if (page.datasource?.collectionId && page.url) {
                const items = collectionItems[page.datasource.collectionId];
                for (const item of items) {
                    const blogPage = structuredClone(page);
                    blogPage.ssgID = `${blogPage.id}-${item.cmsCollectionId}-${item.id}`;
                    blogPage.url = blogPage.url
                        .replace('{slug}', item.slug)
                        .replace('{id}', `${item.id || '0'}`);
                    result.push(blogPage);
                }
            }
            if (page.type === page_type_1.PageType.PAGE || page.type === page_type_1.PageType.BLOG_LIST) {
                if (page.isHome)
                    page.url = 'index';
                else if (!page.url)
                    page.url = this.sanitizedUrl(page.name);
            }
            result.push(page);
        }
        return result;
    }
    sanitizedUrl(url) {
        return url
            .toLocaleLowerCase()
            .trim()
            .replace(/ +/g, '_')
            .replace(/_+/g, '-');
    }
    createPath(base, paths) {
        let path = base;
        for (const p of paths) {
            path += `/${p}`;
            if (!fs.existsSync(path))
                fs.mkdirSync(path);
        }
        return path;
    }
    moveFile(base, page, newPath) {
        const oldFile = `${base}/${page.ssgID}/index.html`;
        const newFile = `${newPath}/${page.url}.html`;
        if (fs.existsSync(oldFile)) {
            fs.copyFileSync(oldFile, newFile, fs.constants.COPYFILE_EXCL);
        }
    }
};
exports.PublishService = PublishService;
exports.PublishService = PublishService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [page_service_1.PageService,
        ftp_service_1.FtpService,
        user_payment_service_1.UserPaymentService,
        cms_collection_items_service_1.CmsCollectionItemsService,
        site_auth_service_1.SiteAuthService])
], PublishService);
//# sourceMappingURL=publish.service.js.map