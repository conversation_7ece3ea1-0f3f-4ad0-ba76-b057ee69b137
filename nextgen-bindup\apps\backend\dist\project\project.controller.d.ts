import { ProjectService } from './project.service';
import { CreateProjectReq } from './dto/project.dto';
import { JwtPayloadDto } from 'src/auth/dto/auth.dto';
import { ProjectEntity } from './entities/project.entity';
export declare class ProjectController {
    private readonly projectService;
    constructor(projectService: ProjectService);
    createProject(user: JwtPayloadDto, projectData: CreateProjectReq): Promise<ProjectEntity>;
    updateProject(projectId: string, updateData: Partial<ProjectEntity>): Promise<ProjectEntity>;
    deleteProject(projectId: string): Promise<boolean>;
    getProjectsByUser(user: JwtPayloadDto): Promise<import("./dto/project.dto").Project[]>;
    getProjectById(id: string): Promise<ProjectEntity>;
}
