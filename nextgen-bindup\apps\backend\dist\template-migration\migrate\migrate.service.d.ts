import { Site2_Page } from '../dto/site2_page.dto';
import { Site3_Block } from '../dto/site3_block.dto';
import { Site4_BlockData } from '../dto/site4_blockdata.dto';
import { Site5_Resource } from '../dto/site5_resource.dto';
import { TemplatePageIdDto } from '../dto/template_page_id.dto';
import { Component } from '@nextgen-bindup/common/dto/component';
export declare class MigrateService {
    dbdata: string;
    components: Record<string, Component>;
    templatePageIds: TemplatePageIdDto[];
    page: Site2_Page;
    blocks: Site3_Block[];
    blockDatas: Site4_BlockData[];
    resources: Site5_Resource[];
    ID_INDEX: number;
    constructor(inp: {
        dbdata: string;
        templatePageIds: TemplatePageIdDto[];
    });
    migratePage(inp: {
        page: Site2_Page;
        components: Record<string, Component>;
    }): Record<string, Component>;
    private createBillboard;
    private createSideA;
    private createSideB;
    private createHeader;
    private createFooter;
    private checkParentSide;
}
