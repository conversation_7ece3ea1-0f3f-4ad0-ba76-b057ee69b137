{"name": "first-chunk-stream", "version": "1.0.0", "description": "Transform the first chunk in a stream", "license": "MIT", "repository": "sindresorhus/first-chunk-stream", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["buffer", "stream", "streams", "transform", "first", "chunk", "size", "min", "minimum"], "devDependencies": {"concat-stream": "^1.4.5", "mocha": "*"}}