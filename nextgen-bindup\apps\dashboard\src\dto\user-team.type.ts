export type UserTeamDto = {
  id?: number | null;
  userId: string;
  email: string;
  teamName: string;
  fullname: string;
  isAdmin: boolean;
  isDeleted?: boolean;
};

export type UserTeamEntity = {
  id?: number;
  rootUserId: string;
  userId: string | null;
  email: string;
  teamId: number;
  isAdmin: boolean;
};

export type InviteMemberReq = {
  team: {
    id: number;
    name: string;
  };
  emails: string[];
};

export interface UpdateMemberOfTeamReq {
  teamId: number;
  members: UpdateTeamMemberData[];
}

export interface UpdateTeamMemberData {
  id?: number | null;
  email: string;
  isAdmin: boolean;
  isDeleted?: boolean;
}
