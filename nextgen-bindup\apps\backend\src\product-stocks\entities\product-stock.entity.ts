import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity(`product_stocks`, {
  schema: process.env.DATABASE_SCHEMA,
})
export class ProductStockEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @Column({
    name: 'productId',
    type: 'integer',
    nullable: false,
  })
  productId: number;

  @Column({
    name: 'x',
    type: 'integer',
    nullable: false,
  })
  x: number;

  @Column({
    name: 'y',
    type: 'integer',
    nullable: false,
  })
  y: number;

  @Column({
    name: 'quantity',
    type: 'integer',
    nullable: false,
  })
  quantity: number;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
