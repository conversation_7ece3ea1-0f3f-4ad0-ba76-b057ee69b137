import { Injectable } from '@nestjs/common';
import { AssetComponent } from './entities/asset-component.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProjectService } from 'src/project/project.service';
import { AppException } from 'src/common/exceptions/app.exception';

@Injectable()
export class AssetComponentService {
  @InjectRepository(AssetComponent)
  readonly assetComponentRepository: Repository<AssetComponent>;

  constructor(private readonly projectService: ProjectService) {}

  async create(assetComponent: AssetComponent): Promise<AssetComponent> {
    const project = await this.projectService.findById(
      assetComponent.projectId,
    );
    if (!project) throw new AppException('error.project_not_found');

    const now: Date = new Date();
    const component = new AssetComponent();
    component.type = assetComponent.type;
    component.projectId = assetComponent.projectId;
    component.siteId = assetComponent.siteId;
    component.name = assetComponent.name;
    component.data = assetComponent.data;
    component.createdAt = now;
    component.updatedAt = now;
    return await this.assetComponentRepository.save(component);
  }

  async update(
    id: string,
    assetComponent: Partial<AssetComponent>,
  ): Promise<AssetComponent> {
    const component = await this.assetComponentRepository.findOneBy({ id: id });
    if (!component) throw new AppException('error.asset_component_not_found');

    delete assetComponent.id;
    assetComponent.updatedAt = new Date();
    await this.assetComponentRepository.update(id, assetComponent);
    return { ...component, ...assetComponent };
  }

  async findById(id: string): Promise<AssetComponent> {
    return await this.assetComponentRepository.findOneBy({ id });
  }

  async findByProjectId(projectId: number): Promise<AssetComponent[]> {
    return await this.assetComponentRepository.findBy({ projectId });
  }

  async findBySiteId(
    projectId: number,
    siteId: number,
  ): Promise<AssetComponent[]> {
    return await this.assetComponentRepository.findBy({
      projectId: projectId,
      siteId: siteId,
    });
  }

  async delete(id: string): Promise<boolean> {
    const asset: AssetComponent = await this.assetComponentRepository.findOneBy(
      { id },
    );
    if (!asset) throw new AppException('error.asset_component_not_found');

    await this.assetComponentRepository.delete(id);
    return true;
  }
}
