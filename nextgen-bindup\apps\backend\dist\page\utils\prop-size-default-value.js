"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PROP_PAGE_SIZE_DEFAULT_VALUE = exports.PROP_SIZE_DEFAULT_VALUE = void 0;
const PROP_SIZE_DEFAULT_VALUE = (ts, prop) => ({
    ts: ts,
    width: {
        value: '',
        unit: 'auto',
    },
    height: {
        value: '',
        unit: 'auto',
    },
    minWidth: {
        value: '',
        unit: 'auto',
    },
    maxWidth: {
        value: '',
        unit: 'auto',
    },
    minHeight: {
        value: '',
        unit: 'auto',
    },
    maxHeight: {
        value: '',
        unit: 'auto',
    },
    overflow: 'unset',
    ...(prop || undefined),
});
exports.PROP_SIZE_DEFAULT_VALUE = PROP_SIZE_DEFAULT_VALUE;
const PROP_PAGE_SIZE_DEFAULT_VALUE = (ts, prop) => ({
    width: {
        value: '100',
        unit: '%',
    },
    height: {
        value: '',
        unit: 'auto',
    },
    minWidth: {
        value: '',
        unit: 'auto',
    },
    maxWidth: {
        value: '',
        unit: 'auto',
    },
    minHeight: {
        value: '100',
        unit: 'dvh',
    },
    maxHeight: {
        value: '',
        unit: 'auto',
    },
    overflow: 'unset',
    ts: ts,
    ...(prop || undefined),
});
exports.PROP_PAGE_SIZE_DEFAULT_VALUE = PROP_PAGE_SIZE_DEFAULT_VALUE;
//# sourceMappingURL=prop-size-default-value.js.map