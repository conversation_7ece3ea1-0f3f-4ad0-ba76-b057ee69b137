import { Injectable } from '@nestjs/common';
import { CmsCollectionEntity } from './entities/cms-collection.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppException } from 'src/common/exceptions/app.exception';
import { PRODUCT_COLUMNS } from 'src/product/constants';
import { CmsDataType } from '@nextgen-bindup/common/dto/cms-collection/cms-collection-struct.dto';

@Injectable()
export class CmsCollectionService {
  @InjectRepository(CmsCollectionEntity)
  readonly collectionRepo: Repository<CmsCollectionEntity>;

  constructor() {}

  async create(
    collectionEntity: CmsCollectionEntity,
  ): Promise<CmsCollectionEntity> {
    const now: Date = new Date();
    const collection = new CmsCollectionEntity();
    collection.siteId = collectionEntity.siteId;
    collection.name = collectionEntity.name;
    collection.slug = collectionEntity.slug;
    collection.struct = collectionEntity.struct;
    collection.rootUserId = collectionEntity.rootUserId;
    collection.userId = collectionEntity.userId;
    collection.dataType = CmsDataType.CUSTOM;
    collection.createdAt = now;
    collection.updatedAt = now;
    return await this.collectionRepo.save(collection);
  }

  async update(
    id: number,
    collectionData: Partial<CmsCollectionEntity>,
  ): Promise<CmsCollectionEntity> {
    const collection = await this.collectionRepo.findOneBy({ id: id });
    if (!collection) throw new AppException('cms.collection.error.not_found');

    delete collectionData.id;
    delete collectionData.siteId;
    delete collectionData.rootUserId;
    delete collectionData.createdAt;
    collectionData.dataType = collection.dataType;
    collectionData.updatedAt = new Date();

    await this.collectionRepo.update(id, collectionData);
    return { ...collection, ...collectionData };
  }

  async findById(id: number): Promise<CmsCollectionEntity> {
    return await this.collectionRepo.findOneBy({ id });
  }

  async findByDataType(
    siteId: number,
    dataType: CmsDataType,
  ): Promise<CmsCollectionEntity> {
    return await this.collectionRepo.findOneBy({
      siteId: siteId,
      dataType: dataType,
    });
  }

  async findBySiteId(siteId: number): Promise<CmsCollectionEntity[]> {
    return await this.collectionRepo.findBy({ siteId });
  }

  async findWithProductBySiteId(
    siteId: number,
  ): Promise<CmsCollectionEntity[]> {
    const productCollection = new CmsCollectionEntity();
    productCollection.id = -1;
    productCollection.siteId = siteId;
    productCollection.name = 'Product Mapping';
    productCollection.slug = {
      id: -1,
      label: 'Slug',
      desc: '',
      type: 'slug',
      required: true,
      extra: { baseOn: 0 },
    };

    productCollection.struct = PRODUCT_COLUMNS.map((column, index) => ({
      id: index + 1,
      label: column,
      desc: '',
      type: 'text',
      required: false,
      extra: { baseOn: 0 },
    }));

    const collections = await this.collectionRepo.findBy({ siteId });
    return [productCollection, ...collections];
  }

  async delete(id: number): Promise<boolean> {
    const collection = await this.collectionRepo.findOneBy({ id });
    if (!collection) throw new AppException('cms.collection.error.not_found');

    if (collection.dataType !== CmsDataType.CUSTOM)
      throw new AppException('cms.collection.error.cannot_delete_default');

    await this.collectionRepo.delete(id);
    return true;
  }
}
