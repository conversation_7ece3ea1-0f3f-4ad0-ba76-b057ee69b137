import { Repository } from 'typeorm';
import { ShippingNoteSettingEntity } from './entities/shipping-note--settings.entity';
export declare class ShippingNoteSettingService {
    readonly shippingNoteSettingRepo: Repository<ShippingNoteSettingEntity>;
    constructor();
    create(shippingNoteSettingEntity: ShippingNoteSettingEntity): Promise<ShippingNoteSettingEntity>;
    update(id: number, settingData: Partial<ShippingNoteSettingEntity>): Promise<ShippingNoteSettingEntity>;
    findById(id: number): Promise<ShippingNoteSettingEntity>;
    findOneBySiteId(siteId: number): Promise<ShippingNoteSettingEntity>;
    delete(id: number): Promise<boolean>;
}
