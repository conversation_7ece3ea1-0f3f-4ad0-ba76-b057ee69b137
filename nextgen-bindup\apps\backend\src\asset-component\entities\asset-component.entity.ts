import {
  Column,
  CreateDate<PERSON>olumn,
  <PERSON>tity,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AssetComponentType } from '../enum/asset-component-type.enum';
import { AssetComponentData } from '@nextgen-bindup/common/dto/assetComponent';

@Entity('asset_component', { schema: process.env.DATABASE_SCHEMA })
// export class ContentEntity {
export class AssetComponent {
  @PrimaryColumn({
    name: 'id',
    type: 'varchar',
    length: 36,
  })
  id: string;

  @Column({
    name: 'type',
    type: 'smallint',
    nullable: false,
    default: AssetComponentType.COMPONENT,
  })
  type: AssetComponentType;

  @Column({
    name: 'projectId',
    type: 'integer',
    nullable: true,
  })
  projectId: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
    default: 1,
  })
  siteId: number;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  name: string;

  @Column({
    name: 'folder',
    type: 'boolean',
    default: false,
  })
  folder: boolean;

  @Column({
    name: 'parentFolder',
    type: 'varchar',
    nullable: false,
    length: 255,
    default: '',
  })
  parentFolder: string;

  @Column({
    name: 'data',
    type: 'jsonb',
    nullable: true,
  })
  data: AssetComponentData;

  @Column({
    name: 'ts',
    type: 'bigint',
    nullable: true,
  })
  ts: number;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
