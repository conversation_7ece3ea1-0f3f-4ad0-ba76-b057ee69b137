import { useCallback, type FC, useEffect, useState, Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/styled';
import AddIcon from '@mui/icons-material/Add';
import { Dialog, Typography } from '@mui/material';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import { grey } from '@mui/material/colors';
import { useNavigate } from '@tanstack/react-router';
import { useAuth } from '../../auth';
import { ProjectFolderEntity } from '../../dto/project-folder.type';
import { ProjectEntity } from '../../dto/project.type';
import { ProjectInfo } from '../../routes/index.route';
import { projectFolderService } from '../../services/project-folder-service';
import { projectService } from '../../services/project-service';
import { PAGE_TYPE } from '../common/constants';
import { ProjectFolderForm } from './project-folder-form';
import { ProjectForm } from './project-form';

type Props = {
  selectedPage: (page: PAGE_TYPE) => void;
  setProjectInfo: (info: ProjectInfo) => void;
};

const Sidebar: FC<Props> = ({ selectedPage, setProjectInfo }) => {
  const { t } = useTranslation();
  const { session } = useAuth();
  const [projects, setProjects] = useState<ProjectEntity[]>([]);
  const navigate = useNavigate();
  const handleTemplates = useCallback(() => {
    navigate({ to: '/templates' });
  }, [navigate]);
  const [newProjectFolder, setNewProjectFolder] = useState<ProjectFolderEntity>(
    {
      id: undefined,
      projectId: undefined,
      name: '',
    },
  );

  const [isOpenNewPFDialog, setIsOpenNewPFDialog] = useState(false);

  const [newProject, setNewProject] = useState<ProjectEntity>({
    id: undefined,
    name: '',
  });

  const [isOpenNewProjectDialog, setIsOpenNewProjectDialog] = useState(false);

  useEffect(() => {
    const fetchProjects = async () => {
      if (!session) return;
      const data = await projectService.getMyProjects();
      if (data) setProjects(data);

      if (data && data.length > 0) {
        setProjectInfo({ id: data[0].id });
      }
    };
    fetchProjects();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleNewProjectFolderInputChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = event.target;
    setNewProjectFolder({ ...newProjectFolder, [name]: value });
  };

  const handleNewProjectInputChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = event.target;
    setNewProject({ ...newProject, [name]: value });
  };

  const handleCreateProjectFolder = async () => {
    try {
      if (!session) return;
      await projectFolderService.create(newProjectFolder);
      setIsOpenNewPFDialog(false);
      const projects = await projectService.getMyProjects();
      setProjects(projects);
      setNewProjectFolder({
        id: undefined,
        projectId: undefined,
        name: '',
      });
    } catch (error) {
      console.error('Error creating Project Folder:', error);
    }
  };

  const handleCreateProject = async () => {
    try {
      if (!session) return;
      await projectService.create(newProject);
      setIsOpenNewProjectDialog(false);
      const projects = await projectService.getMyProjects();
      setProjects(projects || []);
      setNewProjectFolder({
        id: undefined,
        projectId: undefined,
        name: '',
      });
    } catch (error) {
      console.error('Error creating Project Folder:', error);
    }
  };

  return (
    <Box sx={{ width: '240px', height: '100%', backgroundColor: grey[200] }}>
      <List>
        <StyledListItem>
          <ListItemButton
            onClick={() => {
              selectedPage('recently_sites');
            }}
          >
            <ListItemText primary={t('sidebar.recently_used_sites')} />
          </ListItemButton>
        </StyledListItem>
        <StyledListItem
          sx={{ paddingRight: 0 }}
          secondaryAction={
            <IconButton
              onClick={() => {
                if (!session) return;
                setIsOpenNewProjectDialog(true);
                setNewProject({
                  ...newProject,
                  userId: session.user.userId,
                });
              }}
            >
              <AddIcon />
            </IconButton>
          }
        >
          <ListItemButton>
            <ListItemText
              primary={
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 'bold',
                  }}
                >
                  {t('sidebar.projects')}
                </Typography>
              }
            />
          </ListItemButton>
        </StyledListItem>
        {projects.map((project, index) => (
          <Fragment key={`project_${index}`}>
            <StyledListItem
              sx={{ paddingRight: 0 }}
              secondaryAction={
                <IconButton
                  onClick={() => {
                    setIsOpenNewPFDialog(true);
                    setNewProjectFolder({
                      ...newProjectFolder,
                      projectId: project.id,
                    });
                  }}
                >
                  <AddIcon />
                </IconButton>
              }
            >
              <ListItemButton
                onClick={() => {
                  selectedPage('sites');
                  setProjectInfo({ id: project.id });
                }}
              >
                <ListItemText primary={project.name} />
              </ListItemButton>
            </StyledListItem>
            <SubList>
              {project.projectFolders?.map(projectFolder => (
                <StyledListItem key={projectFolder.id}>
                  <ListItemButton
                    onClick={() => {
                      selectedPage('sites');
                      setProjectInfo({
                        id: project.id,
                        projectFolderId: projectFolder.id,
                      });
                    }}
                  >
                    <ListItemText primary={projectFolder.name} />
                  </ListItemButton>
                </StyledListItem>
              ))}
              <StyledListItem>
                <ListItemButton>
                  <ListItemText primary={t('sidebar.archive')} />
                </ListItemButton>
              </StyledListItem>
            </SubList>
          </Fragment>
        ))}
      </List>
      <Divider />
      <List>
        <StyledListItem>
          <ListItemButton onClick={handleTemplates}>
            <ListItemText primary={t('sidebar.templates')} />
          </ListItemButton>
        </StyledListItem>
        <StyledListItem>
          <ListItemButton
            onClick={() => {
              selectedPage('services');
            }}
          >
            <ListItemText primary={t('sidebar.collaborative_services')} />
          </ListItemButton>
        </StyledListItem>
        <StyledListItem>
          <ListItemButton
            onClick={() => {
              selectedPage('learning');
            }}
          >
            <ListItemText primary={t('sidebar.learning')} />
          </ListItemButton>
        </StyledListItem>
        <StyledListItem>
          <ListItemButton>
            <ListItemText primary={t('sidebar.domain_management')} />
          </ListItemButton>
        </StyledListItem>
        <StyledListItem>
          <ListItemButton
            onClick={() => {
              selectedPage('members');
            }}
          >
            <ListItemText primary={t('sidebar.member_management')} />
          </ListItemButton>
        </StyledListItem>
        <StyledListItem>
          <ListItemButton
            onClick={() => {
              selectedPage('teams');
            }}
          >
            <ListItemText primary={t('sidebar.team_management')} />
          </ListItemButton>
        </StyledListItem>
        <StyledListItem>
          <ListItemButton
            onClick={() => {
              selectedPage('myaccount');
            }}
          >
            <ListItemText primary={t('sidebar.my_account')} />
          </ListItemButton>
        </StyledListItem>
        <StyledListItem>
          <ListItemButton
            onClick={() => {
              selectedPage('billing');
            }}
          >
            <ListItemText primary={t('sidebar.billing_and_plans')} />
          </ListItemButton>
        </StyledListItem>
      </List>

      <Dialog
        open={isOpenNewPFDialog}
        onClose={() => setIsOpenNewPFDialog(false)}
      >
        <ProjectFolderForm
          isEditing={false}
          initialProjectFolder={newProjectFolder}
          handleInputChange={handleNewProjectFolderInputChange}
          onCreate={handleCreateProjectFolder}
          onUpdate={() => {}}
          onCancel={() => {
            setIsOpenNewPFDialog(false);
          }}
        />
      </Dialog>
      <Dialog
        open={isOpenNewProjectDialog}
        onClose={() => setIsOpenNewProjectDialog(false)}
      >
        <ProjectForm
          isEditing={false}
          initialProject={newProject}
          handleInputChange={handleNewProjectInputChange}
          onCreate={handleCreateProject}
          onUpdate={() => {}}
          onCancel={() => {
            setIsOpenNewProjectDialog(false);
          }}
        />
      </Dialog>
    </Box>
  );
};

export default Sidebar;

const StyledListItem = styled(ListItem)`
  padding: 0;
`;

const SubList = styled(List)`
  padding: 0 0 0 16px;
`;
