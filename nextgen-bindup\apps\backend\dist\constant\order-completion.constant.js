"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ORDER_COMPLETION_EMAIL_FOOTER = exports.ORDER_COMPLETION_EMAIL_HEADER = exports.ORDER_COMPLETION_EMAIL_SUBJECT = exports.ORDER_COMPLETION_DISPLAY_TEXT = void 0;
exports.ORDER_COMPLETION_DISPLAY_TEXT = `注文内容がショップに送信されました。
ご登録いただいたアドレスに確認のメールをお送りいたします。
      
※24時間経過してもショップより注文完了メールが届かない場合、ショップまでお問い合わせください。`;
const ORDER_COMPLETION_EMAIL_SUBJECT = (shopName) => `【${shopName}】注文内容のご確認（自動送信メール）`;
exports.ORDER_COMPLETION_EMAIL_SUBJECT = ORDER_COMPLETION_EMAIL_SUBJECT;
const ORDER_COMPLETION_EMAIL_HEADER = (shopName) => `---------------------------------------------------------------------
本メールはお客様のご注文情報時に送信される、自動配信メールです。
ショップからの確認の連絡、または商品の発送をもって売買契約成立となります。
---------------------------------------------------------------------
${shopName}をご利用いただき、まことにありがとうございます。
下記の内容でご注文を承りましたのでご連絡申し上げます。`;
exports.ORDER_COMPLETION_EMAIL_HEADER = ORDER_COMPLETION_EMAIL_HEADER;
const ORDER_COMPLETION_EMAIL_FOOTER = (shopName, email) => `─────────────問い合わせ─────────────────
メールによるお問い合せ　${email}
${shopName}
────────────────────────────────`;
exports.ORDER_COMPLETION_EMAIL_FOOTER = ORDER_COMPLETION_EMAIL_FOOTER;
//# sourceMappingURL=order-completion.constant.js.map