"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsCollectionTable1743388643396 = void 0;
const typeorm_1 = require("typeorm");
class UpdateCmsCollectionTable1743388643396 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}cms_collection`;
    }
    async up(queryRunner) {
        const title = new typeorm_1.TableColumn({
            name: 'title',
            type: 'jsonb',
            isNullable: false,
        });
        await queryRunner.addColumn(this.TABLE_NAME, title);
        const slug = new typeorm_1.TableColumn({
            name: 'slug',
            type: 'jsonb',
            isNullable: false,
        });
        await queryRunner.addColumn(this.TABLE_NAME, slug);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'title');
        await queryRunner.dropColumn(this.TABLE_NAME, 'slug');
    }
}
exports.UpdateCmsCollectionTable1743388643396 = UpdateCmsCollectionTable1743388643396;
//# sourceMappingURL=1743388643396-update-cms_collection-table.js.map