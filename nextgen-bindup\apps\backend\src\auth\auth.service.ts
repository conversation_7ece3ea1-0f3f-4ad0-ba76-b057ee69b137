import { Injectable } from '@nestjs/common';
import * as jose from 'jose';
import {
  CreateTempTokenReq,
  CreateTempTokenRes,
  JwtPayloadDto,
  JwtRemotePayloadDto,
  VerifyTempTokenRes,
} from './dto/auth.dto';
import { VersionHistoryService } from 'src/version-history/version-history.service';
import { SiteVersionEntity } from 'src/version-history/entities/site-version.entity';
import { ResetPwdReq } from './dto/reset-pwd.dto';
import { ForgotPwdReq } from './dto/forgot-pwd.dto';
import { LoginReq, LoginRes } from './dto/login.dto';
import { UserInfoEntity } from 'src/user-info/entities/user-info.entity';
import { SignUpReq, SignUpRes } from './dto/sign-up.dto';
import { IdentityService } from 'src/identity/identity.service';
import { UserInfoService } from 'src/user-info/user-info.service';
import { CountryCode, UserType } from 'src/user-info/enum/user-info.enum';
import { AppException } from 'src/common/exceptions/app.exception';
import { ChangePwdReq } from './dto/change-pwd.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly versionHistoryService: VersionHistoryService,
    private readonly identityService: IdentityService,
    private readonly userInfoService: UserInfoService,
  ) {}

  async createTempToken(
    userId: string,
    regDto: CreateTempTokenReq,
  ): Promise<CreateTempTokenRes> {
    const jwtPayload: JwtRemotePayloadDto = {
      userId: userId,
      type: regDto.type,
      siteId: regDto.siteId,
      projectId: regDto.projectId,
      versionId: regDto.versionId,
    };

    const secret = new TextEncoder().encode(process.env.APP_JWT);
    const token = await new jose.SignJWT(jwtPayload)
      .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
      .setExpirationTime('30s')
      .sign(secret);

    const result: CreateTempTokenRes = {
      token: token,
    };

    return result;
  }

  async verifyTempToken(token: string): Promise<VerifyTempTokenRes> {
    const secret = new TextEncoder().encode(process.env.APP_JWT);
    const { payload } = await jose.jwtVerify(token, secret);
    const payloadBody: JwtRemotePayloadDto = payload as JwtRemotePayloadDto;

    const newToken = await new jose.SignJWT({ userId: payloadBody.userId })
      .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
      .setExpirationTime('360d')
      .sign(secret);

    let siteVersion: SiteVersionEntity;
    if (payload.versionId) {
      siteVersion = await this.versionHistoryService.getSiteVersion(
        +payload.versionId,
      );
    }

    const result: VerifyTempTokenRes = {
      token: newToken,
      userId: payloadBody.userId,
      type: payloadBody.type,
      siteId: payloadBody.siteId,
      projectId: payloadBody.projectId,
      siteVersion: siteVersion,
    };

    return result;
  }

  async verifyDesignEditorToken(token: string): Promise<VerifyTempTokenRes> {
    const secret = new TextEncoder().encode(process.env.APP_JWT);
    const { payload } = await jose.jwtVerify(token, secret);
    const payloadBody: JwtRemotePayloadDto = payload as JwtRemotePayloadDto;

    let siteVersion: SiteVersionEntity;
    if (payload.versionId) {
      siteVersion = await this.versionHistoryService.getSiteVersion(
        +payload.versionId,
      );
    }

    const result: VerifyTempTokenRes = {
      token: token,
      userId: payloadBody.userId,
      type: payloadBody.type,
      siteId: payloadBody.siteId,
      projectId: payloadBody.projectId,
      siteVersion: siteVersion,
    };

    return result;
  }

  async signUp(input: SignUpReq): Promise<SignUpRes> {
    const identity = await this.identityService.createIdentity({
      email: input.email,
      password: input.password,
    });

    const userInfo = await this.userInfoService.userInfoRepo.findOneBy({
      userId: identity.user.userId,
    });

    const now = new Date();
    if (userInfo) {
      await this.userInfoService.userInfoRepo.update(
        {
          userId: userInfo.userId,
        },
        {
          name: input.name,
          updatedAt: now,
        },
      );
    } else {
      const newUserInfo: UserInfoEntity = new UserInfoEntity();
      newUserInfo.userId = identity.user.userId;
      newUserInfo.name = input.name;
      newUserInfo.type = UserType.INDIVIDUAL;
      newUserInfo.countryCode = CountryCode.JAPAN;
      newUserInfo.createdAt = now;
      newUserInfo.updatedAt = now;
      await this.userInfoService.userInfoRepo.save(newUserInfo);
    }

    const jwtPayload: JwtPayloadDto = {
      userId: identity.user.userId,
      email: identity.user.email,
    };
    const accessToken = await this.createAccessToken(jwtPayload);

    return {
      accessToken,
      user: identity.user,
    };
  }

  async login(input: LoginReq): Promise<LoginRes> {
    const identity = await this.identityService.signInWithPassword(
      input.email,
      input.password,
    );

    const jwtPayload: JwtPayloadDto = {
      userId: identity.user.userId,
      email: identity.user.email,
    };
    const accessToken = await this.createAccessToken(jwtPayload);

    return {
      accessToken,
      user: identity.user,
    };
  }

  async forgotPwd(input: ForgotPwdReq): Promise<boolean> {
    await this.identityService.forgotPasswordForEmail(
      input.email,
      input.redirectTo,
    );

    return true;
  }

  async resetPwd(input: ResetPwdReq): Promise<boolean> {
    await this.identityService.changePassword(input.email, input.password);
    return true;
  }

  private async createAccessToken(payload: JwtPayloadDto): Promise<string> {
    const secret = new TextEncoder().encode(process.env.APP_JWT);
    const accessToken = await new jose.SignJWT(payload)
      .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
      .setExpirationTime('360d')
      .sign(secret);
    return accessToken;
  }

  async changePassword(email: string, data: ChangePwdReq): Promise<boolean> {
    try {
      await this.identityService.signInWithPassword(email, data.currentPwd);
    } catch (e) {
      console.log(e);
      throw new AppException('myaccount.change_pwd.error.current_pwd_invalid');
    }

    await this.identityService.changePassword(email, data.newPassword);
    return true;
  }
}
