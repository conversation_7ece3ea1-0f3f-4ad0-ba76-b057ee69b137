"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePagesTable1743410233712 = void 0;
class UpdatePagesTable1743410233712 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}pages`;
    }
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}
      ALTER COLUMN type TYPE VARCHAR(15);
    `);
    }
    async down(queryRunner) {
        console.log(!!queryRunner);
    }
}
exports.UpdatePagesTable1743410233712 = UpdatePagesTable1743410233712;
//# sourceMappingURL=1743410233712-update-page-table.js.map