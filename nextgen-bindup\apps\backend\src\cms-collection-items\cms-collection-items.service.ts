import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppException } from 'src/common/exceptions/app.exception';
import { CmsCollectionItemEntity } from './entities/cms-collection-items.entity';
import { PaginationDto } from './dto/pagination.dto';
import { CmsCollectionService } from 'src/cms-collection/cms-collection.service';
import { ProductService } from 'src/product/product.service';
import { CollectionItemValue } from './dto/cms-collection-item-data.dto';
import { ShopInformationSettingService } from 'src/shop-information-settings/shop-information-settings.service';
import {
  calculatePriceWithTax,
  formatPriceWithTax,
  formatDisplayPrice,
} from 'src/utils/price.util';

@Injectable()
export class CmsCollectionItemsService {
  @InjectRepository(CmsCollectionItemEntity)
  readonly collectionItemRepo: Repository<CmsCollectionItemEntity>;

  constructor(
    private readonly cmsCollectionService: CmsCollectionService,
    private readonly productService: ProductService,
    private readonly shopInformationSettingService: ShopInformationSettingService,
  ) {}

  async create(
    collectionItemEntity: CmsCollectionItemEntity,
  ): Promise<CmsCollectionItemEntity> {
    const now: Date = new Date();
    const collectionItem = new CmsCollectionItemEntity();
    collectionItem.cmsCollectionId = collectionItemEntity.cmsCollectionId;
    collectionItem.title = collectionItemEntity.title;
    collectionItem.slug = collectionItemEntity.slug;
    collectionItem.data = collectionItemEntity.data;
    collectionItem.status = collectionItemEntity.status;
    collectionItem.siteId = collectionItemEntity.siteId;
    collectionItem.rootUserId = collectionItemEntity.rootUserId;
    collectionItem.userId = collectionItemEntity.userId;
    collectionItem.createdAt = now;
    collectionItem.updatedAt = now;
    return await this.collectionItemRepo.save(collectionItem);
  }

  async update(
    id: number,
    collectionItemData: Partial<CmsCollectionItemEntity>,
  ): Promise<CmsCollectionItemEntity> {
    const collectionItem = await this.collectionItemRepo.findOneBy({ id: id });
    if (!collectionItem)
      throw new AppException('cms.collection_item.error.not_found');

    delete collectionItemData.id;
    delete collectionItemData.cmsCollectionId;
    delete collectionItemData.siteId;
    delete collectionItemData.rootUserId;
    delete collectionItemData.createdAt;
    collectionItemData.updatedAt = new Date();

    await this.collectionItemRepo.update(id, collectionItemData);
    return { ...collectionItem, ...collectionItemData };
  }

  async findById(id: number): Promise<CmsCollectionItemEntity> {
    return await this.collectionItemRepo.findOneBy({ id });
  }

  async findByCollectionId(
    cmsCollectionId: number,
    dto?: PaginationDto,
  ): Promise<{
    data: CmsCollectionItemEntity[];
    count: number;
  }> {
    const { offset, limit, searchText } = dto;
    const queryBuilder =
      this.collectionItemRepo.createQueryBuilder('cmsCollectionItem');
    queryBuilder.where('cmsCollectionItem.cmsCollectionId = :cmsCollectionId', {
      cmsCollectionId,
    });
    if (searchText) {
      const collection =
        await this.cmsCollectionService.findById(cmsCollectionId);
      const keys: number[] = collection.struct.reduce(
        (result, struct) =>
          struct.type === 'text' ? [...result, struct.id] : result,
        [],
      );
      if (keys.length > 0) {
        const escapedSearchText = searchText.replace(/[%_]/g, '\\$&');
        const conditions = keys.map(
          key =>
            `cmsCollectionItem.data -> '${key}' ->> 'value' ILIKE :searchValue`,
        );
        const whereClause = conditions.join(' OR ');
        queryBuilder.andWhere(whereClause, {
          searchValue: `%${escapedSearchText}%`,
        });
      }
    }
    if (offset) {
      queryBuilder.skip(offset);
    }
    if (limit) {
      queryBuilder.take(limit);
    }
    const [data, count] = await queryBuilder.getManyAndCount();
    return { data, count };
  }

  async findBySiteId(
    siteId: number,
  ): Promise<Record<string, CmsCollectionItemEntity[]>> {
    const result: Record<string, CmsCollectionItemEntity[]> = {};

    const list = await this.collectionItemRepo.find({
      where: { siteId },
      order: { cmsCollectionId: 'ASC' },
    });
    let oldSiteCollectionId: number = -1;

    for (const item of list) {
      if (item.cmsCollectionId !== oldSiteCollectionId) {
        result[item.cmsCollectionId] = [];
        oldSiteCollectionId = item.cmsCollectionId;
      }
      result[item.cmsCollectionId].push(item);
    }

    const products = await this.productService.getAllProducts(siteId);
    const shopInformationSetting =
      await this.shopInformationSettingService.findOneBySiteId(siteId);
    const productCollectionId: number = -1;
    const productCollectionStrId: string = '-1';

    const productCollectionItems = products.map(product => {
      const collectionItem = new CmsCollectionItemEntity();
      collectionItem.cmsCollectionId = productCollectionId;
      collectionItem.title = product.name;
      collectionItem.id = product.id;
      collectionItem.data = {};

      // Calculate prices with tax
      const priceWithTax = calculatePriceWithTax(
        product.price,
        shopInformationSetting?.taxMode,
        shopInformationSetting?.taxRate,
        shopInformationSetting?.taxRegulation,
      );

      const salePriceWithTax =
        product.sale > 0
          ? calculatePriceWithTax(
              product.sale,
              shopInformationSetting?.taxMode,
              shopInformationSetting?.taxRate,
              shopInformationSetting?.taxRegulation,
            )
          : null;

      // Format prices with tax included text
      const formattedPriceWithTax = formatPriceWithTax(priceWithTax);
      const formattedSalePriceWithTax = salePriceWithTax
        ? formatPriceWithTax(salePriceWithTax)
        : null;

      // Create display price string
      const displayPrice = formatDisplayPrice(priceWithTax, salePriceWithTax);

      const productFlatValue = [
        product.isOrderable,
        product.code,
        product.name,
        product.title,
        product.description,
        product.images,
        formattedPriceWithTax,
        formattedSalePriceWithTax,
        product.purchaseLimitQuantity,
        product.individualShippingCharges,
        product.fileDownload,
        product.unlimitedPurchase,
        product.productType,
        product.productVariantType,
        product.variants,
        product.priceLabel,
        product.saleLabel,
        displayPrice,
      ];
      for (let i = 0; i < productFlatValue.length; i++) {
        const id = i + 1;
        collectionItem.data[id] = {
          id: id,
          value: productFlatValue[i] as
            | CollectionItemValue
            | CollectionItemValue[],
        };
      }

      collectionItem.status = 'published';
      collectionItem.siteId = siteId;
      return collectionItem;
    });

    result[productCollectionStrId] = productCollectionItems;

    return result;
  }

  async delete(id: number): Promise<boolean> {
    const collectionItem = await this.collectionItemRepo.findOneBy({ id });
    if (!collectionItem)
      throw new AppException('cms.collection_item.error.not_found');

    await this.collectionItemRepo.delete(id);
    return true;
  }
}
