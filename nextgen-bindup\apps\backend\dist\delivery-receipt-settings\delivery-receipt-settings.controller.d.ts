import { ShippingNoteSettingEntity } from 'src/shipping-note-settings/entities/shipping-note--settings.entity';
import { DeliveryReceiptSettingsService } from './delivery-receipt-settings.service';
export declare class DeliveryReceiptSettingsController {
    private readonly deliveryReceiptSettingService;
    constructor(deliveryReceiptSettingService: DeliveryReceiptSettingsService);
    update(id: string, data: Partial<ShippingNoteSettingEntity>): Promise<import("./entities/delivery-receipt-settings.entity").DeliveryReceiptSettingEntity>;
    getOneBySiteId(siteId: string): Promise<import("./entities/delivery-receipt-settings.entity").DeliveryReceiptSettingEntity>;
}
