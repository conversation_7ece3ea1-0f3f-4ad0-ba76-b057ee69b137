{"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "2.1.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/is-number", "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^3.0.2"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "mocha": "*"}, "keywords": ["check", "coerce", "coercion", "integer", "is", "is number", "is-number", "istype", "kind of", "math", "number", "test", "type", "typeof", "value"], "verb": {"related": {"list": ["kind-of", "is-primitive", "even", "odd", "is-even", "is-odd"]}}}