import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SubDomainEntity } from './entities/sub-domain.entity';
import { AppException } from 'src/common/exceptions/app.exception';

@Injectable()
export class SubDomainService {
  @InjectRepository(SubDomainEntity)
  readonly subDomainRepo: Repository<SubDomainEntity>;

  async create(data: SubDomainEntity): Promise<SubDomainEntity> {
    delete data.id;

    const now = new Date();
    data.createdAt = now;
    data.updatedAt = now;

    return await this.subDomainRepo.save(data);
  }

  async update(
    id: number,
    data: Partial<SubDomainEntity>,
  ): Promise<SubDomainEntity> {
    const entity = await this.subDomainRepo.findOneBy({ id });
    if (!entity) throw new AppException('api.error.sub_domain_not_found');

    const now = new Date();
    data.updatedAt = now;

    delete data.id;
    await this.subDomainRepo.update(id, data);
    return { ...entity, ...data };
  }

  async delete(id: number): Promise<boolean> {
    const entity = await this.subDomainRepo.findOneBy({ id });
    if (!entity) throw new AppException('api.error.sub_domain_not_found');

    await this.subDomainRepo.delete(id);
    return true;
  }

  async findBySiteId(siteId: number): Promise<SubDomainEntity[]> {
    return await this.subDomainRepo.findBy({ siteId });
  }

  async findByProjectId(projectId: number): Promise<SubDomainEntity[]> {
    return await this.subDomainRepo.findBy({ projectId });
  }
}
