import { Component } from '@nextgen-bindup/common/dto/component';
import { PROP_ACTION_DEFAULT_VALUE } from 'src/page/utils/prop-action-default-value';
import { PROP_SPACING_DEFAULT_VALUE } from 'src/page/utils/prop-spacing-default-value';
import { PROP_SIZE_DEFAULT_VALUE } from 'src/page/utils/prop-size-default-value';
import { PROP_BORDER_DEFAULT_VALUE } from 'src/page/utils/prop-border-default-value';
import { PROP_POSITION_DEFAULT_VALUE } from 'src/page/utils/prop-position-default-value';
import { PROP_BACKGROUND_LIST_DEFAULT_VALUE } from 'src/page/utils/prop-background-default-value';
import { PROP_EFFECT_LIST_DEFAULT_VALUE } from 'src/page/utils/prop-effect-default-value';
import { PROP_FILTER_DEFAULT_VALUE } from 'src/page/utils/prop-filter-default-value';
import { TextFormat } from '@nextgen-bindup/common/dto/types/text-format.type';
import {
  BgSet_Ilay,
  BlockData_BGSets,
  BlockData_Info,
  BlockData_Info_BwType,
  BlockData_Info_BwUseRefPage,
  BlockData_Info_FrameType,
  BlockData_Layout,
  BlockData_LayoutOpt,
  Site4_BlockData,
} from '../dto/site4_blockdata.dto';
import { NEW_TS } from 'src/utils/common.util';
import { PropertyDto } from '@nextgen-bindup/common/dto/component-properties/general-prop.dto';
import { ComponentType } from 'src/page/types/component.type';
import {
  BlockData_Content,
  BlockData_Content_Extra,
  BlockData_Group,
} from '../dto/blockdata-content.dto';
import { ParseUtil } from './parse.util';
import {
  Resource_PartsProperty,
  Resource_PartsType,
  Site5_Resource,
} from '../dto/site5_resource.dto';
import { Block_Area, Site3_Block } from '../dto/site3_block.dto';
import { PROP_DATASOURCE_DEFAULT_VALUE } from 'src/page/utils/prop-datasource-default-value';
import { PROP_MEDIA_IMAGE_DEFAULT_VALUE } from 'src/page/utils/prop-media-default-value';
import { PROP_TRANSFORM_DEFAULT_VALUE } from 'src/page/utils/prop-transform-default-value';
import { PROP_BINDING_DEFAULT_VALUE } from 'src/page/utils/prop-binding-default-value';
import { Site6_Srclist1 } from '../dto/site6_srclist1.dto';
import {
  BackgroundListPropDto,
  BgGradientPropDto,
  BgImagePropDto,
  BgSolidColorPropDto,
} from '@nextgen-bindup/common/dto/setting-properties/background-prop.dto';
import {
  BorderPropDto,
  BorderRadiusUnit,
} from '@nextgen-bindup/common/dto/setting-properties/border-prop.dto';
import { BorderUnit } from '@nextgen-bindup/common/dto/setting-properties/border-unit.dto';
import { LayoutPropDto } from '@nextgen-bindup/common/dto/setting-properties/layout-prop.dto';
import { Site2_Page } from '../dto/site2_page.dto';
import { PROP_HTML_DEFAULT_VALUE } from 'src/page/utils/prop-html-default-value';
import { LayoutFlexItemPropDto } from '@nextgen-bindup/common/dto/setting-properties/layout/layout-flex-item-prop.dto';
import { HozType } from '@nextgen-bindup/common/dto/types/layout.type';
import { PROP_TEXT_DEFAULT_VALUE } from 'src/page/utils/prop-text-default-value';
import { UnitObject } from '@nextgen-bindup/common/dto/setting-properties/unit-object.dto';
import { MediaImagePropDto } from '@nextgen-bindup/common/dto/setting-properties/media-prop.dto';
import { PROP_LINK_DEFAULT_VALUE } from 'src/page/utils/prop-link-default-value';
import {
  BlankSpacePropDto,
  MarginPaddingPropDto,
} from '@nextgen-bindup/common/dto/setting-properties/margin-padding-prop.dto';
import { SizePropDto } from '@nextgen-bindup/common/dto/setting-properties/size-prop.dto';

const DEFAULT_TEMPLATE_COMPONENT: Record<string, Component> = {
  __main__: {
    id: '__main__',
    ts: 1752204281913,
    name: 'Main',
    type: ComponentType.Main,
    children: [],
    parentId: '__page__',
    breakpoint: {
      phone: { ts: 1752204281913 },
      tablet: { ts: 1752204281913 },
    },
    properties: {
      ts: 1752204281913,
      size: {
        ts: 1752204281913,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: '%', value: '100' },
      },
      border: {
        ts: 1752204281913,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: 1752204281913, applyTo: 'global' },
      actions: { ts: 1752204281913, list: [] },
      effects: { ts: 1752204281913, list: [] },
      position: { ts: 1752204281913, position: 'relative' },
      backgrounds: { ts: 1752204281913, list: [] },
      marginPadding: {
        ts: 1752204281913,
        margin: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
  },
  __page__: {
    id: '__page__',
    ts: 1752215842124,
    name: 'Page',
    type: ComponentType.Page,
    children: [
      '__main__',
      '__right-side__',
      '__left-side__',
      '__header__',
      '__footer__',
    ],
    breakpoint: {
      phone: { ts: 1752204281913 },
      tablet: { ts: 1752204281913 },
    },
    properties: {
      ts: 1752204281913,
      size: {
        ts: 1752204281913,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'dvh', value: '100' },
      },
      border: {
        ts: 1752204281913,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: 1752204281913, applyTo: 'global' },
      actions: { ts: 1752204281913, list: [] },
      effects: { ts: 1752204281913, list: [] },
      position: { ts: 1752204281913, position: 'relative' },
      backgrounds: { ts: 1752204281913, list: [] },
      marginPadding: {
        ts: 1752204281913,
        margin: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'auto', value: '' },
          right: { unit: 'auto', value: '' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
  },
  __footer__: {
    id: '__footer__',
    ts: 1752216501512,
    name: 'Footer',
    type: ComponentType.Footer,
    children: [],
    parentId: '__page__',
    breakpoint: {
      phone: { ts: 1752216501512 },
      tablet: { ts: 1752216501512 },
    },
    properties: {
      ts: 1752216501512,
      size: {
        ts: 1752216501512,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      border: {
        ts: 1752216501512,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: 1752216501512, applyTo: 'global' },
      actions: { ts: 1752216501512, list: [] },
      effects: { ts: 1752216501512, list: [] },
      position: { ts: 1752216501512, position: 'relative' },
      backgrounds: { ts: 1752216501512, list: [] },
      marginPadding: {
        ts: 1752216501512,
        margin: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
  },
  __header__: {
    id: '__header__',
    ts: 1752216476217,
    name: 'Header',
    type: ComponentType.Header,
    children: [],
    parentId: '__page__',
    breakpoint: {
      phone: { ts: 1752216476217 },
      tablet: { ts: 1752216476217 },
    },
    properties: {
      ts: 1752216476217,
      size: {
        ts: 1752216476217,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      border: {
        ts: 1752216476217,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: 1752216476217, applyTo: 'global' },
      actions: { ts: 1752216476217, list: [] },
      effects: { ts: 1752216476217, list: [] },
      position: { ts: 1752216476217, position: 'relative' },
      backgrounds: { ts: 1752216476217, list: [] },
      marginPadding: {
        ts: 1752216476217,
        margin: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
  },
  // '__right-side__': {
  //   id: '__right-side__',
  //   ts: 1752215834885,
  //   name: 'Right Side',
  //   type: ComponentType.RightSide,
  //   children: [],
  //   parentId: '__page__',
  //   breakpoint: {
  //     phone: { ts: 1752215834885 },
  //     tablet: { ts: 1752215834885 },
  //   },
  //   properties: {
  //     ts: 1752215834885,
  //     size: {
  //       ts: 1752215834885,
  //       width: { unit: '%', value: '100' },
  //       height: { unit: 'auto', value: '' },
  //       maxWidth: { unit: 'auto', value: '' },
  //       minWidth: { unit: 'auto', value: '' },
  //       overflow: 'unset',
  //       maxHeight: { unit: 'auto', value: '' },
  //       minHeight: { unit: 'dvh', value: '100' },
  //     },
  //     border: {
  //       ts: 1752215834885,
  //       top: {
  //         color: '#000',
  //         width: { unit: 'px', value: '0' },
  //         borderStyle: 'none',
  //       },
  //       left: {
  //         color: '#000',
  //         width: { unit: 'px', value: '0' },
  //         borderStyle: 'none',
  //       },
  //       right: {
  //         color: '#000',
  //         width: { unit: 'px', value: '0' },
  //         borderStyle: 'none',
  //       },
  //       bottom: {
  //         color: '#000',
  //         width: { unit: 'px', value: '0' },
  //         borderStyle: 'none',
  //       },
  //       isDetail: false,
  //       radiusTopLeft: {
  //         width: { unit: 'px', value: '0' },
  //         height: { unit: 'px', value: '0' },
  //         isDetail: false,
  //       },
  //       radiusTopRight: {
  //         width: { unit: 'px', value: '0' },
  //         height: { unit: 'px', value: '0' },
  //         isDetail: false,
  //       },
  //       radiusBottomLeft: {
  //         width: { unit: 'px', value: '0' },
  //         height: { unit: 'px', value: '0' },
  //         isDetail: false,
  //       },
  //       radiusBottomRight: {
  //         width: { unit: 'px', value: '0' },
  //         height: { unit: 'px', value: '0' },
  //         isDetail: false,
  //       },
  //     },
  //     filter: { ts: 1752215834885, applyTo: 'global' },
  //     actions: { ts: 1752215834885, list: [] },
  //     effects: { ts: 1752215834885, list: [] },
  //     position: { ts: 1752215834885, position: 'relative' },
  //     sideArea: { ts: 1752215844539, show: true },
  //     backgrounds: { ts: 1752215834885, list: [] },
  //     marginPadding: {
  //       ts: 1752215834885,
  //       margin: {
  //         ts: 0,
  //         top: { unit: 'px', value: '0' },
  //         left: { unit: 'px', value: '0' },
  //         right: { unit: 'px', value: '0' },
  //         bottom: { unit: 'px', value: '0' },
  //         isDetail: false,
  //       },
  //       padding: {
  //         ts: 0,
  //         top: { unit: 'px', value: '0' },
  //         left: { unit: 'px', value: '0' },
  //         right: { unit: 'px', value: '0' },
  //         bottom: { unit: 'px', value: '0' },
  //         isDetail: false,
  //       },
  //     },
  //   },
  // },
};

export class TemplateUtil {
  page: Site2_Page;
  blocks: Site3_Block[];
  components: Record<string, Component> = structuredClone(
    DEFAULT_TEMPLATE_COMPONENT,
  );
  blockDatas: Site4_BlockData[];
  resources: Site5_Resource[];
  ID_INDEX: number = 1;

  constructor(inp: {
    page: Site2_Page;
    blocks: Site3_Block[];
    blockDatas: Site4_BlockData[];
    resources: Site5_Resource[];
  }) {
    this.page = inp.page;
    this.blocks = inp.blocks;
    this.blockDatas = inp.blockDatas;
    this.resources = inp.resources;
    this.migrationLayout();
  }

  migrationLayout() {
    const headerWidth = this.getLayoutWidth({
      layoutId: this.page.layoutId,
      area: 'area-header',
    });

    if (headerWidth) {
      this.components['__header__'].properties.size.width = headerWidth;
      this.components['__page__'].properties.size.width = headerWidth;
    }

    const mainWidth = this.getLayoutWidth({
      layoutId: this.page.layoutId,
      area: 'area-main',
    });

    if (mainWidth) {
      this.components['__main__'].properties.size.width = mainWidth;
    }

    const footerWidth = this.getLayoutWidth({
      layoutId: this.page.layoutId,
      area: 'area-footer',
    });

    if (footerWidth) {
      this.components['__footer__'].properties.size.width = footerWidth;
    }
  }

  createBillboard() {
    if (this.components['block_billboard']) return;
    const ts: number = NEW_TS();

    const properties: PropertyDto = {
      ts: ts,
      actions: PROP_ACTION_DEFAULT_VALUE(ts),
      marginPadding: PROP_SPACING_DEFAULT_VALUE(ts, {
        margin: {
          top: { value: '0', unit: 'px' },
          left: { value: '', unit: 'auto' },
          right: { value: '', unit: 'auto' },
          bottom: { value: '0', unit: 'px' },
          isDetail: false,
          ts: ts,
        },
      }),
      size: PROP_SIZE_DEFAULT_VALUE(ts, {
        width: {
          value: '100',
          unit: '%',
        },
      }),
      border: PROP_BORDER_DEFAULT_VALUE(ts),
      position: PROP_POSITION_DEFAULT_VALUE(ts),
      backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts),
      effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
      filter: PROP_FILTER_DEFAULT_VALUE(ts),
      datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
    };

    const blockBillboard: Component = {
      id: 'block_billboard',
      type: ComponentType.Block,
      name: 'Billboard',
      parentId: '__header__',
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };

    this.components[blockBillboard.id] = blockBillboard;
    this.components['__header__'].children.push(blockBillboard.id);
  }

  createSideA() {
    if (this.components['__left-side__']) return;
    const ts: number = NEW_TS();

    const leftSideWidth = this.getLayoutWidth({
      layoutId: this.page.layoutId,
      area: 'area-side-a',
    });

    const properties: PropertyDto = {
      ts: ts,
      sideArea: { ts: ts, show: true },
      actions: PROP_ACTION_DEFAULT_VALUE(ts),
      marginPadding: PROP_SPACING_DEFAULT_VALUE(ts, {
        margin: {
          top: { value: '0', unit: 'px' },
          left: { value: '', unit: 'auto' },
          right: { value: '', unit: 'auto' },
          bottom: { value: '0', unit: 'px' },
          isDetail: false,
          ts: ts,
        },
      }),
      size: PROP_SIZE_DEFAULT_VALUE(ts, {
        width: leftSideWidth,
      }),
      border: PROP_BORDER_DEFAULT_VALUE(ts),
      position: PROP_POSITION_DEFAULT_VALUE(ts),
      backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts),
      effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
      filter: PROP_FILTER_DEFAULT_VALUE(ts),
      datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
    };

    const sideA: Component = {
      id: '__left-side__',
      type: ComponentType.LeftSide,
      name: 'Left Side',
      parentId: '__page__',
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };

    this.components[sideA.id] = sideA;
    this.components['__page__'].children.push(sideA.id);
  }

  migrateBlock(
    inp: {
      areaId: Block_Area;
      seq: number;
    },
    log?: boolean,
  ): Record<string, Component> {
    if (log) console.log('');
    const ts: number = NEW_TS();

    const block: Site3_Block = this.blocks.find(
      block =>
        block.pageId === this.page.pageId &&
        block.areaId === inp.areaId &&
        block.seq === inp.seq,
    );

    const blockData: Site4_BlockData = this.blockDatas.find(
      blockData => blockData.blockdataId === block.blockdataId,
    );
    const blockDataInfoJson = blockData.blockdataInfoJson;

    // CHILDREN --------------------------------------------
    const blockMargin: Component = this.createDataBlocks({
      areaId: inp.areaId,
      block,
      blockData,
      ts,
    });

    // CHILDREN --------------------------------------------
    const dataContents: BlockData_Content[] = ParseUtil.parseContent(
      blockData,
      this.resources,
    );
    const dataGroups: BlockData_Group[] = ParseUtil.groupChild(dataContents);
    if (log) console.log(JSON.stringify(dataGroups));

    for (const dataGroup of dataGroups) {
      const dataGroupId = `blockGrp_${blockData.blockdataId}_${dataGroup.index}_${ts}`;

      const borderMarginProps: BorderPropDto = PROP_BORDER_DEFAULT_VALUE(ts);
      const bgMarginProps: BackgroundListPropDto =
        PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts);
      if (blockData.blockdataInfoJson.blockdata_skinNo === '1') {
        if (
          blockData.blockdataInfoJson.blockdata_layoutID ===
          BlockData_Layout.TABLE
        ) {
          bgMarginProps.list.push({
            type: 'image',
            url: 'https://tuekgiwcwyuckzgwxejb.supabase.co/storage/v1/object/public/bind/theme/default08/blockskin/skin-3/index_bg.gif',
            position: {
              x: { value: '0', unit: 'px' },
              y: { value: '0', unit: 'px' },
              offsetX: { value: '0', unit: 'px' },
              offsetY: { value: '0', unit: 'px' },
            },
            repeat: 'repeat',
            visibility: true,
          } as BgImagePropDto);

          borderMarginProps.top = {
            color: '#bbb',
            width: { value: '1', unit: 'px' },
            borderStyle: 'solid',
          };
          const columns: number =
            blockData.blockdataInfoJson.blockdata_layoutOptID ===
            BlockData_LayoutOpt.STEP_2
              ? 2
              : blockData.blockdataInfoJson.blockdata_layoutOptID ===
                  BlockData_LayoutOpt.STEP_3
                ? 3
                : blockData.blockdataInfoJson.blockdata_layoutOptID ===
                    BlockData_LayoutOpt.STEP_4
                  ? 4
                  : blockData.blockdataInfoJson.blockdata_layoutOptID ===
                      BlockData_LayoutOpt.STEP_5
                    ? 5
                    : 1;

          if (dataGroup.index % columns !== 1) {
            borderMarginProps.isDetail = true;
            borderMarginProps.right = {
              color: '#bbb',
              width: { value: '1', unit: 'px' },
              borderStyle: 'solid',
            };
            borderMarginProps.bottom = {
              color: '#bbb',
              width: { value: '1', unit: 'px' },
              borderStyle: 'solid',
            };
          }
        }
      }

      const groupBlockProps: PropertyDto = {
        ts: ts,
        actions: PROP_ACTION_DEFAULT_VALUE(ts),
        marginPadding: PROP_SPACING_DEFAULT_VALUE(ts),
        size: PROP_SIZE_DEFAULT_VALUE(ts),
        border: borderMarginProps,
        position: PROP_POSITION_DEFAULT_VALUE(ts),
        backgrounds: bgMarginProps,
        effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
        filter: PROP_FILTER_DEFAULT_VALUE(ts),
      };

      // BlockData_Layout.ASYMM
      let flexItem: LayoutFlexItemPropDto;
      if (blockDataInfoJson.blockdata_layoutID === BlockData_Layout.ASYMM) {
        flexItem = {
          flexGrow:
            (blockDataInfoJson.blockdata_layoutOptID ===
              BlockData_LayoutOpt.RIGHT_WIDEL &&
              dataGroup.index === 2) ||
            (blockDataInfoJson.blockdata_layoutOptID ===
              BlockData_LayoutOpt.LEFT_WIDEL &&
              dataGroup.index === 1)
              ? '2'
              : '1',
          ts: ts,
        };
      }
      if (flexItem) {
        groupBlockProps['flexItem'] = flexItem;
      }

      // BlockData_Layout.TABLE
      let padding: BlankSpacePropDto;
      if (blockDataInfoJson.blockdata_layoutID === BlockData_Layout.TABLE) {
        padding = {
          ts: ts,
          top: {
            unit: 'px',
            value: '10',
          },
          left: {
            unit: 'px',
            value: '10',
          },
          right: {
            unit: 'px',
            value: '10',
          },
          bottom: {
            unit: 'px',
            value: '0',
          },
          isDetail: true,
        };
      }

      if (padding) {
        groupBlockProps.marginPadding.padding = padding;
      }

      const groupBlock: Component = {
        id: dataGroupId,
        type: ComponentType.Block,
        name: dataGroupId,
        parentId: blockMargin.id,
        properties: groupBlockProps,
        children: [],
        breakpoint: {
          tablet: { ts: ts },
          phone: { ts: ts },
        },
        ts: ts,
      };
      this.components[groupBlock.id] = groupBlock;
      this.components[groupBlock.parentId].children.push(groupBlock.id);

      let idIndex: number = 1;

      for (const dataContent of dataGroup.contents) {
        this.createContentBlock({
          ts: ts,
          idIndex: idIndex++,
          blockData: blockData,
          parentId: groupBlock.id,
          dataContent: dataContent,
          extra: {
            areaId: inp.areaId,
          },
        });
      }
    }

    return this.components;
  }

  private createContentBlock(inp: {
    ts: number;
    idIndex: number;
    blockData: Site4_BlockData;
    parentId: string;
    dataContent: BlockData_Content;
    extra: BlockData_Content_Extra;
  }) {
    const ts: number = inp.ts++;
    const resource: Site5_Resource = inp.dataContent.resource;
    const partsPropJson: Resource_PartsProperty = resource?.partsPropertyJson;

    const uls: string[] = inp.extra?.ul || [];
    const len = uls.length;
    const parentClass = len ? uls[len - 1] : '';

    const borederProp: BorderPropDto = PROP_BORDER_DEFAULT_VALUE(ts);
    const marginPaddingProp: MarginPaddingPropDto =
      PROP_SPACING_DEFAULT_VALUE(ts);
    const sizeProp: SizePropDto = PROP_SIZE_DEFAULT_VALUE(ts, {
      width: {
        value: `${partsPropJson?.wd || ''}`,
        unit: `${partsPropJson?.wd ? 'px' : 'auto'}`,
      },
      height: {
        value: `${partsPropJson?.ht || ''}`,
        unit: `${partsPropJson?.ht ? 'px' : 'auto'}`,
      },
    });

    if (parentClass === 'indent_bubble') {
      sizeProp.width = {
        value: '100',
        unit: '%',
      };
      sizeProp.height = {
        value: '100',
        unit: '%',
      };

      borederProp.bottom = {
        color: '#ddd',
        width: { value: '1', unit: 'px' },
        borderStyle: 'solid',
      };
      borederProp.isDetail = true;

      marginPaddingProp.padding = {
        top: { value: '2', unit: 'px' },
        left: { value: '0', unit: 'px' },
        right: { value: '0', unit: 'px' },
        bottom: { value: '4', unit: 'px' },
        isDetail: true,
        ts: ts,
      };
    }

    const properties: PropertyDto = {
      ts: ts,
      actions: PROP_ACTION_DEFAULT_VALUE(ts),
      marginPadding: marginPaddingProp,
      size: sizeProp,
      border: borederProp,
      position: PROP_POSITION_DEFAULT_VALUE(ts),
      backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts),
      effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
      filter: PROP_FILTER_DEFAULT_VALUE(ts),
    };

    //-----------------------------------------------------
    let component: Component;
    switch (inp.dataContent.nodeName) {
      case '#text':
        // Parse CSS styles from parent if available
        const textFormat: TextFormat[] =
          parentClass === 'indent_bubble' ? ['bold'] : [];
        let textDecoration: string = '';
        let fontStyle: string = '';
        let fontWeight: string = '';
        let fontSize: string = '';
        let color: string = '';

        // Check if parent has style attributes for text formatting
        if (inp.extra?.parentStyle) {
          const style = inp.extra.parentStyle;
          if (style['text-decoration']) {
            textDecoration = style['text-decoration'];
          }
          if (style['font-style']) {
            fontStyle = style['font-style'];
          }
          if (style['font-weight']) {
            fontWeight = style['font-weight'];
          }
          if (style['font-size']) {
            fontSize = style['font-size'];
          }
          if (style['color']) {
            color = style['color'];
          }
        }

        // Apply text formatting based on parent styles
        if (textDecoration === 'underline') {
          textFormat.push('underline');
        }
        if (textDecoration === 'line-through') {
          textFormat.push('strike-through');
        }
        if (fontStyle === 'italic') {
          textFormat.push('italic');
        }
        if (fontWeight === 'bold' || fontWeight === '700') {
          textFormat.push('bold');
        }

        // Create CSS styles for SPAN wrapper
        const spanStyles: string[] = [];
        if (fontSize) {
          spanStyles.push(`font-size: ${fontSize}`);
        }
        if (color) {
          spanStyles.push(`color: ${color}`);
        }
        if (fontWeight && fontWeight !== 'bold') {
          spanStyles.push(`font-weight: ${fontWeight}`);
        }
        if (fontStyle && fontStyle !== 'italic') {
          spanStyles.push(`font-style: ${fontStyle}`);
        }
        if (
          textDecoration &&
          textDecoration !== 'underline' &&
          textDecoration !== 'line-through'
        ) {
          spanStyles.push(`text-decoration: ${textDecoration}`);
        }

        // flex item
        let flexItem: LayoutFlexItemPropDto;
        if (
          inp.blockData.blockdataInfoJson.blockdata_layoutID ===
          BlockData_Layout.TABLE
        ) {
          if (inp.idIndex === 2) {
            flexItem = {
              flexShrink: '1',
              ts: ts,
            };
          }
        }

        // Create SPAN wrapper with CSS styles
        let wrappedText = inp.dataContent.text;
        if (spanStyles.length > 0) {
          const styleAttr = spanStyles.join('; ');
          wrappedText = `<span style="${styleAttr}">${inp.dataContent.text}</span>`;
        }

        component = {
          id: `${inp.parentId}_Text_${inp.idIndex}`,
          type: ComponentType.Text,
          name: 'text',
          parentId: inp.parentId,
          properties: {
            ...properties,
            datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
            text: PROP_TEXT_DEFAULT_VALUE(ts, {
              text: wrappedText,
              format: textFormat,
            }),
            size: PROP_SIZE_DEFAULT_VALUE(ts, {
              maxWidth: {
                value: '100',
                unit: '%',
              },
            }),
            flexItem: flexItem,
          },
          children: [],
          breakpoint: {
            tablet: { ts: ts },
            phone: { ts: ts },
          },
          ts: ts,
        };
        break;

      case 'STRONG':
      case 'EM':
      case 'S':
      case 'I': {
        // These tags don't create components, just pass styles to children
        // Parse style attributes and pass them down to children
        let parentStyle: Record<string, string> = {};

        if (inp.dataContent.attributes.style) {
          parentStyle = ParseUtil.parseStyleString(
            inp.dataContent.attributes.style,
          );
        }

        // Add semantic styles based on tag name
        switch (inp.dataContent.nodeName) {
          case 'STRONG':
            parentStyle['font-weight'] = 'bold';
            break;
          case 'EM':
          case 'I':
            parentStyle['font-style'] = 'italic';
            break;
          case 'S':
            parentStyle['text-decoration'] = 'line-through';
            break;
        }

        // Merge with existing parent styles
        const mergedStyle = { ...inp.extra.parentStyle, ...parentStyle };
        const newExtra = {
          ...inp.extra,
          parentStyle: mergedStyle,
        };

        // Process children with the new style context
        let idIndex: number = inp.idIndex;
        for (const childContent of inp.dataContent.children) {
          this.createContentBlock({
            ts: ts,
            idIndex: idIndex++,
            blockData: inp.blockData,
            parentId: inp.parentId,
            dataContent: childContent,
            extra: newExtra,
          });
        }
        // Don't create a component for these tags
        return;
      }

      case 'span':
        // For SPAN, apply all styles directly to text children
        let spanStyle: Record<string, string> = {};

        if (inp.dataContent.attributes.style) {
          spanStyle = ParseUtil.parseStyleString(
            inp.dataContent.attributes.style,
          );
        }

        // Merge with existing parent styles
        const spanMergedStyle = { ...inp.extra.parentStyle, ...spanStyle };
        const spanExtra = {
          ...inp.extra,
          parentStyle: spanMergedStyle,
        };

        // Process children with the new style context
        let spanIdIndex: number = inp.idIndex;
        for (const childContent of inp.dataContent.children) {
          this.createContentBlock({
            ts: ts,
            idIndex: spanIdIndex++,
            blockData: inp.blockData,
            parentId: inp.parentId,
            dataContent: childContent,
            extra: spanExtra,
          });
        }
        // Don't create a component for SPAN
        return;

      case 'P': {
        // Handle P tag with class="tag" - create HTML Component and return early
        if (inp.dataContent.attributes.class === 'tag') {
          const htmlContent = inp.dataContent.text || '';

          component = {
            id: `${inp.parentId}_HTML_${inp.idIndex}`,
            type: ComponentType.Html,
            name: 'html tag',
            parentId: inp.parentId,
            properties: {
              ...properties,
              html: PROP_HTML_DEFAULT_VALUE(ts, {
                content: htmlContent,
              }),
              binding: PROP_BINDING_DEFAULT_VALUE(ts),
            },
            children: [],
            breakpoint: {
              tablet: { ts: ts },
              phone: { ts: ts },
            },
            ts: ts,
          };
          break;
        }

        // Handle regular P tags
        let justifyContent: HozType;
        if (inp.dataContent.attributes.style) {
          const style = ParseUtil.parseStyleString(
            inp.dataContent.attributes.style,
          );
          if (style['text-align'] === 'center') {
            justifyContent = 'center';
          } else if (style['text-align'] === 'right') {
            justifyContent = 'end';
          } else if (style['text-align'] === 'left') {
            justifyContent = 'start';
          }
        }

        const layout: LayoutPropDto = {
          type: 'flex',
          flex: {
            flexDirection: 'row',
            verSpacing: { value: '0', unit: 'px' },
            hozSpacing: { value: '0', unit: 'px' },
            justifyContent: justifyContent,
            alignContent: '',
            alignItems: '',
            flexGrow: '0',
            flexShrink: '0',
            flexWrap: 'nowrap',
          },
          grid: null,
          carousel: null,
          ts: inp.ts,
        };

        // Check for special styling based on blockdata_skinNo and note class
        const marginPadding = PROP_SPACING_DEFAULT_VALUE(ts, {
          padding: {
            ts: ts,
            top: {
              unit: 'px',
              value: '0',
            },
            left: {
              unit: 'px',
              value: '0',
            },
            right: {
              unit: 'px',
              value: '0',
            },
            bottom: {
              unit: 'px',
              value: '10',
            },
            isDetail: true,
          },
        });

        const border = PROP_BORDER_DEFAULT_VALUE(ts);
        const backgrounds = PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts);

        // Apply skin-based styling for P tags with note class
        const parentStyle: Record<string, string> = {};
        if (inp.dataContent.attributes.class === 'note') {
          marginPadding.padding.left = {
            value: '10',
            unit: 'px',
          };
          const skinNo = inp.blockData.blockdataInfoJson?.blockdata_skinNo;

          switch (skinNo) {
            // case '1':
            case '2':
              parentStyle['color'] = '#666';
              break;
            case '1': // TODO: không hiểu lý do vì sao setting no = 1 nhưng đang render no = 3
            case '3':
              marginPadding.padding.top = {
                unit: 'px',
                value: '6',
              };
              marginPadding.padding.isDetail = true;
              border.top = {
                color: '#BBB',
                width: { value: '1', unit: 'px' },
                borderStyle: 'solid',
              };
              border.isDetail = true;
              parentStyle['color'] = '#999';
              break;
            case '4':
              marginPadding.padding.top = {
                unit: 'px',
                value: '6',
              };
              marginPadding.padding.isDetail = true;
              border.top = {
                color: '#666',
                width: { value: '1', unit: 'px' },
                borderStyle: 'solid',
              };
              border.isDetail = true;
              parentStyle['color'] = '#666';
              break;
            case '6':
              marginPadding.padding.left = {
                unit: 'px',
                value: '12',
              };
              marginPadding.padding.isDetail = true;
              backgrounds.list.push({
                id: `image_${ts}`,
                ts: ts,
                url: 'https://tuekgiwcwyuckzgwxejb.supabase.co/storage/v1/object/public/bind/theme/default08/blockskin/skin-6/note.gif',
                size: 'auto',
                type: 'image',
                repeat: 'no-repeat',
                clipText: false,
                position: {
                  x: {
                    unit: 'px',
                    value: '0',
                  },
                  y: {
                    unit: 'px',
                    value: '2',
                  },
                  offsetX: {
                    unit: 'px',
                    value: '',
                  },
                  offsetY: {
                    unit: 'px',
                    value: '',
                  },
                },
                sizeLength: {
                  width: {
                    unit: 'px',
                    value: '',
                  },
                  height: {
                    unit: 'px',
                    value: '',
                  },
                },
                visibility: true,
                attachmentFixed: false,
              } as BgImagePropDto);
              parentStyle['color'] = '#666';
              break;
            case '7':
              border.top = {
                color: '#F99',
                width: { value: '1', unit: 'px' },
                borderStyle: 'solid',
              };
              border.isDetail = true;
              parentStyle['color'] = '#999';
              break;
            case '8':
              border.top = {
                color: '#FC0',
                width: { value: '1', unit: 'px' },
                borderStyle: 'solid',
              };
              border.isDetail = true;
              parentStyle['color'] = '#999';
              break;
            case '9':
              border.top = {
                color: '#9C0',
                width: { value: '1', unit: 'px' },
                borderStyle: 'solid',
              };
              border.isDetail = true;
              parentStyle['color'] = '#999';
              break;
            case '10':
              border.top = {
                color: '#69C',
                width: { value: '1', unit: 'px' },
                borderStyle: 'solid',
              };
              border.isDetail = true;
              parentStyle['color'] = '#999';
              break;
            case '11':
              border.top = {
                color: '#738299',
                width: { value: '1', unit: 'px' },
                borderStyle: 'solid',
              };
              border.isDetail = true;
              parentStyle['color'] = '#999';
              break;
          }
        }

        // Update extra with parent style for children
        if (Object.keys(parentStyle).length > 0) {
          inp.extra = {
            ...inp.extra,
            parentStyle: { ...inp.extra?.parentStyle, ...parentStyle },
          };
        }

        component = {
          id: `${inp.parentId}_P_${inp.idIndex}`,
          type: ComponentType.Block,
          name: inp.dataContent.nodeName,
          parentId: inp.parentId,
          properties: {
            ...properties,
            marginPadding,
            border,
            backgrounds,
            datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
            layout,
          },
          children: [],
          breakpoint: {
            tablet: { ts: ts },
            phone: { ts: ts },
          },
          ts: ts,
        };

        break;
      }

      case 'H1': {
        let justifyContent: HozType;
        if (inp.dataContent.attributes.style) {
          const style = ParseUtil.parseStyleString(
            inp.dataContent.attributes.style,
          );
          if (style['text-align'] === 'center') {
            justifyContent = 'center';
          } else if (style['text-align'] === 'right') {
            justifyContent = 'end';
          } else if (style['text-align'] === 'left') {
            justifyContent = 'start';
          }
        }

        const layout: LayoutPropDto = {
          type: 'flex',
          flex: {
            flexDirection: 'row',
            verSpacing: { value: '0', unit: 'px' },
            hozSpacing: { value: '0', unit: 'px' },
            justifyContent: justifyContent,
            alignContent: '',
            alignItems: '',
            flexGrow: '0',
            flexShrink: '0',
            flexWrap: 'nowrap',
          },
          grid: null,
          carousel: null,
          ts: inp.ts,
        };

        const backgrounds = PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts);

        component = {
          id: `${inp.parentId}_H1_${inp.idIndex}`,
          type: ComponentType.Block,
          name: inp.dataContent.nodeName,
          parentId: inp.parentId,
          properties: {
            ...properties,
            datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
            layout,
            backgrounds,
          },
          children: [],
          breakpoint: {
            tablet: { ts: ts },
            phone: { ts: ts },
          },
          ts: ts,
        };
        break;
      }

      case 'H2': {
        let justifyContent: HozType;
        if (inp.dataContent.attributes.style) {
          const style = ParseUtil.parseStyleString(
            inp.dataContent.attributes.style,
          );
          if (style['text-align'] === 'center') {
            justifyContent = 'center';
          } else if (style['text-align'] === 'right') {
            justifyContent = 'end';
          } else if (style['text-align'] === 'left') {
            justifyContent = 'start';
          }
        }

        const layout: LayoutPropDto = {
          type: 'flex',
          flex: {
            flexDirection: 'row',
            verSpacing: { value: '0', unit: 'px' },
            hozSpacing: { value: '0', unit: 'px' },
            justifyContent: justifyContent,
            alignContent: '',
            alignItems: '',
            flexGrow: '0',
            flexShrink: '0',
            flexWrap: 'nowrap',
          },
          grid: null,
          carousel: null,
          ts: inp.ts,
        };

        const backgrounds = PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts);

        component = {
          id: `${inp.parentId}_H2_${inp.idIndex}`,
          type: ComponentType.Block,
          name: inp.dataContent.nodeName,
          parentId: inp.parentId,
          properties: {
            ...properties,
            datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
            layout,
            backgrounds,
          },
          children: [],
          breakpoint: {
            tablet: { ts: ts },
            phone: { ts: ts },
          },
          ts: ts,
        };
        break;
      }

      case 'H3': {
        let justifyContent: HozType;
        if (inp.dataContent.attributes.style) {
          const style = ParseUtil.parseStyleString(
            inp.dataContent.attributes.style,
          );
          if (style['text-align'] === 'center') {
            justifyContent = 'center';
          } else if (style['text-align'] === 'right') {
            justifyContent = 'end';
          } else if (style['text-align'] === 'left') {
            justifyContent = 'start';
          }
        }

        const layout: LayoutPropDto = {
          type: 'flex',
          flex: {
            flexDirection: 'row',
            verSpacing: { value: '0', unit: 'px' },
            hozSpacing: { value: '0', unit: 'px' },
            justifyContent: justifyContent,
            alignContent: '',
            alignItems: '',
            flexGrow: '0',
            flexShrink: '0',
            flexWrap: 'nowrap',
          },
          grid: null,
          carousel: null,
          ts: inp.ts,
        };

        // Check for special styling based on blockdata_skinNo
        const marginPadding = PROP_SPACING_DEFAULT_VALUE(ts, {
          padding: {
            ts: ts,
            top: {
              unit: 'px',
              value: '0',
            },
            left: {
              unit: 'px',
              value: '0',
            },
            right: {
              unit: 'px',
              value: '0',
            },
            bottom: {
              unit: 'px',
              value: '10',
            },
            isDetail: true,
          },
        });

        const border = PROP_BORDER_DEFAULT_VALUE(ts);

        // Apply special styling if blockdata_skinNo = 4
        if (inp.blockData.blockdataInfoJson?.blockdata_skinNo === '4') {
          marginPadding.padding.left = {
            unit: 'px',
            value: '8',
          };
          marginPadding.padding.isDetail = true;

          border.left = {
            color: '#444',
            width: { value: '5', unit: 'px' },
            borderStyle: 'solid',
          };
          border.isDetail = true;
        }

        component = {
          id: `${inp.parentId}_H3_${inp.idIndex}`,
          type: ComponentType.Block,
          name: inp.dataContent.nodeName,
          parentId: inp.parentId,
          properties: {
            ...properties,
            marginPadding: marginPadding,
            border: border,
            datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
            layout,
            text: PROP_TEXT_DEFAULT_VALUE(ts, {
              text: '',
              value: {
                fontWeight: 'bold',
              },
            }),
          },
          children: [],
          breakpoint: {
            tablet: { ts: ts },
            phone: { ts: ts },
          },
          ts: ts,
        };
        break;
      }

      case 'H4': {
        let justifyContent: HozType;
        if (inp.dataContent.attributes.style) {
          const style = ParseUtil.parseStyleString(
            inp.dataContent.attributes.style,
          );
          if (style['text-align'] === 'center') {
            justifyContent = 'center';
          } else if (style['text-align'] === 'right') {
            justifyContent = 'end';
          } else if (style['text-align'] === 'left') {
            justifyContent = 'start';
          }
        }

        const layout: LayoutPropDto = {
          type: 'flex',
          flex: {
            flexDirection: 'row',
            verSpacing: { value: '0', unit: 'px' },
            hozSpacing: { value: '0', unit: 'px' },
            justifyContent: justifyContent,
            alignContent: '',
            alignItems: '',
            flexGrow: '0',
            flexShrink: '0',
            flexWrap: 'nowrap',
          },
          grid: null,
          carousel: null,
          ts: inp.ts,
        };

        // Check for special styling based on blockdata_skinNo
        const marginPadding = PROP_SPACING_DEFAULT_VALUE(ts, {
          padding: {
            ts: ts,
            top: {
              unit: 'px',
              value: '2',
            },
            left: {
              unit: 'px',
              value: '0',
            },
            right: {
              unit: 'px',
              value: '0',
            },
            bottom: {
              unit: 'px',
              value: '2',
            },
            isDetail: true,
          },
        });

        const backgrounds = PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts);

        // Apply special styling if blockdata_skinNo = 1
        if (inp.blockData.blockdataInfoJson?.blockdata_skinNo === '1') {
          marginPadding.padding.left = {
            unit: 'px',
            value: '22',
          };
          marginPadding.padding.isDetail = true;
          backgrounds.list.push({
            id: `image_${ts}`,
            ts: ts,
            url: 'https://tuekgiwcwyuckzgwxejb.supabase.co/storage/v1/object/public/bind/theme/default08/blockskin/skin-3/h4.gif',
            size: 'auto',
            type: 'image',
            repeat: 'no-repeat',
            clipText: false,
            position: {
              x: {
                unit: 'px',
                value: '0',
              },
              y: {
                unit: 'px',
                value: '4',
              },
              offsetX: {
                unit: 'px',
                value: '',
              },
              offsetY: {
                unit: 'px',
                value: '',
              },
            },
            sizeLength: {
              width: {
                unit: 'px',
                value: '',
              },
              height: {
                unit: 'px',
                value: '',
              },
            },
            visibility: true,
            attachmentFixed: false,
          } as BgImagePropDto);
        }

        component = {
          id: `${inp.parentId}_H4_${inp.idIndex}`,
          type: ComponentType.Block,
          name: inp.dataContent.nodeName,
          parentId: inp.parentId,
          properties: {
            ...properties,
            marginPadding: marginPadding,
            datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
            layout,
            text: PROP_TEXT_DEFAULT_VALUE(ts, {
              text: '',
              value: {
                fontWeight: 'bold',
                fontSize: {
                  value: '12',
                  unit: 'px',
                },
              },
            }),
            backgrounds,
          },
          children: [],
          breakpoint: {
            tablet: { ts: ts },
            phone: { ts: ts },
          },
          ts: ts,
        };
        break;
      }

      case 'img':
        {
          let url: string = '';

          // Check if img has hr attribute (no resource but has hr tag)
          if (!resource && inp.dataContent.attributes?.tag === 'hr') {
            // TODO: check skin
            const backgroundUrl = `https://tuekgiwcwyuckzgwxejb.supabase.co/storage/v1/object/public/bind/theme/default08/blockskin/skin-3/hr.gif`;

            // Create background properties with repeat-x
            const backgroundProps: BackgroundListPropDto =
              PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts);
            if (backgroundUrl) {
              backgroundProps.list.push({
                id: '1',
                type: 'image',
                url: backgroundUrl,
                position: {
                  x: { value: '0', unit: 'px' },
                  y: { value: '0', unit: 'px' },
                  offsetX: { value: '0', unit: 'px' },
                  offsetY: { value: '0', unit: 'px' },
                },
                strPosition: 'center',
                size: '',
                sizeLength: {
                  width: { value: '', unit: 'auto' },
                  height: { value: '', unit: 'auto' },
                },
                blendMode: 'normal',
                repeat: 'repeat-x',
                attachmentFixed: false,
                clipText: false,
                visibility: true,
                ts: ts,
              } as BgImagePropDto);
            }

            // Create margin/padding properties: padding: 0 0 10px 0
            const marginPaddingProps: MarginPaddingPropDto =
              PROP_SPACING_DEFAULT_VALUE(ts, {
                padding: {
                  ts: ts,
                  top: { value: '0', unit: 'px' },
                  left: { value: '0', unit: 'px' },
                  right: { value: '0', unit: 'px' },
                  bottom: { value: '10', unit: 'px' },
                  isDetail: true,
                },
              });

            const parentComponent = this.components[inp.parentId];

            const parentHasChildren =
              parentComponent && parentComponent.children.length > 0;

            const hrParentId = parentHasChildren
              ? parentComponent?.parentId || inp.parentId
              : inp.parentId;

            component = {
              id: `${hrParentId}_HR_BLOCK_${inp.idIndex}`,
              type: ComponentType.Block,
              name: 'HR Block',
              parentId: hrParentId,
              properties: {
                ...properties,
                backgrounds: backgroundProps,
                marginPadding: marginPaddingProps,
                size: PROP_SIZE_DEFAULT_VALUE(ts, {
                  height: {
                    value: '10',
                    unit: 'px',
                  },
                  width: {
                    value: '100',
                    unit: '%',
                  },
                }),
              },
              children: [],
              breakpoint: {
                tablet: { ts: ts },
                phone: { ts: ts },
              },
              ts: ts,
            };
          } else {
            // Original img handling logic
            switch (resource?.partsType) {
              case Resource_PartsType.SIGN:
                if (resource.srcList?.length) {
                  const srcList1: Site6_Srclist1 = resource.srcList[0];
                  url = `https://tuekgiwcwyuckzgwxejb.supabase.co/storage/v1/object/public/bind/b02_002_pc/312c/${srcList1.srclistId}/${srcList1.filename}`;
                }
                const algn = resource.partsPropertyJson.algn;

                // Add margin based on algn value
                let marginPadding = PROP_SPACING_DEFAULT_VALUE(ts);
                if (algn === '1') {
                  marginPadding = PROP_SPACING_DEFAULT_VALUE(ts, {
                    margin: {
                      ts: ts,
                      top: { unit: 'px', value: '0' },
                      left: { unit: 'px', value: '0' },
                      right: { unit: 'px', value: '0' },
                      bottom: { unit: 'px', value: '0' },
                      isDetail: true,
                    },
                  });
                } else if (algn === '2') {
                  marginPadding = PROP_SPACING_DEFAULT_VALUE(ts, {
                    margin: {
                      ts: ts,
                      top: { unit: 'px', value: '0' },
                      left: { unit: 'px', value: '0' },
                      right: { unit: 'px', value: '0' },
                      bottom: { unit: 'px', value: '0' },
                      isDetail: true,
                    },
                  });
                }

                component = {
                  id: `${inp.parentId}_IMG_${inp.idIndex}`,
                  type: ComponentType.Image,
                  name: inp.dataContent.nodeName,
                  parentId: inp.parentId,
                  properties: {
                    ...properties,
                    marginPadding: marginPadding,
                    media: PROP_MEDIA_IMAGE_DEFAULT_VALUE(ts, {
                      type: 'image',
                      url: url,
                    }),
                    transform: PROP_TRANSFORM_DEFAULT_VALUE(ts),
                    binding: PROP_BINDING_DEFAULT_VALUE(ts),
                  },
                  children: [],
                  breakpoint: {
                    tablet: { ts: ts },
                    phone: { ts: ts },
                  },
                  ts: ts,
                };
                break;

              case Resource_PartsType.TAG:
                if (resource.partsPropertyJson.html) {
                  let html = resource.partsPropertyJson.html;
                  // check [BD:site_createdate]
                  if (html.includes('[BD:site_createdate]')) {
                    html = html.replace('[BD:site_createdate]', '2025-07-10');
                  }

                  // check [BD:navi_numbers]
                  if (html.includes('[BD:navi_numbers]')) {
                    component = this.createNaviNumbersComponent({
                      ts: ts,
                      parentId: inp.parentId,
                      idIndex: inp.idIndex,
                    });
                  } else {
                    component = {
                      id: `${inp.parentId}_HTML_${inp.idIndex}`,
                      type: ComponentType.Html,
                      name: 'html tag',
                      parentId: inp.parentId,
                      properties: {
                        ...properties,
                        html: PROP_HTML_DEFAULT_VALUE(ts, {
                          content: html,
                        }),
                        binding: PROP_BINDING_DEFAULT_VALUE(ts),
                      },
                      children: [],
                      breakpoint: {
                        tablet: { ts: ts },
                        phone: { ts: ts },
                      },
                      ts: ts,
                    };
                  }
                }
                break;

              case Resource_PartsType.LINK: {
                const linkId = `${inp.parentId}_LINK_${inp.idIndex}`;
                const imageId = `${linkId}_IMG`;
                const textId = `${linkId}_Text`;
                let imageUrl = '';
                if (resource.srcList.length) {
                  const srcList1: Site6_Srclist1 = resource.srcList[0];
                  imageUrl = `https://tuekgiwcwyuckzgwxejb.supabase.co/storage/v1/object/public/bind/b02_002_pc/312c/${srcList1.srclistId}/${srcList1.filename}`;
                }

                const borderLinkProp: BorderPropDto = PROP_BORDER_DEFAULT_VALUE(
                  ts,
                  {
                    isDetail: true,
                    bottom: {
                      borderStyle: 'dotted',
                      color: '#000',
                      width: {
                        value: '1',
                        unit: 'px',
                      },
                    },
                  },
                );

                if (uls.length && uls[0] === 'menu_menu-b') {
                  borderLinkProp.isDetail = false;
                  borderLinkProp.bottom = {
                    color: '#000',
                    width: { value: '0', unit: 'px' },
                    borderStyle: 'none',
                  };
                }

                component = {
                  id: linkId,
                  type: ComponentType.Link,
                  name: 'Link',
                  parentId: inp.parentId,
                  properties: {
                    ...properties,
                    text: PROP_TEXT_DEFAULT_VALUE(ts, {
                      text: '',
                    }),
                    link: PROP_LINK_DEFAULT_VALUE(ts, {
                      value: resource.partsPropertyJson.link,
                    }),
                    binding: PROP_BINDING_DEFAULT_VALUE(ts),
                    border: borderLinkProp,
                    layout: {
                      type: 'flex',
                      flex: {
                        flexDirection:
                          resource.partsPropertyJson?.iblw === '0'
                            ? 'row-reverse'
                            : 'row',
                        verSpacing: { value: '4', unit: 'px' },
                        hozSpacing: { value: '3', unit: 'px' },
                        justifyContent: 'start',
                        alignContent: 'center',
                        alignItems: 'center',
                        flexGrow: '0',
                        flexShrink: '0',
                        flexWrap: 'wrap',
                      },
                      grid: null,
                      carousel: null,
                      ts: inp.ts,
                    },
                    ts: ts,
                  },
                  children: imageUrl ? [textId, imageId] : [textId],
                  breakpoint: {
                    tablet: { ts: ts },
                    phone: { ts: ts },
                  },
                  ts: ts,
                };

                if (imageUrl) {
                  const imageComponent = {
                    id: imageId,
                    type: ComponentType.Image,
                    name: inp.dataContent.nodeName,
                    parentId: linkId,
                    properties: {
                      ...properties,
                      media: PROP_MEDIA_IMAGE_DEFAULT_VALUE(ts, {
                        type: 'image',
                        url: imageUrl,
                      }),
                      transform: PROP_TRANSFORM_DEFAULT_VALUE(ts),
                      binding: PROP_BINDING_DEFAULT_VALUE(ts),
                    },
                    children: [],
                    breakpoint: {
                      tablet: { ts: ts },
                      phone: { ts: ts },
                    },
                    ts: ts,
                  };

                  this.components[imageComponent.id] = imageComponent;
                }

                const textComponent = {
                  id: `${linkId}_Text`,
                  type: ComponentType.Text,
                  name: 'text',
                  parentId: linkId,
                  properties: {
                    ...properties,
                    datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
                    text: PROP_TEXT_DEFAULT_VALUE(ts, {
                      text: resource.partsPropertyJson.text,
                    }),
                  },
                  children: [],
                  breakpoint: {
                    tablet: { ts: ts },
                    phone: { ts: ts },
                  },
                  ts: ts,
                };

                this.components[textComponent.id] = textComponent;
                break;
              }
            }
          }
        }
        break;

      case 'UL':
        if (!inp.extra.ul) inp.extra.ul = [];
        inp.extra.ul.push(inp.dataContent.attributes.class || '');

        if (inp.dataContent.attributes.class === 'indent_bubble') {
          component = this.creatIndentBubble({
            ts: ts,
            parentId: inp.parentId,
            idIndex: inp.idIndex,
            dataContent: inp.dataContent,
          });
        } else if (inp.dataContent.attributes.class === 'menu_menu-b') {
          component = this.createMenuB({
            ts: ts,
            parentId: inp.parentId,
            idIndex: inp.idIndex,
            areaId: inp.extra.areaId,
            dataContent: inp.dataContent,
          });
        } else {
          component = {
            id: `${inp.parentId}_UL_${inp.idIndex}`,
            type: ComponentType.Block,
            name: inp.dataContent.nodeName,
            parentId: inp.parentId,
            properties: {
              ...properties,
              datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
            },
            children: [],
            breakpoint: {
              tablet: { ts: ts },
              phone: { ts: ts },
            },
            ts: ts,
          };
        }
        break;

      case 'LI': {
        if (parentClass === 'indent_bubble') {
          component = this.createIndentBubble_Li({
            idIndex: inp.idIndex,
            parentId: inp.parentId,
            dataContent: inp.dataContent,
          });
          inp.extra.ul.push('');
        } else if (parentClass === 'menu_menu-b') {
          component = this.createMenuB_Li({
            idIndex: inp.idIndex,
            parentId: inp.parentId,
            dataContent: inp.dataContent,
            extra: inp.extra,
          });
          inp.extra.ul.push('');
        } else {
          component = {
            id: `${inp.parentId}_LI_${inp.idIndex}`,
            type: ComponentType.Block,
            name: inp.dataContent.nodeName,
            parentId: inp.parentId,
            properties: {
              ...properties,
              layout: {
                type: 'flex',
                flex: {
                  flexDirection: 'row',
                  verSpacing: { value: '0', unit: 'px' },
                  hozSpacing: { value: '0', unit: 'px' },
                  justifyContent: 'start',
                  alignContent: 'center',
                  alignItems: 'center',
                  flexGrow: '0',
                  flexShrink: '0',
                  flexWrap: 'nowrap',
                },
                grid: null,
                carousel: null,
                ts: ts,
              },
              datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
            },
            children: [],
            breakpoint: {
              tablet: { ts: ts },
              phone: { ts: ts },
            },
            ts: ts,
          };
        }
        break;
      }

      case 'BR': {
        component = {
          id: `${inp.parentId}_BR_${inp.idIndex}`,
          type: ComponentType.Html,
          name: 'html tag',
          parentId: inp.parentId,
          properties: {
            ...properties,
            html: PROP_HTML_DEFAULT_VALUE(ts, {
              content: '<br>',
            }),
            binding: PROP_BINDING_DEFAULT_VALUE(ts),
          },
          children: [],
          breakpoint: {
            tablet: { ts: ts },
            phone: { ts: ts },
          },
          ts: ts,
        };
        break;
      }
    }

    if (component) {
      this.components[component.id] = component;
      this.components[component.parentId].children.push(component.id);

      // Skip processing children for P tags with class="tag"
      if (
        !(
          inp.dataContent.nodeName === 'P' &&
          inp.dataContent.attributes.class === 'tag'
        )
      ) {
        let idIndex: number = 1;
        for (const childContent of inp.dataContent.children) {
          this.createContentBlock({
            ts: ts,
            idIndex: idIndex++,
            blockData: inp.blockData,
            parentId: component.id,
            dataContent: childContent,
            extra: structuredClone(inp.extra),
          });
        }
      }
    }
  }

  private createDataBlocks(inp: {
    areaId: Block_Area;
    block: Site3_Block;
    blockData: Site4_BlockData;
    ts: number;
  }): Component {
    const id: string = `block_${inp.blockData.blockdataId}_${inp.ts}`;
    const blockdataInfo: BlockData_Info = inp.blockData.blockdataInfoJson;
    const bgSets: BlockData_BGSets = inp.blockData.bgSetsJson;

    const properties: PropertyDto = {
      ts: inp.ts,
      actions: PROP_ACTION_DEFAULT_VALUE(inp.ts),
      marginPadding: PROP_SPACING_DEFAULT_VALUE(inp.ts),
      size: PROP_SIZE_DEFAULT_VALUE(inp.ts),
      border: PROP_BORDER_DEFAULT_VALUE(inp.ts),
      position: PROP_POSITION_DEFAULT_VALUE(inp.ts),
      effects: PROP_EFFECT_LIST_DEFAULT_VALUE(inp.ts),
      filter: PROP_FILTER_DEFAULT_VALUE(inp.ts),
      datasource: PROP_DATASOURCE_DEFAULT_VALUE(inp.ts),
    };

    let parentId: string = '__UNDEFINED__';
    switch (inp.block.areaId) {
      case Block_Area.HEADER:
        parentId = '__header__';
        break;
      case Block_Area.BILLBOARD:
        parentId = 'block_billboard';
        break;
      case Block_Area.MAIN:
        parentId = '__main__';
        break;
      case Block_Area.SIDE_A:
        parentId = '__left-side__';
        break;
      case Block_Area.SIDE_B:
        parentId = '__right-side__';
        break;
      case Block_Area.FOOTER:
        parentId = '__footer__';
        break;
    }

    const getColumns = (blockdata_layoutOptID: BlockData_LayoutOpt) => {
      const COLUMNS = {
        [BlockData_LayoutOpt.STEP_1]: 1,
        [BlockData_LayoutOpt.STEP_2]: 2,
        [BlockData_LayoutOpt.STEP_3]: 3,
        [BlockData_LayoutOpt.STEP_4]: 4,
        [BlockData_LayoutOpt.STEP_5]: 5,
      };
      return COLUMNS[blockdata_layoutOptID];
    };

    let layout: LayoutPropDto;
    switch (blockdataInfo.blockdata_layoutID) {
      case BlockData_Layout.PLAIN:
        if (
          blockdataInfo.blockdata_layoutOptID === BlockData_LayoutOpt.STEP_2
        ) {
          layout = {
            type: 'grid',
            flex: {
              flexDirection: 'row',
              verSpacing: { value: '0', unit: 'px' },
              hozSpacing: { value: '0', unit: 'px' },
              justifyContent: 'center',
              alignContent: 'center',
              alignItems: 'center',
              flexGrow: '0',
              flexShrink: '0',
              flexWrap: 'nowrap',
            },
            grid: {
              cols: `${2}`,
              columnGap: {
                value: '0',
                unit: 'px',
              },
              rowGap: {
                value: '0',
                unit: 'px',
              },
              rows: '',
              sizePerCol: {
                list: Array.from({ length: 2 }, () => ({
                  id: '1',
                  size: { unit: 'fr', value: '1' },
                })),
              },
            },
            carousel: null,
            ts: inp.ts,
          };
        } else {
          if (
            blockdataInfo.blockdata_layoutOptID !== BlockData_LayoutOpt.STEP_1
          ) {
            const column = getColumns(blockdataInfo.blockdata_layoutOptID);
            layout = {
              type: 'grid',
              flex: {
                flexDirection: 'row',
                verSpacing: { value: '0', unit: 'px' },
                hozSpacing: { value: '0', unit: 'px' },
                justifyContent: 'center',
                alignContent: 'center',
                alignItems: 'center',
                flexGrow: '1',
                flexShrink: '0',
                flexWrap: 'wrap',
              },
              grid: {
                cols: `${column}`,
                columnGap: {
                  value: '0',
                  unit: 'px',
                },
                rowGap: {
                  value: '0',
                  unit: 'px',
                },
                rows: '',
                sizePerCol: {
                  list: Array.from({ length: column }, () => ({
                    id: '1',
                    size: { unit: 'fr', value: '1' },
                  })),
                },
              },
              carousel: null,
              ts: inp.ts,
            };
          }
        }

        break;
      case BlockData_Layout.ASYMM:
        layout = {
          type: 'flex',
          flex: {
            flexDirection: 'row',
            verSpacing: { value: '0', unit: 'px' },
            hozSpacing: { value: '0', unit: 'px' },
            justifyContent: '',
            alignContent: '',
            alignItems: '',
            flexGrow: '0',
            flexShrink: '0',
            flexWrap: 'nowrap',
          },
          grid: null,
          carousel: null,
          ts: inp.ts,
        };
        break;
      case BlockData_Layout.TABLE:
        const column = getColumns(blockdataInfo.blockdata_layoutOptID);
        layout = {
          type: 'grid',
          flex: {
            flexDirection: 'row',
            verSpacing: { value: '0', unit: 'px' },
            hozSpacing: { value: '0', unit: 'px' },
            justifyContent: '',
            alignContent: '',
            alignItems: '',
            flexGrow: '0',
            flexShrink: '0',
            flexWrap: 'nowrap',
          },
          grid: {
            cols: `${column}`,
            columnGap: {
              value: '0',
              unit: 'px',
            },
            rowGap: {
              value: '10',
              unit: 'px',
            },
            rows: '',
            sizePerCol: {
              list: Array.from({ length: column }, () => ({
                id: '1',
                size: { unit: 'fr', value: '1' },
              })),
            },
          },
          carousel: null,
          ts: inp.ts,
        };
        break;
      case BlockData_Layout.ALBUM:
        break;
      case BlockData_Layout.TAB:
        break;
      case BlockData_Layout.ACCORDION:
        break;
    }

    // Stage 1 - Background div --------------------------------------------
    const borderProps: BorderPropDto = PROP_BORDER_DEFAULT_VALUE(inp.ts);
    const backgroundProps: BackgroundListPropDto =
      PROP_BACKGROUND_LIST_DEFAULT_VALUE(inp.ts);
    const marginBottom: UnitObject = { value: '0', unit: 'px' };
    const padding: BlankSpacePropDto = PROP_SPACING_DEFAULT_VALUE(
      inp.ts,
    ).padding;

    switch (blockdataInfo.blockdata_frameType) {
      case BlockData_Info_FrameType.NONE:
        if (bgSets?.hasc && bgSets?.cval) {
          backgroundProps.list.push({
            id: '1',
            type: 'solid',
            from: 'design',
            backgroundColor: bgSets.cval.startsWith('rgb')
              ? bgSets.cval
              : '#' + bgSets.cval,
            presetId: null,
            alpha: 1,
            visibility: true,
            ts: inp.ts,
          } as BgSolidColorPropDto);
        }

        if (bgSets?.hasi && bgSets?.imgf) {
          backgroundProps.list.push({
            id: '2',
            type: 'image',
            url: `https://edit3.bindcloud.jp/bindcld/site_data/338884/_src/90214562/${bgSets.imgf}`,
            position: {
              x: { value: '0', unit: 'px' },
              y: { value: '0', unit: 'px' },
              offsetX: { value: '0', unit: 'px' },
              offsetY: { value: '0', unit: 'px' },
            },
            size: '',
            sizeLength: {
              width: { value: '', unit: 'auto' },
              height: { value: '', unit: 'auto' },
            },
            repeat: [
              BgSet_Ilay.LEFT_JUSTIFIED,
              BgSet_Ilay.VERTICAL_REPEAT,
              BgSet_Ilay.RIGHT_JUSTIFIED,
              BgSet_Ilay.TOP_ALIGNMENT,
              BgSet_Ilay.HORIZONTAL_REPEAT,
              BgSet_Ilay.BOTTOM_ALIGNMENT,
            ].includes(bgSets.ilay)
              ? 'repeat'
              : '',
            attachmentFixed: false,
            clipText: false,
            visibility: true,
            ts: inp.ts,
          } as BgImagePropDto);
        }
        break;

      case BlockData_Info_FrameType.BORDER_ONLY: {
        borderProps.isDetail = true;
        const borderValue: BorderUnit = {
          color: '#E4E4E4',
          width: { value: '1', unit: 'px' },
          borderStyle: 'solid',
        };
        borderProps.top = borderValue;
        borderProps.right = borderValue;
        borderProps.bottom = borderValue;
        borderProps.left = borderValue;
        padding.top = { value: '10', unit: 'px' };
        padding.right = { value: '10', unit: 'px' };
        padding.bottom = { value: '10', unit: 'px' };
        padding.left = { value: '10', unit: 'px' };
        padding.isDetail = true;
        break;
      }

      case BlockData_Info_FrameType.BORDER_RADIUS_ONLY: {
        borderProps.isDetail = true;
        const borderValue: BorderUnit = {
          color: '#E4E4E4',
          width: { value: '1', unit: 'px' },
          borderStyle: 'solid',
        };
        borderProps.top = borderValue;
        borderProps.right = borderValue;
        borderProps.bottom = borderValue;
        borderProps.left = borderValue;

        const radiusValue: BorderRadiusUnit = {
          width: { value: '6', unit: 'px' },
          height: { value: '6', unit: 'px' },
          isDetail: true,
        };
        borderProps.radiusTopLeft = radiusValue;
        borderProps.radiusTopRight = radiusValue;
        borderProps.radiusBottomLeft = radiusValue;
        borderProps.radiusBottomRight = radiusValue;
        break;
      }

      case BlockData_Info_FrameType.BORDER_GRADIENT: {
        borderProps.isDetail = true;
        borderProps.bottom = {
          color: '#E4E4E4',
          width: { value: '2', unit: 'px' },
          borderStyle: 'solid',
        };

        const radiusValue: BorderRadiusUnit = {
          width: { value: '6', unit: 'px' },
          height: { value: '6', unit: 'px' },
          isDetail: true,
        };
        borderProps.radiusTopLeft = radiusValue;
        borderProps.radiusTopRight = radiusValue;
        borderProps.radiusBottomLeft = radiusValue;
        borderProps.radiusBottomRight = radiusValue;

        backgroundProps.list.push({
          id: '3',
          type: 'gradient',
          from: `design`,
          background: 'linear-gradient(180deg, #fdfbfb 0%, #ebedee 100%)',
          presetId: null,
          size: '',
          sizeLength: {
            width: { value: '', unit: 'auto' },
            height: { value: '', unit: 'auto' },
          },
          repeat: '',
          attachmentFixed: false,
          clipText: false,
          visibility: true,
          ts: inp.ts,
        } as BgGradientPropDto);
        break;
      }
    }

    if (blockdataInfo.blockdata_frameType !== BlockData_Info_FrameType.NONE) {
      marginBottom.value = '10';
    }

    const blockBg: Component = {
      id: `${id}-Bg`,
      type: ComponentType.Block,
      name: `${id}-Bg`,
      parentId: parentId,
      properties: {
        ...properties,
        marginPadding: PROP_SPACING_DEFAULT_VALUE(inp.ts, {
          margin: {
            top: { value: '0', unit: 'px' },
            left: { value: '0', unit: 'px' },
            right: { value: '0', unit: 'px' },
            bottom: marginBottom,
            isDetail: true,
            ts: inp.ts,
          },
          padding: padding,
        }),
        backgrounds: backgroundProps,
        border: borderProps,
      },
      children: [],
      breakpoint: {
        tablet: { ts: inp.ts },
        phone: { ts: inp.ts },
      },
      ts: inp.ts,
    };
    this.components[blockBg.id] = blockBg;
    this.components[parentId].children.push(blockBg.id);

    // Stage 2 - Size  --------------------------------------------

    let size: SizePropDto;
    if (blockdataInfo?.bwUseRefPage === BlockData_Info_BwUseRefPage.TRUE) {
      size = PROP_SIZE_DEFAULT_VALUE(inp.ts, {
        width: {
          value:
            inp.areaId === Block_Area.SIDE_A
              ? ''
              : `${blockdataInfo?.bwVal ? blockdataInfo.bwVal : ''}`,
          unit:
            inp.areaId === Block_Area.SIDE_A
              ? 'auto'
              : `${!blockdataInfo?.bwVal ? 'auto' : blockdataInfo?.bwType === BlockData_Info_BwType.PIXEL ? 'px' : '%'}`,
        },
      });
    }

    const blockSize: Component = {
      id: `${id}-Size`,
      type: ComponentType.Block,
      name: `${id}-Size`,
      parentId: blockBg.id,
      properties: {
        ...properties,
        size,
      },
      children: [],
      breakpoint: {
        tablet: { ts: inp.ts },
        phone: { ts: inp.ts },
      },
      ts: inp.ts,
    };
    this.components[blockSize.id] = blockSize;
    this.components[blockBg.id].children.push(blockSize.id);

    // Stage 3 - Margin  --------------------------------------------
    const marginInf: string[] = blockdataInfo?.blockdata_marginInf
      ? blockdataInfo?.blockdata_marginInf.split(',')
      : ['0'];

    const blockMargin: Component = {
      id: `${id}-Margin`,
      type: ComponentType.Block,
      name: `${id}-Margin`,
      parentId: blockSize.id,
      properties: {
        ...properties,
        layout: layout,
        marginPadding:
          marginInf[0] === '1'
            ? PROP_SPACING_DEFAULT_VALUE(inp.ts, {
                padding: {
                  left: { value: marginInf[2], unit: 'px' },
                  top: { value: marginInf[3], unit: 'px' },
                  right: { value: marginInf[4], unit: 'px' },
                  bottom: { value: marginInf[5], unit: 'px' },
                  isDetail: true,
                  ts: inp.ts,
                },
              })
            : PROP_SPACING_DEFAULT_VALUE(inp.ts),
      },
      children: [],
      breakpoint: {
        tablet: { ts: inp.ts },
        phone: { ts: inp.ts },
      },
      ts: inp.ts,
    };
    this.components[blockMargin.id] = blockMargin;
    this.components[blockSize.id].children.push(blockMargin.id);

    return blockMargin;
  }

  // <UL class="indent_bubble"/> ======================================================
  private creatIndentBubble(inp: {
    ts: number;
    parentId: string;
    idIndex: number;
    dataContent: BlockData_Content;
  }): Component {
    const resource: Site5_Resource = inp.dataContent.resource;
    const partsPropJson: Resource_PartsProperty = resource?.partsPropertyJson;

    const layout: LayoutPropDto = {
      type: 'grid',
      flex: null,
      grid: {
        cols: '2',
        rows: '',
        columnGap: { value: '0', unit: 'px' },
        rowGap: { value: '2', unit: 'px' },
        justifyContent: 'start',
        alignContent: 'start',
        sizePerCol: {
          list: [
            { id: '1', size: { value: '1', unit: 'fr' } },
            { id: '2', size: { value: '82', unit: '%' } },
          ],
        },
        justifyItems: 'start',
        alignItems: 'start',
        fillEmptyCell: true,
      },
      carousel: null,
      ts: inp.ts,
    };

    const properties: PropertyDto = {
      ts: inp.ts,
      actions: PROP_ACTION_DEFAULT_VALUE(inp.ts),
      marginPadding: PROP_SPACING_DEFAULT_VALUE(inp.ts, {
        margin: {
          top: { value: '0', unit: 'px' },
          left: { value: '10', unit: 'px' },
          right: { value: '0', unit: 'px' },
          bottom: { value: '0', unit: 'px' },
          isDetail: false,
          ts: inp.ts,
        },
      }),
      size: PROP_SIZE_DEFAULT_VALUE(inp.ts, {
        width: {
          value: `${partsPropJson?.wd || ''}`,
          unit: `${partsPropJson?.wd ? 'px' : 'auto'}`,
        },
        height: {
          value: `${partsPropJson?.ht || ''}`,
          unit: `${partsPropJson?.ht ? 'px' : 'auto'}`,
        },
      }),
      border: PROP_BORDER_DEFAULT_VALUE(inp.ts),
      position: PROP_POSITION_DEFAULT_VALUE(inp.ts),
      backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(inp.ts),
      effects: PROP_EFFECT_LIST_DEFAULT_VALUE(inp.ts),
      filter: PROP_FILTER_DEFAULT_VALUE(inp.ts),
      datasource: PROP_DATASOURCE_DEFAULT_VALUE(inp.ts),
      layout: layout,
    };

    const component: Component = {
      id: `${inp.parentId}_UL_INDENT_BUBBLE_${inp.idIndex}`,
      type: ComponentType.Block,
      name: 'UL_INDENT_BUBBLE',
      parentId: inp.parentId,
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: inp.ts },
        phone: { ts: inp.ts },
      },
      ts: inp.ts,
    };

    return component;
  }

  private createIndentBubble_Li(inp: {
    idIndex: number;
    parentId: string;
    dataContent: BlockData_Content;
  }): Component {
    const ts = NEW_TS();
    const id = `${inp.parentId}_LI_${inp.idIndex}_${ts}`;
    const liBlock: Component = {
      id: id,
      type: ComponentType.Block,
      name: inp.dataContent.nodeName,
      parentId: inp.parentId,
      properties: {
        ts: ts,
        actions: PROP_ACTION_DEFAULT_VALUE(ts),
        marginPadding: PROP_SPACING_DEFAULT_VALUE(ts, {
          padding: {
            top: { value: '2', unit: 'px' },
            left: { value: '0', unit: 'px' },
            right: { value: '0', unit: 'px' },
            bottom: { value: '4', unit: 'px' },
            isDetail: true,
            ts: ts,
          },
        }),
        size: PROP_SIZE_DEFAULT_VALUE(ts, {
          width: {
            value: '100',
            unit: '%',
          },
          height: {
            value: '100',
            unit: '%',
          },
        }),
        border: PROP_BORDER_DEFAULT_VALUE(ts, {
          bottom: {
            color: '#ddd',
            width: { value: '1', unit: 'px' },
            borderStyle: 'solid',
          },
          isDetail: true,
        }),
        position: PROP_POSITION_DEFAULT_VALUE(ts),
        effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
        filter: PROP_FILTER_DEFAULT_VALUE(ts),
        datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
      },
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };
    this.components[liBlock.id] = liBlock;
    this.components[liBlock.parentId].children.push(liBlock.id);

    const block: Component = {
      id: `${id}-detail`,
      type: ComponentType.Block,
      name: inp.dataContent.nodeName,
      parentId: liBlock.id,
      properties: {
        ts: ts,
        actions: PROP_ACTION_DEFAULT_VALUE(ts),
        marginPadding: PROP_SPACING_DEFAULT_VALUE(ts),
        size: PROP_SIZE_DEFAULT_VALUE(ts),
        border: PROP_BORDER_DEFAULT_VALUE(ts),
        position: PROP_POSITION_DEFAULT_VALUE(ts),
        effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
        filter: PROP_FILTER_DEFAULT_VALUE(ts),
        datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
        layout: {
          type: 'flex',
          flex: {
            flexDirection: 'row',
            verSpacing: { value: '10', unit: 'px' },
            hozSpacing: { value: '0', unit: 'px' },
            justifyContent: 'start',
            alignContent: 'center',
            alignItems: 'center',
            flexGrow: '0',
            flexShrink: '0',
            flexWrap: 'nowrap',
          },
          grid: null,
          carousel: null,
          ts: ts,
        },
      },
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };

    // Không update component vì đã được update ở `createContentBlock`
    // this.components[block.id] = block;
    // this.components[block.parentId].children.push(block.id);

    const imgComponent: Component = {
      id: `${id}-img-indent`,
      type: ComponentType.Image,
      name: 'Image indent',
      parentId: block.id,
      properties: {
        ts: ts,
        actions: PROP_ACTION_DEFAULT_VALUE(ts),
        marginPadding: PROP_SPACING_DEFAULT_VALUE(ts),
        size: PROP_SIZE_DEFAULT_VALUE(ts, {
          width: { value: '8', unit: 'px' },
          height: { value: '7', unit: 'px' },
        }),
        border: PROP_BORDER_DEFAULT_VALUE(ts),
        position: PROP_POSITION_DEFAULT_VALUE(ts),
        effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
        filter: PROP_FILTER_DEFAULT_VALUE(ts),
        datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
        media: {
          type: 'image',
          url: 'https://tuekgiwcwyuckzgwxejb.supabase.co/storage/v1/object/public/bind/theme/default08/blockskin/skin-3/indent_bubble.gif',
          position: {
            x: { value: '0', unit: 'px' },
            y: { value: '0', unit: 'px' },
            offsetX: { value: '0', unit: 'px' },
            offsetY: { value: '0', unit: 'px' },
          },
          aspectRatio: 'auto',
        } as MediaImagePropDto,
      },
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };
    this.components[imgComponent.id] = imgComponent;
    block.children.push(imgComponent.id);

    return block;
  }

  // <UL class="menu_menu-b"/> ======================================================
  private createMenuB(inp: {
    ts: number;
    parentId: string;
    idIndex: number;
    areaId: Block_Area;
    dataContent: BlockData_Content;
  }): Component {
    const resource: Site5_Resource = inp.dataContent.resource;
    const partsPropJson: Resource_PartsProperty = resource?.partsPropertyJson;

    const properties: PropertyDto = {
      ts: inp.ts,
      actions: PROP_ACTION_DEFAULT_VALUE(inp.ts),
      marginPadding: PROP_SPACING_DEFAULT_VALUE(inp.ts, {
        margin: {
          top: { value: '0', unit: 'px' },
          left: { value: '10', unit: 'px' },
          right: { value: '0', unit: 'px' },
          bottom: { value: '0', unit: 'px' },
          isDetail: false,
          ts: inp.ts,
        },
      }),
      size: PROP_SIZE_DEFAULT_VALUE(inp.ts, {
        width: {
          value: `${partsPropJson?.wd || ''}`,
          unit: `${partsPropJson?.wd ? 'px' : 'auto'}`,
        },
        height: {
          value: `${partsPropJson?.ht || ''}`,
          unit: `${partsPropJson?.ht ? 'px' : 'auto'}`,
        },
      }),
      border: PROP_BORDER_DEFAULT_VALUE(inp.ts),
      position: PROP_POSITION_DEFAULT_VALUE(inp.ts),
      backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(inp.ts),
      effects: PROP_EFFECT_LIST_DEFAULT_VALUE(inp.ts),
      filter: PROP_FILTER_DEFAULT_VALUE(inp.ts),
      datasource: PROP_DATASOURCE_DEFAULT_VALUE(inp.ts),
      layout: {
        type: 'flex',
        flex: {
          flexDirection: inp.areaId === Block_Area.SIDE_A ? 'column' : 'row',
          verSpacing: { value: '0', unit: 'px' },
          hozSpacing: { value: '0', unit: 'px' },
          justifyContent: 'start',
          alignContent: 'center',
          alignItems: 'center',
          flexGrow: '0',
          flexShrink: '0',
          flexWrap: 'nowrap',
        },
        grid: null,
        carousel: null,
        ts: inp.ts,
      },
    };

    const component: Component = {
      id: `${inp.parentId}_UL_MENU-B_${inp.idIndex}`,
      type: ComponentType.Block,
      name: 'UL_MENU-B',
      parentId: inp.parentId,
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: inp.ts },
        phone: { ts: inp.ts },
      },
      ts: inp.ts,
    };

    return component;
  }

  private createMenuB_Li(inp: {
    idIndex: number;
    parentId: string;
    dataContent: BlockData_Content;
    extra: BlockData_Content_Extra;
  }): Component {
    const ts = NEW_TS();
    const id = `${inp.parentId}_LI_${inp.idIndex}_${ts}`;

    const borderProp: BorderPropDto = PROP_BORDER_DEFAULT_VALUE(ts);
    if (inp.extra.areaId === Block_Area.SIDE_A) {
      borderProp.top = {
        width: { value: inp.idIndex === 1 ? '1' : '0', unit: 'px' },
        borderStyle: 'solid',
        color: '#444',
      };
      borderProp.bottom = {
        width: { value: '1', unit: 'px' },
        borderStyle: 'solid',
        color: '#444',
      };
      borderProp.isDetail = true;
    } else {
      borderProp.left = {
        width: { value: inp.idIndex === 1 ? '1' : '0', unit: 'px' },
        borderStyle: 'solid',
        color: '#999',
      };
      borderProp.right = {
        width: { value: '1', unit: 'px' },
        borderStyle: 'solid',
        color: '#999',
      };
      borderProp.isDetail = true;
    }

    const liBlock: Component = {
      id: id,
      type: ComponentType.Block,
      name: inp.dataContent.nodeName,
      parentId: inp.parentId,
      properties: {
        ts: ts,
        actions: PROP_ACTION_DEFAULT_VALUE(ts),
        marginPadding: PROP_SPACING_DEFAULT_VALUE(ts, {
          padding:
            inp.extra.areaId === Block_Area.SIDE_A
              ? {
                  top: { value: '14', unit: 'px' },
                  left: { value: '0', unit: 'px' },
                  right: { value: '0', unit: 'px' },
                  bottom: { value: '12', unit: 'px' },
                  isDetail: true,
                  ts: ts,
                }
              : {
                  top: { value: '4', unit: 'px' },
                  left: { value: '20', unit: 'px' },
                  right: { value: '0', unit: 'px' },
                  bottom: { value: '0', unit: 'px' },
                  isDetail: false,
                  ts: ts,
                },
        }),
        size: PROP_SIZE_DEFAULT_VALUE(ts, {
          width:
            inp.extra.areaId === Block_Area.SIDE_A
              ? {
                  value: '100',
                  unit: '%',
                }
              : {
                  value: '',
                  unit: 'auto',
                },
          height: {
            value: '100',
            unit: '%',
          },
        }),
        border: borderProp,
        // backgrounds: {
        //   list: [
        //     {
        //       id: '1',
        //       type: 'image',
        //       url: `https://tuekgiwcwyuckzgwxejb.supabase.co/storage/v1/object/public/bind/theme/default08/blockskin/share/1_menuh_b_bg_cr.gif`,
        //       position: {
        //         x: { value: '0', unit: 'px' },
        //         y: { value: '0', unit: 'px' },
        //         offsetX: { value: '0', unit: 'px' },
        //         offsetY: { value: '0', unit: 'px' },
        //       },
        //       repeat: 'repeat',
        //       attachmentFixed: false,
        //       clipText: false,
        //       visibility: true,
        //       ts: ts,
        //     } as BgImagePropDto,
        //   ],
        //   ts: ts,
        // },
        position: PROP_POSITION_DEFAULT_VALUE(ts),
        effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
        filter: PROP_FILTER_DEFAULT_VALUE(ts),
        datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
      },
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };
    // Không update component vì đã được update ở `createContentBlock`
    // this.components[liBlock.id] = liBlock;
    // this.components[liBlock.parentId].children.push(liBlock.id);

    return liBlock;
  }

  private createNaviNumbersComponent(inp: {
    ts: number;
    parentId: string;
    idIndex: number;
  }): Component {
    const ts = inp.ts;
    const containerId = `${inp.parentId}_NAVI_NUMBERS_${inp.idIndex}`;

    // Create main container block
    const containerComponent: Component = {
      id: containerId,
      type: ComponentType.Block,
      name: 'Navigation Numbers',
      parentId: inp.parentId,
      properties: {
        ts: ts,
        actions: PROP_ACTION_DEFAULT_VALUE(ts),
        marginPadding: PROP_SPACING_DEFAULT_VALUE(ts),
        size: PROP_SIZE_DEFAULT_VALUE(ts),
        border: PROP_BORDER_DEFAULT_VALUE(ts),
        position: PROP_POSITION_DEFAULT_VALUE(ts),
        backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts),
        effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
        filter: PROP_FILTER_DEFAULT_VALUE(ts),
        datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
        layout: {
          type: 'flex',
          flex: {
            flexDirection: 'row',
            verSpacing: { value: '0', unit: 'px' },
            hozSpacing: { value: '0', unit: 'px' },
            justifyContent: 'center',
            alignContent: 'center',
            alignItems: 'center',
            flexGrow: '0',
            flexShrink: '0',
            flexWrap: 'nowrap',
          },
          grid: null,
          carousel: null,
          ts: ts,
        },
      },
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };

    // Create 5 page navigation links
    const pageNumbers = [1, 2, 3, 4, 5];
    const linkIds: string[] = [];

    pageNumbers.forEach((pageNum, index) => {
      const linkId = `${containerId}_LINK_${pageNum}`;
      const textId = `${linkId}_TEXT`;
      linkIds.push(linkId);

      // Create border properties - all have right border, first one also has left border
      const borderProps: BorderPropDto = PROP_BORDER_DEFAULT_VALUE(ts, {
        isDetail: true,
        right: {
          color: '#ccc',
          width: { value: '1', unit: 'px' },
          borderStyle: 'solid',
        },
        bottom: {
          borderStyle: 'dotted',
          color: '#000',
          width: {
            value: '1',
            unit: 'px',
          },
        },
      });

      // First item has additional left border
      if (index === 0) {
        borderProps.left = {
          color: '#ccc',
          width: { value: '1', unit: 'px' },
          borderStyle: 'solid',
        };
      }

      const linkComponent: Component = {
        id: linkId,
        type: ComponentType.Link,
        name: `Page ${pageNum}`,
        parentId: containerId,
        properties: {
          ts: ts,
          actions: PROP_ACTION_DEFAULT_VALUE(ts),
          marginPadding: PROP_SPACING_DEFAULT_VALUE(ts, {
            padding: {
              top: { value: '1', unit: 'px' },
              left: { value: '1', unit: 'px' },
              right: { value: '1', unit: 'px' },
              bottom: { value: '1', unit: 'px' },
              isDetail: true,
              ts: ts,
            },
          }),
          size: PROP_SIZE_DEFAULT_VALUE(ts),
          border: borderProps,
          position: PROP_POSITION_DEFAULT_VALUE(ts),
          backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts),
          effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
          filter: PROP_FILTER_DEFAULT_VALUE(ts),
          datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
          link: PROP_LINK_DEFAULT_VALUE(ts, {
            value: `#page-${pageNum}`,
          }),
          binding: PROP_BINDING_DEFAULT_VALUE(ts),
          layout: {
            type: 'flex',
            flex: {
              flexDirection: 'row',
              verSpacing: { value: '0', unit: 'px' },
              hozSpacing: { value: '0', unit: 'px' },
              justifyContent: 'center',
              alignContent: 'center',
              alignItems: 'center',
              flexGrow: '0',
              flexShrink: '0',
              flexWrap: 'nowrap',
            },
            grid: null,
            carousel: null,
            ts: ts,
          },
        },
        children: [textId],
        breakpoint: {
          tablet: { ts: ts },
          phone: { ts: ts },
        },
        ts: ts,
      };

      // Create text component for the link
      const textComponent: Component = {
        id: textId,
        type: ComponentType.Text,
        name: 'text',
        parentId: linkId,
        properties: {
          ts: ts,
          actions: PROP_ACTION_DEFAULT_VALUE(ts),
          marginPadding: PROP_SPACING_DEFAULT_VALUE(ts),
          size: PROP_SIZE_DEFAULT_VALUE(ts),
          border: PROP_BORDER_DEFAULT_VALUE(ts),
          position: PROP_POSITION_DEFAULT_VALUE(ts),
          backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts),
          effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
          filter: PROP_FILTER_DEFAULT_VALUE(ts),
          datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
          text: PROP_TEXT_DEFAULT_VALUE(ts, {
            text: pageNum.toString(),
          }),
        },
        children: [],
        breakpoint: {
          tablet: { ts: ts },
          phone: { ts: ts },
        },
        ts: ts,
      };

      // Add both components to the components collection
      this.components[linkComponent.id] = linkComponent;
      this.components[textComponent.id] = textComponent;
    });

    // Set children for container
    containerComponent.children = linkIds;

    return containerComponent;
  }

  private getLayoutWidth(inp: { layoutId: string; area: string }) {
    const cssMap = {
      L01: {
        'area-header': {
          width: { value: '825', unit: 'px' },
        },
        'area-main': {
          width: { value: '825', unit: 'px' },
        },
        'area-footer': {
          width: { value: '825', unit: 'px' },
        },
      },
      L02: {},
      L03: {
        'area-header': {
          width: { value: '825', unit: 'px' },
        },
        'area-side-a': {
          width: { value: '239', unit: 'px' },
        },
        'area-main': {
          width: { value: '577', unit: 'px' },
        },
        'area-footer': {
          width: { value: '825', unit: 'px' },
        },
      },
      L04: {},
      L05: {},
      L06: {},
      L07: {},
      L08: {},
    };

    return cssMap[inp.layoutId][inp.area]?.width;
  }
}
