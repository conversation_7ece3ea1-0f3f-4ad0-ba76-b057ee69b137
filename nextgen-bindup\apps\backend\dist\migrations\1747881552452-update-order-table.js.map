{"version": 3, "file": "1747881552452-update-order-table.js", "sourceRoot": "", "sources": ["../../src/migrations/1747881552452-update-order-table.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,6BAA6B;IAA1C;QACE,eAAU,GAAW,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,QAAQ,CAAC;IA2GlE,CAAC;IAzGQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEtC,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAE7D,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAE3D,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAE3D,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAE7D,MAAM,WAAW,CAAC,SAAS,CACzB,IAAI,CAAC,UAAU,EACf,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CACzB,IAAI,CAAC,UAAU,EACf,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,mBAAmB;YACzB,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CACzB,IAAI,CAAC,UAAU,EACf,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CACzB,IAAI,CAAC,UAAU,EACf,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CACzB,IAAI,CAAC,UAAU,EACf,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,mBAAmB;YACzB,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,IAAI;YACZ,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QAExC,MAAM,WAAW,CAAC,SAAS,CACzB,IAAI,CAAC,UAAU,EACf,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CACzB,IAAI,CAAC,UAAU,EACf,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CACzB,IAAI,CAAC,UAAU,EACf,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CACzB,IAAI,CAAC,UAAU,EACf,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAE1D,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;QAEnE,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAEvD,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;QAEnE,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;IACjE,CAAC;CACF;AA5GD,sEA4GC"}