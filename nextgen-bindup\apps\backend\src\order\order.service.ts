import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MoreThanOrEqual, Repository } from 'typeorm';
import { AppException } from 'src/common/exceptions/app.exception';
import { OrderEntity } from './entites/order.entity';
import { OrderItemEntity } from './entites/order-item.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { OrderStatus } from './enum/order.enum';
import { PaginatedResponse } from 'src/common/paginated-response';
import { PaymentMethodType } from './enum/payment-method-type.enum';
import { ProductService } from 'src/product/product.service';
import { calculateFees, getDisplayPrice } from 'src/utils/price.util';
import { ShippingNoteSettingService } from 'src/shipping-note-settings/shipping-note-settings.service';
import { ShopInformationSettingService } from 'src/shop-information-settings/shop-information-settings.service';
import { GetOrdersQueryDto } from './dto/get-order.dto';
import { CsvService } from '../product/csv.service';
import { Readable } from 'stream';

@Injectable()
export class OrderService {
  @InjectRepository(OrderEntity)
  private orderRepository: Repository<OrderEntity>;
  @InjectRepository(OrderItemEntity)
  private orderItemRepository: Repository<OrderItemEntity>;

  constructor(
    private readonly productService: ProductService,
    private readonly shippingNoteSettingService: ShippingNoteSettingService,
    private readonly shopInformationSettingService: ShopInformationSettingService,
    private readonly csvService: CsvService,
  ) {}

  async getOrders(
    siteId: string,
    query: GetOrdersQueryDto,
  ): Promise<PaginatedResponse<OrderEntity>> {
    const queryBuilder = this.orderRepository
      .createQueryBuilder('orders')
      .leftJoinAndSelect('orders.orderItems', 'orderItems')
      .leftJoinAndSelect('orderItems.product', 'product')
      .where('orders.siteId = :siteId', { siteId });

    // Add search filter
    if (query.search?.trim()) {
      queryBuilder.andWhere(
        '(orders.firstName ILIKE :search OR orders.lastName ILIKE :search OR orders.email ILIKE :search OR orders.phoneNumber ILIKE :search)',
        { search: `%${query.search.trim()}%` },
      );
    }

    // Add email filter
    if (query.email) {
      queryBuilder.andWhere('orders.email = :email', { email: query.email });
    }

    // Add phone number filter
    if (query.phoneNumber) {
      queryBuilder.andWhere('orders.phoneNumber = :phoneNumber', {
        phoneNumber: query.phoneNumber,
      });
    }

    // Add order status filter
    if (query.orderStatus) {
      queryBuilder.andWhere('orders.orderStatus = :orderStatus', {
        orderStatus: query.orderStatus,
      });
    }

    // Add payment method filter
    if (query.paymentMethodType) {
      queryBuilder.andWhere('orders.paymentMethodType = :paymentMethodType', {
        paymentMethodType: query.paymentMethodType,
      });
    }

    // Add date range filters
    if (query.startDate) {
      queryBuilder.andWhere('orders.createdAt >= :startDate', {
        startDate: query.startDate,
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('orders.createdAt <= :endDate', {
        endDate: query.endDate,
      });
    }

    // Pagination
    const skip = (query.page - 1) * query.limit;
    queryBuilder
      .orderBy('orders.createdAt', 'DESC')
      .skip(skip)
      .take(query.limit);

    // Execute query and return results
    const [orders, total] = await queryBuilder.getManyAndCount();

    return {
      data: orders,
      total,
      page: query.page,
      limit: query.limit,
      totalPage: Math.ceil(total / query.limit),
    };
  }

  async createOrder(
    dto: CreateOrderDto,
    paymentMethodType: PaymentMethodType,
    orderStatus: OrderStatus,
  ): Promise<OrderEntity> {
    try {
      const { orderItems, ...orderData } = dto;
      const { contact, shipping } = orderData;

      const productIds = orderItems.map(item => item.productId);

      const products = await this.productService.findByIds(
        orderData.siteId,
        productIds,
      );
      const shippingNoteSetting =
        await this.shippingNoteSettingService.findOneBySiteId(orderData.siteId);
      const shopInformationSetting =
        await this.shopInformationSettingService.findOneBySiteId(
          orderData.siteId,
        );

      if (!shippingNoteSetting) {
        throw new AppException('api.error.shipping_note_setting_not_found');
      }
      if (!shopInformationSetting) {
        throw new AppException('api.error.shop_information_not_found');
      }

      console.log('orderData:', orderData);

      const item: Omit<OrderEntity, 'id'> = {
        siteId: orderData.siteId,
        lastName: contact.lastName,
        firstName: contact.firstName,
        lastNameKana: contact.lastNameKana,
        firstNameKana: contact.firstNameKana,
        email: contact.email,
        postalCode: contact.postalCode,
        prefecture: contact.prefecture,
        addressLine1: contact.addressLine1,
        addressLine2: contact.addressLine2,
        phoneNumber: contact.phoneNumber,
        shippingLastName: shipping?.lastName || '',
        shippingFirstName: shipping?.firstName || '',
        shippingLastNameKana: shipping?.lastNameKana || '',
        shippingFirstNameKana: shipping?.firstNameKana || '',
        shippingEmail: shipping?.email || '',
        shippingPostalCode: shipping?.postalCode || '',
        shippingPrefecture: shipping?.prefecture || '',
        shippingAddressLine1: shipping?.addressLine1 || '',
        shippingAddressLine2: shipping?.addressLine2 || '',
        shippingPhoneNumber: shipping?.phoneNumber || '',
        additionalInformation: orderData.additionalInformation,
        orderStatus: orderStatus,
        createdAt: new Date(),
        updatedAt: new Date(),
        checkoutSessionUrl: '',
        checkoutSessionId: '',
        subtotal: 0,
        shippingFee: 0,
        platformFee: 0,
        paymentGatewayFee: 0,
        total: 0,
        shopNetPayout: 0,
        paymentMethodType: paymentMethodType,
      };
      const order = this.orderRepository.create(item);

      const orderItemEntities: OrderItemEntity[] = orderItems.map(item => {
        const product = products.find(p => p.id === item.productId);
        if (!product) {
          throw new AppException(
            `api.error.product_not_found: ${item.productId}`,
          );
        }
        if (!product.isOrderable) {
          throw new AppException(
            `api.error.product_not_orderable: ${item.productId}`,
          );
        }
        if (product.isDeleted) {
          throw new AppException(
            `api.error.product_is_deleted: ${item.productId}`,
          );
        }
        if (item.quantity <= 0) {
          throw new AppException(
            `api.error.product_invalid_quantity: ${item.productId}`,
          );
        }
        const displayPrice = getDisplayPrice(product, shopInformationSetting);
        const orderItem = this.orderItemRepository.create({
          orderId: order.id,
          productId: item.productId,
          productName: product.name,
          productType: product.productType,
          unitPrice: product.price,
          individualShippingCharges: product.individualShippingCharges,
          salePrice: product.sale,
          displayPrice: displayPrice,
          quantity: item.quantity,
          subtotal: displayPrice * item.quantity,
          attributes: item.attributes,
          image: product.images?.[0],
        });
        return orderItem;
      });

      const fee = calculateFees(
        orderItemEntities,
        shippingNoteSetting,
        0.05, // platform fee rate
        0.036, // payment gateway fee rate
        order.paymentMethodType,
        shipping?.prefecture || '',
      );
      order.subtotal = fee.subtotal;
      order.shippingFee = fee.shippingFee;
      order.platformFee = fee.platformFee;
      order.paymentGatewayFee = fee.paymentGatewayFee;
      order.total = fee.total;
      order.shopNetPayout = fee.shopNetPayout;
      order.checkoutSessionUrl = '';
      order.checkoutSessionId = '';

      const savedOrder = await this.orderRepository.save(order);

      await this.orderItemRepository.save(
        orderItemEntities.map(item => ({ ...item, orderId: savedOrder.id })),
      );
      return this.findOrderById(savedOrder.id);
    } catch (error) {
      console.error('Error creating pending order:', error);
    }
  }

  async updateOrder(
    orderId: number,
    orderData: Partial<OrderEntity>,
  ): Promise<OrderEntity> {
    const order = await this.orderRepository.findOneBy({ id: orderId });
    if (!order) {
      throw new AppException('api.error.order_not_found');
    }

    delete orderData.id;
    delete orderData.createdAt;
    orderData.updatedAt = new Date();

    await this.orderRepository.update(orderId, orderData);
    return { ...order, ...orderData };
  }

  async findOrderById(orderId: number): Promise<OrderEntity> {
    const order = await this.orderRepository.findOne({
      where: { id: orderId },
      relations: ['orderItems', 'orderItems.product'], // Load the related order items
    });
    if (!order) {
      throw new AppException('api.error.order_not_found');
    }
    return order;
  }

  async deleteOrder(orderId: number): Promise<boolean> {
    const order = await this.orderRepository.findOneBy({ id: orderId });
    if (!order) {
      throw new AppException('api.error.order_not_found');
    }
    await this.orderRepository.delete(orderId);
    return true;
  }

  async getWaitingPaymentOrdersInLast2Days() {
    const twoDaysAgo = new Date();
    twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);

    const orders = await this.orderRepository.find({
      where: {
        orderStatus: OrderStatus.WAITING_PAYMENT,
        paymentMethodType: PaymentMethodType.CREDIT_CARD,
        createdAt: MoreThanOrEqual(twoDaysAgo),
      },
      relations: ['orderItems', 'orderItems.product'], // Load the related order items
    });

    return orders;
  }

  async downloadCSV(
    siteId: string,
    query?: GetOrdersQueryDto,
  ): Promise<Readable> {
    const queryBuilder = this.orderRepository
      .createQueryBuilder('orders')
      .leftJoinAndSelect('orders.orderItems', 'orderItems')
      .leftJoinAndSelect('orderItems.product', 'product')
      .where('orders.siteId = :siteId', { siteId });

    // Add filters if provided
    if (query?.email) {
      queryBuilder.andWhere('orders.email = :email', { email: query.email });
    }
    if (query?.phoneNumber) {
      queryBuilder.andWhere('orders.phoneNumber = :phoneNumber', {
        phoneNumber: query.phoneNumber,
      });
    }
    if (query?.startDate) {
      queryBuilder.andWhere('orders.createdAt >= :startDate', {
        startDate: query.startDate,
      });
    }
    if (query?.endDate) {
      queryBuilder.andWhere('orders.createdAt <= :endDate', {
        endDate: query.endDate,
      });
    }
    if (query?.orderStatus) {
      queryBuilder.andWhere('orders.orderStatus = :orderStatus', {
        orderStatus: query.orderStatus,
      });
    }

    const orders = await queryBuilder.getMany();

    const headers = [
      // Basic Order Information
      '注文番号', // Order ID
      '注文日時', // Order Date
      '注文ステータス', // Order Status
      '支払方法', // Payment Method
      '合計金額', // Total Amount
      '商品合計', // Subtotal
      '送料', // Shipping Fee
      'プラットフォーム手数料', // Platform Fee
      '決済手数料', // Payment Gateway Fee
      '店舗支払額', // Shop Net Payout

      // Customer Information
      'お客様名（姓）', // Last Name
      'お客様名（名）', // First Name
      'お客様名カナ（姓）', // Last Name Kana
      'お客様名カナ（名）', // First Name Kana
      'メールアドレス', // Email
      '電話番号', // Phone
      '郵便番号', // Postal Code
      '都道府県', // Prefecture
      '住所1', // Address Line 1
      '住所2', // Address Line 2

      // Shipping Information
      '配送先名（姓）', // Shipping Last Name
      '配送先名（名）', // Shipping First Name
      '配送先名カナ（姓）', // Shipping Last Name Kana
      '配送先名カナ（名）', // Shipping First Name Kana
      '配送先メールアドレス', // Shipping Email
      '配送先電話番号', // Shipping Phone
      '配送先郵便番号', // Shipping Postal Code
      '配送先都道府県', // Shipping Prefecture
      '配送先住所1', // Shipping Address Line 1
      '配送先住所2', // Shipping Address Line 2

      // Product Information
      '商品コード', // Product ID
      '商品名', // Product Name
      '商品オプション', // Product Attributes
      '商品タイプ', // Product Type
      '単価', // Unit Price
      'セール価格', // Sale Price
      '表示価格', // Display Price
      '数量', // Quantity
      '小計', // Item Subtotal

      // Additional Information
      '追加情報', // Additional Information
    ];

    const rows = orders
      .map(order =>
        order.orderItems.map(item => ({
          // Basic Order Information
          注文番号: order.id,
          注文日時: order.createdAt.toISOString(),
          注文ステータス: order.orderStatus,
          支払方法: order.paymentMethodType,
          合計金額: order.total,
          商品合計: order.subtotal,
          送料: order.shippingFee,
          プラットフォーム手数料: order.platformFee,
          決済手数料: order.paymentGatewayFee,
          店舗支払額: order.shopNetPayout,

          // Customer Information
          'お客様名（姓）': order.lastName,
          'お客様名（名）': order.firstName,
          'お客様名カナ（姓）': order.lastNameKana,
          'お客様名カナ（名）': order.firstNameKana,
          メールアドレス: order.email,
          電話番号: order.phoneNumber,
          郵便番号: order.postalCode,
          都道府県: order.prefecture,
          住所1: order.addressLine1,
          住所2: order.addressLine2,

          // Shipping Information
          '配送先名（姓）': order.shippingLastName,
          '配送先名（名）': order.shippingFirstName,
          '配送先名カナ（姓）': order.shippingLastNameKana,
          '配送先名カナ（名）': order.shippingFirstNameKana,
          配送先メールアドレス: order.shippingEmail,
          配送先電話番号: order.shippingPhoneNumber,
          配送先郵便番号: order.shippingPostalCode,
          配送先都道府県: order.shippingPrefecture,
          配送先住所1: order.shippingAddressLine1,
          配送先住所2: order.shippingAddressLine2,

          // Product Information
          商品コード: item.product.code,
          商品名: item.productName,
          商品オプション: this.formatProductOptions(item.attributes),
          商品タイプ: item.productType,
          単価: item.unitPrice,
          セール価格: item.salePrice,
          表示価格: item.displayPrice,
          数量: item.quantity,
          小計: item.subtotal,

          // Additional Information
          追加情報: order.additionalInformation,
        })),
      )
      .flat();

    return await this.csvService.exportToCsv(rows, headers);
  }

  private formatProductOptions(
    attributes: Record<string, string> | null,
  ): string {
    if (!attributes) return '';

    const hasAttribute1 = 'attribute1' in attributes && attributes.attribute1;
    const hasAttribute2 = 'attribute2' in attributes && attributes.attribute2;

    if (hasAttribute1 && hasAttribute2) {
      return `${attributes.attribute1} - ${attributes.attribute2}`;
    }

    if (hasAttribute1) {
      return attributes.attribute1;
    }

    return '';
  }

  async markAsPaid(orderId: number): Promise<OrderEntity> {
    const order = await this.findOrderById(orderId);
    if (!order) {
      throw new AppException('api.error.order_not_found');
    }

    if (order.orderStatus === OrderStatus.PAID) {
      throw new AppException('api.error.order_already_paid');
    }

    order.orderStatus = OrderStatus.PAID;
    order.updatedAt = new Date();

    return await this.orderRepository.save(order);
  }
}
