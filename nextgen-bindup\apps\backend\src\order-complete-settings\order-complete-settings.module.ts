import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrderCompletionSettingEntity } from './entities/order-complete-settings.entity';
import { OrderCompletionSettingController } from './order-complete-settings.controller';
import { OrderCompletionSettingService } from './order-complete-settings.service';
import { SiteModule } from 'src/site/site.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([OrderCompletionSettingEntity]),
    SiteModule,
  ],
  controllers: [OrderCompletionSettingController],
  providers: [OrderCompletionSettingService],
  exports: [OrderCompletionSettingService],
})
export class OrderCompletionSettingModule {}
