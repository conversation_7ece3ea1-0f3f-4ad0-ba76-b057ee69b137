import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { ShippingNoteSettingService } from './shipping-note-settings.service';
import { ShippingNoteSettingEntity } from './entities/shipping-note--settings.entity';

@Controller('shipping-note-settings')
@UseGuards(AuthGuard)
export class ShippingNoteSettingController {
  constructor(
    private readonly shippingNoteSettingService: ShippingNoteSettingService,
  ) {}

  @Post('create')
  async create(@Body() shippingNoteSettingEntity: ShippingNoteSettingEntity) {
    return await this.shippingNoteSettingService.create(
      shippingNoteSettingEntity,
    );
  }

  @Put('update/:id')
  async update(
    @Param('id') id: string,
    @Body() data: Partial<ShippingNoteSettingEntity>,
  ) {
    return await this.shippingNoteSettingService.update(+id, data);
  }

  @Get('one-by-site/:siteId')
  async getOneBySiteId(@Param('siteId') siteId: string) {
    return await this.shippingNoteSettingService.findOneBySiteId(+siteId);
  }
}
