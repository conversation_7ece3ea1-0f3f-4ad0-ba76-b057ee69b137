"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsCollectionItemsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
const cms_collection_items_entity_1 = require("./entities/cms-collection-items.entity");
const cms_collection_service_1 = require("../cms-collection/cms-collection.service");
const product_service_1 = require("../product/product.service");
const shop_information_settings_service_1 = require("../shop-information-settings/shop-information-settings.service");
const price_util_1 = require("../utils/price.util");
let CmsCollectionItemsService = class CmsCollectionItemsService {
    constructor(cmsCollectionService, productService, shopInformationSettingService) {
        this.cmsCollectionService = cmsCollectionService;
        this.productService = productService;
        this.shopInformationSettingService = shopInformationSettingService;
    }
    async create(collectionItemEntity) {
        const now = new Date();
        const collectionItem = new cms_collection_items_entity_1.CmsCollectionItemEntity();
        collectionItem.cmsCollectionId = collectionItemEntity.cmsCollectionId;
        collectionItem.title = collectionItemEntity.title;
        collectionItem.slug = collectionItemEntity.slug;
        collectionItem.data = collectionItemEntity.data;
        collectionItem.status = collectionItemEntity.status;
        collectionItem.siteId = collectionItemEntity.siteId;
        collectionItem.rootUserId = collectionItemEntity.rootUserId;
        collectionItem.userId = collectionItemEntity.userId;
        collectionItem.createdAt = now;
        collectionItem.updatedAt = now;
        return await this.collectionItemRepo.save(collectionItem);
    }
    async update(id, collectionItemData) {
        const collectionItem = await this.collectionItemRepo.findOneBy({ id: id });
        if (!collectionItem)
            throw new app_exception_1.AppException('cms.collection_item.error.not_found');
        delete collectionItemData.id;
        delete collectionItemData.cmsCollectionId;
        delete collectionItemData.siteId;
        delete collectionItemData.rootUserId;
        delete collectionItemData.createdAt;
        collectionItemData.updatedAt = new Date();
        await this.collectionItemRepo.update(id, collectionItemData);
        return { ...collectionItem, ...collectionItemData };
    }
    async findById(id) {
        return await this.collectionItemRepo.findOneBy({ id });
    }
    async findByCollectionId(cmsCollectionId, dto) {
        const { offset, limit, searchText } = dto;
        const queryBuilder = this.collectionItemRepo.createQueryBuilder('cmsCollectionItem');
        queryBuilder.where('cmsCollectionItem.cmsCollectionId = :cmsCollectionId', {
            cmsCollectionId,
        });
        if (searchText) {
            const collection = await this.cmsCollectionService.findById(cmsCollectionId);
            const keys = collection.struct.reduce((result, struct) => struct.type === 'text' ? [...result, struct.id] : result, []);
            if (keys.length > 0) {
                const escapedSearchText = searchText.replace(/[%_]/g, '\\$&');
                const conditions = keys.map(key => `cmsCollectionItem.data -> '${key}' ->> 'value' ILIKE :searchValue`);
                const whereClause = conditions.join(' OR ');
                queryBuilder.andWhere(whereClause, {
                    searchValue: `%${escapedSearchText}%`,
                });
            }
        }
        if (offset) {
            queryBuilder.skip(offset);
        }
        if (limit) {
            queryBuilder.take(limit);
        }
        const [data, count] = await queryBuilder.getManyAndCount();
        return { data, count };
    }
    async findBySiteId(siteId) {
        const result = {};
        const list = await this.collectionItemRepo.find({
            where: { siteId },
            order: { cmsCollectionId: 'ASC' },
        });
        let oldSiteCollectionId = -1;
        for (const item of list) {
            if (item.cmsCollectionId !== oldSiteCollectionId) {
                result[item.cmsCollectionId] = [];
                oldSiteCollectionId = item.cmsCollectionId;
            }
            result[item.cmsCollectionId].push(item);
        }
        const products = await this.productService.getAllProducts(siteId);
        const shopInformationSetting = await this.shopInformationSettingService.findOneBySiteId(siteId);
        const productCollectionId = -1;
        const productCollectionStrId = '-1';
        const productCollectionItems = products.map(product => {
            const collectionItem = new cms_collection_items_entity_1.CmsCollectionItemEntity();
            collectionItem.cmsCollectionId = productCollectionId;
            collectionItem.title = product.name;
            collectionItem.id = product.id;
            collectionItem.data = {};
            const priceWithTax = (0, price_util_1.calculatePriceWithTax)(product.price, shopInformationSetting?.taxMode, shopInformationSetting?.taxRate, shopInformationSetting?.taxRegulation);
            const salePriceWithTax = product.sale > 0
                ? (0, price_util_1.calculatePriceWithTax)(product.sale, shopInformationSetting?.taxMode, shopInformationSetting?.taxRate, shopInformationSetting?.taxRegulation)
                : null;
            const formattedPriceWithTax = (0, price_util_1.formatPriceWithTax)(priceWithTax);
            const formattedSalePriceWithTax = salePriceWithTax
                ? (0, price_util_1.formatPriceWithTax)(salePriceWithTax)
                : null;
            const displayPrice = (0, price_util_1.formatDisplayPrice)(priceWithTax, salePriceWithTax);
            const productFlatValue = [
                product.isOrderable,
                product.code,
                product.name,
                product.title,
                product.description,
                product.images,
                formattedPriceWithTax,
                formattedSalePriceWithTax,
                product.purchaseLimitQuantity,
                product.individualShippingCharges,
                product.fileDownload,
                product.unlimitedPurchase,
                product.productType,
                product.productVariantType,
                product.variants,
                product.priceLabel,
                product.saleLabel,
                displayPrice,
            ];
            for (let i = 0; i < productFlatValue.length; i++) {
                const id = i + 1;
                collectionItem.data[id] = {
                    id: id,
                    value: productFlatValue[i],
                };
            }
            collectionItem.status = 'published';
            collectionItem.siteId = siteId;
            return collectionItem;
        });
        result[productCollectionStrId] = productCollectionItems;
        return result;
    }
    async delete(id) {
        const collectionItem = await this.collectionItemRepo.findOneBy({ id });
        if (!collectionItem)
            throw new app_exception_1.AppException('cms.collection_item.error.not_found');
        await this.collectionItemRepo.delete(id);
        return true;
    }
};
exports.CmsCollectionItemsService = CmsCollectionItemsService;
__decorate([
    (0, typeorm_1.InjectRepository)(cms_collection_items_entity_1.CmsCollectionItemEntity),
    __metadata("design:type", typeorm_2.Repository)
], CmsCollectionItemsService.prototype, "collectionItemRepo", void 0);
exports.CmsCollectionItemsService = CmsCollectionItemsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cms_collection_service_1.CmsCollectionService,
        product_service_1.ProductService,
        shop_information_settings_service_1.ShopInformationSettingService])
], CmsCollectionItemsService);
//# sourceMappingURL=cms-collection-items.service.js.map