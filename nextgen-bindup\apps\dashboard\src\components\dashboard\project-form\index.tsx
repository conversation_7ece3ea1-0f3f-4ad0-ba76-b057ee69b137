import { FC, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Stack,
  Typography,
  DialogContent,
  TextField,
  DialogActions,
  Button,
} from '@mui/material';
import { ProjectEntity } from '../../../dto/project.type';

export const ProjectForm: FC<{
  isEditing: boolean;
  initialProject: ProjectEntity;
  handleInputChange: (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void;
  onCreate: () => void;
  onUpdate: () => void;
  onCancel: () => void;
}> = ({
  isEditing,
  initialProject,
  handleInputChange,
  onCreate,
  onUpdate,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [project, setProject] = useState(initialProject);

  useEffect(() => {
    setProject(initialProject);
  }, [initialProject]);

  return (
    <Stack sx={{ width: '600px' }}>
      <Typography
        sx={{ paddingBlock: 2, paddingInline: 3 }}
        variant="subtitle2"
        color="textPrimary"
      >
        {t('project.form.title')}
      </Typography>
      <DialogContent sx={{ paddingBlock: 1 }}>
        <Stack direction={'column'} spacing={2}>
          <TextField
            id="name"
            name="name"
            label={t('project.form.name')}
            variant="filled"
            onChange={handleInputChange}
            value={project.name}
            size="small"
          />
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button variant="text" onClick={onCancel}>
          {t('common.cancel')}
        </Button>
        <Button
          color="inherit"
          variant="outlined"
          onClick={isEditing ? onUpdate : onCreate}
        >
          {isEditing ? t('common.update') : t('common.create')}
        </Button>
      </DialogActions>
    </Stack>
  );
};
