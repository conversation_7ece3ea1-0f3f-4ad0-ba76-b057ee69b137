"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SiteEntity = void 0;
const typeorm_1 = require("typeorm");
const site_type_1 = require("../types/site.type");
let SiteEntity = class SiteEntity {
    constructor() {
        this.status = site_type_1.SiteStatus.DRAFT;
    }
};
exports.SiteEntity = SiteEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], SiteEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'projectId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], SiteEntity.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'projectFolderId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], SiteEntity.prototype, "projectFolderId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'managementName',
        type: 'varchar',
        length: 255,
        nullable: false,
    }),
    __metadata("design:type", String)
], SiteEntity.prototype, "managementName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'status',
        type: 'smallint',
        nullable: false,
        default: site_type_1.SiteStatus.DRAFT,
    }),
    __metadata("design:type", Number)
], SiteEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'url',
        type: 'varchar',
        length: 255,
    }),
    __metadata("design:type", String)
], SiteEntity.prototype, "url", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'title',
        type: 'varchar',
        length: 255,
    }),
    __metadata("design:type", String)
], SiteEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'description',
        type: 'text',
    }),
    __metadata("design:type", String)
], SiteEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isSearch',
        type: 'boolean',
    }),
    __metadata("design:type", Boolean)
], SiteEntity.prototype, "isSearch", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'thumb',
        type: 'varchar',
        length: 255,
    }),
    __metadata("design:type", String)
], SiteEntity.prototype, "thumb", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'headCode',
        type: 'text',
    }),
    __metadata("design:type", String)
], SiteEntity.prototype, "headCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'bodyCode',
        type: 'text',
    }),
    __metadata("design:type", String)
], SiteEntity.prototype, "bodyCode", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], SiteEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], SiteEntity.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isArchived',
        type: 'boolean',
    }),
    __metadata("design:type", Boolean)
], SiteEntity.prototype, "isArchived", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'userId',
        type: 'varchar',
        length: '36',
    }),
    __metadata("design:type", String)
], SiteEntity.prototype, "userId", void 0);
exports.SiteEntity = SiteEntity = __decorate([
    (0, typeorm_1.Entity)('sites', { schema: process.env.DATABASE_SCHEMA })
], SiteEntity);
//# sourceMappingURL=site.entity.js.map