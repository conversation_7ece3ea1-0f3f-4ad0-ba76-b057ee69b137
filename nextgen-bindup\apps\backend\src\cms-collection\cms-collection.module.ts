import { Module } from '@nestjs/common';
import { CmsCollectionController } from './cms-collection.controller';
import { CmsCollectionService } from './cms-collection.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CmsCollectionEntity } from './entities/cms-collection.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CmsCollectionEntity])],
  controllers: [CmsCollectionController],
  providers: [CmsCollectionService],
  exports: [CmsCollectionService],
})
export class CmsCollectionModule {}
