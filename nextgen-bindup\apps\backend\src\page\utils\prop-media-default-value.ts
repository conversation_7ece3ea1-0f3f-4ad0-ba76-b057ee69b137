import { MediaImagePropDto } from '@nextgen-bindup/common/dto/setting-properties/media-prop.dto';

export const PROP_MEDIA_IMAGE_DEFAULT_VALUE = (
  ts: number,
  prop?: Partial<MediaImagePropDto>,
): MediaImagePropDto => ({
  type: 'image',
  url: '',
  position: {
    x: { unit: 'px', value: '' },
    y: { unit: 'px', value: '' },
    offsetX: { unit: 'px', value: '' },
    offsetY: { unit: 'px', value: '' },
  },
  alt: '',
  aspectRatio: 'auto',
  freeRatio: {
    width: '4',
    height: '3',
  },
  objectFit: 'fill',
  float: 'none',
  ts: ts,
  ...(prop || undefined),
});
