{"name": "@nextgen-bindup/backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "main": "main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:create": "npx typeorm-ts-node-esm migration:create src/migrations/new-migraton"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.2", "@nestjs/typeorm": "^10.0.2", "@nextgen-bindup/common": "1.0.0", "@supabase/supabase-js": "^2.45.4", "@types/adm-zip": "^0.5.7", "@types/jsftp": "^2.1.5", "@xmldom/xmldom": "^0.9.8", "adm-zip": "^0.5.16", "aws-sdk": "^2.1692.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "csv-parser": "^3.2.0", "csv-stringify": "^6.5.2", "form-data": "^4.0.1", "jose": "^5.10.0", "jsftp": "^2.1.3", "module": "^1.2.5", "pg": "^8.13.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "stripe": "^18.0.0", "typeorm": "^0.3.20", "unescape": "^1.0.1", "unescape-js": "^1.1.4", "winston": "^3.16.0", "xml2json": "^0.12.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/pg": "^8.11.10", "@types/supertest": "^6.0.0", "@types/unescape-js": "^1.0.3", "@types/xml2json": "^0.11.6", "jest": "^29.5.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}