import { createTheme } from '@mui/material/styles';

export default createTheme({
  colorSchemes: {
    light: {
      palette: {
        primary: {
          main: '#616161',
          dark: '#424242',
          light: '#9E9E9E',
        },
      },
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: `
        html,body,#root {
          height: 100%;
        }
        * {
          box-sizing: border-box;
        }
      `,
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          '&.MuiButton-sizeLarge.MuiButton-outlinedPrimary': {
            color: 'var(--mui-palette-primary-main)',
            fontFeatureSettings: "'liga' off, 'clig' off",
            fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
            fontSize: '0.9375rem',
            fontStyle: 'normal',
            fontWeight: '500',
            lineHeight: '26px /* 173.333% */',
            letterSpacing: '0.46px',
            textTransform: 'uppercase',
          },
          '&.MuiButton-sizeLarge.MuiButton-textPrimary': {
            color: 'var(--mui-palette-primary-main)',
            fontFeatureSettings: "'liga' off, 'clig' off",
            fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
            fontSize: '0.9375rem',
            fontStyle: 'normal',
            fontWeight: '500',
            lineHeight: '26px /* 173.333% */',
            letterSpacing: '0.46px',
            textTransform: 'uppercase',
          },
        },
      },
    },
  },
});
