import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdatePageTable1740982117023 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}pages`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const column: TableColumn = new TableColumn({
      name: 'isDeleted',
      type: 'boolean',
      default: false,
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, column);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'isDeleted');
  }
}
