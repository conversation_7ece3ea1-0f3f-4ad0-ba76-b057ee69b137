"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateContentTable1743388643393 = void 0;
const typeorm_1 = require("typeorm");
class UpdateContentTable1743388643393 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}contents`;
    }
    async up(queryRunner) {
        const column = new typeorm_1.TableColumn({
            name: 'isDeleted',
            type: 'boolean',
            default: false,
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, column);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'isDeleted');
    }
}
exports.UpdateContentTable1743388643393 = UpdateContentTable1743388643393;
//# sourceMappingURL=1743388643393-update-content-table.js.map