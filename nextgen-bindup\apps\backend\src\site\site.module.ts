import { Module } from '@nestjs/common';
import { SiteController } from './site.controller';
import { SiteService } from './site.service';
import { SiteEntity } from './entities/site.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProjectModule } from 'src/project/project.module';
import { CmsCollectionModule } from 'src/cms-collection/cms-collection.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SiteEntity]),
    ProjectModule,
    CmsCollectionModule,
  ],
  controllers: [SiteController],
  providers: [SiteService],
  exports: [SiteService],
})
export class SiteModule {}
