import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { CmsCollectionItemsService } from './cms-collection-items.service';
import { CmsCollectionItemEntity } from './entities/cms-collection-items.entity';
import { PaginationDto } from './dto/pagination.dto';

@Controller('cms-collection-items')
@UseGuards(AuthGuard)
export class CmsCollectionItemsController {
  constructor(
    private readonly collectionItemsService: CmsCollectionItemsService,
  ) {}

  @Post('create')
  async create(@Body() collectionItemEntity: CmsCollectionItemEntity) {
    return await this.collectionItemsService.create(collectionItemEntity);
  }

  @Put('update/:id')
  async update(
    @Param('id') id: string,
    @Body() data: Partial<CmsCollectionItemEntity>,
  ) {
    return await this.collectionItemsService.update(+id, data);
  }

  @Get('one/:id')
  async getById(@Param('id') id: string) {
    return await this.collectionItemsService.findById(+id);
  }

  @Get('collection/:cmsCollectionId')
  async getByCollectionId(
    @Param('cmsCollectionId') cmsCollectionId: string,
    @Query() dto: PaginationDto,
  ) {
    return await this.collectionItemsService.findByCollectionId(
      +cmsCollectionId,
      dto,
    );
  }

  @Get('site/:siteId')
  async getBySiteId(@Param('siteId') siteId: string) {
    return await this.collectionItemsService.findBySiteId(+siteId);
  }

  @Delete(':id')
  async delete(@Param('id') id: string) {
    return await this.collectionItemsService.delete(+id);
  }
}
