"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderItemTable1747792372964 = void 0;
const typeorm_1 = require("typeorm");
class UpdateOrderItemTable1747792372964 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}order_items`;
        this.TABLE_NAME_PRODUCT = `${process.env.ENTITY_PREFIX || ''}products`;
        this.FOREIGN_KEY_NAME = 'FK_PRODUCT_ID_ON_ORDER_ITEMS';
    }
    async up(queryRunner) {
        await queryRunner.createForeignKey(this.TABLE_NAME, new typeorm_1.TableForeignKey({
            columnNames: ['productId'],
            referencedColumnNames: ['id'],
            referencedTableName: this.TABLE_NAME_PRODUCT,
            onDelete: 'SET NULL',
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropForeignKey(this.TABLE_NAME, this.FOREIGN_KEY_NAME);
    }
}
exports.UpdateOrderItemTable1747792372964 = UpdateOrderItemTable1747792372964;
//# sourceMappingURL=1747792372964-update-order-item-table.js.map