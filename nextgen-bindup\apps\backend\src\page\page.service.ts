import { Injectable } from '@nestjs/common';
import { PageEntity } from './entities/page.entity';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Not, Repository } from 'typeorm';
import { AppException } from 'src/common/exceptions/app.exception';
import { CreateBlogRequest } from '@nextgen-bindup/common/dto/blog';
import { Component } from '@nextgen-bindup/common/dto/component';
import { PageStatus, PageType } from './types/page.type';
import { ConvertToSlug, NEW_TS } from 'src/utils/common.util';
import { ComponentType } from './types/component.type';
import { PROP_ACTION_DEFAULT_VALUE } from './utils/prop-action-default-value';
import { PROP_SPACING_DEFAULT_VALUE } from './utils/prop-spacing-default-value';
import { PROP_PAGE_SIZE_DEFAULT_VALUE } from './utils/prop-size-default-value';
import { PROP_BORDER_DEFAULT_VALUE } from './utils/prop-border-default-value';
import { PROP_POSITION_DEFAULT_VALUE } from './utils/prop-position-default-value';
import { PROP_BACKGROUND_LIST_DEFAULT_VALUE } from './utils/prop-background-default-value';
import { PROP_EFFECT_LIST_DEFAULT_VALUE } from './utils/prop-effect-default-value';
import { PROP_FILTER_DEFAULT_VALUE } from './utils/prop-filter-default-value';
import { BLOG_LIST_DEFAULT_COMPONENT_VALUE } from './utils/blog-list-default-value';
import { BLOG_DETAIL_DEFAULT_COMPONENT_VALUE } from './utils/blog-detail-default-value';
import { CmsCollectionService } from 'src/cms-collection/cms-collection.service';
import { CmsDataType } from '@nextgen-bindup/common/dto/cms-collection/cms-collection-struct.dto';

@Injectable()
export class PageService {
  @InjectRepository(PageEntity)
  readonly pageRepo: Repository<PageEntity>;

  constructor(
    @InjectDataSource() private dataSource: DataSource,
    private cmsCollectionService: CmsCollectionService,
  ) {}

  // === PAGE =============================================
  async getPagesByProjectId(projectId: number = 1): Promise<PageEntity[]> {
    return await this.pageRepo.find({
      where: {
        projectId: projectId,
        isDeleted: false,
      },
    });
  }

  async getPagesBySiteId(siteId: number): Promise<PageEntity[]> {
    return await this.pageRepo.find({
      where: {
        siteId: siteId,
        isDeleted: false,
      },
    });
  }

  async getBySiteIdForRender(siteId: number): Promise<PageEntity[]> {
    return await this.pageRepo.find({
      select: {
        id: true,
        parentId: true,
        type: true,
        datasource: true,
        name: true,
        status: true,
        url: true,
        isPrivate: true,
        isHome: true,
        isDeleted: true,
      },
      where: {
        siteId: siteId,
        type: Not(PageType.ROOT),
      },
    });
  }

  async getRootPageBySiteId(siteId: number): Promise<PageEntity> {
    return await this.pageRepo.findOneBy({
      siteId: siteId,
      type: PageType.ROOT,
      isDeleted: false,
    });
  }

  async getById(pageId: number): Promise<PageEntity> {
    return await this.pageRepo.findOneBy({
      id: pageId,
    });
  }

  async createPage(
    projectId: number,
    pageData: PageEntity,
  ): Promise<PageEntity> {
    const page = new PageEntity();
    page.type = pageData.type;
    page.parentId = pageData.parentId;
    page.projectId = projectId;
    page.siteId = pageData.siteId;
    page.datasource = pageData.datasource;
    page.name = pageData.name;
    page.components = pageData.components;
    page.ts = pageData.ts;
    page.status = pageData.status;
    page.url = pageData.url;
    page.title = pageData.title;
    page.description = pageData.description;
    page.isSearch = pageData.isSearch;
    page.thumb = pageData.thumb;
    page.headCode = pageData.headCode;
    page.bodyCode = pageData.bodyCode;
    page.isPrivate = pageData.isPrivate;
    page.isHome = pageData.isHome;
    page.children = pageData.children;
    page.userId = pageData.userId;
    page.isDeleted = false;

    return await this.pageRepo.save(page);
  }

  async updatePage(
    pageId: number,
    pageData: Partial<PageEntity>,
  ): Promise<PageEntity> {
    const page: PageEntity = await this.pageRepo.findOneBy({
      id: pageId,
    });

    if (!page) throw new AppException('page.error.not_found');

    const data: Partial<PageEntity> = {
      siteId: pageData.siteId,
      datasource: pageData.datasource,
      name: pageData.name,
      status: pageData.status,
      url: pageData.url,
      title: pageData.title,
      description: pageData.description,
      isSearch: pageData.isSearch,
      thumb: pageData.thumb,
      headCode: pageData.headCode,
      bodyCode: pageData.bodyCode,
      isPrivate: pageData.isPrivate,
      isHome: pageData.isHome,
    };

    await this.pageRepo.update(page.id, data);

    return { ...page, ...data };
  }

  async updateComponent(
    pageId: number,
    components: Record<string, Component>,
  ): Promise<PageEntity> {
    const page: PageEntity = await this.pageRepo.findOneBy({
      id: pageId,
    });

    if (!page) throw new AppException('page.error.not_found');

    const data: Partial<PageEntity> = {
      components: components,
    };

    await this.pageRepo.update(page.id, data);

    return { ...page, ...data };
  }

  async deletePage(pageId: number): Promise<boolean> {
    const page: PageEntity = await this.pageRepo.findOneBy({
      id: pageId,
    });
    if (!page) throw new AppException('page.error.not_found');

    await this.pageRepo.delete(page.id);
    return true;
  }

  // === BLOG =============================================
  async createBlog(
    projectId: number = 1,
    data: CreateBlogRequest,
  ): Promise<boolean> {
    if (!projectId) throw new AppException('error.project_id_is_required');
    if (!data.siteId) throw new AppException('error.site_id_required');
    if (!data.userId) throw new AppException('error.user_id_is_required');
    if (!data.name || !data.name.trim())
      throw new AppException('page.error.name_is_required');
    if (!data.collectionId) throw new AppException('error.collection_required');

    const collection = await this.cmsCollectionService.findById(
      data.collectionId,
    );
    if (!collection) throw new AppException('cms.collection.error.not_found');
    let categoryCollectionId: number = 0;
    if (collection.dataType === CmsDataType.ARTICLE) {
      const categoryCollection = await this.cmsCollectionService.findByDataType(
        data.siteId,
        CmsDataType.CATEGORY,
      );
      if (categoryCollection) categoryCollectionId = categoryCollection.id;
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const ts = NEW_TS();
    const components: Record<string, Component> = {
      __page__: {
        id: '__page__',
        type: ComponentType.Page,
        name: 'Page',
        parentId: undefined,
        properties: {
          ts: ts,
          actions: PROP_ACTION_DEFAULT_VALUE(ts),
          marginPadding: PROP_SPACING_DEFAULT_VALUE(ts),
          size: PROP_PAGE_SIZE_DEFAULT_VALUE(ts),
          border: PROP_BORDER_DEFAULT_VALUE(ts),
          position: PROP_POSITION_DEFAULT_VALUE(ts),
          backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts),
          effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
          filter: PROP_FILTER_DEFAULT_VALUE(ts),
        },
        children: ['__main__'],
        breakpoint: {
          tablet: { ts: ts },
          phone: { ts: ts },
        },
        ts: ts,
      },
      __main__: {
        id: '__main__',
        type: ComponentType.Main,
        name: 'Main',
        parentId: '__page__',
        properties: {
          ts: ts,
          actions: PROP_ACTION_DEFAULT_VALUE(ts),
          marginPadding: PROP_SPACING_DEFAULT_VALUE(ts),
          size: PROP_PAGE_SIZE_DEFAULT_VALUE(ts, {
            height: { value: '', unit: 'auto' },
            minHeight: { value: '100', unit: '%' },
          }),
          border: PROP_BORDER_DEFAULT_VALUE(ts),
          position: PROP_POSITION_DEFAULT_VALUE(ts),
          backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts),
          effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
          filter: PROP_FILTER_DEFAULT_VALUE(ts),
        },
        children: [],
        breakpoint: {
          tablet: { ts: ts },
          phone: { ts: ts },
        },
        ts: ts,
      },
    };

    try {
      // find root page
      const rootPage: PageEntity = await this.getRootPageBySiteId(data.siteId);
      if (!rootPage) throw new AppException('page.error.root_page_not_found');

      const slug: string = ConvertToSlug(data.name);

      // create blog directory
      let directory = new PageEntity();
      directory.type = PageType.BLOG;
      directory.parentId = rootPage.id;
      directory.projectId = projectId;
      directory.siteId = data.siteId;
      directory.name = data.name;
      directory.components = {};
      directory.ts = ts;
      directory.status = PageStatus.PUBLISHED;
      directory.url = slug;
      directory.title = '';
      directory.description = '';
      directory.isSearch = false;
      directory.thumb = '';
      directory.headCode = '';
      directory.bodyCode = '';
      directory.isPrivate = false;
      directory.isHome = false;
      directory.children = [];
      directory.userId = data.userId;
      directory.isDeleted = false;
      directory = await this.pageRepo.save(directory);

      //-------------------------------------------
      // create blog list
      let blogList = new PageEntity();
      blogList.type = PageType.BLOG_LIST;
      blogList.parentId = directory.id;
      blogList.projectId = projectId;
      blogList.siteId = data.siteId;
      blogList.datasource = null;
      blogList.name = `${data.name} List`;
      blogList.components =
        collection.dataType === CmsDataType.ARTICLE
          ? BLOG_LIST_DEFAULT_COMPONENT_VALUE(ts, data.collectionId)
          : components;
      blogList.ts = ts;
      blogList.status = PageStatus.DRAFT;
      blogList.url = '';
      blogList.title = `${data.name} List`;
      blogList.description = '';
      blogList.isSearch = false;
      blogList.thumb = '';
      blogList.headCode = '';
      blogList.bodyCode = '';
      blogList.isPrivate = false;
      blogList.isHome = true;
      blogList.children = [];
      blogList.userId = data.userId;
      blogList.isDeleted = false;
      blogList = await this.pageRepo.save(blogList);

      // create blog detail
      let blogDetail = new PageEntity();
      blogDetail.type = PageType.BLOG_DETAIL;
      blogDetail.parentId = directory.id;
      blogDetail.projectId = projectId;
      blogDetail.siteId = data.siteId;
      blogDetail.datasource = {
        collectionId: data.collectionId,
        type: 'list',
        sorts: [],
        filters: {
          match: 'and',
          conditions: [],
        },
        ts: 0,
      };
      blogDetail.name = `${data.name} Detail`;
      blogDetail.components =
        collection.dataType === CmsDataType.ARTICLE
          ? BLOG_DETAIL_DEFAULT_COMPONENT_VALUE(
              ts,
              data.collectionId,
              categoryCollectionId,
            )
          : components;
      blogDetail.ts = ts;
      blogDetail.status = PageStatus.DRAFT;
      blogDetail.url = `${slug}/:slug`;
      blogDetail.title = `${data.name} Detail`;
      blogDetail.description = '';
      blogDetail.isSearch = false;
      blogDetail.thumb = '';
      blogDetail.headCode = '';
      blogDetail.bodyCode = '';
      blogDetail.isPrivate = false;
      blogDetail.isHome = false;
      blogDetail.children = [];
      blogDetail.userId = data.userId;
      blogDetail.isDeleted = false;
      blogDetail = await this.pageRepo.save(blogDetail);

      // update directory's children
      directory.children = [blogList.id, blogDetail.id];
      await this.pageRepo.update(directory.id, {
        children: directory.children,
      });

      //-------------------------------------------
      return true;
    } catch (e) {
      console.log(e);
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }
}
