import { useCallback, useEffect, useState, type FC } from 'react';
import { useTranslation } from 'react-i18next';
import SearchIcon from '@mui/icons-material/Search';
import { debounce } from '@mui/material';
import Box from '@mui/material/Box';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import AppBindCart from '../../assets/app_bindcart.png';
import AppMaterials from '../../assets/app_materials.png';
import AppSmoothBooking from '../../assets/app_smoothbooking.png';
import AppSmoothContact from '../../assets/app_smoothcontact.png';
import Thumbs from './Thumbs';

export interface CoServiceDto {
  type: 'smooth_contact' | 'smooth_booking' | 'bind_cart' | 'materials';
  name: string;
  image: string;
  url?: string;
}
const LIST: CoServiceDto[] = [
  {
    type: 'smooth_contact',
    name: '<PERSON>mooth<PERSON><PERSON><PERSON>',
    image: AppSmoothContact,
  },
  {
    type: 'smooth_booking',
    name: '<PERSON>mo<PERSON><PERSON><PERSON>ing',
    image: AppSmoothBooking,
  },
  {
    type: 'bind_cart',
    name: 'BiND Cart',
    image: AppBindCart,
  },
  {
    type: 'materials',
    name: 'Materials',
    image: AppMaterials,
    url: 'https://materials.digitalstage.jp',
  },
];

const Services: FC = () => {
  const { t } = useTranslation();
  const [list, setList] = useState<CoServiceDto[]>(LIST);
  const [search, setSearch] = useState<string>('');

  const filter = (search: string) => {
    if (!search) {
      setList(LIST);
      return;
    }

    const text: string = search.toLowerCase();
    const result = LIST.filter(member =>
      (member.name || '').toLowerCase().includes(text),
    );
    setList(result);
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceFilter = useCallback(
    debounce(text => filter(text), 500),
    [],
  );

  useEffect(() => {
    debounceFilter(search);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search]);

  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="h4">{t('services.title')}</Typography>
        <Box sx={{ display: 'inherit', gap: 2 }}>
          <TextField
            size="small"
            placeholder={t('common.search')}
            value={search}
            onChange={e => setSearch(e.target.value)}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              },
            }}
          />
        </Box>
      </Box>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
        {list.map(n => (
          <Thumbs
            key={n.type}
            image={n.image}
            content={n.name}
            onClick={() => {
              if (n.url) {
                window.open(n.url, '_blank');
              }
            }}
          />
        ))}
      </Box>
    </>
  );
};

export default Services;
