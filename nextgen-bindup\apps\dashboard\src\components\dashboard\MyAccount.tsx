import { useEffect, useState, type FC } from 'react';
import { useTranslation } from 'react-i18next';
import { CircularProgress } from '@mui/material';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { getErrMsg } from '@nextgen-bindup/common/utility';
import { useAuth } from '../../auth';
import {
  CountryCode,
  UserInfoEntity,
  UserType,
} from '../../dto/user-info.type';
import { UserDto } from '../../dto/user.dto';
import { userInfoService } from '../../services/user-info.service';
import { userService } from '../../services/user.service';
import { ChangePwd } from './my-account/ChangePwd';
import { PersonalInformation } from './my-account/PersonalInformation';
import { SubscribeNews } from './my-account/SubscribeNews';

const MyAccount: FC = () => {
  const { t } = useTranslation();
  const { session } = useAuth();
  const [errorMsg, setErrorMsg] = useState<string>('');
  const [user, setUser] = useState<UserDto | null>(null);
  const [userInfo, setUserInfo] = useState<UserInfoEntity | null>(null);

  useEffect(() => {
    getData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getData = async () => {
    if (!session) return;

    try {
      const loginUser: UserDto = await userService.findByUserId();
      setUser(loginUser);

      if (!loginUser) throw new Error('user_not_found');

      let loginUserInfo: UserInfoEntity | null =
        await userInfoService.findByUserId();

      if (!loginUserInfo) {
        loginUserInfo = {
          userId: loginUser.userid,
          name: loginUser.name,
          type: UserType.INDIVIDUAL,
          countryCode: CountryCode.JAPAN,
          prefecture: 0,
        };
      }

      if (!loginUserInfo.type) loginUserInfo.type = UserType.INDIVIDUAL;
      if (!loginUserInfo.countryCode)
        loginUserInfo.countryCode = CountryCode.JAPAN;

      setUserInfo(loginUserInfo);
    } catch (e) {
      setErrorMsg(t(getErrMsg(e)));
    }
  };

  return (
    <Box sx={{ paddingBottom: '3rem' }}>
      <Box sx={{ marginBottom: '1rem' }}>
        <Typography variant="h4">ACCOUNT INFORMATION</Typography>
        <Typography variant="body1">
          If you wish to make any changes, please fill in the form and click the
          "Save" button.
        </Typography>
        <Typography variant="body1" color="error">
          * indicates required fields.
        </Typography>
      </Box>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
        {errorMsg && <Typography color="error">{t(errorMsg)}</Typography>}
        {!user || !userInfo ? (
          !errorMsg && <CircularProgress />
        ) : (
          <Box sx={{ width: '100%' }}>
            <PersonalInformation
              user={user}
              userInfoData={userInfo}
              onUpdate={() => {}}
            />

            <ChangePwd onUpdate={() => {}} />

            <SubscribeNews userInfoData={userInfo} onUpdate={() => {}} />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default MyAccount;
