import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { Express } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileService } from './file.service';
import {
  DeleteImageResponseDto,
  ListImagesResponseDto,
  // MetadataDto,
  // PresignedUrlResponseDto,
  UploadFileDto,
} from './dto/file.dto';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { ensureFolderExists } from 'src/utils/file.util';
import { R2StorageService } from './r2-storage.service';

@Controller('file')
export class FileController {
  constructor(
    private readonly fileService: FileService,
    private readonly r2StorageService: R2StorageService,
  ) {}

  @Post()
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: (req, file, cb) => {
          ensureFolderExists('./upload');
          cb(null, './upload');
        },
        filename: (req, file, callback) => {
          const uniqueSuffix =
            Date.now() + '-' + Math.round(Math.random() * 1e9);
          callback(null, `${uniqueSuffix}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, callback) => {
        const validMimeTypes = [
          'image/jpeg',
          'image/png',
          'image/webp',
          'image/gif',
          'image/svg+xml',
          'video/mp4',
        ];

        if (!validMimeTypes.includes(file.mimetype)) {
          return callback(
            new BadRequestException(
              'Invalid file type. Please upload an image with a valid format.',
            ),
            false,
          );
        }

        callback(null, true);
      },
    }),
  )
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadFileDto: UploadFileDto,
  ) {
    if (file.mimetype === 'video/mp4') {
      return this.fileService.uploadVideo(file, uploadFileDto.metadata);
    }
    return this.fileService.uploadImage(file, uploadFileDto.metadata);
  }

  @Get('images')
  async getImages(
    @Query('page') page: number = 1,
    @Query('perPage') perPage: number = 10,
    @Query('order') order: string = 'uploaded',
  ): Promise<ListImagesResponseDto> {
    const result = await this.fileService.getImages(page, perPage, order);
    return result;
  }

  @Delete(':id')
  async deleteImage(@Param('id') id: string): Promise<DeleteImageResponseDto> {
    const result = await this.fileService.deleteImage(id);
    return result as DeleteImageResponseDto;
  }

  @Get('generate-presigned-url-private')
  async generatePresignedUrlPrivate(
    @Query('fileName') fileName: string,
  ): Promise<{ url: string }> {
    const presignedUrl =
      await this.r2StorageService.generatePresignedUrlForUploadPrivate(
        fileName,
      );
    return { url: presignedUrl };
  }

  @Get('generate-presigned-url')
  async generatePresignedUrl(
    @Query('bucket') bucket: string,
    @Query('fileName') fileName: string,
  ): Promise<{ url: string }> {
    const presignedUrl =
      await this.r2StorageService.generatePresignedUrl(fileName);
    return { url: presignedUrl };
  }
}
