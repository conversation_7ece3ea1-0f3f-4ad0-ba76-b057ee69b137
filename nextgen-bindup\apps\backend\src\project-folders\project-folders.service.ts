import { Injectable } from '@nestjs/common';
import { ProjectFolderEntity } from './entities/project-folders.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class ProjectFoldersService {
  @InjectRepository(ProjectFolderEntity)
  readonly projectRepo: Repository<ProjectFolderEntity>;

  async create(data: ProjectFolderEntity): Promise<ProjectFolderEntity> {
    const now = new Date();
    data.id = undefined;
    data.createdAt = now;
    data.updatedAt = now;
    return await this.projectRepo.save(data);
  }

  async findByProjectId(projectId: number): Promise<ProjectFolderEntity[]> {
    return await this.projectRepo.find({ where: { projectId } });
  }
}
