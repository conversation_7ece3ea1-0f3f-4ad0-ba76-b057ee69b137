import { ProductType, ProductVariantType } from '../enum/product.enum';
import { ProductVariant } from '../dto/product-variant.dto';
export declare class ProductEntity {
    id: number;
    siteId: number;
    isOrderable: boolean;
    code: string;
    name: string;
    title?: string;
    description?: string;
    images: string[];
    priceLabel: string;
    saleLabel: string;
    price: number;
    sale: number;
    purchaseLimitQuantity: number;
    individualShippingCharges: number;
    fileDownload?: FileDownload;
    unlimitedPurchase: boolean;
    productType: ProductType;
    productVariantType: ProductVariantType;
    variants: ProductVariant;
    createdAt: Date;
    updatedAt: Date;
    isDeleted: boolean;
}
export interface FileDownload {
    name: string;
    url: string;
    size: number;
    type: string;
}
