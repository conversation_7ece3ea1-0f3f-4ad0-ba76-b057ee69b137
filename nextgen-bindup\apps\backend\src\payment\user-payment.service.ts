import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { UserInfoEntity } from 'src/user-info/entities/user-info.entity';
import Stripe from 'stripe';
import { Repository } from 'typeorm';
import { OrderService } from 'src/order/order.service';
import { CreateOrderDto } from 'src/order/dto/create-order.dto';
import { OrderStatus } from 'src/order/enum/order.enum';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EventType } from 'src/common/event-type.enum';
import { PaymentMethodType } from 'src/order/enum/payment-method-type.enum';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { ShopInformationSettingService } from 'src/shop-information-settings/shop-information-settings.service';
import { OrderCompletionSettingService } from 'src/order-complete-settings/order-complete-settings.service';
import { OrderEntity } from 'src/order/entites/order.entity';

@Injectable()
export class UserPaymentService {
  private stripe: Stripe;
  @InjectRepository(UserInfoEntity)
  private readonly userInfoRepo: Repository<UserInfoEntity>;

  constructor(
    private readonly configService: ConfigService,
    private readonly orderService: OrderService,
    private readonly paymentMethodService: PaymentMethodService,
    private readonly shopInformationService: ShopInformationSettingService,
    private readonly orderCompletionSettingService: OrderCompletionSettingService,
    private eventEmitter: EventEmitter2,
  ) {
    if (this.configService.get('STRIPE_SECRET_KEY')) {
      this.stripe = new Stripe(this.configService.get('STRIPE_SECRET_KEY'), {
        apiVersion: '2025-05-28.basil',
      });
    }
  }

  private async getOrderEmailContext(order: OrderEntity) {
    const [paymentMethod, shopInfo, orderCompletionSetting] = await Promise.all(
      [
        this.paymentMethodService.getPaymentMethodBySiteId(order.siteId),
        this.shopInformationService.findOneBySiteId(order.siteId),
        this.orderCompletionSettingService.findOneBySiteId(order.siteId),
      ],
    );

    return {
      order,
      paymentMethod,
      shopInfo,
      orderCompletionSetting,
    };
  }

  async enableStripe(userId: string): Promise<{ url: string }> {
    const user = await this.userInfoRepo.findOne({ where: { userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    let stripeAccountId = user.stripeAccountId;
    if (!stripeAccountId) {
      const account = await this.stripe.accounts.create({
        type: 'standard',
        country: 'JP',
        capabilities: {
          transfers: { requested: true },
          card_payments: { requested: true },
        },
        business_type: 'individual',
        metadata: {
          userId: user.userId,
        },
      });

      stripeAccountId = account.id;
      await this.userInfoRepo.update(user.userId, { stripeAccountId });
    }
    const accountLink = await this.stripe.accountLinks.create({
      account: stripeAccountId,
      refresh_url: `http://localhost:5173/cart-management/shop-setting`,
      return_url: `http://localhost:5173/cart-management/shop-setting`,
      type: 'account_onboarding',
    });
    return { url: accountLink.url };
  }

  async checkStripeStatus(userId: string): Promise<{
    connected: boolean;
    id?: string;
    email?: string;
    name?: string;
    type?: string;
    stripeDashboardUrl?: string;
  }> {
    const user = await this.userInfoRepo.findOne({ where: { userId } });
    if (!user || !user.stripeAccountId) return { connected: false };
    const account = await this.stripe.accounts.retrieve(user.stripeAccountId);
    return {
      connected: !!account.details_submitted,
      email: account.email,
      type: account.type,
      id: account.id,
      stripeDashboardUrl: `https://dashboard.stripe.com/${account.id}`,
    };
  }

  async handleEventCheckoutSessionCompleted(
    metadata: Stripe.MetadataParam,
  ): Promise<void> {
    const orderId = metadata.orderId;

    if (!orderId) {
      throw new NotFoundException(
        'Order ID or User ID not found in session metadata',
      );
    }
    const order = await this.orderService.findOrderById(+orderId);
    await this.orderService.updateOrder(order.id, {
      orderStatus: OrderStatus.PAID,
    });
    order.orderStatus = OrderStatus.PAID;

    const context = await this.getOrderEmailContext(order);
    this.eventEmitter.emit(EventType.ORDER_CREATED, context);
  }

  async checkoutWithCreaditCard(
    domain: string,
    createOrderDto: CreateOrderDto,
  ): Promise<{ url: string }> {
    try {
      const paymentMethodSettings =
        await this.paymentMethodService.getPaymentMethodBySiteId(
          +createOrderDto.siteId,
        );
      if (!paymentMethodSettings.stripePaymentGateway.isEnabled) {
        throw new NotFoundException(
          'Stripe payment gateway is not enabled for this site',
        );
      }
      const stripeAccountId =
        paymentMethodSettings.stripePaymentGateway.stripeAccountId;
      if (!stripeAccountId) {
        throw new NotFoundException('Stripe account not found for user/shop');
      }

      const order = await this.orderService.createOrder(
        createOrderDto,
        PaymentMethodType.CREDIT_CARD,
        OrderStatus.WAITING_PAYMENT,
      );

      const lineItems: Stripe.Checkout.SessionCreateParams.LineItem[] =
        order.orderItems.map(item => ({
          price_data: {
            currency: 'jpy',
            product_data: {
              name: item.productName,
              images: [item.image],
            },
            unit_amount: item.displayPrice,
          },
          quantity: item.quantity,
        }));

      if (order.shippingFee > 0) {
        lineItems.push({
          price_data: {
            currency: 'jpy',
            product_data: {
              name: 'Shipping Fee',
            },
            unit_amount: order.shippingFee,
          },
          quantity: 1,
        });
      }

      const session = await this.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: lineItems,
        invoice_creation: {
          enabled: true,
          invoice_data: {
            metadata: {
              type: 'order',
              orderId: order.id.toString(),
              siteId: order.siteId.toString(),
              platformFee: order.platformFee.toString(),
              paymentGatewayFee: order.paymentGatewayFee.toString(),
              subtotal: order.subtotal.toString(),
              shippingFee: order.shippingFee.toString(),
              total: order.total.toString(),
              shopNetPayout: order.shopNetPayout.toString(),
            },
          },
        },
        payment_intent_data: {
          receipt_email: order.email,
          application_fee_amount:
            Number(order.platformFee) + Number(order.paymentGatewayFee),
          transfer_data: {
            destination: stripeAccountId,
          },
          on_behalf_of: stripeAccountId,
        },
        metadata: {
          type: 'order',
          orderId: order.id.toString(),
          siteId: order.siteId.toString(),
          platformFee: order.platformFee.toString(),
          paymentGatewayFee: order.paymentGatewayFee.toString(),
          subtotal: order.subtotal.toString(),
          shippingFee: order.shippingFee.toString(),
          total: order.total.toString(),
          shopNetPayout: order.shopNetPayout.toString(),
        },
        mode: 'payment',
        success_url: `${domain}/order-complete?orderId=${order.id.toString()}`,
        cancel_url: `${domain}/checkout`,
      });

      await this.orderService.updateOrder(order.id, {
        checkoutSessionId: session.id,
        checkoutSessionUrl: session.url,
      });

      return { url: session.url };
    } catch (err) {
      console.error('Error creating checkout session:', err);
      throw err;
    }
  }

  async checkoutWithBankTransfer(createOrderDto: CreateOrderDto) {
    const order = await this.orderService.createOrder(
      createOrderDto,
      PaymentMethodType.BANK_TRANSFER,
      OrderStatus.PLACED,
    );

    const context = await this.getOrderEmailContext(order);
    this.eventEmitter.emit(EventType.ORDER_CREATED, context);

    return {
      orderId: order.id,
    };
  }

  async checkoutWithCashOnDelivery(createOrderDto: CreateOrderDto) {
    const order = await this.orderService.createOrder(
      createOrderDto,
      PaymentMethodType.CASH_ON_DELIVERY,
      OrderStatus.PLACED,
    );

    const context = await this.getOrderEmailContext(order);
    this.eventEmitter.emit(EventType.ORDER_CREATED, context);

    return {
      orderId: order.id,
    };
  }

  async checkoutWithPostalTransfer(createOrderDto: CreateOrderDto) {
    const order = await this.orderService.createOrder(
      createOrderDto,
      PaymentMethodType.POSTAL_TRANSFER,
      OrderStatus.PLACED,
    );

    const context = await this.getOrderEmailContext(order);
    this.eventEmitter.emit(EventType.ORDER_CREATED, context);

    return {
      orderId: order.id,
    };
  }

  async retryHandleWaitingPendingOrder() {
    if (!this.stripe) {
      console.error('Stripe is not configured');
      return;
    }
    const orders = await this.orderService.getWaitingPaymentOrdersInLast2Days();
    for (const order of orders) {
      try {
        const session = await this.stripe.checkout.sessions.retrieve(
          order.checkoutSessionId,
        );
        if (session.payment_status === 'paid') {
          await this.orderService.updateOrder(order.id, {
            orderStatus: OrderStatus.PAID,
          });

          const context = await this.getOrderEmailContext(order);
          this.eventEmitter.emit(EventType.ORDER_CREATED, context);
        } else {
          console.warn(
            `Order ${order.id} is still pending, payment status: ${session.payment_status}`,
          );
        }
      } catch (error) {
        console.error(`Error retrieving session for order ${order.id}:`, error);
      }
    }
  }
}
