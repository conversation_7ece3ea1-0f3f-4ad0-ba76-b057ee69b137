"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateMigrationModule = void 0;
const common_1 = require("@nestjs/common");
const template_migration_controller_1 = require("./template-migration.controller");
const template_migration_service_1 = require("./template-migration.service");
const page_module_1 = require("../page/page.module");
const template_entity_1 = require("./entities/template.entity");
const typeorm_1 = require("@nestjs/typeorm");
const template_page_entity_1 = require("./entities/template-page.entity");
let TemplateMigrationModule = class TemplateMigrationModule {
};
exports.TemplateMigrationModule = TemplateMigrationModule;
exports.TemplateMigrationModule = TemplateMigrationModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                template_entity_1.TemplateEntity,
                template_page_entity_1.TemplatePageEntity,
                template_page_entity_1.TemplatePageEntity,
            ]),
            page_module_1.PageModule,
        ],
        controllers: [template_migration_controller_1.TemplateMigrationController],
        providers: [template_migration_service_1.TemplateMigrationService],
        exports: [template_migration_service_1.TemplateMigrationService],
    })
], TemplateMigrationModule);
//# sourceMappingURL=template-migration.module.js.map