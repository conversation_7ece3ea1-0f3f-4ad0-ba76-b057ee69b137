"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateProjectFoldersTable1732001826302 = void 0;
const typeorm_1 = require("typeorm");
class UpdateProjectFoldersTable1732001826302 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}project_folders`;
    }
    async up(queryRunner) {
        await queryRunner.createForeignKey(this.TABLE_NAME, new typeorm_1.TableForeignKey({
            columnNames: ['projectId'],
            referencedColumnNames: ['id'],
            referencedTableName: 'projects',
            onDelete: 'CASCADE',
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropForeignKey(this.TABLE_NAME, 'FK_project_folders_projectId');
    }
}
exports.UpdateProjectFoldersTable1732001826302 = UpdateProjectFoldersTable1732001826302;
//# sourceMappingURL=1732001826302-update-project-folders-table.js.map