{"version": 3, "sources": ["../../../../../node_modules/@mui/material/NoSsr/NoSsr.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\n/**\n * NoSsr purposely removes components from the subject of Server Side Rendering (SSR).\n *\n * This component can be useful in a variety of situations:\n *\n * * Escape hatch for broken dependencies not supporting SSR.\n * * Improve the time-to-first paint on the client by only rendering above the fold.\n * * Reduce the rendering time on the server.\n * * Under too heavy server load, you can turn on service degradation.\n *\n * Demos:\n *\n * - [No SSR](https://v6.mui.com/material-ui/react-no-ssr/)\n *\n * API:\n *\n * - [NoSsr API](https://v6.mui.com/material-ui/api/no-ssr/)\n */\nfunction NoSsr(props) {\n  const {\n    children,\n    defer = false,\n    fallback = null\n  } = props;\n  const [mountedState, setMountedState] = React.useState(false);\n  useEnhancedEffect(() => {\n    if (!defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n  React.useEffect(() => {\n    if (defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n\n  // TODO casting won't be needed at one point https://github.com/DefinitelyTyped/DefinitelyTyped/pull/65135\n  return mountedState ? children : fallback;\n}\nprocess.env.NODE_ENV !== \"production\" ? NoSsr.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the component will not only prevent server-side rendering.\n   * It will also defer the rendering of the children into a different screen frame.\n   * @default false\n   */\n  defer: PropTypes.bool,\n  /**\n   * The fallback content to display.\n   * @default null\n   */\n  fallback: PropTypes.node\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  NoSsr['propTypes' + ''] = exactProp(NoSsr.propTypes);\n}\nexport default NoSsr;"], "mappings": ";;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;AAoBtB,SAAS,MAAM,OAAO;AACpB,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,IAAI;AACJ,QAAM,CAAC,cAAc,eAAe,IAAU,eAAS,KAAK;AAC5D,4BAAkB,MAAM;AACtB,QAAI,CAAC,OAAO;AACV,sBAAgB,IAAI;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,KAAK,CAAC;AACV,EAAM,gBAAU,MAAM;AACpB,QAAI,OAAO;AACT,sBAAgB,IAAI;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,KAAK,CAAC;AAGV,SAAO,eAAe,WAAW;AACnC;AACA,OAAwC,MAAM,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ/E,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,UAAU,kBAAAA,QAAU;AACtB,IAAI;AACJ,IAAI,MAAuC;AAEzC,QAAM,WAAgB,IAAI,UAAU,MAAM,SAAS;AACrD;AACA,IAAO,gBAAQ;", "names": ["PropTypes"]}