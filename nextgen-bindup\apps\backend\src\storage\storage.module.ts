import { Module } from '@nestjs/common';
import { StorageController } from './storage.controller';
import { StorageService } from './storage.service';
import { SupabaseModule } from 'src/supabase/supabase.module';
import { SupabaseStorageService } from 'src/supabase/supabase-storage.service';

@Module({
  imports: [SupabaseModule],
  controllers: [StorageController],
  providers: [StorageService, SupabaseStorageService],
})
export class StorageModule {}
