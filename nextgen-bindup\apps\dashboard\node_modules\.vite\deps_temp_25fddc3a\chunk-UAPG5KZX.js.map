{"version": 3, "sources": ["../../../../../node_modules/@mui/material/TableBody/TableBody.js", "../../../../../node_modules/@mui/material/TableBody/tableBodyClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Tablelvl2Context from \"../Table/Tablelvl2Context.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTableBodyUtilityClass } from \"./tableBodyClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getTableBodyUtilityClass, classes);\n};\nconst TableBodyRoot = styled('tbody', {\n  name: 'MuiTableBody',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  display: 'table-row-group'\n});\nconst tablelvl2 = {\n  variant: 'body'\n};\nconst defaultComponent = 'tbody';\nconst TableBody = /*#__PURE__*/React.forwardRef(function TableBody(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTableBody'\n  });\n  const {\n    className,\n    component = defaultComponent,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(Tablelvl2Context.Provider, {\n    value: tablelvl2,\n    children: /*#__PURE__*/_jsx(TableBodyRoot, {\n      className: clsx(classes.root, className),\n      as: component,\n      ref: ref,\n      role: component === defaultComponent ? null : 'rowgroup',\n      ownerState: ownerState,\n      ...other\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TableBody.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `TableRow`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TableBody;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableBodyUtilityClass(slot) {\n  return generateUtilityClass('MuiTableBody', slot);\n}\nconst tableBodyClasses = generateUtilityClasses('MuiTableBody', ['root']);\nexport default tableBodyClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,yBAAyB,MAAM;AAC7C,SAAO,qBAAqB,gBAAgB,IAAI;AAClD;AACA,IAAM,mBAAmB,uBAAuB,gBAAgB,CAAC,MAAM,CAAC;AACxE,IAAO,2BAAQ;;;ADIf,yBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,EACf;AACA,SAAO,eAAe,OAAO,0BAA0B,OAAO;AAChE;AACA,IAAM,gBAAgB,eAAO,SAAS;AAAA,EACpC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,SAAS;AACX,CAAC;AACD,IAAM,YAAY;AAAA,EAChB,SAAS;AACX;AACA,IAAM,mBAAmB;AACzB,IAAM,YAA+B,iBAAW,SAASA,WAAU,SAAS,KAAK;AAC/E,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,YAAY;AAAA,IACZ,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,yBAAiB,UAAU;AAAA,IAClD,OAAO;AAAA,IACP,cAAuB,mBAAAA,KAAK,eAAe;AAAA,MACzC,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,MACvC,IAAI;AAAA,MACJ;AAAA,MACA,MAAM,cAAc,mBAAmB,OAAO;AAAA,MAC9C;AAAA,MACA,GAAG;AAAA,IACL,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AACD,OAAwC,UAAU,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,oBAAQ;", "names": ["TableBody", "_jsx", "PropTypes"]}