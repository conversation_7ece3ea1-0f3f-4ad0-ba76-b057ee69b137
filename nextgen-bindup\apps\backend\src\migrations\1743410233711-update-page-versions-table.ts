import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdatePageVersionsTable1743410233711
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}page_versions`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const cmsCollectionIdColumn: TableColumn = new TableColumn({
      name: 'cmsCollectionId',
      type: 'integer',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, cmsCollectionIdColumn);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'cmsCollectionId');
  }
}
