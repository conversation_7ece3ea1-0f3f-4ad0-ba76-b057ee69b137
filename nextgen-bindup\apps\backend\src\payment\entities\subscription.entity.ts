import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SubscriptionStatus } from '../dto/payment.dto';

@Entity('subscriptions', { schema: process.env.DATABASE_SCHEMA })
export class SubscriptionEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'userId',
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  userId: string;

  @Column({
    name: 'planId',
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  planId: string;

  @Column({
    name: 'stripeSubscriptionId',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  stripeSubscriptionId: string;

  @Column({
    name: 'status',
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: SubscriptionStatus;

  @Column({
    name: 'currentPeriodStart',
    type: 'timestamptz',
    nullable: false,
  })
  currentPeriodStart: Date;

  @Column({
    name: 'currentPeriodEnd',
    type: 'timestamptz',
    nullable: false,
  })
  currentPeriodEnd: Date;

  @Column({
    name: 'cancelAt',
    type: 'timestamptz',
    nullable: true,
  })
  cancelAt: Date;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
