import { AssetEntity } from './entities/asset.entity';
import { Repository } from 'typeorm';
import { ProjectService } from 'src/project/project.service';
export declare class AssetService {
    private readonly projectService;
    readonly assetRepo: Repository<AssetEntity>;
    constructor(projectService: ProjectService);
    create(assetEntity: AssetEntity): Promise<AssetEntity>;
    update(id: number, assetData: Partial<AssetEntity>): Promise<AssetEntity>;
    findById(id: number): Promise<AssetEntity>;
    findByProjectId(projectId: number): Promise<AssetEntity[]>;
    delete(id: number): Promise<boolean>;
    findByUrl(url: string): Promise<AssetEntity>;
}
