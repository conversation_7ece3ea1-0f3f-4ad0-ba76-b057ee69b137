"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliveryReceiptSettingsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const delivery_receipt_settings_entity_1 = require("./entities/delivery-receipt-settings.entity");
const typeorm_2 = require("typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
const delivery_receipt_settings_util_1 = require("./delivery-receipt-settings.util");
let DeliveryReceiptSettingsService = class DeliveryReceiptSettingsService {
    constructor() { }
    async update(id, settingData) {
        const setting = await this.deliveryReceiptSettingRepo.findOneBy({ id: id });
        if (!setting) {
            throw new app_exception_1.AppException('api.error.delivery_receipt_setting_not_found');
        }
        delete settingData.id;
        delete settingData.siteId;
        delete settingData.createdAt;
        settingData.updatedAt = new Date();
        await this.deliveryReceiptSettingRepo.update(id, settingData);
        return { ...setting, ...settingData };
    }
    async findById(id) {
        return await this.deliveryReceiptSettingRepo.findOneBy({ id });
    }
    async findOneBySiteId(siteId) {
        let setting = await this.deliveryReceiptSettingRepo.findOneBy({ siteId });
        if (!setting) {
            setting = this.getDefaultSetting(siteId);
            setting = await this.deliveryReceiptSettingRepo.save(setting);
        }
        return setting;
    }
    getDefaultSetting(siteId) {
        const now = new Date();
        const setting = new delivery_receipt_settings_entity_1.DeliveryReceiptSettingEntity();
        const defaultDeliveryReceipt = (0, delivery_receipt_settings_util_1.getDefaultDeliveryReceiptSettings)('', '');
        setting.siteId = siteId;
        setting.header = defaultDeliveryReceipt.header;
        setting.footer = defaultDeliveryReceipt.footer;
        setting.createdAt = now;
        setting.updatedAt = now;
        return setting;
    }
    async delete(id) {
        const setting = await this.deliveryReceiptSettingRepo.findOneBy({ id });
        if (!setting) {
            throw new app_exception_1.AppException('api.error.delivery_receipt_setting_not_found');
        }
        await this.deliveryReceiptSettingRepo.delete(id);
        return true;
    }
};
exports.DeliveryReceiptSettingsService = DeliveryReceiptSettingsService;
__decorate([
    (0, typeorm_1.InjectRepository)(delivery_receipt_settings_entity_1.DeliveryReceiptSettingEntity),
    __metadata("design:type", typeorm_2.Repository)
], DeliveryReceiptSettingsService.prototype, "deliveryReceiptSettingRepo", void 0);
exports.DeliveryReceiptSettingsService = DeliveryReceiptSettingsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], DeliveryReceiptSettingsService);
//# sourceMappingURL=delivery-receipt-settings.service.js.map