"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsCollectionModule = void 0;
const common_1 = require("@nestjs/common");
const cms_collection_controller_1 = require("./cms-collection.controller");
const cms_collection_service_1 = require("./cms-collection.service");
const typeorm_1 = require("@nestjs/typeorm");
const cms_collection_entity_1 = require("./entities/cms-collection.entity");
let CmsCollectionModule = class CmsCollectionModule {
};
exports.CmsCollectionModule = CmsCollectionModule;
exports.CmsCollectionModule = CmsCollectionModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([cms_collection_entity_1.CmsCollectionEntity])],
        controllers: [cms_collection_controller_1.CmsCollectionController],
        providers: [cms_collection_service_1.CmsCollectionService],
        exports: [cms_collection_service_1.CmsCollectionService],
    })
], CmsCollectionModule);
//# sourceMappingURL=cms-collection.module.js.map