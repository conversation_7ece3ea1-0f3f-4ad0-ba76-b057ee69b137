"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderCompletionSettingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
const order_complete_settings_entity_1 = require("./entities/order-complete-settings.entity");
const site_service_1 = require("../site/site.service");
let OrderCompletionSettingService = class OrderCompletionSettingService {
    constructor(siteService) {
        this.siteService = siteService;
    }
    async create(orderCompletionSettingEntity) {
        const now = new Date();
        const setting = new order_complete_settings_entity_1.OrderCompletionSettingEntity();
        setting.siteId = orderCompletionSettingEntity.siteId;
        setting.displayText = orderCompletionSettingEntity.displayText;
        setting.emailSubject = orderCompletionSettingEntity.emailSubject;
        setting.emailHeader = orderCompletionSettingEntity.emailHeader;
        setting.emailFooter = orderCompletionSettingEntity.emailFooter;
        setting.createdAt = now;
        setting.updatedAt = now;
        return await this.orderCompletionSettingRepo.save(setting);
    }
    async update(id, settingData) {
        const setting = await this.orderCompletionSettingRepo.findOneBy({ id: id });
        if (!setting)
            throw new app_exception_1.AppException('api.error.order_completion_setting_not_found');
        delete settingData.id;
        delete settingData.siteId;
        delete settingData.createdAt;
        settingData.updatedAt = new Date();
        await this.orderCompletionSettingRepo.update(id, settingData);
        return { ...setting, ...settingData };
    }
    async findById(id) {
        return await this.orderCompletionSettingRepo.findOneBy({ id });
    }
    async findOneBySiteId(siteId) {
        return await this.orderCompletionSettingRepo.findOneBy({ siteId });
    }
    async delete(id) {
        const setting = await this.orderCompletionSettingRepo.findOneBy({ id });
        if (!setting)
            throw new app_exception_1.AppException('api.error.order_completion_setting_not_found');
        await this.orderCompletionSettingRepo.delete(id);
        return true;
    }
};
exports.OrderCompletionSettingService = OrderCompletionSettingService;
__decorate([
    (0, typeorm_1.InjectRepository)(order_complete_settings_entity_1.OrderCompletionSettingEntity),
    __metadata("design:type", typeorm_2.Repository)
], OrderCompletionSettingService.prototype, "orderCompletionSettingRepo", void 0);
exports.OrderCompletionSettingService = OrderCompletionSettingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [site_service_1.SiteService])
], OrderCompletionSettingService);
//# sourceMappingURL=order-complete-settings.service.js.map