import { MailerService } from '@nestjs-modules/mailer';
import { OrderEntity } from 'src/order/entites/order.entity';
import { PaymentMethodEntity } from 'src/payment-method/entities/payment-method.entity';
import { ShopInformationSettingEntity } from 'src/shop-information-settings/entities/shop-information-settings.entity';
import { OrderCompletionSettingEntity } from 'src/order-complete-settings/entities/order-complete-settings.entity';
export declare class MailController {
    private mailerService;
    constructor(mailerService: MailerService);
    handleOrderCreatedEvent(payload: {
        order: OrderEntity;
        paymentMethod: PaymentMethodEntity;
        shopInfo: ShopInformationSettingEntity;
        orderCompletionSetting: OrderCompletionSettingEntity;
    }): Promise<void>;
}
