import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { PaymentMethodService } from './payment-method.service';
import { PaymentMethodResponseDto } from './dto/payment-method-response.dto';
import { PaymentMethodEntity } from './entities/payment-method.entity';
import { AuthGuard } from 'src/auth/auth.guard';
@UseGuards(AuthGuard)
@Controller('payment-method')
export class PaymentMethodController {
  constructor(private readonly paymentMethodService: PaymentMethodService) {}

  @Get(':siteId')
  async getPaymentMethod(
    @Param('siteId') siteId: number,
  ): Promise<PaymentMethodResponseDto> {
    return await this.paymentMethodService.getPaymentMethodBySiteId(siteId);
  }

  @Post(':siteId')
  async updatePaymentMethod(
    @Param('siteId') siteId: number,
    @Body() paymentMethod: Partial<PaymentMethodEntity>,
  ): Promise<boolean> {
    return await this.paymentMethodService.updatePaymentMethod(
      siteId,
      paymentMethod,
    );
  }

  @Post('enable-stripe-payment-gateway/:siteId')
  async enableStripePaymentGateway(@Param('siteId') siteId: string) {
    const result =
      await this.paymentMethodService.enableStripePaymentGateway(+siteId);
    return {
      success: true,
      onboardingUrl: result.url,
    };
  }

  @Get('check-stripe-status/:siteId')
  async checkStripeStatus(@Param('siteId') siteId: string) {
    const result = await this.paymentMethodService.checkStripeStatus(+siteId);
    return result;
  }

  @Post('remove-stripe-account/:siteId')
  async removeStripeAccount(@Param('siteId') siteId: string) {
    return await this.paymentMethodService.removeStripeAccount(+siteId);
  }
}
