import { Block_Area } from './site3_block.dto';
import { Site5_Resource } from './site5_resource.dto';
export interface BlockData_Content {
    index: number;
    nodeName: string;
    text: string;
    attributes: BlockData_Content_Attributes;
    children: BlockData_Content[];
    resource?: Site5_Resource;
}
export interface BlockData_Content_Attributes {
    class?: string;
    tag?: string;
    src?: string;
    style?: string;
}
export interface BlockData_Group {
    index: number;
    contents: BlockData_Content[];
}
export interface BlockData_Content_Extra {
    ul?: string[];
    parentStyle?: Record<string, string>;
    areaId: Block_Area;
}
