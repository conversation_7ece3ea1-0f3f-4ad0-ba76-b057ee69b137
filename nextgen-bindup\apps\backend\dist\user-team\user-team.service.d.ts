import { UserTeamEntity } from './entities/user-team.entity';
import { DataSource, Repository } from 'typeorm';
import { UserTeamDto } from './dto/user-team.dto';
export declare class UserTeamService {
    private dataSource;
    readonly userTeamRepo: Repository<UserTeamEntity>;
    constructor(dataSource: DataSource);
    getAllByRootUserId(rootUserId: string): Promise<UserTeamEntity[]>;
    getAllByTeamId(rootUserId: string, teamId: number): Promise<UserTeamEntity[]>;
    getMemberInfoByTeamId(rootUserId: string, teamId: number): Promise<UserTeamDto[]>;
    getTeamsOfMember(rootUserId: string): Promise<UserTeamDto[]>;
}
