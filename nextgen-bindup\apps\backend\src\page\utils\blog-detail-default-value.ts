import { Component } from '@nextgen-bindup/common/dto/component';
import { ComponentType } from '../types/component.type';
import { BgSolidColorPropDto } from '@nextgen-bindup/common/dto/setting-properties/background-prop.dto';
import { MediaImagePropDto } from '@nextgen-bindup/common/dto/setting-properties/media-prop.dto';

export const BLOG_DETAIL_DEFAULT_COMPONENT_VALUE = (
  ts: number,
  collectionId: number,
  categoryCollectionId: number,
): Record<string, Component> => ({
  __main__: {
    id: '__main__',
    ts: ts,
    name: 'Main',
    type: ComponentType.Main,
    children: ['block_1746520217492'],
    parentId: '__page__',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: '%', value: '100' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: true,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  __page__: {
    id: '__page__',
    ts: ts,
    name: 'Page',
    type: ComponentType.Page,
    children: ['__main__'],
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'dvh', value: '100' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  text_1745918670474: {
    id: 'text_1745918670474',
    ts: ts,
    name: 'Text',
    type: ComponentType.Text,
    children: [],
    parentId: 'block_1745918646778',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      text: {
        ts: ts,
        tag: 'p',
        text: '<p>Text</p>',
        type: 'specification',
        value: {},
        format: [],
        colorStyleId: null,
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 1744702441737 },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  text_1746520412908: {
    id: 'text_1746520412908',
    ts: ts,
    name: 'Text',
    type: ComponentType.Text,
    children: [],
    parentId: 'block_1746520343613',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      text: {
        ts: ts,
        tag: 'p',
        text: '<p>Text</p>',
        type: 'specification',
        value: {},
        format: [],
        colorStyleId: null,
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 1 },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  block_1745918538721: {
    id: 'block_1745918538721',
    ts: ts,
    name: 'Block',
    type: ComponentType.Block,
    children: ['heading_1746610589781'],
    parentId: 'block_1746520217492',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      layout: {
        ts: ts,
        flex: {
          flexGrow: '0',
          flexWrap: 'nowrap',
          alignItems: 'center',
          flexShrink: '0',
          hozSpacing: { unit: 'px', value: '0' },
          verSpacing: { unit: 'px', value: '0' },
          alignContent: 'center',
          flexDirection: '',
          justifyContent: 'center',
        },
        grid: null,
        type: 'flex',
        carousel: null,
      },
      actions: { ts: ts, list: [] },
      binding: { ts: ts },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      datasource: {
        ts: ts,
        sorts: [],
        filters: { match: 'and', conditions: [] },
        collectionId: 0,
        type: 'list',
      },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '20' },
          left: { unit: 'px', value: '20' },
          right: { unit: 'px', value: '20' },
          bottom: { unit: 'px', value: '20' },
          isDetail: true,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  block_1745918646778: {
    id: 'block_1745918646778',
    ts: ts,
    name: 'Block',
    type: ComponentType.Block,
    children: ['text_1745918670474'],
    parentId: 'block_1746520217492',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      datasource: {
        ts: ts,
        sorts: [],
        filters: { match: 'and', conditions: [] },
        collectionId: 0,
        type: 'list',
      },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '20' },
          left: { unit: 'px', value: '20' },
          right: { unit: 'px', value: '20' },
          bottom: { unit: 'px', value: '20' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  block_1746520217492: {
    id: 'block_1746520217492',
    ts: ts,
    name: 'Block',
    type: ComponentType.Block,
    children: [
      'image_1745918401135',
      'block_1745918538721',
      'block_1746520325972',
      'block_1745918646778',
    ],
    parentId: '__main__',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      datasource: {
        ts: ts,
        type: 'one',
        sorts: [{ id: 1747381736384, order: 'asc', fieldId: 1744702444500 }],
        filters: {
          match: 'and',
          conditions: [
            {
              id: 1747379302871,
              value: '-1',
              fieldId: 999,
              operator: 'is-set',
            },
          ],
        },
        collectionId: collectionId,
      },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  block_1746520325972: {
    id: 'block_1746520325972',
    ts: ts,
    name: 'Block',
    type: ComponentType.Block,
    children: ['block_1746520343613'],
    parentId: 'block_1746520217492',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      layout: {
        ts: ts,
        flex: {
          flexGrow: '0',
          flexWrap: 'wrap',
          alignItems: '',
          flexShrink: '0',
          hozSpacing: { unit: 'px', value: '10' },
          verSpacing: { unit: 'px', value: '10' },
          alignContent: 'center',
          flexDirection: 'row',
          justifyContent: 'start',
        },
        grid: null,
        type: 'flex',
        carousel: null,
      },
      actions: { ts: ts, list: [] },
      binding: { ts: ts },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      datasource: {
        ts: ts,
        type: 'list',
        sorts: [],
        filters: {
          match: 'and',
          conditions: [
            {
              id: 1747018821112,
              value: '1744705577720',
              fieldId: 1000,
              operator: 'is-set',
            },
          ],
        },
        collectionId: categoryCollectionId,
      },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '20' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: true,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  block_1746520343613: {
    id: 'block_1746520343613',
    ts: ts,
    name: 'Block',
    type: ComponentType.Block,
    children: ['text_1746520412908'],
    parentId: 'block_1746520325972',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '1' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '1' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '1' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '1' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '20' },
          height: { unit: 'px', value: '20' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '20' },
          height: { unit: 'px', value: '20' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '20' },
          height: { unit: 'px', value: '20' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '20' },
          height: { unit: 'px', value: '20' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      layout: {
        ts: ts,
        flex: {
          flexGrow: '0',
          flexWrap: 'nowrap',
          alignItems: '',
          flexShrink: '0',
          hozSpacing: { unit: 'px', value: '0' },
          verSpacing: { unit: 'px', value: '0' },
          alignContent: '',
          flexDirection: '',
          justifyContent: '',
        },
        grid: null,
        type: 'flex',
        carousel: null,
      },
      actions: { ts: ts, list: [] },
      binding: { ts: ts },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      datasource: {
        ts: ts,
        type: 'list',
        sorts: [],
        filters: { match: 'and', conditions: [] },
        collectionId: 0,
      },
      backgrounds: {
        ts: ts,
        list: [
          {
            id: 'solid_1746520509187',
            ts: ts,
            from: 'design',
            type: 'solid',
            alpha: 1,
            presetId: null,
            visibility: true,
            backgroundColor: '#e7e4e4',
          } as BgSolidColorPropDto,
        ],
      },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '5' },
          left: { unit: 'px', value: '15' },
          right: { unit: 'px', value: '15' },
          bottom: { unit: 'px', value: '5' },
          isDetail: true,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  image_1745918401135: {
    id: 'image_1745918401135',
    ts: ts,
    name: 'Image',
    type: ComponentType.Image,
    children: [],
    parentId: 'block_1746520217492',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'auto', value: '100' },
        height: { unit: 'px', value: '350' },
        maxWidth: { unit: '%', value: '100' },
        minWidth: { unit: '%', value: '100' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      media: {
        ts: ts,
        alt: '',
        url: '',
        type: 'image',
        float: 'none',
        position: {
          x: { unit: 'px', value: '' },
          y: { unit: 'px', value: '' },
          offsetX: { unit: 'px', value: '' },
          offsetY: { unit: 'px', value: '' },
        },
        freeRatio: { width: '4', height: '3' },
        objectFit: 'cover',
        aspectRatio: 'auto',
      } as MediaImagePropDto,
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 1744702445997 },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      transform: {
        ts: ts,
        move: { x: { unit: 'px', value: '0' }, y: { unit: 'px', value: '0' } },
        expand: { x: { unit: '%', value: '0' } },
        origin: '中心',
        rotate: 0,
        distort: { x: { unit: '%', value: '0' }, y: { unit: '%', value: '0' } },
      },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  heading_1746610589781: {
    id: 'heading_1746610589781',
    ts: ts,
    name: 'Heading',
    type: ComponentType.Heading,
    children: [],
    parentId: 'block_1745918538721',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      text: {
        ts: ts,
        tag: 'h1',
        text: '<h1>Heading</h1>',
        type: 'specification',
        value: {},
        format: [],
        colorStyleId: 5,
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 1744702444500 },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      datasource: {
        ts: ts,
        type: 'list',
        sorts: [],
        filters: { match: 'and', conditions: [] },
      },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
});
