"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProxyModule = void 0;
const common_1 = require("@nestjs/common");
const proxy_controller_1 = require("./proxy.controller");
const proxy_service_1 = require("./proxy.service");
const payment_module_1 = require("../payment/payment.module");
const site_auth_service_1 = require("./site-auth.service");
const shipping_note_settings_module_1 = require("../shipping-note-settings/shipping-note-settings.module");
const shop_information_settings_module_1 = require("../shop-information-settings/shop-information-settings.module");
const payment_method_module_1 = require("../payment-method/payment-method.module");
const order_module_1 = require("../order/order.module");
const product_stocks_module_1 = require("../product-stocks/product-stocks.module");
const site_module_1 = require("../site/site.module");
const order_complete_settings_module_1 = require("../order-complete-settings/order-complete-settings.module");
let ProxyModule = class ProxyModule {
};
exports.ProxyModule = ProxyModule;
exports.ProxyModule = ProxyModule = __decorate([
    (0, common_1.Module)({
        imports: [
            payment_module_1.PaymentModule,
            shipping_note_settings_module_1.ShippingNoteSettingModule,
            shop_information_settings_module_1.ShopInformationSettingModule,
            payment_method_module_1.PaymentMethodModule,
            order_complete_settings_module_1.OrderCompletionSettingModule,
            order_module_1.OrderModule,
            product_stocks_module_1.ProductStocksModule,
            payment_module_1.PaymentModule,
            site_module_1.SiteModule,
        ],
        controllers: [proxy_controller_1.ProxyController],
        providers: [proxy_service_1.ProxyService, site_auth_service_1.SiteAuthService],
        exports: [site_auth_service_1.SiteAuthService],
    })
], ProxyModule);
//# sourceMappingURL=proxy.module.js.map