import "./chunk-EWTE5DHJ.js";

// ../../node_modules/react-material-symbols/dist/index.es.js
var Lr = { exports: {} };
var Vr = { exports: {} };
var Ke = { exports: {} };
Ke.exports;
var ct;
function Mt() {
  return ct || (ct = 1, function(Y, v) {
    (function() {
      typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ < "u" && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart == "function" && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());
      var pe = "18.2.0", G = Symbol.for("react.element"), ne = Symbol.for("react.portal"), ee = Symbol.for("react.fragment"), H = Symbol.for("react.strict_mode"), K = Symbol.for("react.profiler"), D = Symbol.for("react.provider"), J = Symbol.for("react.context"), I = Symbol.for("react.forward_ref"), W = Symbol.for("react.suspense"), X = Symbol.for("react.suspense_list"), k = Symbol.for("react.memo"), fe = Symbol.for("react.lazy"), Ie = Symbol.for("react.offscreen"), ae = Symbol.iterator, oe = "@@iterator";
      function L(e) {
        if (e === null || typeof e != "object")
          return null;
        var r = ae && e[ae] || e[oe];
        return typeof r == "function" ? r : null;
      }
      var he = {
        /**
         * @internal
         * @type {ReactComponent}
         */
        current: null
      }, ie = {
        transition: null
      }, N = {
        current: null,
        // Used to reproduce behavior of `batchedUpdates` in legacy mode.
        isBatchingLegacy: false,
        didScheduleLegacyUpdate: false
      }, Q = {
        /**
         * @internal
         * @type {ReactComponent}
         */
        current: null
      }, ce = {}, ve = null;
      function Re(e) {
        ve = e;
      }
      ce.setExtraStackFrame = function(e) {
        ve = e;
      }, ce.getCurrentStack = null, ce.getStackAddendum = function() {
        var e = "";
        ve && (e += ve);
        var r = ce.getCurrentStack;
        return r && (e += r() || ""), e;
      };
      var Ce = false, Ue = false, we = false, Z = false, re = false, B = {
        ReactCurrentDispatcher: he,
        ReactCurrentBatchConfig: ie,
        ReactCurrentOwner: Q
      };
      B.ReactDebugCurrentFrame = ce, B.ReactCurrentActQueue = N;
      function ue(e) {
        {
          for (var r = arguments.length, a = new Array(r > 1 ? r - 1 : 0), o = 1; o < r; o++)
            a[o - 1] = arguments[o];
          le("warn", e, a);
        }
      }
      function d(e) {
        {
          for (var r = arguments.length, a = new Array(r > 1 ? r - 1 : 0), o = 1; o < r; o++)
            a[o - 1] = arguments[o];
          le("error", e, a);
        }
      }
      function le(e, r, a) {
        {
          var o = B.ReactDebugCurrentFrame, u = o.getStackAddendum();
          u !== "" && (r += "%s", a = a.concat([u]));
          var p = a.map(function(l) {
            return String(l);
          });
          p.unshift("Warning: " + r), Function.prototype.apply.call(console[e], console, p);
        }
      }
      var Se = {};
      function n(e, r) {
        {
          var a = e.constructor, o = a && (a.displayName || a.name) || "ReactClass", u = o + "." + r;
          if (Se[u])
            return;
          d("Can't call %s on a component that is not yet mounted. This is a no-op, but it might indicate a bug in your application. Instead, assign to `this.state` directly or define a `state = {};` class property with the desired state in the %s component.", r, o), Se[u] = true;
        }
      }
      var s = {
        /**
         * Checks whether or not this composite component is mounted.
         * @param {ReactClass} publicInstance The instance we want to test.
         * @return {boolean} True if mounted, false otherwise.
         * @protected
         * @final
         */
        isMounted: function(e) {
          return false;
        },
        /**
         * Forces an update. This should only be invoked when it is known with
         * certainty that we are **not** in a DOM transaction.
         *
         * You may want to call this when you know that some deeper aspect of the
         * component's state has changed but `setState` was not called.
         *
         * This will not invoke `shouldComponentUpdate`, but it will invoke
         * `componentWillUpdate` and `componentDidUpdate`.
         *
         * @param {ReactClass} publicInstance The instance that should rerender.
         * @param {?function} callback Called after component is updated.
         * @param {?string} callerName name of the calling function in the public API.
         * @internal
         */
        enqueueForceUpdate: function(e, r, a) {
          n(e, "forceUpdate");
        },
        /**
         * Replaces all of the state. Always use this or `setState` to mutate state.
         * You should treat `this.state` as immutable.
         *
         * There is no guarantee that `this.state` will be immediately updated, so
         * accessing `this.state` after calling this method may return the old value.
         *
         * @param {ReactClass} publicInstance The instance that should rerender.
         * @param {object} completeState Next state.
         * @param {?function} callback Called after component is updated.
         * @param {?string} callerName name of the calling function in the public API.
         * @internal
         */
        enqueueReplaceState: function(e, r, a, o) {
          n(e, "replaceState");
        },
        /**
         * Sets a subset of the state. This only exists because _pendingState is
         * internal. This provides a merging strategy that is not available to deep
         * properties which is confusing. TODO: Expose pendingState or don't use it
         * during the merge.
         *
         * @param {ReactClass} publicInstance The instance that should rerender.
         * @param {object} partialState Next partial state to be merged with state.
         * @param {?function} callback Called after component is updated.
         * @param {?string} Name of the calling function in the public API.
         * @internal
         */
        enqueueSetState: function(e, r, a, o) {
          n(e, "setState");
        }
      }, h = Object.assign, C = {};
      Object.freeze(C);
      function _(e, r, a) {
        this.props = e, this.context = r, this.refs = C, this.updater = a || s;
      }
      _.prototype.isReactComponent = {}, _.prototype.setState = function(e, r) {
        if (typeof e != "object" && typeof e != "function" && e != null)
          throw new Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");
        this.updater.enqueueSetState(this, e, r, "setState");
      }, _.prototype.forceUpdate = function(e) {
        this.updater.enqueueForceUpdate(this, e, "forceUpdate");
      };
      {
        var P = {
          isMounted: ["isMounted", "Instead, make sure to clean up subscriptions and pending requests in componentWillUnmount to prevent memory leaks."],
          replaceState: ["replaceState", "Refactor your code to use setState instead (see https://github.com/facebook/react/issues/3236)."]
        }, j = function(e, r) {
          Object.defineProperty(_.prototype, e, {
            get: function() {
              ue("%s(...) is deprecated in plain JavaScript React classes. %s", r[0], r[1]);
            }
          });
        };
        for (var T in P)
          P.hasOwnProperty(T) && j(T, P[T]);
      }
      function w() {
      }
      w.prototype = _.prototype;
      function U(e, r, a) {
        this.props = e, this.context = r, this.refs = C, this.updater = a || s;
      }
      var me = U.prototype = new w();
      me.constructor = U, h(me, _.prototype), me.isPureReactComponent = true;
      function yr() {
        var e = {
          current: null
        };
        return Object.seal(e), e;
      }
      var Je = Array.isArray;
      function Fe(e) {
        return Je(e);
      }
      function hr(e) {
        {
          var r = typeof Symbol == "function" && Symbol.toStringTag, a = r && e[Symbol.toStringTag] || e.constructor.name || "Object";
          return a;
        }
      }
      function $e(e) {
        try {
          return Te(e), false;
        } catch {
          return true;
        }
      }
      function Te(e) {
        return "" + e;
      }
      function Oe(e) {
        if ($e(e))
          return d("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", hr(e)), Te(e);
      }
      function Xe(e, r, a) {
        var o = e.displayName;
        if (o)
          return o;
        var u = r.displayName || r.name || "";
        return u !== "" ? a + "(" + u + ")" : a;
      }
      function Pe(e) {
        return e.displayName || "Context";
      }
      function de(e) {
        if (e == null)
          return null;
        if (typeof e.tag == "number" && d("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof e == "function")
          return e.displayName || e.name || null;
        if (typeof e == "string")
          return e;
        switch (e) {
          case ee:
            return "Fragment";
          case ne:
            return "Portal";
          case K:
            return "Profiler";
          case H:
            return "StrictMode";
          case W:
            return "Suspense";
          case X:
            return "SuspenseList";
        }
        if (typeof e == "object")
          switch (e.$$typeof) {
            case J:
              var r = e;
              return Pe(r) + ".Consumer";
            case D:
              var a = e;
              return Pe(a._context) + ".Provider";
            case I:
              return Xe(e, e.render, "ForwardRef");
            case k:
              var o = e.displayName || null;
              return o !== null ? o : de(e.type) || "Memo";
            case fe: {
              var u = e, p = u._payload, l = u._init;
              try {
                return de(l(p));
              } catch {
                return null;
              }
            }
          }
        return null;
      }
      var ke = Object.prototype.hasOwnProperty, Le = {
        key: true,
        ref: true,
        __self: true,
        __source: true
      }, Qe, Ze, Ve;
      Ve = {};
      function Ye(e) {
        if (ke.call(e, "ref")) {
          var r = Object.getOwnPropertyDescriptor(e, "ref").get;
          if (r && r.isReactWarning)
            return false;
        }
        return e.ref !== void 0;
      }
      function ge(e) {
        if (ke.call(e, "key")) {
          var r = Object.getOwnPropertyDescriptor(e, "key").get;
          if (r && r.isReactWarning)
            return false;
        }
        return e.key !== void 0;
      }
      function mr(e, r) {
        var a = function() {
          Qe || (Qe = true, d("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", r));
        };
        a.isReactWarning = true, Object.defineProperty(e, "key", {
          get: a,
          configurable: true
        });
      }
      function er(e, r) {
        var a = function() {
          Ze || (Ze = true, d("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", r));
        };
        a.isReactWarning = true, Object.defineProperty(e, "ref", {
          get: a,
          configurable: true
        });
      }
      function rr(e) {
        if (typeof e.ref == "string" && Q.current && e.__self && Q.current.stateNode !== e.__self) {
          var r = de(Q.current.type);
          Ve[r] || (d('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref', r, e.ref), Ve[r] = true);
        }
      }
      var je = function(e, r, a, o, u, p, l) {
        var y = {
          // This tag allows us to uniquely identify this as a React Element
          $$typeof: G,
          // Built-in properties that belong on the element
          type: e,
          key: r,
          ref: a,
          props: l,
          // Record the component responsible for creating this element.
          _owner: p
        };
        return y._store = {}, Object.defineProperty(y._store, "validated", {
          configurable: false,
          enumerable: false,
          writable: true,
          value: false
        }), Object.defineProperty(y, "_self", {
          configurable: false,
          enumerable: false,
          writable: false,
          value: o
        }), Object.defineProperty(y, "_source", {
          configurable: false,
          enumerable: false,
          writable: false,
          value: u
        }), Object.freeze && (Object.freeze(y.props), Object.freeze(y)), y;
      };
      function gr(e, r, a) {
        var o, u = {}, p = null, l = null, y = null, E = null;
        if (r != null) {
          Ye(r) && (l = r.ref, rr(r)), ge(r) && (Oe(r.key), p = "" + r.key), y = r.__self === void 0 ? null : r.__self, E = r.__source === void 0 ? null : r.__source;
          for (o in r)
            ke.call(r, o) && !Le.hasOwnProperty(o) && (u[o] = r[o]);
        }
        var O = arguments.length - 2;
        if (O === 1)
          u.children = a;
        else if (O > 1) {
          for (var A = Array(O), x = 0; x < O; x++)
            A[x] = arguments[x + 2];
          Object.freeze && Object.freeze(A), u.children = A;
        }
        if (e && e.defaultProps) {
          var $ = e.defaultProps;
          for (o in $)
            u[o] === void 0 && (u[o] = $[o]);
        }
        if (p || l) {
          var M = typeof e == "function" ? e.displayName || e.name || "Unknown" : e;
          p && mr(u, M), l && er(u, M);
        }
        return je(e, p, l, y, E, Q.current, u);
      }
      function _r(e, r) {
        var a = je(e.type, r, e.ref, e._self, e._source, e._owner, e.props);
        return a;
      }
      function br(e, r, a) {
        if (e == null)
          throw new Error("React.cloneElement(...): The argument must be a React element, but you passed " + e + ".");
        var o, u = h({}, e.props), p = e.key, l = e.ref, y = e._self, E = e._source, O = e._owner;
        if (r != null) {
          Ye(r) && (l = r.ref, O = Q.current), ge(r) && (Oe(r.key), p = "" + r.key);
          var A;
          e.type && e.type.defaultProps && (A = e.type.defaultProps);
          for (o in r)
            ke.call(r, o) && !Le.hasOwnProperty(o) && (r[o] === void 0 && A !== void 0 ? u[o] = A[o] : u[o] = r[o]);
        }
        var x = arguments.length - 2;
        if (x === 1)
          u.children = a;
        else if (x > 1) {
          for (var $ = Array(x), M = 0; M < x; M++)
            $[M] = arguments[M + 2];
          u.children = $;
        }
        return je(e.type, p, l, y, E, O, u);
      }
      function _e(e) {
        return typeof e == "object" && e !== null && e.$$typeof === G;
      }
      var tr = ".", Er = ":";
      function Rr(e) {
        var r = /[=:]/g, a = {
          "=": "=0",
          ":": "=2"
        }, o = e.replace(r, function(u) {
          return a[u];
        });
        return "$" + o;
      }
      var Ne = false, nr = /\/+/g;
      function ye(e) {
        return e.replace(nr, "$&/");
      }
      function Ae(e, r) {
        return typeof e == "object" && e !== null && e.key != null ? (Oe(e.key), Rr("" + e.key)) : r.toString(36);
      }
      function be(e, r, a, o, u) {
        var p = typeof e;
        (p === "undefined" || p === "boolean") && (e = null);
        var l = false;
        if (e === null)
          l = true;
        else
          switch (p) {
            case "string":
            case "number":
              l = true;
              break;
            case "object":
              switch (e.$$typeof) {
                case G:
                case ne:
                  l = true;
              }
          }
        if (l) {
          var y = e, E = u(y), O = o === "" ? tr + Ae(y, 0) : o;
          if (Fe(E)) {
            var A = "";
            O != null && (A = ye(O) + "/"), be(E, r, A, "", function(Vt) {
              return Vt;
            });
          } else
            E != null && (_e(E) && (E.key && (!y || y.key !== E.key) && Oe(E.key), E = _r(
              E,
              // Keep both the (mapped) and old keys if they differ, just as
              // traverseAllChildren used to do for objects as children
              a + // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key
              (E.key && (!y || y.key !== E.key) ? (
                // $FlowFixMe Flow incorrectly thinks existing element's key can be a number
                // eslint-disable-next-line react-internal/safe-string-coercion
                ye("" + E.key) + "/"
              ) : "") + O
            )), r.push(E));
          return 1;
        }
        var x, $, M = 0, q = o === "" ? tr : o + Er;
        if (Fe(e))
          for (var vr = 0; vr < e.length; vr++)
            x = e[vr], $ = q + Ae(x, vr), M += be(x, r, a, $, u);
        else {
          var $r = L(e);
          if (typeof $r == "function") {
            var it = e;
            $r === it.entries && (Ne || ue("Using Maps as children is not supported. Use an array of keyed ReactElements instead."), Ne = true);
            for (var $t = $r.call(it), ut, Lt = 0; !(ut = $t.next()).done; )
              x = ut.value, $ = q + Ae(x, Lt++), M += be(x, r, a, $, u);
          } else if (p === "object") {
            var st = String(e);
            throw new Error("Objects are not valid as a React child (found: " + (st === "[object Object]" ? "object with keys {" + Object.keys(e).join(", ") + "}" : st) + "). If you meant to render a collection of children, use an array instead.");
          }
        }
        return M;
      }
      function xe(e, r, a) {
        if (e == null)
          return e;
        var o = [], u = 0;
        return be(e, o, "", "", function(p) {
          return r.call(a, p, u++);
        }), o;
      }
      function Cr(e) {
        var r = 0;
        return xe(e, function() {
          r++;
        }), r;
      }
      function ar(e, r, a) {
        xe(e, function() {
          r.apply(this, arguments);
        }, a);
      }
      function wr(e) {
        return xe(e, function(r) {
          return r;
        }) || [];
      }
      function or(e) {
        if (!_e(e))
          throw new Error("React.Children.only expected to receive a single React element child.");
        return e;
      }
      function ir(e) {
        var r = {
          $$typeof: J,
          // As a workaround to support multiple concurrent renderers, we categorize
          // some renderers as primary and others as secondary. We only expect
          // there to be two concurrent renderers at most: React Native (primary) and
          // Fabric (secondary); React DOM (primary) and React ART (secondary).
          // Secondary renderers store their context values on separate fields.
          _currentValue: e,
          _currentValue2: e,
          // Used to track how many concurrent renderers this context currently
          // supports within in a single renderer. Such as parallel server rendering.
          _threadCount: 0,
          // These are circular
          Provider: null,
          Consumer: null,
          // Add these to use same hidden class in VM as ServerContext
          _defaultValue: null,
          _globalName: null
        };
        r.Provider = {
          $$typeof: D,
          _context: r
        };
        var a = false, o = false, u = false;
        {
          var p = {
            $$typeof: J,
            _context: r
          };
          Object.defineProperties(p, {
            Provider: {
              get: function() {
                return o || (o = true, d("Rendering <Context.Consumer.Provider> is not supported and will be removed in a future major release. Did you mean to render <Context.Provider> instead?")), r.Provider;
              },
              set: function(l) {
                r.Provider = l;
              }
            },
            _currentValue: {
              get: function() {
                return r._currentValue;
              },
              set: function(l) {
                r._currentValue = l;
              }
            },
            _currentValue2: {
              get: function() {
                return r._currentValue2;
              },
              set: function(l) {
                r._currentValue2 = l;
              }
            },
            _threadCount: {
              get: function() {
                return r._threadCount;
              },
              set: function(l) {
                r._threadCount = l;
              }
            },
            Consumer: {
              get: function() {
                return a || (a = true, d("Rendering <Context.Consumer.Consumer> is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?")), r.Consumer;
              }
            },
            displayName: {
              get: function() {
                return r.displayName;
              },
              set: function(l) {
                u || (ue("Setting `displayName` on Context.Consumer has no effect. You should set it directly on the context with Context.displayName = '%s'.", l), u = true);
              }
            }
          }), r.Consumer = p;
        }
        return r._currentRenderer = null, r._currentRenderer2 = null, r;
      }
      var De = -1, Be = 0, ze = 1, Sr = 2;
      function Tr(e) {
        if (e._status === De) {
          var r = e._result, a = r();
          if (a.then(function(p) {
            if (e._status === Be || e._status === De) {
              var l = e;
              l._status = ze, l._result = p;
            }
          }, function(p) {
            if (e._status === Be || e._status === De) {
              var l = e;
              l._status = Sr, l._result = p;
            }
          }), e._status === De) {
            var o = e;
            o._status = Be, o._result = a;
          }
        }
        if (e._status === ze) {
          var u = e._result;
          return u === void 0 && d(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))

Did you accidentally put curly braces around the import?`, u), "default" in u || d(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))`, u), u.default;
        } else
          throw e._result;
      }
      function Or(e) {
        var r = {
          // We use these fields to store the result.
          _status: De,
          _result: e
        }, a = {
          $$typeof: fe,
          _payload: r,
          _init: Tr
        };
        {
          var o, u;
          Object.defineProperties(a, {
            defaultProps: {
              configurable: true,
              get: function() {
                return o;
              },
              set: function(p) {
                d("React.lazy(...): It is not supported to assign `defaultProps` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."), o = p, Object.defineProperty(a, "defaultProps", {
                  enumerable: true
                });
              }
            },
            propTypes: {
              configurable: true,
              get: function() {
                return u;
              },
              set: function(p) {
                d("React.lazy(...): It is not supported to assign `propTypes` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."), u = p, Object.defineProperty(a, "propTypes", {
                  enumerable: true
                });
              }
            }
          });
        }
        return a;
      }
      function Pr(e) {
        e != null && e.$$typeof === k ? d("forwardRef requires a render function but received a `memo` component. Instead of forwardRef(memo(...)), use memo(forwardRef(...)).") : typeof e != "function" ? d("forwardRef requires a render function but was given %s.", e === null ? "null" : typeof e) : e.length !== 0 && e.length !== 2 && d("forwardRef render functions accept exactly two parameters: props and ref. %s", e.length === 1 ? "Did you forget to use the ref parameter?" : "Any additional parameter will be undefined."), e != null && (e.defaultProps != null || e.propTypes != null) && d("forwardRef render functions do not support propTypes or defaultProps. Did you accidentally pass a React component?");
        var r = {
          $$typeof: I,
          render: e
        };
        {
          var a;
          Object.defineProperty(r, "displayName", {
            enumerable: false,
            configurable: true,
            get: function() {
              return a;
            },
            set: function(o) {
              a = o, !e.name && !e.displayName && (e.displayName = o);
            }
          });
        }
        return r;
      }
      var t;
      t = Symbol.for("react.module.reference");
      function i(e) {
        return !!(typeof e == "string" || typeof e == "function" || e === ee || e === K || re || e === H || e === W || e === X || Z || e === Ie || Ce || Ue || we || typeof e == "object" && e !== null && (e.$$typeof === fe || e.$$typeof === k || e.$$typeof === D || e.$$typeof === J || e.$$typeof === I || // This needs to include all possible module reference object
        // types supported by any Flight configuration anywhere since
        // we don't know which Flight build this will end up being used
        // with.
        e.$$typeof === t || e.getModuleId !== void 0));
      }
      function f(e, r) {
        i(e) || d("memo: The first argument must be a component. Instead received: %s", e === null ? "null" : typeof e);
        var a = {
          $$typeof: k,
          type: e,
          compare: r === void 0 ? null : r
        };
        {
          var o;
          Object.defineProperty(a, "displayName", {
            enumerable: false,
            configurable: true,
            get: function() {
              return o;
            },
            set: function(u) {
              o = u, !e.name && !e.displayName && (e.displayName = u);
            }
          });
        }
        return a;
      }
      function c() {
        var e = he.current;
        return e === null && d(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.`), e;
      }
      function R(e) {
        var r = c();
        if (e._context !== void 0) {
          var a = e._context;
          a.Consumer === e ? d("Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be removed in a future major release. Did you mean to call useContext(Context) instead?") : a.Provider === e && d("Calling useContext(Context.Provider) is not supported. Did you mean to call useContext(Context) instead?");
        }
        return r.useContext(e);
      }
      function S(e) {
        var r = c();
        return r.useState(e);
      }
      function b(e, r, a) {
        var o = c();
        return o.useReducer(e, r, a);
      }
      function m(e) {
        var r = c();
        return r.useRef(e);
      }
      function z(e, r) {
        var a = c();
        return a.useEffect(e, r);
      }
      function F(e, r) {
        var a = c();
        return a.useInsertionEffect(e, r);
      }
      function V(e, r) {
        var a = c();
        return a.useLayoutEffect(e, r);
      }
      function te(e, r) {
        var a = c();
        return a.useCallback(e, r);
      }
      function Ee(e, r) {
        var a = c();
        return a.useMemo(e, r);
      }
      function ur(e, r, a) {
        var o = c();
        return o.useImperativeHandle(e, r, a);
      }
      function se(e, r) {
        {
          var a = c();
          return a.useDebugValue(e, r);
        }
      }
      function pt() {
        var e = c();
        return e.useTransition();
      }
      function vt(e) {
        var r = c();
        return r.useDeferredValue(e);
      }
      function yt() {
        var e = c();
        return e.useId();
      }
      function ht(e, r, a) {
        var o = c();
        return o.useSyncExternalStore(e, r, a);
      }
      var qe = 0, Mr, Wr, Ur, Yr, Br, zr, qr;
      function Gr() {
      }
      Gr.__reactDisabledLog = true;
      function mt() {
        {
          if (qe === 0) {
            Mr = console.log, Wr = console.info, Ur = console.warn, Yr = console.error, Br = console.group, zr = console.groupCollapsed, qr = console.groupEnd;
            var e = {
              configurable: true,
              enumerable: true,
              value: Gr,
              writable: true
            };
            Object.defineProperties(console, {
              info: e,
              log: e,
              warn: e,
              error: e,
              group: e,
              groupCollapsed: e,
              groupEnd: e
            });
          }
          qe++;
        }
      }
      function gt() {
        {
          if (qe--, qe === 0) {
            var e = {
              configurable: true,
              enumerable: true,
              writable: true
            };
            Object.defineProperties(console, {
              log: h({}, e, {
                value: Mr
              }),
              info: h({}, e, {
                value: Wr
              }),
              warn: h({}, e, {
                value: Ur
              }),
              error: h({}, e, {
                value: Yr
              }),
              group: h({}, e, {
                value: Br
              }),
              groupCollapsed: h({}, e, {
                value: zr
              }),
              groupEnd: h({}, e, {
                value: qr
              })
            });
          }
          qe < 0 && d("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
        }
      }
      var kr = B.ReactCurrentDispatcher, jr;
      function sr(e, r, a) {
        {
          if (jr === void 0)
            try {
              throw Error();
            } catch (u) {
              var o = u.stack.trim().match(/\n( *(at )?)/);
              jr = o && o[1] || "";
            }
          return `
` + jr + e;
        }
      }
      var Ar = false, fr;
      {
        var _t = typeof WeakMap == "function" ? WeakMap : Map;
        fr = new _t();
      }
      function Hr(e, r) {
        if (!e || Ar)
          return "";
        {
          var a = fr.get(e);
          if (a !== void 0)
            return a;
        }
        var o;
        Ar = true;
        var u = Error.prepareStackTrace;
        Error.prepareStackTrace = void 0;
        var p;
        p = kr.current, kr.current = null, mt();
        try {
          if (r) {
            var l = function() {
              throw Error();
            };
            if (Object.defineProperty(l.prototype, "props", {
              set: function() {
                throw Error();
              }
            }), typeof Reflect == "object" && Reflect.construct) {
              try {
                Reflect.construct(l, []);
              } catch (q) {
                o = q;
              }
              Reflect.construct(e, [], l);
            } else {
              try {
                l.call();
              } catch (q) {
                o = q;
              }
              e.call(l.prototype);
            }
          } else {
            try {
              throw Error();
            } catch (q) {
              o = q;
            }
            e();
          }
        } catch (q) {
          if (q && o && typeof q.stack == "string") {
            for (var y = q.stack.split(`
`), E = o.stack.split(`
`), O = y.length - 1, A = E.length - 1; O >= 1 && A >= 0 && y[O] !== E[A]; )
              A--;
            for (; O >= 1 && A >= 0; O--, A--)
              if (y[O] !== E[A]) {
                if (O !== 1 || A !== 1)
                  do
                    if (O--, A--, A < 0 || y[O] !== E[A]) {
                      var x = `
` + y[O].replace(" at new ", " at ");
                      return e.displayName && x.includes("<anonymous>") && (x = x.replace("<anonymous>", e.displayName)), typeof e == "function" && fr.set(e, x), x;
                    }
                  while (O >= 1 && A >= 0);
                break;
              }
          }
        } finally {
          Ar = false, kr.current = p, gt(), Error.prepareStackTrace = u;
        }
        var $ = e ? e.displayName || e.name : "", M = $ ? sr($) : "";
        return typeof e == "function" && fr.set(e, M), M;
      }
      function bt(e, r, a) {
        return Hr(e, false);
      }
      function Et(e) {
        var r = e.prototype;
        return !!(r && r.isReactComponent);
      }
      function cr(e, r, a) {
        if (e == null)
          return "";
        if (typeof e == "function")
          return Hr(e, Et(e));
        if (typeof e == "string")
          return sr(e);
        switch (e) {
          case W:
            return sr("Suspense");
          case X:
            return sr("SuspenseList");
        }
        if (typeof e == "object")
          switch (e.$$typeof) {
            case I:
              return bt(e.render);
            case k:
              return cr(e.type, r, a);
            case fe: {
              var o = e, u = o._payload, p = o._init;
              try {
                return cr(p(u), r, a);
              } catch {
              }
            }
          }
        return "";
      }
      var Kr = {}, Jr = B.ReactDebugCurrentFrame;
      function lr(e) {
        if (e) {
          var r = e._owner, a = cr(e.type, e._source, r ? r.type : null);
          Jr.setExtraStackFrame(a);
        } else
          Jr.setExtraStackFrame(null);
      }
      function Rt(e, r, a, o, u) {
        {
          var p = Function.call.bind(ke);
          for (var l in e)
            if (p(e, l)) {
              var y = void 0;
              try {
                if (typeof e[l] != "function") {
                  var E = Error((o || "React class") + ": " + a + " type `" + l + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof e[l] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                  throw E.name = "Invariant Violation", E;
                }
                y = e[l](r, l, o, a, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
              } catch (O) {
                y = O;
              }
              y && !(y instanceof Error) && (lr(u), d("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", o || "React class", a, l, typeof y), lr(null)), y instanceof Error && !(y.message in Kr) && (Kr[y.message] = true, lr(u), d("Failed %s type: %s", a, y.message), lr(null));
            }
        }
      }
      function Me(e) {
        if (e) {
          var r = e._owner, a = cr(e.type, e._source, r ? r.type : null);
          Re(a);
        } else
          Re(null);
      }
      var xr;
      xr = false;
      function Xr() {
        if (Q.current) {
          var e = de(Q.current.type);
          if (e)
            return `

Check the render method of \`` + e + "`.";
        }
        return "";
      }
      function Ct(e) {
        if (e !== void 0) {
          var r = e.fileName.replace(/^.*[\\\/]/, ""), a = e.lineNumber;
          return `

Check your code at ` + r + ":" + a + ".";
        }
        return "";
      }
      function wt(e) {
        return e != null ? Ct(e.__source) : "";
      }
      var Qr = {};
      function St(e) {
        var r = Xr();
        if (!r) {
          var a = typeof e == "string" ? e : e.displayName || e.name;
          a && (r = `

Check the top-level render call using <` + a + ">.");
        }
        return r;
      }
      function Zr(e, r) {
        if (!(!e._store || e._store.validated || e.key != null)) {
          e._store.validated = true;
          var a = St(r);
          if (!Qr[a]) {
            Qr[a] = true;
            var o = "";
            e && e._owner && e._owner !== Q.current && (o = " It was passed a child from " + de(e._owner.type) + "."), Me(e), d('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', a, o), Me(null);
          }
        }
      }
      function et(e, r) {
        if (typeof e == "object") {
          if (Fe(e))
            for (var a = 0; a < e.length; a++) {
              var o = e[a];
              _e(o) && Zr(o, r);
            }
          else if (_e(e))
            e._store && (e._store.validated = true);
          else if (e) {
            var u = L(e);
            if (typeof u == "function" && u !== e.entries)
              for (var p = u.call(e), l; !(l = p.next()).done; )
                _e(l.value) && Zr(l.value, r);
          }
        }
      }
      function rt(e) {
        {
          var r = e.type;
          if (r == null || typeof r == "string")
            return;
          var a;
          if (typeof r == "function")
            a = r.propTypes;
          else if (typeof r == "object" && (r.$$typeof === I || // Note: Memo only checks outer props here.
          // Inner props are checked in the reconciler.
          r.$$typeof === k))
            a = r.propTypes;
          else
            return;
          if (a) {
            var o = de(r);
            Rt(a, e.props, "prop", o, e);
          } else if (r.PropTypes !== void 0 && !xr) {
            xr = true;
            var u = de(r);
            d("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", u || "Unknown");
          }
          typeof r.getDefaultProps == "function" && !r.getDefaultProps.isReactClassApproved && d("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
        }
      }
      function Tt(e) {
        {
          for (var r = Object.keys(e.props), a = 0; a < r.length; a++) {
            var o = r[a];
            if (o !== "children" && o !== "key") {
              Me(e), d("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", o), Me(null);
              break;
            }
          }
          e.ref !== null && (Me(e), d("Invalid attribute `ref` supplied to `React.Fragment`."), Me(null));
        }
      }
      function tt(e, r, a) {
        var o = i(e);
        if (!o) {
          var u = "";
          (e === void 0 || typeof e == "object" && e !== null && Object.keys(e).length === 0) && (u += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var p = wt(r);
          p ? u += p : u += Xr();
          var l;
          e === null ? l = "null" : Fe(e) ? l = "array" : e !== void 0 && e.$$typeof === G ? (l = "<" + (de(e.type) || "Unknown") + " />", u = " Did you accidentally export a JSX literal instead of a component?") : l = typeof e, d("React.createElement: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", l, u);
        }
        var y = gr.apply(this, arguments);
        if (y == null)
          return y;
        if (o)
          for (var E = 2; E < arguments.length; E++)
            et(arguments[E], e);
        return e === ee ? Tt(y) : rt(y), y;
      }
      var nt = false;
      function Ot(e) {
        var r = tt.bind(null, e);
        return r.type = e, nt || (nt = true, ue("React.createFactory() is deprecated and will be removed in a future major release. Consider using JSX or use React.createElement() directly instead.")), Object.defineProperty(r, "type", {
          enumerable: false,
          get: function() {
            return ue("Factory.type is deprecated. Access the class directly before passing it to createFactory."), Object.defineProperty(this, "type", {
              value: e
            }), e;
          }
        }), r;
      }
      function Pt(e, r, a) {
        for (var o = br.apply(this, arguments), u = 2; u < arguments.length; u++)
          et(arguments[u], o.type);
        return rt(o), o;
      }
      function kt(e, r) {
        var a = ie.transition;
        ie.transition = {};
        var o = ie.transition;
        ie.transition._updatedFibers = /* @__PURE__ */ new Set();
        try {
          e();
        } finally {
          if (ie.transition = a, a === null && o._updatedFibers) {
            var u = o._updatedFibers.size;
            u > 10 && ue("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."), o._updatedFibers.clear();
          }
        }
      }
      var at = false, dr = null;
      function jt(e) {
        if (dr === null)
          try {
            var r = ("require" + Math.random()).slice(0, 7), a = Y && Y[r];
            dr = a.call(Y, "timers").setImmediate;
          } catch {
            dr = function(u) {
              at === false && (at = true, typeof MessageChannel > "u" && d("This browser does not have a MessageChannel implementation, so enqueuing tasks via await act(async () => ...) will fail. Please file an issue at https://github.com/facebook/react/issues if you encounter this warning."));
              var p = new MessageChannel();
              p.port1.onmessage = u, p.port2.postMessage(void 0);
            };
          }
        return dr(e);
      }
      var We = 0, ot = false;
      function At(e) {
        {
          var r = We;
          We++, N.current === null && (N.current = []);
          var a = N.isBatchingLegacy, o;
          try {
            if (N.isBatchingLegacy = true, o = e(), !a && N.didScheduleLegacyUpdate) {
              var u = N.current;
              u !== null && (N.didScheduleLegacyUpdate = false, Fr(u));
            }
          } catch ($) {
            throw pr(r), $;
          } finally {
            N.isBatchingLegacy = a;
          }
          if (o !== null && typeof o == "object" && typeof o.then == "function") {
            var p = o, l = false, y = {
              then: function($, M) {
                l = true, p.then(function(q) {
                  pr(r), We === 0 ? Dr(q, $, M) : $(q);
                }, function(q) {
                  pr(r), M(q);
                });
              }
            };
            return !ot && typeof Promise < "u" && Promise.resolve().then(function() {
            }).then(function() {
              l || (ot = true, d("You called act(async () => ...) without await. This could lead to unexpected testing behaviour, interleaving multiple act calls and mixing their scopes. You should - await act(async () => ...);"));
            }), y;
          } else {
            var E = o;
            if (pr(r), We === 0) {
              var O = N.current;
              O !== null && (Fr(O), N.current = null);
              var A = {
                then: function($, M) {
                  N.current === null ? (N.current = [], Dr(E, $, M)) : $(E);
                }
              };
              return A;
            } else {
              var x = {
                then: function($, M) {
                  $(E);
                }
              };
              return x;
            }
          }
        }
      }
      function pr(e) {
        e !== We - 1 && d("You seem to have overlapping act() calls, this is not supported. Be sure to await previous act() calls before making a new one. "), We = e;
      }
      function Dr(e, r, a) {
        {
          var o = N.current;
          if (o !== null)
            try {
              Fr(o), jt(function() {
                o.length === 0 ? (N.current = null, r(e)) : Dr(e, r, a);
              });
            } catch (u) {
              a(u);
            }
          else
            r(e);
        }
      }
      var Ir = false;
      function Fr(e) {
        if (!Ir) {
          Ir = true;
          var r = 0;
          try {
            for (; r < e.length; r++) {
              var a = e[r];
              do
                a = a(true);
              while (a !== null);
            }
            e.length = 0;
          } catch (o) {
            throw e = e.slice(r + 1), o;
          } finally {
            Ir = false;
          }
        }
      }
      var xt = tt, Dt = Pt, It = Ot, Ft = {
        map: xe,
        forEach: ar,
        count: Cr,
        toArray: wr,
        only: or
      };
      v.Children = Ft, v.Component = _, v.Fragment = ee, v.Profiler = K, v.PureComponent = U, v.StrictMode = H, v.Suspense = W, v.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = B, v.cloneElement = Dt, v.createContext = ir, v.createElement = xt, v.createFactory = It, v.createRef = yr, v.forwardRef = Pr, v.isValidElement = _e, v.lazy = Or, v.memo = f, v.startTransition = kt, v.unstable_act = At, v.useCallback = te, v.useContext = R, v.useDebugValue = se, v.useDeferredValue = vt, v.useEffect = z, v.useId = yt, v.useImperativeHandle = ur, v.useInsertionEffect = F, v.useLayoutEffect = V, v.useMemo = Ee, v.useReducer = b, v.useRef = m, v.useState = S, v.useSyncExternalStore = ht, v.useTransition = pt, v.version = pe, typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ < "u" && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop == "function" && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());
    })();
  }(Ke, Ke.exports)), Ke.exports;
}
false ? Vr.exports = Nt() : Vr.exports = Mt();
var Nr = Vr.exports;
var He = {};
var dt;
function Ut() {
  return dt || (dt = 1, function() {
    var Y = Nr, v = Symbol.for("react.element"), pe = Symbol.for("react.portal"), G = Symbol.for("react.fragment"), ne = Symbol.for("react.strict_mode"), ee = Symbol.for("react.profiler"), H = Symbol.for("react.provider"), K = Symbol.for("react.context"), D = Symbol.for("react.forward_ref"), J = Symbol.for("react.suspense"), I = Symbol.for("react.suspense_list"), W = Symbol.for("react.memo"), X = Symbol.for("react.lazy"), k = Symbol.for("react.offscreen"), fe = Symbol.iterator, Ie = "@@iterator";
    function ae(t) {
      if (t === null || typeof t != "object")
        return null;
      var i = fe && t[fe] || t[Ie];
      return typeof i == "function" ? i : null;
    }
    var oe = Y.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    function L(t) {
      {
        for (var i = arguments.length, f = new Array(i > 1 ? i - 1 : 0), c = 1; c < i; c++)
          f[c - 1] = arguments[c];
        he("error", t, f);
      }
    }
    function he(t, i, f) {
      {
        var c = oe.ReactDebugCurrentFrame, R = c.getStackAddendum();
        R !== "" && (i += "%s", f = f.concat([R]));
        var S = f.map(function(b) {
          return String(b);
        });
        S.unshift("Warning: " + i), Function.prototype.apply.call(console[t], console, S);
      }
    }
    var ie = false, N = false, Q = false, ce = false, ve = false, Re;
    Re = Symbol.for("react.module.reference");
    function Ce(t) {
      return !!(typeof t == "string" || typeof t == "function" || t === G || t === ee || ve || t === ne || t === J || t === I || ce || t === k || ie || N || Q || typeof t == "object" && t !== null && (t.$$typeof === X || t.$$typeof === W || t.$$typeof === H || t.$$typeof === K || t.$$typeof === D || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      t.$$typeof === Re || t.getModuleId !== void 0));
    }
    function Ue(t, i, f) {
      var c = t.displayName;
      if (c)
        return c;
      var R = i.displayName || i.name || "";
      return R !== "" ? f + "(" + R + ")" : f;
    }
    function we(t) {
      return t.displayName || "Context";
    }
    function Z(t) {
      if (t == null)
        return null;
      if (typeof t.tag == "number" && L("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof t == "function")
        return t.displayName || t.name || null;
      if (typeof t == "string")
        return t;
      switch (t) {
        case G:
          return "Fragment";
        case pe:
          return "Portal";
        case ee:
          return "Profiler";
        case ne:
          return "StrictMode";
        case J:
          return "Suspense";
        case I:
          return "SuspenseList";
      }
      if (typeof t == "object")
        switch (t.$$typeof) {
          case K:
            var i = t;
            return we(i) + ".Consumer";
          case H:
            var f = t;
            return we(f._context) + ".Provider";
          case D:
            return Ue(t, t.render, "ForwardRef");
          case W:
            var c = t.displayName || null;
            return c !== null ? c : Z(t.type) || "Memo";
          case X: {
            var R = t, S = R._payload, b = R._init;
            try {
              return Z(b(S));
            } catch {
              return null;
            }
          }
        }
      return null;
    }
    var re = Object.assign, B = 0, ue, d, le, Se, n, s, h;
    function C() {
    }
    C.__reactDisabledLog = true;
    function _() {
      {
        if (B === 0) {
          ue = console.log, d = console.info, le = console.warn, Se = console.error, n = console.group, s = console.groupCollapsed, h = console.groupEnd;
          var t = {
            configurable: true,
            enumerable: true,
            value: C,
            writable: true
          };
          Object.defineProperties(console, {
            info: t,
            log: t,
            warn: t,
            error: t,
            group: t,
            groupCollapsed: t,
            groupEnd: t
          });
        }
        B++;
      }
    }
    function P() {
      {
        if (B--, B === 0) {
          var t = {
            configurable: true,
            enumerable: true,
            writable: true
          };
          Object.defineProperties(console, {
            log: re({}, t, {
              value: ue
            }),
            info: re({}, t, {
              value: d
            }),
            warn: re({}, t, {
              value: le
            }),
            error: re({}, t, {
              value: Se
            }),
            group: re({}, t, {
              value: n
            }),
            groupCollapsed: re({}, t, {
              value: s
            }),
            groupEnd: re({}, t, {
              value: h
            })
          });
        }
        B < 0 && L("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
      }
    }
    var j = oe.ReactCurrentDispatcher, T;
    function w(t, i, f) {
      {
        if (T === void 0)
          try {
            throw Error();
          } catch (R) {
            var c = R.stack.trim().match(/\n( *(at )?)/);
            T = c && c[1] || "";
          }
        return `
` + T + t;
      }
    }
    var U = false, me;
    {
      var yr = typeof WeakMap == "function" ? WeakMap : Map;
      me = new yr();
    }
    function Je(t, i) {
      if (!t || U)
        return "";
      {
        var f = me.get(t);
        if (f !== void 0)
          return f;
      }
      var c;
      U = true;
      var R = Error.prepareStackTrace;
      Error.prepareStackTrace = void 0;
      var S;
      S = j.current, j.current = null, _();
      try {
        if (i) {
          var b = function() {
            throw Error();
          };
          if (Object.defineProperty(b.prototype, "props", {
            set: function() {
              throw Error();
            }
          }), typeof Reflect == "object" && Reflect.construct) {
            try {
              Reflect.construct(b, []);
            } catch (se) {
              c = se;
            }
            Reflect.construct(t, [], b);
          } else {
            try {
              b.call();
            } catch (se) {
              c = se;
            }
            t.call(b.prototype);
          }
        } else {
          try {
            throw Error();
          } catch (se) {
            c = se;
          }
          t();
        }
      } catch (se) {
        if (se && c && typeof se.stack == "string") {
          for (var m = se.stack.split(`
`), z = c.stack.split(`
`), F = m.length - 1, V = z.length - 1; F >= 1 && V >= 0 && m[F] !== z[V]; )
            V--;
          for (; F >= 1 && V >= 0; F--, V--)
            if (m[F] !== z[V]) {
              if (F !== 1 || V !== 1)
                do
                  if (F--, V--, V < 0 || m[F] !== z[V]) {
                    var te = `
` + m[F].replace(" at new ", " at ");
                    return t.displayName && te.includes("<anonymous>") && (te = te.replace("<anonymous>", t.displayName)), typeof t == "function" && me.set(t, te), te;
                  }
                while (F >= 1 && V >= 0);
              break;
            }
        }
      } finally {
        U = false, j.current = S, P(), Error.prepareStackTrace = R;
      }
      var Ee = t ? t.displayName || t.name : "", ur = Ee ? w(Ee) : "";
      return typeof t == "function" && me.set(t, ur), ur;
    }
    function Fe(t, i, f) {
      return Je(t, false);
    }
    function hr(t) {
      var i = t.prototype;
      return !!(i && i.isReactComponent);
    }
    function $e(t, i, f) {
      if (t == null)
        return "";
      if (typeof t == "function")
        return Je(t, hr(t));
      if (typeof t == "string")
        return w(t);
      switch (t) {
        case J:
          return w("Suspense");
        case I:
          return w("SuspenseList");
      }
      if (typeof t == "object")
        switch (t.$$typeof) {
          case D:
            return Fe(t.render);
          case W:
            return $e(t.type, i, f);
          case X: {
            var c = t, R = c._payload, S = c._init;
            try {
              return $e(S(R), i, f);
            } catch {
            }
          }
        }
      return "";
    }
    var Te = Object.prototype.hasOwnProperty, Oe = {}, Xe = oe.ReactDebugCurrentFrame;
    function Pe(t) {
      if (t) {
        var i = t._owner, f = $e(t.type, t._source, i ? i.type : null);
        Xe.setExtraStackFrame(f);
      } else
        Xe.setExtraStackFrame(null);
    }
    function de(t, i, f, c, R) {
      {
        var S = Function.call.bind(Te);
        for (var b in t)
          if (S(t, b)) {
            var m = void 0;
            try {
              if (typeof t[b] != "function") {
                var z = Error((c || "React class") + ": " + f + " type `" + b + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof t[b] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw z.name = "Invariant Violation", z;
              }
              m = t[b](i, b, c, f, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
            } catch (F) {
              m = F;
            }
            m && !(m instanceof Error) && (Pe(R), L("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", c || "React class", f, b, typeof m), Pe(null)), m instanceof Error && !(m.message in Oe) && (Oe[m.message] = true, Pe(R), L("Failed %s type: %s", f, m.message), Pe(null));
          }
      }
    }
    var ke = Array.isArray;
    function Le(t) {
      return ke(t);
    }
    function Qe(t) {
      {
        var i = typeof Symbol == "function" && Symbol.toStringTag, f = i && t[Symbol.toStringTag] || t.constructor.name || "Object";
        return f;
      }
    }
    function Ze(t) {
      try {
        return Ve(t), false;
      } catch {
        return true;
      }
    }
    function Ve(t) {
      return "" + t;
    }
    function Ye(t) {
      if (Ze(t))
        return L("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", Qe(t)), Ve(t);
    }
    var ge = oe.ReactCurrentOwner, mr = {
      key: true,
      ref: true,
      __self: true,
      __source: true
    }, er, rr, je;
    je = {};
    function gr(t) {
      if (Te.call(t, "ref")) {
        var i = Object.getOwnPropertyDescriptor(t, "ref").get;
        if (i && i.isReactWarning)
          return false;
      }
      return t.ref !== void 0;
    }
    function _r(t) {
      if (Te.call(t, "key")) {
        var i = Object.getOwnPropertyDescriptor(t, "key").get;
        if (i && i.isReactWarning)
          return false;
      }
      return t.key !== void 0;
    }
    function br(t, i) {
      if (typeof t.ref == "string" && ge.current && i && ge.current.stateNode !== i) {
        var f = Z(ge.current.type);
        je[f] || (L('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref', Z(ge.current.type), t.ref), je[f] = true);
      }
    }
    function _e(t, i) {
      {
        var f = function() {
          er || (er = true, L("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", i));
        };
        f.isReactWarning = true, Object.defineProperty(t, "key", {
          get: f,
          configurable: true
        });
      }
    }
    function tr(t, i) {
      {
        var f = function() {
          rr || (rr = true, L("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", i));
        };
        f.isReactWarning = true, Object.defineProperty(t, "ref", {
          get: f,
          configurable: true
        });
      }
    }
    var Er = function(t, i, f, c, R, S, b) {
      var m = {
        // This tag allows us to uniquely identify this as a React Element
        $$typeof: v,
        // Built-in properties that belong on the element
        type: t,
        key: i,
        ref: f,
        props: b,
        // Record the component responsible for creating this element.
        _owner: S
      };
      return m._store = {}, Object.defineProperty(m._store, "validated", {
        configurable: false,
        enumerable: false,
        writable: true,
        value: false
      }), Object.defineProperty(m, "_self", {
        configurable: false,
        enumerable: false,
        writable: false,
        value: c
      }), Object.defineProperty(m, "_source", {
        configurable: false,
        enumerable: false,
        writable: false,
        value: R
      }), Object.freeze && (Object.freeze(m.props), Object.freeze(m)), m;
    };
    function Rr(t, i, f, c, R) {
      {
        var S, b = {}, m = null, z = null;
        f !== void 0 && (Ye(f), m = "" + f), _r(i) && (Ye(i.key), m = "" + i.key), gr(i) && (z = i.ref, br(i, R));
        for (S in i)
          Te.call(i, S) && !mr.hasOwnProperty(S) && (b[S] = i[S]);
        if (t && t.defaultProps) {
          var F = t.defaultProps;
          for (S in F)
            b[S] === void 0 && (b[S] = F[S]);
        }
        if (m || z) {
          var V = typeof t == "function" ? t.displayName || t.name || "Unknown" : t;
          m && _e(b, V), z && tr(b, V);
        }
        return Er(t, m, z, R, c, ge.current, b);
      }
    }
    var Ne = oe.ReactCurrentOwner, nr = oe.ReactDebugCurrentFrame;
    function ye(t) {
      if (t) {
        var i = t._owner, f = $e(t.type, t._source, i ? i.type : null);
        nr.setExtraStackFrame(f);
      } else
        nr.setExtraStackFrame(null);
    }
    var Ae;
    Ae = false;
    function be(t) {
      return typeof t == "object" && t !== null && t.$$typeof === v;
    }
    function xe() {
      {
        if (Ne.current) {
          var t = Z(Ne.current.type);
          if (t)
            return `

Check the render method of \`` + t + "`.";
        }
        return "";
      }
    }
    function Cr(t) {
      {
        if (t !== void 0) {
          var i = t.fileName.replace(/^.*[\\\/]/, ""), f = t.lineNumber;
          return `

Check your code at ` + i + ":" + f + ".";
        }
        return "";
      }
    }
    var ar = {};
    function wr(t) {
      {
        var i = xe();
        if (!i) {
          var f = typeof t == "string" ? t : t.displayName || t.name;
          f && (i = `

Check the top-level render call using <` + f + ">.");
        }
        return i;
      }
    }
    function or(t, i) {
      {
        if (!t._store || t._store.validated || t.key != null)
          return;
        t._store.validated = true;
        var f = wr(i);
        if (ar[f])
          return;
        ar[f] = true;
        var c = "";
        t && t._owner && t._owner !== Ne.current && (c = " It was passed a child from " + Z(t._owner.type) + "."), ye(t), L('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', f, c), ye(null);
      }
    }
    function ir(t, i) {
      {
        if (typeof t != "object")
          return;
        if (Le(t))
          for (var f = 0; f < t.length; f++) {
            var c = t[f];
            be(c) && or(c, i);
          }
        else if (be(t))
          t._store && (t._store.validated = true);
        else if (t) {
          var R = ae(t);
          if (typeof R == "function" && R !== t.entries)
            for (var S = R.call(t), b; !(b = S.next()).done; )
              be(b.value) && or(b.value, i);
        }
      }
    }
    function De(t) {
      {
        var i = t.type;
        if (i == null || typeof i == "string")
          return;
        var f;
        if (typeof i == "function")
          f = i.propTypes;
        else if (typeof i == "object" && (i.$$typeof === D || // Note: Memo only checks outer props here.
        // Inner props are checked in the reconciler.
        i.$$typeof === W))
          f = i.propTypes;
        else
          return;
        if (f) {
          var c = Z(i);
          de(f, t.props, "prop", c, t);
        } else if (i.PropTypes !== void 0 && !Ae) {
          Ae = true;
          var R = Z(i);
          L("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", R || "Unknown");
        }
        typeof i.getDefaultProps == "function" && !i.getDefaultProps.isReactClassApproved && L("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
      }
    }
    function Be(t) {
      {
        for (var i = Object.keys(t.props), f = 0; f < i.length; f++) {
          var c = i[f];
          if (c !== "children" && c !== "key") {
            ye(t), L("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", c), ye(null);
            break;
          }
        }
        t.ref !== null && (ye(t), L("Invalid attribute `ref` supplied to `React.Fragment`."), ye(null));
      }
    }
    function ze(t, i, f, c, R, S) {
      {
        var b = Ce(t);
        if (!b) {
          var m = "";
          (t === void 0 || typeof t == "object" && t !== null && Object.keys(t).length === 0) && (m += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var z = Cr(R);
          z ? m += z : m += xe();
          var F;
          t === null ? F = "null" : Le(t) ? F = "array" : t !== void 0 && t.$$typeof === v ? (F = "<" + (Z(t.type) || "Unknown") + " />", m = " Did you accidentally export a JSX literal instead of a component?") : F = typeof t, L("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", F, m);
        }
        var V = Rr(t, i, f, R, S);
        if (V == null)
          return V;
        if (b) {
          var te = i.children;
          if (te !== void 0)
            if (c)
              if (Le(te)) {
                for (var Ee = 0; Ee < te.length; Ee++)
                  ir(te[Ee], t);
                Object.freeze && Object.freeze(te);
              } else
                L("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else
              ir(te, t);
        }
        return t === G ? Be(V) : De(V), V;
      }
    }
    function Sr(t, i, f) {
      return ze(t, i, f, true);
    }
    function Tr(t, i, f) {
      return ze(t, i, f, false);
    }
    var Or = Tr, Pr = Sr;
    He.Fragment = G, He.jsx = Or, He.jsxs = Pr;
  }()), He;
}
false ? Lr.exports = Wt() : Lr.exports = Ut();
var Yt = Lr.exports;
var Bt = Yt.jsx;
function zt(...Y) {
  return Y.filter(Boolean).map((v) => v.trim()).join(" ");
}
var qt = Nr.forwardRef(
  ({
    icon: Y,
    onClick: v,
    as: pe,
    weight: G,
    fill: ne = false,
    grade: ee,
    size: H,
    style: K,
    color: D,
    className: J,
    ...I
  }, W) => {
    const X = v !== void 0 ? "button" : pe ?? "span", k = { color: D, ...K };
    return ne && (k.fontVariationSettings = [k.fontVariationSettings, '"FILL" 1'].filter(Boolean).join(", ")), G && (k.fontVariationSettings = [k.fontVariationSettings, `"wght" ${G}`].filter(Boolean).join(", ")), ee && (k.fontVariationSettings = [k.fontVariationSettings, `"GRAD" ${ee}`].filter(Boolean).join(", ")), H && (k.fontVariationSettings = [k.fontVariationSettings, `"opsz" ${H}`].filter(Boolean).join(", "), k.fontSize = H), Bt(
      X,
      {
        ...I,
        ref: W,
        style: k,
        onClick: v,
        className: zt("material-symbols", J),
        children: Y
      }
    );
  }
);
export {
  qt as MaterialSymbol
};
/*! Bundled license information:

react-material-symbols/dist/index.es.js:
  (**
   * @license React
   * react.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-material-symbols/dist/index.es.js:
  (**
   * @license React
   * react.development.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
  (**
   * @license React
   * react-jsx-runtime.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-material-symbols/dist/index.es.js:
  (**
   * @license React
   * react-jsx-runtime.development.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=react-material-symbols.js.map
