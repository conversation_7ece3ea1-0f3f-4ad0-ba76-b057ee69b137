import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateProductTable1747793781803 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}products`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    // add isDeleted column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'isDeleted',
        type: 'boolean',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // remove isDeleted column
    await queryRunner.dropColumn(this.TABLE_NAME, 'isDeleted');
  }
}
