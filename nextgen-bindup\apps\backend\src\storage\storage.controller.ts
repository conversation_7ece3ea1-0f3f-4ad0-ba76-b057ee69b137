import { Body, Controller, Post } from '@nestjs/common';
import { StorageService } from './storage.service';
import {
  SignUploadDto,
  SignUploadRequestDto,
} from 'src/supabase/dto/sign-upload.dto';

@Controller('storage')
export class StorageController {
  constructor(private readonly storageService: StorageService) {}

  @Post('create-signed-upload-url')
  async createSignedUploadUrl(
    @Body() data: SignUploadRequestDto,
  ): Promise<SignUploadDto> {
    return await this.storageService.createSignedUploadUrl(data.filepath);
  }
}
