import { MigrationInterface, QueryRunner, TableIndex } from 'typeorm';

export class UpdateSitesTable1738652412289 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}sites`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'IDX_sites_projectId',
        columnNames: ['projectId'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_sites_projectId');
  }
}
