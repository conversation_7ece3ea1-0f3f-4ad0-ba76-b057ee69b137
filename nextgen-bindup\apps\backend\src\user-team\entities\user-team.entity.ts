import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('user_team', { schema: process.env.DATABASE_SCHEMA })
export class UserTeamEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'rootUserId',
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  rootUserId: string;

  @Column({
    name: 'userId',
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  userId: string;

  @Column({
    name: 'email',
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  email: string;

  @Column({
    name: 'teamId',
    type: 'integer',
    nullable: true,
  })
  teamId: number;

  @Column({
    name: 'isAdmin',
    type: 'boolean',
    nullable: true,
  })
  isAdmin: boolean;
}
