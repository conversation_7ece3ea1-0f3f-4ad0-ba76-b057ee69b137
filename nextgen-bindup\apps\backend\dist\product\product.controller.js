"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductController = void 0;
const common_1 = require("@nestjs/common");
const product_service_1 = require("./product.service");
const product_entity_1 = require("./entities/product.entity");
const get_product_dto_1 = require("./dto/get-product.dto");
const file_util_1 = require("../utils/file.util");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
let ProductController = class ProductController {
    constructor(productService) {
        this.productService = productService;
    }
    async downloadTemplate(res) {
        const csvStream = await this.productService.getCSVTemplate();
        res.header('Content-Type', 'text/csv');
        res.header('Content-Disposition', 'attachment; filename="product_template.csv"');
        res.header('Cache-Control', 'no-cache');
        csvStream.pipe(res);
    }
    async uploadCSV(siteId, file) {
        return await this.productService.uploadCSV(+siteId, file);
    }
    async downloadCsv(siteId, query, res) {
        const csvStream = await this.productService.downloadCSV(+siteId, query);
        res.header('Content-Type', 'text/csv');
        res.header('Content-Disposition', `attachment; filename="products_${siteId}_${new Date().toISOString().slice(0, 10)}.csv"`);
        res.header('Cache-Control', 'no-cache');
        csvStream.pipe(res);
    }
    async getAll(siteId) {
        console.log('siteId', siteId);
        return await this.productService.getAllProducts(siteId);
    }
    async getList(siteId, query) {
        return await this.productService.searchProducts(siteId, query.page, query.limit, query.search, query.productType, query.isOrderable);
    }
    async create(productEntity) {
        return await this.productService.create(productEntity);
    }
    async update(id, data) {
        return await this.productService.update(+id, data);
    }
    async getById(id) {
        return await this.productService.findById(+id);
    }
    async duplicate(id) {
        return await this.productService.duplicate(+id);
    }
    async delete(id) {
        return await this.productService.delete(+id);
    }
};
exports.ProductController = ProductController;
__decorate([
    (0, common_1.Get)('template'),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "downloadTemplate", null);
__decorate([
    (0, common_1.Post)('upload/:siteId'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        storage: (0, multer_1.diskStorage)({
            destination: (req, file, cb) => {
                (0, file_util_1.ensureFolderExists)('./upload');
                cb(null, './upload');
            },
            filename: (req, file, callback) => {
                const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
                callback(null, `${uniqueSuffix}${(0, path_1.extname)(file.originalname)}`);
            },
        }),
        fileFilter: (req, file, callback) => {
            if (file.mimetype !== 'text/csv') {
                return callback(new common_1.BadRequestException('Invalid file type'), false);
            }
            callback(null, true);
        },
    })),
    __param(0, (0, common_1.Param)('siteId')),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "uploadCSV", null);
__decorate([
    (0, common_1.Get)('download/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, get_product_dto_1.GetProductsQueryDto, Object]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "downloadCsv", null);
__decorate([
    (0, common_1.Get)('all/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "getAll", null);
__decorate([
    (0, common_1.Get)('search/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, get_product_dto_1.GetProductsQueryDto]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "getList", null);
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [product_entity_1.ProductEntity]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "update", null);
__decorate([
    (0, common_1.Get)('one/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "getById", null);
__decorate([
    (0, common_1.Post)('duplicate/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "duplicate", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "delete", null);
exports.ProductController = ProductController = __decorate([
    (0, common_1.Controller)('product'),
    __metadata("design:paramtypes", [product_service_1.ProductService])
], ProductController);
//# sourceMappingURL=product.controller.js.map