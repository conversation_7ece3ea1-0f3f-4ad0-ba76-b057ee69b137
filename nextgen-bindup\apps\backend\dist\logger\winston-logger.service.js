"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WinstonLoggerService = void 0;
const common_1 = require("@nestjs/common");
const winston = require("winston");
let <PERSON>LoggerService = class WinstonLoggerService {
    constructor() {
        const wlFormatter = winston.format(info => {
            if (info.meta) {
                const { meta, ...others } = info.meta;
                Object.assign(info, others, { meta });
            }
            return info;
        })();
        this.logger = winston.createLogger({
            level: process.env.LOG_LEVEL || 'info',
            format: winston.format.combine(winston.format.timestamp({ format: 'YYYY/MM/DD HH:mm:ss' }), wlFormatter, winston.format.json()),
            transports: [new winston.transports.Console()],
        });
    }
    info(logData) {
        this.logger.info(logData);
    }
    error(logData) {
        this.logger.error(logData);
    }
    warn(logData) {
        this.logger.warn(logData);
    }
    debug(logData) {
        this.logger.debug(logData);
    }
};
exports.WinstonLoggerService = WinstonLoggerService;
exports.WinstonLoggerService = WinstonLoggerService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], WinstonLoggerService);
//# sourceMappingURL=winston-logger.service.js.map