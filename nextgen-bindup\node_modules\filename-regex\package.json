{"name": "filename-regex", "description": "Regular expression for matching file names, with or without extension.", "version": "2.0.1", "homepage": "https://github.com/regexhq/filename-regex", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "regexhq/filename-regex", "bugs": {"url": "https://github.com/regexhq/filename-regex/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "keywords": ["basename", "file", "filename", "filepath", "match", "name", "path", "regex", "regexp", "regular expression"], "devDependencies": {"gulp-format-md": "^0.1.12"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}}