import { FC, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Stack,
  Typography,
  DialogContent,
  TextField,
  DialogActions,
  Button,
} from '@mui/material';
import { ProjectFolderEntity } from '../../../dto/project-folder.type';

export const ProjectFolderForm: FC<{
  isEditing: boolean;
  initialProjectFolder: ProjectFolderEntity;
  handleInputChange: (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void;
  onCreate: () => void;
  onUpdate: () => void;
  onCancel: () => void;
}> = ({
  isEditing,
  initialProjectFolder,
  handleInputChange,
  onCreate,
  onUpdate,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [projectFolder, setProjectFolder] = useState(initialProjectFolder);

  useEffect(() => {
    setProjectFolder(initialProjectFolder);
  }, [initialProjectFolder]);

  return (
    <Stack sx={{ width: '600px' }}>
      <Typography
        sx={{ paddingBlock: 2, paddingInline: 3 }}
        variant="subtitle2"
        color="textPrimary"
      >
        {t('project.folder_form.title')}
      </Typography>
      <DialogContent sx={{ paddingBlock: 1 }}>
        <Stack direction={'column'} spacing={2}>
          <TextField
            id="name"
            name="name"
            label={t('project.folder_form.name')}
            variant="filled"
            onChange={handleInputChange}
            value={projectFolder.name}
            size="small"
          />
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button variant="text" onClick={onCancel}>
          {t('common.cancel')}
        </Button>
        <Button
          color="inherit"
          variant="outlined"
          onClick={isEditing ? onUpdate : onCreate}
        >
          {isEditing ? t('common.update') : t('common.create')}
        </Button>
      </DialogActions>
    </Stack>
  );
};
