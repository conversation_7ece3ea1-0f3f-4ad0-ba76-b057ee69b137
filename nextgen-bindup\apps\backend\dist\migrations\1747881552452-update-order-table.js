"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderTable1747881552452 = void 0;
const typeorm_1 = require("typeorm");
class UpdateOrderTable1747881552452 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}orders`;
    }
    async up(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'totalAmount');
        await queryRunner.dropColumn(this.TABLE_NAME, 'stripeFee');
        await queryRunner.dropColumn(this.TABLE_NAME, 'netAmount');
        await queryRunner.dropColumn(this.TABLE_NAME, 'shippingFee');
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'subtotal',
            type: 'bigint',
            isNullable: true,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'paymentGatewayFee',
            type: 'bigint',
            isNullable: true,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'total',
            type: 'bigint',
            isNullable: true,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'shopNetPayout',
            type: 'bigint',
            isNullable: true,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'paymentMethodType',
            type: 'varchar',
            length: '50',
            isNullable: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'totalAmount',
            type: 'bigint',
            isNullable: true,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'stripeFee',
            type: 'bigint',
            isNullable: true,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'netAmount',
            type: 'bigint',
            isNullable: true,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'shippingFee',
            type: 'bigint',
            isNullable: true,
        }));
        await queryRunner.dropColumn(this.TABLE_NAME, 'subtotal');
        await queryRunner.dropColumn(this.TABLE_NAME, 'paymentGatewayFee');
        await queryRunner.dropColumn(this.TABLE_NAME, 'total');
        await queryRunner.dropColumn(this.TABLE_NAME, 'paymentMethodType');
        await queryRunner.dropColumn(this.TABLE_NAME, 'shopNetPayout');
    }
}
exports.UpdateOrderTable1747881552452 = UpdateOrderTable1747881552452;
//# sourceMappingURL=1747881552452-update-order-table.js.map