"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckStockResponse = exports.CartItemResponse = exports.CartItem = exports.StockErrorType = void 0;
var StockErrorType;
(function (StockErrorType) {
    StockErrorType["OUT_OF_STOCK"] = "OUT_OF_STOCK";
    StockErrorType["EXCEED_PURCHASE_LIMIT"] = "EXCEED_PURCHASE_LIMIT";
    StockErrorType["NOT_ORDERABLE"] = "NOT_ORDERABLE";
})(StockErrorType || (exports.StockErrorType = StockErrorType = {}));
class CartItem {
}
exports.CartItem = CartItem;
class CartItemResponse {
}
exports.CartItemResponse = CartItemResponse;
class CheckStockResponse {
}
exports.CheckStockResponse = CheckStockResponse;
//# sourceMappingURL=product-stock.dto.js.map