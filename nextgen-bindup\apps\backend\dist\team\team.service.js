"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeamService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const team_entity_1 = require("./entities/team.entity");
const app_exception_1 = require("../common/exceptions/app.exception");
let TeamService = class TeamService {
    constructor() { }
    async findById(id) {
        return await this.teamRepo.findOneBy({ id });
    }
    async getAllByRootUserId(rootUserId) {
        return await this.teamRepo.find({ where: { rootUserId } });
    }
    async create(userId, data) {
        delete data.id;
        const now = new Date();
        data.createdAt = now;
        data.updatedAt = now;
        data.rootUserId = userId;
        return await this.teamRepo.save(data);
    }
    async update(id, userId, data) {
        const team = await this.teamRepo.findOneBy({ id: id, rootUserId: userId });
        if (!team)
            throw new app_exception_1.AppException('api.error.team_not_found');
        data.updatedAt = new Date();
        await this.teamRepo.update({ id }, data);
        return { ...team, ...data };
    }
};
exports.TeamService = TeamService;
__decorate([
    (0, typeorm_1.InjectRepository)(team_entity_1.TeamEntity),
    __metadata("design:type", typeorm_2.Repository)
], TeamService.prototype, "teamRepo", void 0);
exports.TeamService = TeamService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], TeamService);
//# sourceMappingURL=team.service.js.map