import { UserPaymentService } from './user-payment.service';
export declare class UserPaymentController {
    private readonly userPaymentService;
    constructor(userPaymentService: UserPaymentService);
    enableStripe(userId: string): Promise<{
        success: boolean;
        onboardingUrl: string;
    }>;
    checkStripeStatus(userId: string): Promise<{
        connected: boolean;
        id?: string;
        email?: string;
        name?: string;
        type?: string;
        stripeDashboardUrl?: string;
    }>;
    handleUpdateOrderStatus(): void;
}
