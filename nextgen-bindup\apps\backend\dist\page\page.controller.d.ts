import { Response } from 'express';
import { PageService } from './page.service';
import { PageEntity } from './entities/page.entity';
import { PublishService } from 'src/publish/publish.service';
import { ZipService } from './zip.service';
import { JwtPayloadDto } from 'src/auth/dto/auth.dto';
import { CreateBlogRequest } from '@nextgen-bindup/common/dto/blog';
export declare class PageController {
    private readonly pageService;
    private readonly publishService;
    private readonly zipService;
    constructor(pageService: PageService, publishService: PublishService, zipService: ZipService);
    zipAndDownload(projectId: string, siteId: string, res: Response): Promise<void>;
    createPage(user: JwtPayloadDto, projectId: string, pageData: PageEntity): Promise<PageEntity>;
    updatePage(pageId: string, pageData: Partial<PageEntity>): Promise<PageEntity>;
    deletePage(pageId: string): Promise<boolean>;
    getPagesBySiteId(siteId: string): Promise<PageEntity[]>;
    getPageByd(pageId: string): Promise<PageEntity>;
    createBlog(user: JwtPayloadDto, projectId: string, data: CreateBlogRequest): Promise<boolean>;
}
