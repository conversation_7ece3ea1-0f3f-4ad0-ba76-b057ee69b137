import { Injectable } from '@nestjs/common';
import { StyleEntity } from './entities/style.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProjectService } from 'src/project/project.service';
import { AppException } from 'src/common/exceptions/app.exception';

@Injectable()
export class StyleService {
  @InjectRepository(StyleEntity)
  readonly styleRepo: Repository<StyleEntity>;

  constructor(private readonly projectService: ProjectService) {}

  async create(styleEntity: StyleEntity): Promise<StyleEntity> {
    const project = await this.projectService.findById(styleEntity.projectId);
    if (!project) throw new AppException('error.project_not_found');

    const now: Date = new Date();
    const style = new StyleEntity();
    style.type = styleEntity.type;
    style.projectId = styleEntity.projectId;
    style.siteId = styleEntity.siteId;
    style.name = styleEntity.name;
    style.data = styleEntity.data;
    style.ts = styleEntity.ts || 0;
    style.createdAt = now;
    style.updatedAt = now;
    return await this.styleRepo.save(style);
  }

  async update(
    id: number,
    styleData: Partial<StyleEntity>,
  ): Promise<StyleEntity> {
    const style = await this.styleRepo.findOneBy({ id: id });
    if (!style) throw new AppException('error.style_not_found');

    delete styleData.id;
    styleData.updatedAt = new Date();
    await this.styleRepo.update(id, styleData);
    return { ...style, ...styleData };
  }

  async findById(id: number): Promise<StyleEntity> {
    return await this.styleRepo.findOneBy({ id });
  }

  async findByProjectId(projectId: number): Promise<StyleEntity[]> {
    return await this.styleRepo.findBy({ projectId });
  }

  async findBySiteId(
    projectId: number,
    siteId: number,
  ): Promise<StyleEntity[]> {
    return await this.styleRepo.findBy({
      projectId: projectId,
      siteId: siteId,
    });
  }

  async delete(id: number): Promise<boolean> {
    const style: StyleEntity = await this.styleRepo.findOneBy({ id });
    if (!style) throw new AppException('error.style_not_found');

    await this.styleRepo.delete(style.id);
    return true;
  }
}
