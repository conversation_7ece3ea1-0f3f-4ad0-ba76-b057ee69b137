"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsCollectionItemsController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../auth/auth.guard");
const cms_collection_items_service_1 = require("./cms-collection-items.service");
const cms_collection_items_entity_1 = require("./entities/cms-collection-items.entity");
let CmsCollectionItemsController = class CmsCollectionItemsController {
    constructor(collectionItemsService) {
        this.collectionItemsService = collectionItemsService;
    }
    async create(collectionItemEntity) {
        return await this.collectionItemsService.create(collectionItemEntity);
    }
    async update(id, data) {
        return await this.collectionItemsService.update(+id, data);
    }
    async getById(id) {
        return await this.collectionItemsService.findById(+id);
    }
    async getByCollectionId(cmsCollectionId, dto) {
        return await this.collectionItemsService.findByCollectionId(+cmsCollectionId, dto);
    }
    async getBySiteId(siteId) {
        return await this.collectionItemsService.findBySiteId(+siteId);
    }
    async delete(id) {
        return await this.collectionItemsService.delete(+id);
    }
};
exports.CmsCollectionItemsController = CmsCollectionItemsController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [cms_collection_items_entity_1.CmsCollectionItemEntity]),
    __metadata("design:returntype", Promise)
], CmsCollectionItemsController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CmsCollectionItemsController.prototype, "update", null);
__decorate([
    (0, common_1.Get)('one/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CmsCollectionItemsController.prototype, "getById", null);
__decorate([
    (0, common_1.Get)('collection/:cmsCollectionId'),
    __param(0, (0, common_1.Param)('cmsCollectionId')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CmsCollectionItemsController.prototype, "getByCollectionId", null);
__decorate([
    (0, common_1.Get)('site/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CmsCollectionItemsController.prototype, "getBySiteId", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CmsCollectionItemsController.prototype, "delete", null);
exports.CmsCollectionItemsController = CmsCollectionItemsController = __decorate([
    (0, common_1.Controller)('cms-collection-items'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [cms_collection_items_service_1.CmsCollectionItemsService])
], CmsCollectionItemsController);
//# sourceMappingURL=cms-collection-items.controller.js.map