import { ProjectFoldersService } from './project-folders.service';
import { ProjectFolderEntity } from './entities/project-folders.entity';
export declare class ProjectFoldersController {
    private readonly projectFoldersService;
    constructor(projectFoldersService: ProjectFoldersService);
    create(projectFolder: ProjectFolderEntity): Promise<ProjectFolderEntity>;
    getProjectsByUser(projectId: string): Promise<ProjectFolderEntity[]>;
}
