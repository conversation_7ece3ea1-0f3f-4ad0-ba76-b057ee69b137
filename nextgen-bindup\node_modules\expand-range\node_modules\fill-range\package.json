{"name": "fill-range", "description": "Fill in a range of numbers or letters, optionally passing an increment or multiplier to use.", "version": "2.2.4", "homepage": "https://github.com/jonschlinkert/fill-range", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/fill-range", "bugs": {"url": "https://github.com/jonschlinkert/fill-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^2.1.0", "isobject": "^2.0.0", "randomatic": "^3.0.0", "repeat-element": "^1.1.2", "repeat-string": "^1.5.2"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "gulp-format-md": "^1.0.0", "should": "^13.2.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "fill", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "sh"], "verb": {"toc": true, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-range", "is-glob", "micromatch"]}, "lint": {"reflinks": true}, "reflinks": ["micromatch", "randomatic"]}}