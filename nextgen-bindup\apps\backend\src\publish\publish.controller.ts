import { Body, Controller, Param, Post } from '@nestjs/common';
import { PublishService } from './publish.service';
import { PublishPagesReq } from '@nextgen-bindup/common/dto/publish/publish-page.dto';

@Controller('publish')
export class PublishController {
  constructor(private readonly publishService: PublishService) {}

  @Post(':projectId/:siteId')
  async publish(
    @Param('projectId') projectId: string,
    @Param('siteId') siteId: string,
    @Body() req: PublishPagesReq,
  ) {
    req.pageIds = [];
    return await this.publishService.publish(+projectId, +siteId, req);
  }
}
