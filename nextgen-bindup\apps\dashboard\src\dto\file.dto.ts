export interface FileDto {
  id: string;
  name: string;
  type: string;
  source: string;
  size?: number;
  [key: string]: unknown; // Add index signature
}
export interface ImageDto {
  id: string;
  filename: string;
  uploaded: string;
  requireSignedURLs: boolean;
  variants: string[];
}
export function convertToFileDto(file: ImageDto): FileDto {
  return {
    id: file.id,
    name: file.filename,
    type: '', // Assuming type is 'image', adjust if necessary
    source: file.variants[0], // Assuming the first variant is the source, adjust if necessary
  };
}
