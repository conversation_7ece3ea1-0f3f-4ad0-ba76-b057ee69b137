"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShippingNoteSettingEntity = void 0;
const typeorm_1 = require("typeorm");
let ShippingNoteSettingEntity = class ShippingNoteSettingEntity {
};
exports.ShippingNoteSettingEntity = ShippingNoteSettingEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], ShippingNoteSettingEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'siteId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], ShippingNoteSettingEntity.prototype, "siteId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'shippingFee',
        type: 'bigint',
        nullable: true,
    }),
    __metadata("design:type", Number)
], ShippingNoteSettingEntity.prototype, "shippingFee", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'shippingFeeDetail',
        type: 'jsonb',
        nullable: false,
    }),
    __metadata("design:type", Object)
], ShippingNoteSettingEntity.prototype, "shippingFeeDetail", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isFreeShippingCondition',
        type: 'boolean',
        nullable: false,
    }),
    __metadata("design:type", Boolean)
], ShippingNoteSettingEntity.prototype, "isFreeShippingCondition", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'freeShippingCondition',
        type: 'bigint',
        nullable: true,
    }),
    __metadata("design:type", Number)
], ShippingNoteSettingEntity.prototype, "freeShippingCondition", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'note',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], ShippingNoteSettingEntity.prototype, "note", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], ShippingNoteSettingEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], ShippingNoteSettingEntity.prototype, "updatedAt", void 0);
exports.ShippingNoteSettingEntity = ShippingNoteSettingEntity = __decorate([
    (0, typeorm_1.Entity)('shipping_note_settings', { schema: process.env.DATABASE_SCHEMA })
], ShippingNoteSettingEntity);
//# sourceMappingURL=shipping-note--settings.entity.js.map