"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubDomainModule = void 0;
const common_1 = require("@nestjs/common");
const sub_domain_controller_1 = require("./sub-domain.controller");
const sub_domain_service_1 = require("./sub-domain.service");
const sub_domain_entity_1 = require("./entities/sub-domain.entity");
const typeorm_1 = require("@nestjs/typeorm");
let SubDomainModule = class SubDomainModule {
};
exports.SubDomainModule = SubDomainModule;
exports.SubDomainModule = SubDomainModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([sub_domain_entity_1.SubDomainEntity])],
        controllers: [sub_domain_controller_1.SubDomainController],
        providers: [sub_domain_service_1.SubDomainService],
        exports: [sub_domain_service_1.SubDomainService],
    })
], SubDomainModule);
//# sourceMappingURL=sub-domain.module.js.map