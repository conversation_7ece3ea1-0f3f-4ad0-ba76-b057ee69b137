import { MarginPaddingPropDto } from '@nextgen-bindup/common/dto/setting-properties/margin-padding-prop.dto';

export const PROP_SPACING_DEFAULT_VALUE = (
  ts: number,
  prop?: Partial<MarginPaddingPropDto>,
): MarginPaddingPropDto => ({
  margin: {
    top: { value: '0', unit: 'px' },
    left: { value: '0', unit: 'px' },
    right: { value: '0', unit: 'px' },
    bottom: { value: '0', unit: 'px' },
    isDetail: false,
    ts: 0,
  },
  padding: {
    top: { value: '0', unit: 'px' },
    left: { value: '0', unit: 'px' },
    right: { value: '0', unit: 'px' },
    bottom: { value: '0', unit: 'px' },
    isDetail: false,
    ts: 0,
  },
  ts: ts,
  ...(prop || undefined),
});
