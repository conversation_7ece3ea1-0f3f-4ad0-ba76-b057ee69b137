"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTemplatesTable1750649574424 = void 0;
const typeorm_1 = require("typeorm");
class CreateTemplatesTable1750649574424 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}templates`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'description',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'category',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'pages',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateTemplatesTable1750649574424 = CreateTemplatesTable1750649574424;
//# sourceMappingURL=1750649574424-create-template-table.js.map