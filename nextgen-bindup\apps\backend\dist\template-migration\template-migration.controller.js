"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateMigrationController = void 0;
const common_1 = require("@nestjs/common");
const template_migration_service_1 = require("./template-migration.service");
const app_exception_1 = require("../common/exceptions/app.exception");
let TemplateMigrationController = class TemplateMigrationController {
    constructor(templateMigrationService) {
        this.templateMigrationService = templateMigrationService;
    }
    async importTemplate() {
        this.templateMigrationService.importTemplate();
        await this.templateMigrationService.importTemplate();
        await this.templateMigrationService.importPorfolioTemplate();
        await this.templateMigrationService.importContactTemplate();
    }
    async importStructure() {
        const userId = '1a0715d3-4b77-4010-a20b-b01d5c8ce0ec';
        const templateId = await this.templateMigrationService.importTemplateToDB(userId);
        return templateId;
    }
    async createSite(templateId) {
        if (!templateId)
            throw new app_exception_1.AppException('templateId is required');
        const userId = '1a0715d3-4b77-4010-a20b-b01d5c8ce0ec';
        const projectId = Number(process.env.TEST_TEMPLATE_PROJECT_ID);
        const siteId = Number(process.env.TEST_TEMPLATE_SITE_ID);
        await this.templateMigrationService.createSite(Number(templateId), userId, projectId, siteId);
    }
    async importPorfolioTemplate() {
        this.templateMigrationService.importPorfolioTemplate();
    }
    async importContactTemplate() {
        this.templateMigrationService.importContactTemplate();
    }
    async importContactPhoto() {
        this.templateMigrationService.importPhotoTemplate();
    }
    async importProfileTemplate() {
        this.templateMigrationService.importProfileTemplate();
    }
    async importTempTemplate() {
        this.templateMigrationService.importTempTemplate();
    }
};
exports.TemplateMigrationController = TemplateMigrationController;
__decorate([
    (0, common_1.Get)('import'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TemplateMigrationController.prototype, "importTemplate", null);
__decorate([
    (0, common_1.Get)('import-structure'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TemplateMigrationController.prototype, "importStructure", null);
__decorate([
    (0, common_1.Get)('create-site/:templateId'),
    __param(0, (0, common_1.Param)('templateId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TemplateMigrationController.prototype, "createSite", null);
__decorate([
    (0, common_1.Get)('import-portfolio'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TemplateMigrationController.prototype, "importPorfolioTemplate", null);
__decorate([
    (0, common_1.Get)('import-contact'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TemplateMigrationController.prototype, "importContactTemplate", null);
__decorate([
    (0, common_1.Get)('import-photo'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TemplateMigrationController.prototype, "importContactPhoto", null);
__decorate([
    (0, common_1.Get)('import-profile'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TemplateMigrationController.prototype, "importProfileTemplate", null);
__decorate([
    (0, common_1.Get)('import-temp'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TemplateMigrationController.prototype, "importTempTemplate", null);
exports.TemplateMigrationController = TemplateMigrationController = __decorate([
    (0, common_1.Controller)('template-migration'),
    __metadata("design:paramtypes", [template_migration_service_1.TemplateMigrationService])
], TemplateMigrationController);
//# sourceMappingURL=template-migration.controller.js.map