import { CountryCode, Industry, JobType, Occupation, Sex, UserType } from '../enum/user-info.enum';
export declare class UserInfoEntity {
    userId: string;
    name: string;
    type: UserType;
    avatar: string;
    company: string;
    lastNameHira: string;
    firstNameHira: string;
    sex: Sex;
    countryCode: CountryCode;
    postalCode: string;
    prefecture: number;
    municipalities: string;
    street: string;
    building: string;
    phone: string;
    fax: string;
    occupation: Occupation;
    industry: Industry;
    jobType: JobType;
    birthday: Date;
    receiveNewsletter: boolean;
    receiveSupport: boolean;
    receiveDirectMail: boolean;
    createdAt: Date;
    updatedAt: Date;
    recentlySite: number[];
    stripeCustomerId: string;
    activeSubscription: boolean;
    stripeAccountId: string;
}
