import { AssetComponent } from './entities/asset-component.entity';
import { Repository } from 'typeorm';
import { ProjectService } from 'src/project/project.service';
export declare class AssetComponentService {
    private readonly projectService;
    readonly assetComponentRepository: Repository<AssetComponent>;
    constructor(projectService: ProjectService);
    create(assetComponent: AssetComponent): Promise<AssetComponent>;
    update(id: string, assetComponent: Partial<AssetComponent>): Promise<AssetComponent>;
    findById(id: string): Promise<AssetComponent>;
    findByProjectId(projectId: number): Promise<AssetComponent[]>;
    findBySiteId(projectId: number, siteId: number): Promise<AssetComponent[]>;
    delete(id: string): Promise<boolean>;
}
