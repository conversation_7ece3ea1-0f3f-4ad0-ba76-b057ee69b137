import { type FC, type ReactNode } from 'react';
import Box from '@mui/material/Box';

type Props = {
  children?: ReactNode;
  index: number;
  value: number;
};

const CustomTabPanel: FC<Props> = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`simple-tabpanel-${index}`}
    aria-labelledby={`simple-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

export default CustomTabPanel;
