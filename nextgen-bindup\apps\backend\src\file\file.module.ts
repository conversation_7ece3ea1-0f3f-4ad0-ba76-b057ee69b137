import { Modu<PERSON> } from '@nestjs/common';
import { FileController } from './file.controller';
import { FileService } from './file.service';
import { MulterModule } from '@nestjs/platform-express';
import { R2StorageService } from './r2-storage.service';

@Module({
  imports: [
    MulterModule.register({
      dest: './upload',
    }),
  ],
  controllers: [FileController],
  providers: [FileService, R2StorageService],
})
export class FileModule {}
