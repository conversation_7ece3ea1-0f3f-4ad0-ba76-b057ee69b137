import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { TeamService } from './team.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { JwtPayloadDto } from 'src/auth/dto/auth.dto';
import { ExtractUser } from 'src/auth/user.decorator';
import { TeamEntity } from './entities/team.entity';

@Controller('team')
@UseGuards(AuthGuard)
export class TeamController {
  constructor(private readonly teamService: TeamService) {}

  @Get('one/:id')
  async findById(@Param('id') id: string) {
    return await this.teamService.findById(+id);
  }

  @Get('my-teams')
  async myTeam(@ExtractUser() user: JwtPayloadDto) {
    return await this.teamService.getAllByRootUserId(user.userId);
  }

  @Post('create')
  async create(@ExtractUser() user: JwtPayloadDto, @Body() data: TeamEntity) {
    return await this.teamService.create(user.userId, data);
  }

  @Put('update/:id')
  async update(
    @ExtractUser() user: JwtPayloadDto,
    @Body() data: Partial<TeamEntity>,
    @Param('id') id: string,
  ) {
    return await this.teamService.update(+id, user.userId, data);
  }
}
