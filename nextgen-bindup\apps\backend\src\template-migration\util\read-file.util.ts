import { Site0_Site } from '../dto/site0_site.dto';
import * as fs from 'fs';
import { Site1_Corner } from '../dto/site1_corner.dto';
import { Site2_Page } from '../dto/site2_page.dto';
import { Site3_Block } from '../dto/site3_block.dto';
import { Site4_BlockData } from '../dto/site4_blockdata.dto';
import { Site5_Resource } from '../dto/site5_resource.dto';
import { Site6_Srclist1 } from '../dto/site6_srclist1.dto';
import { SiteSharedCss } from '../dto/site_shared_css.dto';
import { Template_Site } from '../dto/template_site.dto';

// eslint-disable-next-line @typescript-eslint/no-require-imports
const unescapeJs = require('unescape-js');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const decode = require('unescape');
// eslint-disable-next-line @typescript-eslint/no-require-imports
//const parser = require('xml2json');

// Custom decode function that preserves &nbsp;
const decodePreserveNbsp = (content: string): string => {
  // Temporarily replace &nbsp; with a unique placeholder
  const placeholder = '___NBSP_PLACEHOLDER___';
  const processedContent = content.replace(/&nbsp;/g, placeholder);

  // Decode the content with the placeholder
  let decoded = decode(processedContent);

  // Restore &nbsp; from the placeholder
  decoded = decoded.replace(new RegExp(placeholder, 'g'), '&nbsp;');

  return decoded;
};

export class ReadFileUtil {
  static readSharedCss(dbdata: string): SiteSharedCss[] {
    const sharedCssList: SiteSharedCss[] = JSON.parse(
      fs.readFileSync(`${dbdata}/site_shared_css.json`, {
        encoding: 'utf8',
      }),
    ) as SiteSharedCss[];

    for (const sharedCss of sharedCssList) {
      sharedCss.dataJson = JSON.parse(sharedCss.data);
      delete sharedCss.data;
    }

    return sharedCssList;
  }

  static readSites(dbdata: string): Site0_Site[] {
    const sites: Site0_Site[] = JSON.parse(
      fs.readFileSync(`${dbdata}/site0_site.json`, {
        encoding: 'utf8',
      }),
    ) as Site0_Site[];

    for (const site of sites) {
      site.webfontinfoJson = JSON.parse(site.webfontinfo);
      delete site.webfontinfo;

      site.propsJson = JSON.parse(site.props);
      delete site.props;

      site.openGraphJson = JSON.parse(site.openGraph);
      delete site.openGraph;
    }

    return sites;
  }

  static readCorners(dbdata: string): Site1_Corner[] {
    const corners: Site1_Corner[] = JSON.parse(
      fs.readFileSync(`${dbdata}/site1_corner.json`, {
        encoding: 'utf8',
      }),
    ) as Site1_Corner[];

    for (const corner of corners) {
      corner.infoJson = JSON.parse(corner.info);
      delete corner.info;

      corner.robotsJson = JSON.parse(corner.robots);
      delete corner.robots;
    }

    return corners;
  }

  static readPages(dbdata: string): Site2_Page[] {
    const pages: Site2_Page[] = JSON.parse(
      fs.readFileSync(`${dbdata}/site2_page.json`, {
        encoding: 'utf8',
      }),
    ) as Site2_Page[];

    for (const page of pages) {
      page.name = unescapeJs(page.name);

      page.bgSetsJson = JSON.parse(page.bgSets);
      delete page.bgSets;

      page.headSetsJson = JSON.parse(page.headSets);
      page.headSetsJson.scpt = unescapeJs(page.headSetsJson.scpt || '');
      delete page.headSets;

      page.layoutSetsJson = JSON.parse(page.layoutSets);
      delete page.layoutSets;

      page.robotsJSON = JSON.parse(page.robots);
      delete page.robots;

      page.mobileSetsJson = JSON.parse(page.mobileSets);
      delete page.mobileSets;

      page.cssSetsJson = JSON.parse(page.cssSets);
      delete page.cssSets;

      page.openGraphJson = JSON.parse(page.openGraph);
      delete page.openGraph;

      page.areaFloatJson = JSON.parse(page.areaFloat);
      delete page.areaFloat;
    }

    return pages;
  }

  static readBlocks(dbdata: string): Site3_Block[] {
    const blocks: Site3_Block[] = JSON.parse(
      fs.readFileSync(`${dbdata}/site3_block.json`, {
        encoding: 'utf8',
      }),
    ) as Site3_Block[];

    blocks.sort((a, b) => {
      const v1 = a.pageId * 100000 + a.areaId * 1000 + a.seq;
      const v2 = b.pageId * 100000 + b.areaId * 1000 + b.seq;
      return v1 > v2 ? 1 : v1 < v2 ? -1 : 0;
    });
    return blocks;
  }

  static readBlockDatas(dbdata: string): Site4_BlockData[] {
    const blockDatas: Site4_BlockData[] = JSON.parse(
      fs.readFileSync(`${dbdata}/site4_blockdata.json`, {
        encoding: 'utf8',
      }),
    ) as Site4_BlockData[];

    for (const blockData of blockDatas) {
      blockData.blockdataInfoJson = JSON.parse(blockData.blockdataInfo);
      delete blockData.blockdataInfo;

      blockData.content = decodePreserveNbsp(blockData.content);

      blockData.smartdataJson = JSON.parse(blockData.smartdata);
      delete blockData.smartdata;

      blockData.bgSetsJson = JSON.parse(blockData.bgSets);
      delete blockData.bgSets;
    }

    return blockDatas;
  }

  static readSrcList1(dbdata: string): Site6_Srclist1[] {
    const srcList: Site6_Srclist1[] = JSON.parse(
      fs.readFileSync(`${dbdata}/site6_srclist1.json`, {
        encoding: 'utf8',
      }),
    ) as Site6_Srclist1[];

    for (const src of srcList) {
      delete src.signXml;
      delete src.imageDataThumbnail;
    }

    return srcList;
  }

  static readResources(
    dbdata: string,
    srcList1: Site6_Srclist1[],
  ): Site5_Resource[] {
    const resources: Site5_Resource[] = JSON.parse(
      fs.readFileSync(`${dbdata}/site5_resource.json`, {
        encoding: 'utf8',
      }),
    ) as Site5_Resource[];

    for (const resource of resources) {
      //resource.blockeditIcon = unescapeJs(resource.blockeditIcon || '');
      delete resource.blockeditIcon;

      resource.partsPropertyJson = JSON.parse(resource.partsProperty);
      delete resource.partsProperty;

      resource.srcList = srcList1
        .filter(
          src =>
            src.resourceId === resource.resourceId &&
            src.blockdataId === resource.blockdataId,
        )
        .sort((a, b) => (a.seq > b.seq ? 1 : a.seq < b.seq ? -1 : 0));
    }

    return resources;
  }

  static readTemplateSites(dbdata: string): Template_Site[] {
    const templateSites: Template_Site[] = JSON.parse(
      fs.readFileSync(`${dbdata}/template_site.json`, {
        encoding: 'utf8',
      }),
    ) as Template_Site[];

    for (const templateSite of templateSites) {
      templateSite.pagelistJson = JSON.parse(templateSite.pagelist);
      delete templateSite.pagelist;

      templateSite.previewJson = JSON.parse(templateSite.preview);
      delete templateSite.preview;

      templateSite.downloadJson = JSON.parse(templateSite.download);
      delete templateSite.download;
    }

    return templateSites;
  }
}
