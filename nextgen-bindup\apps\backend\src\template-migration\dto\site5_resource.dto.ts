import { Site6_Srclist1 } from './site6_srclist1.dto';

export enum Resource_PartsType {
  CART = 'cart',
  DOWNLOAD = 'download',
  SHIFT = 'shift',
  TABLE = 'table',
  SYNC = 'sync',
  SIGN = 'sign',
  TAG = 'tag',
  IMAGE = 'image',
  LINK = 'link',
  MOVIE = 'movie',
  ID = 'id',
  OFFICE = 'office',
  LIVE = 'live',
}

export interface Site5_Resource {
  tmpSiteId: number;
  resourceId: number;
  siteId: number;
  blockdataId: number;
  partsType: Resource_PartsType;
  blockeditIcon: string; // base64

  partsProperty: string;
  partsPropertyJson: Resource_PartsProperty;

  delFlg: number;
  insDate: string;
  updDate: string;

  srcList?: Site6_Srclist1[];
}

// https://docs.google.com/spreadsheets/d/124SX9Ds9nNc_EZ1_pY1bfkaTWkcWxLEqTj-a14SEeSg/edit?gid=1715300203#gid=1715300203&range=2:2
export interface Resource_PartsProperty {
  clck: string;
  mdfy: string;
  bodr: string;
  wd: string;
  ht: string;
  algn: string;
  tgnm: string;
  tgwn: string;
  tgwd: string;
  tght: string;
  tgsc: string;
  tgrs: string;
  osize: string;
  alt: string;
  scID: string;
  efct: string;
  ro: string;
  lkon: string;
  cmt: string;
  zmsc: string;
  zmwd: string;
  zmht: string;
  name: string;
  html: string;
  img: string;

  // link
  text: string;
  link: string;
  isrc: string;
  iblw: string;
}
