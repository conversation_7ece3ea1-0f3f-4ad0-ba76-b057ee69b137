import { Repository, DataSource } from 'typeorm';
import { ShopInformationSettingEntity } from './entities/shop-information-settings.entity';
import { SiteService } from 'src/site/site.service';
export declare class ShopInformationSettingService {
    private readonly dataSource;
    private readonly shopInformationSettingRepo;
    private readonly siteService;
    constructor(dataSource: DataSource, shopInformationSettingRepo: Repository<ShopInformationSettingEntity>, siteService: SiteService);
    create(shopInformationSettingEntity: ShopInformationSettingEntity): Promise<ShopInformationSettingEntity>;
    update(id: number, settingData: Partial<ShopInformationSettingEntity>): Promise<ShopInformationSettingEntity>;
    findById(id: number): Promise<ShopInformationSettingEntity>;
    findOneBySiteId(siteId: number): Promise<ShopInformationSettingEntity>;
    delete(id: number): Promise<boolean>;
}
