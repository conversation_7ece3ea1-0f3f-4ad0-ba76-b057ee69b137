import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionActions,
  AccordionDetails,
  AccordionSummary,
  Button,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Typography,
} from '@mui/material';
import { getErrMsg } from '@nextgen-bindup/common/utility';
import { UserInfoEntity } from '../../../dto/user-info.type';
import { userInfoService } from '../../../services/user-info.service';

export const SubscribeNews: FC<{
  userInfoData: UserInfoEntity;
  onUpdate: () => void;
}> = ({ userInfoData, onUpdate }) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errorMsg, setErrorMsg] = useState<string>('');
  const [userInfo, setUserInfo] = useState<UserInfoEntity>(userInfoData);

  const onSave = async () => {
    setIsLoading(true);
    setErrorMsg('');

    try {
      await userInfoService.update({
        userId: userInfo.userId,
        receiveNewsletter: userInfo.receiveNewsletter,
        receiveSupport: userInfo.receiveSupport,
        receiveDirectMail: userInfo.receiveDirectMail,
      });
      onUpdate();
    } catch (e) {
      setErrorMsg(t(getErrMsg(e)));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Accordion defaultExpanded sx={{ width: '100%' }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="h5" color="info">
          {t('myaccount.subscribe.title')}
        </Typography>
      </AccordionSummary>

      <AccordionDetails>
        <FormGroup>
          <FormControlLabel
            sx={{ marginBottom: '1rem' }}
            control={
              <Checkbox
                checked={userInfo.receiveNewsletter || false}
                onChange={e => {
                  setUserInfo({
                    ...userInfo,
                    ...{ receiveNewsletter: e.target.checked },
                  });
                }}
              />
            }
            label={
              <>
                <Typography variant="body1">
                  {t('myaccount.subscribe.weblife_newsletter.label')}
                </Typography>
                <Typography variant="caption">
                  {t('myaccount.subscribe.weblife_newsletter.description')}
                </Typography>
              </>
            }
          />

          <FormControlLabel
            sx={{ marginBottom: '1rem' }}
            required
            control={
              <Checkbox
                checked={userInfo.receiveSupport || false}
                onChange={e => {
                  setUserInfo({
                    ...userInfo,
                    ...{ receiveSupport: e.target.checked },
                  });
                }}
              />
            }
            label={
              <>
                <Typography variant="body1">
                  {t('myaccount.subscribe.support_communication.label')}
                </Typography>
                <Typography variant="caption">
                  {t('myaccount.subscribe.support_communication.description')}
                </Typography>
              </>
            }
          />

          <FormControlLabel
            sx={{ marginBottom: '1rem' }}
            control={
              <Checkbox
                checked={userInfo.receiveDirectMail || false}
                onChange={e => {
                  setUserInfo({
                    ...userInfo,
                    ...{ receiveDirectMail: e.target.checked },
                  });
                }}
              />
            }
            label={
              <>
                <Typography variant="body1">
                  {t('myaccount.subscribe.direct_mail.label')}
                </Typography>
                <Typography variant="caption">
                  {t('myaccount.subscribe.direct_mail.description')}
                </Typography>
              </>
            }
          />
        </FormGroup>
      </AccordionDetails>

      <AccordionActions sx={{ marginBottom: '0.5rem' }}>
        <Typography variant="body1" color="error">
          {errorMsg}
        </Typography>

        <Button
          size="large"
          color="info"
          variant="contained"
          onClick={onSave}
          loading={isLoading}
          loadingPosition="start"
        >
          {t('common.btn_save')}
        </Button>
      </AccordionActions>
    </Accordion>
  );
};
