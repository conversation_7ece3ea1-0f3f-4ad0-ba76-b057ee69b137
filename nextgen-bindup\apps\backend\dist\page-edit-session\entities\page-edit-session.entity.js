"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageEditSessionEntity = void 0;
const typeorm_1 = require("typeorm");
let PageEditSessionEntity = class PageEditSessionEntity {
};
exports.PageEditSessionEntity = PageEditSessionEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], PageEditSessionEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'sessionId',
        type: 'varchar',
        length: 255,
        nullable: false,
    }),
    __metadata("design:type", String)
], PageEditSessionEntity.prototype, "sessonId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'pageId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], PageEditSessionEntity.prototype, "pageId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'userId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], PageEditSessionEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'componentId',
        type: 'varchar',
        length: 255,
        nullable: false,
    }),
    __metadata("design:type", String)
], PageEditSessionEntity.prototype, "componentId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'name',
        type: 'varchar',
        length: 255,
        nullable: false,
    }),
    __metadata("design:type", Object)
], PageEditSessionEntity.prototype, "cursorPosition", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'components',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], PageEditSessionEntity.prototype, "editorSize", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: true,
    }),
    __metadata("design:type", Date)
], PageEditSessionEntity.prototype, "updatedAt", void 0);
exports.PageEditSessionEntity = PageEditSessionEntity = __decorate([
    (0, typeorm_1.Entity)('page_edit_sessions', { schema: process.env.DATABASE_SCHEMA })
], PageEditSessionEntity);
//# sourceMappingURL=page-edit-session.entity.js.map