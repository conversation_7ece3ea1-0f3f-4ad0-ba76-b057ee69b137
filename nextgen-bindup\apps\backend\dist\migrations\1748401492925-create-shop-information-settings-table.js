"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateShopInformationSettingsTable1748401492925 = void 0;
const typeorm_1 = require("typeorm");
class CreateShopInformationSettingsTable1748401492925 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}shop_information_settings`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'siteId',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'isMaintenance',
                    type: 'boolean',
                    isNullable: false,
                },
                {
                    name: 'shopName',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'logoImages',
                    type: 'text',
                    isArray: true,
                    isNullable: true,
                },
                {
                    name: 'colorTheme',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'shopUrl',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'isSetupGuide',
                    type: 'boolean',
                    isNullable: false,
                },
                {
                    name: 'guideUrl',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'email',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'isAddPrivacy',
                    type: 'boolean',
                    isNullable: false,
                },
                {
                    name: 'privacyUrl',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'taxMode',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'taxRate',
                    type: 'bigint',
                    isNullable: true,
                },
                {
                    name: 'taxRegulation',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateShopInformationSettingsTable1748401492925 = CreateShopInformationSettingsTable1748401492925;
//# sourceMappingURL=1748401492925-create-shop-information-settings-table.js.map