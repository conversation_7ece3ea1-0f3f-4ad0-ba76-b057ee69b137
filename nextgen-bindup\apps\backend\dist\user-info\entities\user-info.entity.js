"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserInfoEntity = void 0;
const typeorm_1 = require("typeorm");
const user_info_enum_1 = require("../enum/user-info.enum");
let UserInfoEntity = class UserInfoEntity {
};
exports.UserInfoEntity = UserInfoEntity;
__decorate([
    (0, typeorm_1.Column)({
        name: 'userId',
        type: 'varchar',
        length: 36,
        primary: true,
        nullable: false,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'name',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'type',
        type: 'integer',
        nullable: false,
        default: () => `${user_info_enum_1.UserType.INDIVIDUAL}`,
    }),
    __metadata("design:type", Number)
], UserInfoEntity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'avatar',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "avatar", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'company',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "company", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'lastNameHira',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "lastNameHira", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'firstNameHira',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "firstNameHira", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'sex',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], UserInfoEntity.prototype, "sex", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'countryCode',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], UserInfoEntity.prototype, "countryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'postalCode',
        type: 'varchar',
        length: 10,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "postalCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'prefecture',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], UserInfoEntity.prototype, "prefecture", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'municipalities',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "municipalities", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'street',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "street", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'building',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "building", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'phone',
        type: 'varchar',
        length: 50,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'fax',
        type: 'varchar',
        length: 50,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "fax", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'occupation',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], UserInfoEntity.prototype, "occupation", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'industry',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], UserInfoEntity.prototype, "industry", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'jobType',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], UserInfoEntity.prototype, "jobType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'birthday',
        type: 'date',
        nullable: true,
    }),
    __metadata("design:type", Date)
], UserInfoEntity.prototype, "birthday", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'receiveNewsletter',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], UserInfoEntity.prototype, "receiveNewsletter", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'receiveSupport',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], UserInfoEntity.prototype, "receiveSupport", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'receiveDirectMail',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], UserInfoEntity.prototype, "receiveDirectMail", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], UserInfoEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], UserInfoEntity.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'recentlySite',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Array)
], UserInfoEntity.prototype, "recentlySite", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'stripeCustomerId',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "stripeCustomerId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'activeSubscription',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], UserInfoEntity.prototype, "activeSubscription", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'stripeAccountId',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserInfoEntity.prototype, "stripeAccountId", void 0);
exports.UserInfoEntity = UserInfoEntity = __decorate([
    (0, typeorm_1.Entity)('user_info', { schema: process.env.DATABASE_SCHEMA })
], UserInfoEntity);
//# sourceMappingURL=user-info.entity.js.map