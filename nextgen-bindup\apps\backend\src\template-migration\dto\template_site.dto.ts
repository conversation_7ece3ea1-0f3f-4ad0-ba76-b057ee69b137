import { Boolean3 } from './boolean.enum';

export interface Template_Site {
  tmpSiteId: number;
  siteId: number;
  mainCategory: string;
  title: string;
  titleDetail: string;
  titleSeq: number;
  seq: number;
  id: string;
  description: string;
  category: string;
  smartblock: Boolean3;

  pagelist: string;
  pagelistJson: TemplateSite_Page[];

  preview: string;
  previewJson: TemplateSite_Preview;

  download: string;
  downloadJson: TemplateSite_Download;

  auth: number;
  delFlg: number;
  insDate: string;
  updDate: string;
}

export interface TemplateSite_Page {
  title: string;
  desc: string;
}

export interface TemplateSite_Preview {
  url: string;
}

export interface TemplateSite_Download {
  not_use: string;
}
