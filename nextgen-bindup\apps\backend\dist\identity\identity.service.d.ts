import { SupabaseClient } from '@supabase/supabase-js';
import { CreateIdentityReq, CreateIdentityRes, LoginIdentityRes } from './dto/identity.dto';
export declare class IdentityService {
    private supabase;
    constructor(supabase: SupabaseClient);
    createIdentity(input: CreateIdentityReq): Promise<CreateIdentityRes>;
    signInWithPassword(email: string, password: string): Promise<LoginIdentityRes>;
    forgotPasswordForEmail(email: string, redirectTo: string): Promise<void>;
    changePassword(email: string, password: string): Promise<void>;
}
