# Warning: This file is automatically generated. Removing it is fine, but will
# cause your node_modules installation to become invalidated.

__metadata:
  version: 1
  nmMode: classic

"@alloc/quick-lru@npm:5.2.0":
  locations:
    - "node_modules/@alloc/quick-lru"

"@ampproject/remapping@npm:2.3.0":
  locations:
    - "node_modules/@ampproject/remapping"

"@angular-devkit/core@virtual:27d6a4f677023da2b7228beb18e0dc4d1e1d463ea5073fc0f39e927d08377f976d9992ac83c3ceb8276fa5af4d91302d6d195ad7c22daff89a91b254c544b70e#npm:17.3.11":
  locations:
    - "node_modules/@angular-devkit/core"
  aliases:
    - "virtual:c2c39dde98d474a182562238c33617d1cbfbe354091cbb77fb73b4aac70039669b034eb629f3ba2cb639d0792b5bbf9e4dcee2c329739be30099383bcbe31cad#npm:17.3.11"

"@angular-devkit/schematics-cli@npm:17.3.11":
  locations:
    - "node_modules/@angular-devkit/schematics-cli"

"@angular-devkit/schematics@npm:17.3.11":
  locations:
    - "node_modules/@angular-devkit/schematics"

"@astrojs/compiler@npm:2.12.2":
  locations:
    - "node_modules/@astrojs/compiler"

"@astrojs/internal-helpers@npm:0.6.1":
  locations:
    - "node_modules/@astrojs/internal-helpers"

"@astrojs/markdown-remark@npm:6.3.2":
  locations:
    - "node_modules/@astrojs/markdown-remark"

"@astrojs/prism@npm:3.3.0":
  locations:
    - "node_modules/@astrojs/prism"

"@astrojs/react@virtual:f076ded31fccc59d4f9e6ced6349f34297f2dd657b8275061704bbe29a11a614ffe29c030a051fc61b74e50142507a1ec03a156876f30c45951e0e1e6cc4795e#npm:4.3.0":
  locations:
    - "node_modules/@astrojs/react"

"@astrojs/telemetry@npm:3.3.0":
  locations:
    - "node_modules/@astrojs/telemetry"

"@babel/code-frame@npm:7.27.1":
  locations:
    - "node_modules/@babel/code-frame"

"@babel/compat-data@npm:7.27.5":
  locations:
    - "node_modules/@babel/compat-data"

"@babel/core@npm:7.27.4":
  locations:
    - "node_modules/@babel/core"

"@babel/generator@npm:7.27.5":
  locations:
    - "node_modules/@babel/generator"

"@babel/helper-compilation-targets@npm:7.27.2":
  locations:
    - "node_modules/@babel/helper-compilation-targets"

"@babel/helper-module-imports@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-module-imports"

"@babel/helper-module-transforms@virtual:801e5891a4e0ddec86d520cbe0161c0145d19dde80c9720b9ce287fe7a61a2c2694f551e605f3e20a918ec2d3904d61745c71496a7a2cfd3e8749df38a407dde#npm:7.27.3":
  locations:
    - "node_modules/@babel/helper-module-transforms"

"@babel/helper-plugin-utils@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-plugin-utils"

"@babel/helper-string-parser@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-string-parser"

"@babel/helper-validator-identifier@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-validator-identifier"

"@babel/helper-validator-option@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-validator-option"

"@babel/helpers@npm:7.27.6":
  locations:
    - "node_modules/@babel/helpers"

"@babel/parser@npm:7.27.5":
  locations:
    - "node_modules/@babel/parser"

"@babel/plugin-syntax-async-generators@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.4":
  locations:
    - "node_modules/@babel/plugin-syntax-async-generators"

"@babel/plugin-syntax-bigint@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-bigint"

"@babel/plugin-syntax-class-properties@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.12.13":
  locations:
    - "node_modules/@babel/plugin-syntax-class-properties"

"@babel/plugin-syntax-class-static-block@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-class-static-block"

"@babel/plugin-syntax-import-attributes@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-import-attributes"

"@babel/plugin-syntax-import-meta@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-import-meta"

"@babel/plugin-syntax-json-strings@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-json-strings"

"@babel/plugin-syntax-jsx@virtual:15ef0a4ad61c166598c4d195dc64a0b7270b186e9a584ea25871b4181189fa5a61a49aa37f6bcda6ffed25499ff900f1a33224b0c22868c8eb1eaf1dd4f0dc11#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-jsx"
  aliases:
    - "virtual:460dc45ed39c2f6dc198da416b72fcc46c37836f8268fecb22c4b9ad334405a23083ae668e469887dcd8e810311a3351e7f44e36d785a445c78998b861496bea#npm:7.27.1"

"@babel/plugin-syntax-logical-assignment-operators@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-logical-assignment-operators"

"@babel/plugin-syntax-nullish-coalescing-operator@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-nullish-coalescing-operator"

"@babel/plugin-syntax-numeric-separator@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-numeric-separator"

"@babel/plugin-syntax-object-rest-spread@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-object-rest-spread"

"@babel/plugin-syntax-optional-catch-binding@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-optional-catch-binding"

"@babel/plugin-syntax-optional-chaining@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-optional-chaining"

"@babel/plugin-syntax-private-property-in-object@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-private-property-in-object"

"@babel/plugin-syntax-top-level-await@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-top-level-await"

"@babel/plugin-syntax-typescript@virtual:15ef0a4ad61c166598c4d195dc64a0b7270b186e9a584ea25871b4181189fa5a61a49aa37f6bcda6ffed25499ff900f1a33224b0c22868c8eb1eaf1dd4f0dc11#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-typescript"
  aliases:
    - "virtual:460dc45ed39c2f6dc198da416b72fcc46c37836f8268fecb22c4b9ad334405a23083ae668e469887dcd8e810311a3351e7f44e36d785a445c78998b861496bea#npm:7.27.1"

"@babel/plugin-transform-react-jsx-self@virtual:aaf87f3de6422e84b0c3d51dce2134b41d4c0b2b51242bc32454b17a1d026fd0568931e9dbe98687b3c9fe61121e8398ecefceb579058e4c8d1737ccd610781a#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx-self"

"@babel/plugin-transform-react-jsx-source@virtual:aaf87f3de6422e84b0c3d51dce2134b41d4c0b2b51242bc32454b17a1d026fd0568931e9dbe98687b3c9fe61121e8398ecefceb579058e4c8d1737ccd610781a#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx-source"

"@babel/runtime@npm:7.27.6":
  locations:
    - "node_modules/@babel/runtime"

"@babel/template@npm:7.27.2":
  locations:
    - "node_modules/@babel/template"

"@babel/traverse@npm:7.27.4":
  locations:
    - "node_modules/@babel/traverse"

"@babel/types@npm:7.27.6":
  locations:
    - "node_modules/@babel/types"

"@bcoe/v8-coverage@npm:0.2.3":
  locations:
    - "node_modules/@bcoe/v8-coverage"

"@bufbuild/protobuf@npm:2.5.2":
  locations:
    - "node_modules/@bufbuild/protobuf"

"@capsizecss/unpack@npm:2.4.0":
  locations:
    - "node_modules/@capsizecss/unpack"

"@cloudflare/stream-react@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:1.9.3":
  locations:
    - "node_modules/@cloudflare/stream-react"

"@colors/colors@npm:1.5.0":
  locations:
    - "node_modules/cli-table3/node_modules/@colors/colors"

"@colors/colors@npm:1.6.0":
  locations:
    - "node_modules/@colors/colors"

"@cspotcode/source-map-support@npm:0.8.1":
  locations:
    - "node_modules/@cspotcode/source-map-support"

"@css-inline/css-inline-win32-x64-msvc@npm:0.14.1":
  locations:
    - "node_modules/@css-inline/css-inline-win32-x64-msvc"

"@css-inline/css-inline@npm:0.14.1":
  locations:
    - "node_modules/@css-inline/css-inline"

"@dabh/diagnostics@npm:2.0.3":
  locations:
    - "node_modules/@dabh/diagnostics"

"@dnd-kit/accessibility@virtual:8d044d9200cf33f8fb064aec8a30ec15bdbcfd99e2c57646a536d569583f763171133c28e1957bc8f8a4451d88c11309a028b54f2cb379a423db7f5e5f041e90#npm:3.1.1":
  locations:
    - "node_modules/@dnd-kit/accessibility"

"@dnd-kit/core@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:6.3.1":
  locations:
    - "node_modules/@dnd-kit/core"

"@dnd-kit/modifiers@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:7.0.0":
  locations:
    - "node_modules/@dnd-kit/modifiers"

"@dnd-kit/sortable@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:8.0.0":
  locations:
    - "node_modules/@dnd-kit/sortable"

"@dnd-kit/utilities@virtual:8d044d9200cf33f8fb064aec8a30ec15bdbcfd99e2c57646a536d569583f763171133c28e1957bc8f8a4451d88c11309a028b54f2cb379a423db7f5e5f041e90#npm:3.2.2":
  locations:
    - "node_modules/@dnd-kit/utilities"

"@emotion/babel-plugin@npm:11.13.5":
  locations:
    - "node_modules/@emotion/babel-plugin"

"@emotion/cache@npm:11.14.0":
  locations:
    - "node_modules/@emotion/cache"

"@emotion/hash@npm:0.9.2":
  locations:
    - "node_modules/@emotion/hash"

"@emotion/is-prop-valid@npm:1.2.2":
  locations:
    - "node_modules/styled-components/node_modules/@emotion/is-prop-valid"

"@emotion/is-prop-valid@npm:1.3.1":
  locations:
    - "node_modules/@emotion/is-prop-valid"

"@emotion/memoize@npm:0.8.1":
  locations:
    - "node_modules/styled-components/node_modules/@emotion/memoize"

"@emotion/memoize@npm:0.9.0":
  locations:
    - "node_modules/@emotion/memoize"

"@emotion/react@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:11.14.0":
  locations:
    - "node_modules/@emotion/react"

"@emotion/serialize@npm:1.3.3":
  locations:
    - "node_modules/@emotion/serialize"

"@emotion/server@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:11.11.0":
  locations:
    - "node_modules/@emotion/server"

"@emotion/sheet@npm:1.4.0":
  locations:
    - "node_modules/@emotion/sheet"

"@emotion/styled@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:11.14.0":
  locations:
    - "node_modules/@emotion/styled"

"@emotion/unitless@npm:0.10.0":
  locations:
    - "node_modules/@emotion/unitless"

"@emotion/unitless@npm:0.8.1":
  locations:
    - "node_modules/styled-components/node_modules/@emotion/unitless"

"@emotion/use-insertion-effect-with-fallbacks@virtual:36d80df36e35b0c4db0f2a2516dc9c851d2e4c6bec18bdf06273e1bfeab796df26c184e95707d142b01caf0c14449e84b19ac5513b283917cef389750859d3af#npm:1.2.0":
  locations:
    - "node_modules/@emotion/use-insertion-effect-with-fallbacks"

"@emotion/utils@npm:1.4.2":
  locations:
    - "node_modules/@emotion/utils"

"@emotion/weak-memoize@npm:0.4.0":
  locations:
    - "node_modules/@emotion/weak-memoize"

"@esbuild/win32-x64@npm:0.21.5":
  locations:
    - "node_modules/vite/node_modules/@esbuild/win32-x64"

"@esbuild/win32-x64@npm:0.25.5":
  locations:
    - "node_modules/@esbuild/win32-x64"

"@eslint-community/eslint-utils@virtual:1a034e090ff4007b191837e18bc398f379df81139815b4edbae05481d12f89b85cbdb5fc7a5beefe2bdf41921558d5a20a628244aff901b0d38b9d0a029827f1#npm:4.7.0":
  locations:
    - "node_modules/@eslint-community/eslint-utils"
  aliases:
    - "virtual:dd20287a5a1e86b12a5b04609f98bd729fafd847d08e1fc89cdc68f92d1acf209e53b09ef0af4b6e7781d88e1f9acf94e3bf34619939e434ad5ffb0f24855eb4#npm:4.7.0"

"@eslint-community/regexpp@npm:4.12.1":
  locations:
    - "node_modules/@eslint-community/regexpp"

"@eslint/eslintrc@npm:2.1.4":
  locations:
    - "node_modules/@eslint/eslintrc"

"@eslint/js@npm:8.57.1":
  locations:
    - "node_modules/eslint/node_modules/@eslint/js"

"@eslint/js@npm:9.28.0":
  locations:
    - "node_modules/@eslint/js"

"@floating-ui/core@npm:1.7.1":
  locations:
    - "node_modules/@floating-ui/core"

"@floating-ui/dom@npm:1.7.1":
  locations:
    - "node_modules/@floating-ui/dom"

"@floating-ui/utils@npm:0.2.9":
  locations:
    - "node_modules/@floating-ui/utils"

"@fortawesome/fontawesome-common-types@npm:6.7.2":
  locations:
    - "node_modules/@fortawesome/fontawesome-common-types"

"@fortawesome/fontawesome-svg-core@npm:6.7.2":
  locations:
    - "node_modules/@fortawesome/fontawesome-svg-core"

"@fortawesome/free-brands-svg-icons@npm:6.7.2":
  locations:
    - "node_modules/@fortawesome/free-brands-svg-icons"

"@fortawesome/free-regular-svg-icons@npm:6.7.2":
  locations:
    - "node_modules/@fortawesome/free-regular-svg-icons"

"@fortawesome/free-solid-svg-icons@npm:6.7.2":
  locations:
    - "node_modules/@fortawesome/free-solid-svg-icons"

"@fortawesome/react-fontawesome@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:0.2.2":
  locations:
    - "node_modules/@fortawesome/react-fontawesome"

"@gulp-sourcemaps/map-sources@npm:1.0.0":
  locations:
    - "node_modules/@gulp-sourcemaps/map-sources"

"@hello-pangea/dnd@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:18.0.1":
  locations:
    - "node_modules/@hello-pangea/dnd"

"@hookform/resolvers@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:5.1.1":
  locations:
    - "node_modules/@hookform/resolvers"

"@humanwhocodes/config-array@npm:0.13.0":
  locations:
    - "node_modules/@humanwhocodes/config-array"

"@humanwhocodes/module-importer@npm:1.0.1":
  locations:
    - "node_modules/@humanwhocodes/module-importer"

"@humanwhocodes/object-schema@npm:2.0.3":
  locations:
    - "node_modules/@humanwhocodes/object-schema"

"@img/sharp-win32-x64@npm:0.33.5":
  locations:
    - "node_modules/@img/sharp-win32-x64"

"@isaacs/cliui@npm:8.0.2":
  locations:
    - "node_modules/@isaacs/cliui"

"@isaacs/fs-minipass@npm:4.0.1":
  locations:
    - "node_modules/@isaacs/fs-minipass"

"@istanbuljs/load-nyc-config@npm:1.1.0":
  locations:
    - "node_modules/@istanbuljs/load-nyc-config"

"@istanbuljs/schema@npm:0.1.3":
  locations:
    - "node_modules/@istanbuljs/schema"

"@jest/console@npm:29.7.0":
  locations:
    - "node_modules/@jest/console"

"@jest/core@virtual:13717d5e3be58e58db99845f8389bd23f9e8812cf3a7a6214540f88a9ed068937b46fa880de071f494de7405e6288f36805d637be582dea12a6ddf39bd422fc6#npm:29.7.0":
  locations:
    - "node_modules/@jest/core"

"@jest/environment@npm:29.7.0":
  locations:
    - "node_modules/@jest/environment"

"@jest/expect-utils@npm:29.7.0":
  locations:
    - "node_modules/@jest/expect-utils"

"@jest/expect@npm:29.7.0":
  locations:
    - "node_modules/@jest/expect"

"@jest/fake-timers@npm:29.7.0":
  locations:
    - "node_modules/@jest/fake-timers"

"@jest/globals@npm:29.7.0":
  locations:
    - "node_modules/@jest/globals"

"@jest/reporters@virtual:6f44d8c5cc1cd67367c0e1e1e117bda0efcf068436ba4049b2acd4b39393c79511b57b682b4ba11597f827d3c46467f6452b4497d0e9fd685c7c49a2b5fe08a7#npm:29.7.0":
  locations:
    - "node_modules/@jest/reporters"

"@jest/schemas@npm:29.6.3":
  locations:
    - "node_modules/@jest/schemas"

"@jest/source-map@npm:29.6.3":
  locations:
    - "node_modules/@jest/source-map"

"@jest/test-result@npm:29.7.0":
  locations:
    - "node_modules/@jest/test-result"

"@jest/test-sequencer@npm:29.7.0":
  locations:
    - "node_modules/@jest/test-sequencer"

"@jest/transform@npm:29.7.0":
  locations:
    - "node_modules/@jest/transform"

"@jest/types@npm:29.6.3":
  locations:
    - "node_modules/@jest/types"

"@jridgewell/gen-mapping@npm:0.3.8":
  locations:
    - "node_modules/@jridgewell/gen-mapping"

"@jridgewell/resolve-uri@npm:3.1.2":
  locations:
    - "node_modules/@jridgewell/resolve-uri"

"@jridgewell/set-array@npm:1.2.1":
  locations:
    - "node_modules/@jridgewell/set-array"

"@jridgewell/source-map@npm:0.3.6":
  locations:
    - "node_modules/@jridgewell/source-map"

"@jridgewell/sourcemap-codec@npm:1.5.0":
  locations:
    - "node_modules/@jridgewell/sourcemap-codec"

"@jridgewell/trace-mapping@npm:0.3.25":
  locations:
    - "node_modules/@jridgewell/trace-mapping"

"@jridgewell/trace-mapping@npm:0.3.9":
  locations:
    - "node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping"

"@ljharb/through@npm:2.3.14":
  locations:
    - "node_modules/@ljharb/through"

"@lukeed/csprng@npm:1.1.0":
  locations:
    - "node_modules/@lukeed/csprng"

"@mapbox/node-pre-gyp@npm:1.0.11":
  locations:
    - "node_modules/@mapbox/node-pre-gyp"

"@mui/core-downloads-tracker@npm:6.4.12":
  locations:
    - "node_modules/@mui/core-downloads-tracker"

"@mui/icons-material@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:6.4.12":
  locations:
    - "node_modules/@mui/icons-material"

"@mui/material@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:6.4.12":
  locations:
    - "node_modules/@mui/material"

"@mui/private-theming@virtual:2e4bca958b9f966c0573709a7e149fa087ac43dd70c4de463b5ac887cb91c0050fb0eeb9d235e3875faf259b4c615a99991216eb8299e08713c7670b7ee09469#npm:6.4.9":
  locations:
    - "node_modules/@mui/private-theming"

"@mui/styled-engine@virtual:2e4bca958b9f966c0573709a7e149fa087ac43dd70c4de463b5ac887cb91c0050fb0eeb9d235e3875faf259b4c615a99991216eb8299e08713c7670b7ee09469#npm:6.4.11":
  locations:
    - "node_modules/@mui/styled-engine"

"@mui/system@virtual:8b7c244d6a7e6d8ae9814fa74b3d1d35270987acf177442262f1c7b634d6f55b3d454030170da205794d6b37258530e48419d1ae0665df48caeb2b4480e60eac#npm:6.4.12":
  locations:
    - "node_modules/@mui/system"

"@mui/types@virtual:0fac962a898285b57c4f92646a9bfc5cc1d9bace8652bb4924ea0ef017b7d06f9d5d07144873efec182420da0ad605d27f8b4779fb50566667084ffff1290b48#npm:7.4.3":
  locations:
    - "node_modules/@mui/x-internals/node_modules/@mui/types"
    - "node_modules/@mui/x-date-pickers/node_modules/@mui/types"
    - "node_modules/@mui/x-charts/node_modules/@mui/types"

"@mui/types@virtual:8b7c244d6a7e6d8ae9814fa74b3d1d35270987acf177442262f1c7b634d6f55b3d454030170da205794d6b37258530e48419d1ae0665df48caeb2b4480e60eac#npm:7.2.24":
  locations:
    - "node_modules/@mui/types"

"@mui/utils@virtual:8b7c244d6a7e6d8ae9814fa74b3d1d35270987acf177442262f1c7b634d6f55b3d454030170da205794d6b37258530e48419d1ae0665df48caeb2b4480e60eac#npm:6.4.9":
  locations:
    - "node_modules/@mui/utils"

"@mui/utils@virtual:cca9e933eddbe8d84904008d6a6334aa1f98149ef6d0e0a97ee9a44f72f3bf4171a769f5c62b17225f321af495d324ac1df1a940c8c1ba680cefb84ba83e33d1#npm:7.1.1":
  locations:
    - "node_modules/@mui/x-internals/node_modules/@mui/utils"
    - "node_modules/@mui/x-date-pickers/node_modules/@mui/utils"
    - "node_modules/@mui/x-charts/node_modules/@mui/utils"

"@mui/x-charts-vendor@npm:7.20.0":
  locations:
    - "node_modules/@mui/x-charts-vendor"

"@mui/x-charts@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:7.29.1":
  locations:
    - "node_modules/@mui/x-charts"

"@mui/x-date-pickers@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:7.29.4":
  locations:
    - "node_modules/@mui/x-date-pickers"

"@mui/x-internals@virtual:cca9e933eddbe8d84904008d6a6334aa1f98149ef6d0e0a97ee9a44f72f3bf4171a769f5c62b17225f321af495d324ac1df1a940c8c1ba680cefb84ba83e33d1#npm:7.29.0":
  locations:
    - "node_modules/@mui/x-internals"

"@nestjs-modules/mailer@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:2.0.2":
  locations:
    - "node_modules/@nestjs-modules/mailer"

"@nestjs/cli@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:10.4.9":
  locations:
    - "node_modules/@nestjs/cli"

"@nestjs/common@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:10.4.19":
  locations:
    - "node_modules/@nestjs/common"

"@nestjs/config@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:3.3.0":
  locations:
    - "node_modules/@nestjs/config"

"@nestjs/core@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:10.4.19":
  locations:
    - "node_modules/@nestjs/core"

"@nestjs/event-emitter@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:3.0.1":
  locations:
    - "node_modules/@nestjs/event-emitter"

"@nestjs/platform-express@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:10.4.19":
  locations:
    - "node_modules/@nestjs/platform-express"

"@nestjs/schedule@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:4.1.2":
  locations:
    - "node_modules/@nestjs/schedule"

"@nestjs/schematics@virtual:27d6a4f677023da2b7228beb18e0dc4d1e1d463ea5073fc0f39e927d08377f976d9992ac83c3ceb8276fa5af4d91302d6d195ad7c22daff89a91b254c544b70e#npm:10.2.3":
  locations:
    - "node_modules/@nestjs/cli/node_modules/@nestjs/schematics"

"@nestjs/schematics@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:10.2.3":
  locations:
    - "node_modules/@nestjs/schematics"

"@nestjs/testing@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:10.4.19":
  locations:
    - "node_modules/@nestjs/testing"

"@nestjs/typeorm@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:10.0.2":
  locations:
    - "node_modules/@nestjs/typeorm"

"@next/env@npm:15.0.0":
  locations:
    - "node_modules/@next/env"

"@next/eslint-plugin-next@npm:15.0.1":
  locations:
    - "node_modules/@next/eslint-plugin-next"

"@next/swc-win32-x64-msvc@npm:15.0.0":
  locations:
    - "node_modules/@next/swc-win32-x64-msvc"

"@nextgen-bindup/backend@workspace:apps/backend":
  locations:
    - "node_modules/@nextgen-bindup/backend"

"@nextgen-bindup/common@workspace:packages/common":
  locations:
    - "node_modules/@nextgen-bindup/common"

"@nextgen-bindup/dashboard@workspace:apps/dashboard":
  locations:
    - "node_modules/@nextgen-bindup/dashboard"

"@nextgen-bindup/design-editor@workspace:apps/design-editor":
  locations:
    - "node_modules/@nextgen-bindup/design-editor"

"@nextgen-bindup/live-editor@workspace:apps/live-editor":
  locations:
    - "node_modules/@nextgen-bindup/live-editor"

"@nextgen-bindup/ssg@workspace:apps/ssg":
  locations:
    - "node_modules/@nextgen-bindup/ssg"

"@noble/hashes@npm:1.8.0":
  locations:
    - "node_modules/@noble/hashes"

"@nodelib/fs.scandir@npm:2.1.5":
  locations:
    - "node_modules/@nodelib/fs.scandir"

"@nodelib/fs.stat@npm:2.0.5":
  locations:
    - "node_modules/@nodelib/fs.stat"

"@nodelib/fs.walk@npm:1.2.8":
  locations:
    - "node_modules/@nodelib/fs.walk"

"@nolyfill/is-core-module@npm:1.0.39":
  locations:
    - "node_modules/@nolyfill/is-core-module"

"@npmcli/agent@npm:3.0.0":
  locations:
    - "node_modules/@npmcli/agent"

"@npmcli/fs@npm:4.0.0":
  locations:
    - "node_modules/@npmcli/fs"

"@nuxtjs/opencollective@npm:0.3.2":
  locations:
    - "node_modules/@nuxtjs/opencollective"

"@one-ini/wasm@npm:0.1.1":
  locations:
    - "node_modules/@one-ini/wasm"

"@oslojs/encoding@npm:1.1.0":
  locations:
    - "node_modules/@oslojs/encoding"

"@paralleldrive/cuid2@npm:2.2.2":
  locations:
    - "node_modules/@paralleldrive/cuid2"

"@pkgjs/parseargs@npm:0.11.0":
  locations:
    - "node_modules/@pkgjs/parseargs"

"@pkgr/core@npm:0.2.7":
  locations:
    - "node_modules/@pkgr/core"

"@popperjs/core@npm:2.11.8":
  locations:
    - "node_modules/@popperjs/core"

"@react-dnd/asap@npm:5.0.2":
  locations:
    - "node_modules/@react-dnd/asap"

"@react-dnd/invariant@npm:4.0.2":
  locations:
    - "node_modules/@react-dnd/invariant"

"@react-dnd/shallowequal@npm:4.0.2":
  locations:
    - "node_modules/@react-dnd/shallowequal"

"@react-spring/animated@virtual:20938aa528b2b7468842b574cc9ba9a13f70776efab594edaed11dfadddfbb0bc07f3472e08b58aae96b7171b828dee486fa08f90bfe979becf7013ddc214d97#npm:9.7.5":
  locations:
    - "node_modules/@react-spring/animated"

"@react-spring/core@virtual:20938aa528b2b7468842b574cc9ba9a13f70776efab594edaed11dfadddfbb0bc07f3472e08b58aae96b7171b828dee486fa08f90bfe979becf7013ddc214d97#npm:9.7.5":
  locations:
    - "node_modules/@react-spring/core"

"@react-spring/rafz@npm:9.7.5":
  locations:
    - "node_modules/@react-spring/rafz"

"@react-spring/shared@virtual:20938aa528b2b7468842b574cc9ba9a13f70776efab594edaed11dfadddfbb0bc07f3472e08b58aae96b7171b828dee486fa08f90bfe979becf7013ddc214d97#npm:9.7.5":
  locations:
    - "node_modules/@react-spring/shared"

"@react-spring/types@npm:9.7.5":
  locations:
    - "node_modules/@react-spring/types"

"@react-spring/web@virtual:cca9e933eddbe8d84904008d6a6334aa1f98149ef6d0e0a97ee9a44f72f3bf4171a769f5c62b17225f321af495d324ac1df1a940c8c1ba680cefb84ba83e33d1#npm:9.7.5":
  locations:
    - "node_modules/@react-spring/web"

"@reduxjs/toolkit@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:2.8.2":
  locations:
    - "node_modules/@reduxjs/toolkit"

"@remirror/core-constants@npm:3.0.0":
  locations:
    - "node_modules/@remirror/core-constants"

"@rolldown/pluginutils@npm:1.0.0-beta.9":
  locations:
    - "node_modules/@rolldown/pluginutils"

"@rollup/pluginutils@virtual:f5dea88454b79084bdc2a6366b33908e4411963c25103ab86a651561afb3c9fba8ccb4b0f1aabc8be31aa47d42aa6c371afd88f99665c741faa6c744a2123906#npm:5.1.4":
  locations:
    - "node_modules/@rollup/pluginutils"

"@rollup/rollup-win32-x64-msvc@npm:4.42.0":
  locations:
    - "node_modules/@rollup/rollup-win32-x64-msvc"

"@rtsao/scc@npm:1.1.0":
  locations:
    - "node_modules/@rtsao/scc"

"@rushstack/eslint-patch@npm:1.11.0":
  locations:
    - "node_modules/@rushstack/eslint-patch"

"@scaleflex/icons@virtual:8e8e06b2fff2d5c4b667b5ce1078134105b1b4210a9b301f0d704da7aba64b5b88ec9382743f360d21da636ffa6631419d73ea7ee56faff6a507a3e5648400d4#npm:2.10.27":
  locations:
    - "node_modules/@scaleflex/icons"

"@scaleflex/icons@virtual:d925f3a42177507e4198143f5a702facd90d3fb213e1127c2f189a3ee93e03d61c04f4d16ccf0260f62da33f278033e2f9d31ffa59f3879e747ba6dd8b2030ff#npm:2.11.10":
  locations:
    - "node_modules/@scaleflex/ui/node_modules/@scaleflex/icons"

"@scaleflex/ui@virtual:8e8e06b2fff2d5c4b667b5ce1078134105b1b4210a9b301f0d704da7aba64b5b88ec9382743f360d21da636ffa6631419d73ea7ee56faff6a507a3e5648400d4#npm:2.10.27":
  locations:
    - "node_modules/@scaleflex/ui"

"@selderee/plugin-htmlparser2@npm:0.11.0":
  locations:
    - "node_modules/@selderee/plugin-htmlparser2"

"@shikijs/core@npm:3.6.0":
  locations:
    - "node_modules/@shikijs/core"

"@shikijs/engine-javascript@npm:3.6.0":
  locations:
    - "node_modules/@shikijs/engine-javascript"

"@shikijs/engine-oniguruma@npm:3.6.0":
  locations:
    - "node_modules/@shikijs/engine-oniguruma"

"@shikijs/langs@npm:3.6.0":
  locations:
    - "node_modules/@shikijs/langs"

"@shikijs/themes@npm:3.6.0":
  locations:
    - "node_modules/@shikijs/themes"

"@shikijs/types@npm:3.6.0":
  locations:
    - "node_modules/@shikijs/types"

"@shikijs/vscode-textmate@npm:10.0.2":
  locations:
    - "node_modules/@shikijs/vscode-textmate"

"@sinclair/typebox@npm:0.27.8":
  locations:
    - "node_modules/@sinclair/typebox"

"@sinonjs/commons@npm:3.0.1":
  locations:
    - "node_modules/@sinonjs/commons"

"@sinonjs/fake-timers@npm:10.3.0":
  locations:
    - "node_modules/@sinonjs/fake-timers"

"@sqltools/formatter@npm:1.2.5":
  locations:
    - "node_modules/@sqltools/formatter"

"@standard-schema/spec@npm:1.0.0":
  locations:
    - "node_modules/@standard-schema/spec"

"@standard-schema/utils@npm:0.3.0":
  locations:
    - "node_modules/@standard-schema/utils"

"@stripe/react-stripe-js@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:3.7.0":
  locations:
    - "node_modules/@stripe/react-stripe-js"

"@stripe/stripe-js@npm:6.1.0":
  locations:
    - "node_modules/@stripe/stripe-js"

"@supabase/auth-js@npm:2.70.0":
  locations:
    - "node_modules/@supabase/auth-js"

"@supabase/functions-js@npm:2.4.4":
  locations:
    - "node_modules/@supabase/functions-js"

"@supabase/node-fetch@npm:2.6.15":
  locations:
    - "node_modules/@supabase/node-fetch"

"@supabase/postgrest-js@npm:1.19.4":
  locations:
    - "node_modules/@supabase/postgrest-js"

"@supabase/realtime-js@npm:2.11.10":
  locations:
    - "node_modules/@supabase/realtime-js"

"@supabase/storage-js@npm:2.7.1":
  locations:
    - "node_modules/@supabase/storage-js"

"@supabase/supabase-js@npm:2.50.0":
  locations:
    - "node_modules/@supabase/supabase-js"

"@swc/counter@npm:0.1.3":
  locations:
    - "node_modules/@swc/counter"

"@swc/helpers@npm:0.5.13":
  locations:
    - "node_modules/@swc/helpers"

"@tanstack/history@npm:1.120.17":
  locations:
    - "node_modules/@tanstack/history"

"@tanstack/react-router-devtools@virtual:6d1b0569f68d1b5c88e5d2a297ff776726bec45890c2b0623dc221c4ed804d80f464b73ce279f873e85b2bae7fff9c1add45f1f7d14a7c04a013ca2b2bdd9960#npm:1.120.20":
  locations:
    - "node_modules/@tanstack/react-router-devtools"

"@tanstack/react-router@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:1.120.20":
  locations:
    - "node_modules/@tanstack/react-router"

"@tanstack/react-store@virtual:fb6e6bde432c8042b533b88f4a567f240006733b5dc7ffeb767743d8084291f27d3a8b8091000d4fa65571fe810b10141064df0ccc8e0c236b0e47c981e2a66f#npm:0.7.1":
  locations:
    - "node_modules/@tanstack/react-store"

"@tanstack/router-core@npm:1.120.19":
  locations:
    - "node_modules/@tanstack/router-core"

"@tanstack/router-devtools-core@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:1.120.19":
  locations:
    - "node_modules/@tanstack/router-devtools-core"

"@tanstack/router-devtools@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:1.120.20":
  locations:
    - "node_modules/@tanstack/router-devtools"

"@tanstack/router-generator@virtual:460dc45ed39c2f6dc198da416b72fcc46c37836f8268fecb22c4b9ad334405a23083ae668e469887dcd8e810311a3351e7f44e36d785a445c78998b861496bea#npm:1.120.20":
  locations:
    - "node_modules/@tanstack/router-generator"

"@tanstack/router-plugin@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:1.120.20":
  locations:
    - "node_modules/@tanstack/router-plugin"

"@tanstack/router-utils@npm:1.120.17":
  locations:
    - "node_modules/@tanstack/router-utils"

"@tanstack/store@npm:0.7.1":
  locations:
    - "node_modules/@tanstack/store"

"@tanstack/virtual-file-routes@npm:1.120.17":
  locations:
    - "node_modules/@tanstack/virtual-file-routes"

"@tippyjs/react@virtual:d925f3a42177507e4198143f5a702facd90d3fb213e1127c2f189a3ee93e03d61c04f4d16ccf0260f62da33f278033e2f9d31ffa59f3879e747ba6dd8b2030ff#npm:4.2.6":
  locations:
    - "node_modules/@tippyjs/react"

"@tiptap/core@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/core"

"@tiptap/extension-blockquote@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-blockquote"

"@tiptap/extension-bold@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-bold"

"@tiptap/extension-bubble-menu@virtual:51fa120bcf17229c5304b0c1cba13ff011e8d5c55d051cd31025572b27ad5f4f8127f30a24ae723d7bb77674e09c25d92564008074f73bb7206d26686591af4c#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-bubble-menu"

"@tiptap/extension-bullet-list@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-bullet-list"
  aliases:
    - "virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0"

"@tiptap/extension-code-block@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-code-block"

"@tiptap/extension-code@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-code"

"@tiptap/extension-color@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-color"
  aliases:
    - "virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:2.14.0"

"@tiptap/extension-document@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-document"
  aliases:
    - "virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0"

"@tiptap/extension-dropcursor@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-dropcursor"

"@tiptap/extension-floating-menu@virtual:51fa120bcf17229c5304b0c1cba13ff011e8d5c55d051cd31025572b27ad5f4f8127f30a24ae723d7bb77674e09c25d92564008074f73bb7206d26686591af4c#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-floating-menu"

"@tiptap/extension-gapcursor@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-gapcursor"

"@tiptap/extension-hard-break@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-hard-break"

"@tiptap/extension-heading@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-heading"
  aliases:
    - "virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0"

"@tiptap/extension-highlight@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-highlight"

"@tiptap/extension-history@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-history"

"@tiptap/extension-horizontal-rule@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-horizontal-rule"

"@tiptap/extension-image@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-image"

"@tiptap/extension-italic@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-italic"

"@tiptap/extension-link@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-link"

"@tiptap/extension-list-item@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-list-item"
  aliases:
    - "virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0"

"@tiptap/extension-ordered-list@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-ordered-list"
  aliases:
    - "virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0"

"@tiptap/extension-paragraph@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-paragraph"
  aliases:
    - "virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0"

"@tiptap/extension-strike@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-strike"

"@tiptap/extension-text-align@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-text-align"

"@tiptap/extension-text-style@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-text-style"

"@tiptap/extension-text@virtual:4419588fef9f16d6aa38bba716060a721c86f2f5dce236336a4a37eda0da1947dbca1506f54d0be9b06158a46cf26d1b2f3f245ec4265bfe9449383450bbcbe4#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-text"
  aliases:
    - "virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0"

"@tiptap/extension-underline@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/extension-underline"

"@tiptap/pm@npm:2.14.0":
  locations:
    - "node_modules/@tiptap/pm"

"@tiptap/react@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:2.14.0":
  locations:
    - "node_modules/@tiptap/react"
  aliases:
    - "virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:2.14.0"

"@tiptap/starter-kit@npm:2.14.0":
  locations:
    - "node_modules/@tiptap/starter-kit"

"@tokenizer/inflate@npm:0.2.7":
  locations:
    - "node_modules/@tokenizer/inflate"

"@tokenizer/token@npm:0.3.0":
  locations:
    - "node_modules/@tokenizer/token"

"@trivago/prettier-plugin-sort-imports@virtual:7500e53e5794f26bce60f75d5f75f5411d77123ef1b5430b10e6a6b95d69cf9f113b9a177237f485c5cc0d748dffc13299adf0cf03f6e1ab049a65027d84ebff#npm:5.2.2":
  locations:
    - "node_modules/@trivago/prettier-plugin-sort-imports"

"@tsconfig/node10@npm:1.0.11":
  locations:
    - "node_modules/@tsconfig/node10"

"@tsconfig/node12@npm:1.0.11":
  locations:
    - "node_modules/@tsconfig/node12"

"@tsconfig/node14@npm:1.0.3":
  locations:
    - "node_modules/@tsconfig/node14"

"@tsconfig/node16@npm:1.0.4":
  locations:
    - "node_modules/@tsconfig/node16"

"@types/adm-zip@npm:0.5.7":
  locations:
    - "node_modules/@types/adm-zip"

"@types/babel__core@npm:7.20.5":
  locations:
    - "node_modules/@types/babel__core"

"@types/babel__generator@npm:7.27.0":
  locations:
    - "node_modules/@types/babel__generator"

"@types/babel__template@npm:7.4.4":
  locations:
    - "node_modules/@types/babel__template"

"@types/babel__traverse@npm:7.20.7":
  locations:
    - "node_modules/@types/babel__traverse"

"@types/body-parser@npm:1.19.6":
  locations:
    - "node_modules/@types/body-parser"

"@types/connect@npm:3.4.38":
  locations:
    - "node_modules/@types/connect"

"@types/cookiejar@npm:2.1.5":
  locations:
    - "node_modules/@types/cookiejar"

"@types/culori@npm:2.1.1":
  locations:
    - "node_modules/@types/culori"

"@types/d3-color@npm:3.1.3":
  locations:
    - "node_modules/@types/d3-color"

"@types/d3-delaunay@npm:6.0.4":
  locations:
    - "node_modules/@types/d3-delaunay"

"@types/d3-interpolate@npm:3.0.4":
  locations:
    - "node_modules/@types/d3-interpolate"

"@types/d3-path@npm:3.1.1":
  locations:
    - "node_modules/@types/d3-path"

"@types/d3-scale@npm:4.0.9":
  locations:
    - "node_modules/@types/d3-scale"

"@types/d3-shape@npm:3.1.7":
  locations:
    - "node_modules/@types/d3-shape"

"@types/d3-time@npm:3.0.4":
  locations:
    - "node_modules/@types/d3-time"

"@types/debug@npm:4.1.12":
  locations:
    - "node_modules/@types/debug"

"@types/ejs@npm:3.1.5":
  locations:
    - "node_modules/@types/ejs"

"@types/eslint-scope@npm:3.7.7":
  locations:
    - "node_modules/@types/eslint-scope"

"@types/eslint@npm:9.6.1":
  locations:
    - "node_modules/@types/eslint"

"@types/estree@npm:1.0.7":
  locations:
    - "node_modules/rollup/node_modules/@types/estree"

"@types/estree@npm:1.0.8":
  locations:
    - "node_modules/@types/estree"

"@types/express-serve-static-core@npm:4.19.6":
  locations:
    - "node_modules/@types/express-serve-static-core"

"@types/express-serve-static-core@npm:5.0.6":
  locations:
    - "node_modules/@types/multer/node_modules/@types/express-serve-static-core"

"@types/express@npm:4.17.23":
  locations:
    - "node_modules/@types/express"

"@types/express@npm:5.0.3":
  locations:
    - "node_modules/@types/multer/node_modules/@types/express"

"@types/fontkit@npm:2.0.8":
  locations:
    - "node_modules/@types/fontkit"

"@types/graceful-fs@npm:4.1.9":
  locations:
    - "node_modules/@types/graceful-fs"

"@types/hast@npm:3.0.4":
  locations:
    - "node_modules/@types/hast"

"@types/hoist-non-react-statics@npm:3.3.6":
  locations:
    - "node_modules/@types/hoist-non-react-statics"

"@types/http-errors@npm:2.0.5":
  locations:
    - "node_modules/@types/http-errors"

"@types/istanbul-lib-coverage@npm:2.0.6":
  locations:
    - "node_modules/@types/istanbul-lib-coverage"

"@types/istanbul-lib-report@npm:3.0.3":
  locations:
    - "node_modules/@types/istanbul-lib-report"

"@types/istanbul-reports@npm:3.0.4":
  locations:
    - "node_modules/@types/istanbul-reports"

"@types/jest@npm:29.5.14":
  locations:
    - "node_modules/@types/jest"

"@types/jsftp@npm:2.1.5":
  locations:
    - "node_modules/@types/jsftp"

"@types/json-schema@npm:7.0.15":
  locations:
    - "node_modules/@types/json-schema"

"@types/json5@npm:0.0.29":
  locations:
    - "node_modules/@types/json5"

"@types/linkify-it@npm:5.0.0":
  locations:
    - "node_modules/@types/linkify-it"

"@types/lodash.merge@npm:4.6.9":
  locations:
    - "node_modules/@types/lodash.merge"

"@types/lodash@npm:4.17.17":
  locations:
    - "node_modules/@types/lodash"

"@types/luxon@npm:3.4.2":
  locations:
    - "node_modules/@types/luxon"

"@types/markdown-it@npm:14.1.2":
  locations:
    - "node_modules/@types/markdown-it"

"@types/mdast@npm:4.0.4":
  locations:
    - "node_modules/@types/mdast"

"@types/mdurl@npm:2.0.0":
  locations:
    - "node_modules/@types/mdurl"

"@types/methods@npm:1.1.4":
  locations:
    - "node_modules/@types/methods"

"@types/mime@npm:1.3.5":
  locations:
    - "node_modules/@types/mime"

"@types/mjml-core@npm:4.15.2":
  locations:
    - "node_modules/@types/mjml-core"

"@types/mjml@npm:4.7.4":
  locations:
    - "node_modules/@types/mjml"

"@types/ms@npm:2.1.0":
  locations:
    - "node_modules/@types/ms"

"@types/multer@npm:1.4.13":
  locations:
    - "node_modules/@types/multer"

"@types/nlcst@npm:2.0.3":
  locations:
    - "node_modules/@types/nlcst"

"@types/node@npm:20.19.0":
  locations:
    - "node_modules/@types/node"

"@types/node@npm:22.15.30":
  locations:
    - "node_modules/terser-webpack-plugin/node_modules/@types/node"
    - "node_modules/jest-worker/node_modules/@types/node"
    - "node_modules/jest-watcher/node_modules/@types/node"
    - "node_modules/jest-util/node_modules/@types/node"
    - "node_modules/jest-runtime/node_modules/@types/node"
    - "node_modules/jest-runner/node_modules/@types/node"
    - "node_modules/jest-mock/node_modules/@types/node"
    - "node_modules/jest-haste-map/node_modules/@types/node"
    - "node_modules/jest-environment-node/node_modules/@types/node"
    - "node_modules/jest-circus/node_modules/@types/node"
    - "node_modules/@types/xml2json/node_modules/@types/node"
    - "node_modules/@types/ws/node_modules/@types/node"
    - "node_modules/@types/superagent/node_modules/@types/node"
    - "node_modules/@types/serve-static/node_modules/@types/node"
    - "node_modules/@types/send/node_modules/@types/node"
    - "node_modules/@types/pg/node_modules/@types/node"
    - "node_modules/@types/papaparse/node_modules/@types/node"
    - "node_modules/@types/multer/node_modules/@types/node"
    - "node_modules/@types/jsftp/node_modules/@types/node"
    - "node_modules/@types/graceful-fs/node_modules/@types/node"
    - "node_modules/@types/fontkit/node_modules/@types/node"
    - "node_modules/@types/express-serve-static-core/node_modules/@types/node"
    - "node_modules/@types/connect/node_modules/@types/node"
    - "node_modules/@types/body-parser/node_modules/@types/node"
    - "node_modules/@types/adm-zip/node_modules/@types/node"
    - "node_modules/@jest/types/node_modules/@types/node"
    - "node_modules/@jest/reporters/node_modules/@types/node"
    - "node_modules/@jest/fake-timers/node_modules/@types/node"
    - "node_modules/@jest/environment/node_modules/@types/node"
    - "node_modules/@jest/core/node_modules/@types/node"
    - "node_modules/@jest/console/node_modules/@types/node"

"@types/papaparse@npm:5.3.16":
  locations:
    - "node_modules/@types/papaparse"

"@types/parse-json@npm:4.0.2":
  locations:
    - "node_modules/@types/parse-json"

"@types/pg@npm:8.15.4":
  locations:
    - "node_modules/@types/pg"

"@types/phoenix@npm:1.6.6":
  locations:
    - "node_modules/@types/phoenix"

"@types/prop-types@npm:15.7.14":
  locations:
    - "node_modules/@types/prop-types"

"@types/pug@npm:2.0.10":
  locations:
    - "node_modules/@types/pug"

"@types/qs@npm:6.14.0":
  locations:
    - "node_modules/@types/qs"

"@types/range-parser@npm:1.2.7":
  locations:
    - "node_modules/@types/range-parser"

"@types/react-dom@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:18.3.7":
  locations:
    - "node_modules/@types/react-dom"

"@types/react-reconciler@virtual:7c10321826407f5fb71fbc4a7dd5504185efb590c9f9a855a6b5199aa3bf6995a4af3f68dd20977533d459fd0800ef4f6520727c17e788b16ab8dd5bc0548357#npm:0.28.9":
  locations:
    - "node_modules/@types/react-reconciler"

"@types/react-redux@npm:7.1.34":
  locations:
    - "node_modules/@types/react-redux"

"@types/react-transition-group@virtual:8b7c244d6a7e6d8ae9814fa74b3d1d35270987acf177442262f1c7b634d6f55b3d454030170da205794d6b37258530e48419d1ae0665df48caeb2b4480e60eac#npm:4.4.12":
  locations:
    - "node_modules/@types/react-transition-group"

"@types/react@npm:18.3.23":
  locations:
    - "node_modules/@types/react"

"@types/react@npm:19.1.6":
  locations:
    - "node_modules/@types/react-redux/node_modules/@types/react"
    - "node_modules/@types/hoist-non-react-statics/node_modules/@types/react"

"@types/send@npm:0.17.5":
  locations:
    - "node_modules/@types/send"

"@types/serve-static@npm:1.15.8":
  locations:
    - "node_modules/@types/serve-static"

"@types/stack-utils@npm:2.0.3":
  locations:
    - "node_modules/@types/stack-utils"

"@types/stylis@npm:4.2.5":
  locations:
    - "node_modules/@types/stylis"

"@types/superagent@npm:8.1.9":
  locations:
    - "node_modules/@types/superagent"

"@types/supertest@npm:6.0.3":
  locations:
    - "node_modules/@types/supertest"

"@types/triple-beam@npm:1.3.5":
  locations:
    - "node_modules/@types/triple-beam"

"@types/unescape-js@npm:1.0.3":
  locations:
    - "node_modules/@types/unescape-js"

"@types/unist@npm:3.0.3":
  locations:
    - "node_modules/@types/unist"

"@types/use-sync-external-store@npm:0.0.6":
  locations:
    - "node_modules/@types/use-sync-external-store"

"@types/validator@npm:13.15.1":
  locations:
    - "node_modules/@types/validator"

"@types/ws@npm:8.18.1":
  locations:
    - "node_modules/@types/ws"

"@types/xml2json@npm:0.11.6":
  locations:
    - "node_modules/@types/xml2json"

"@types/yargs-parser@npm:21.0.3":
  locations:
    - "node_modules/@types/yargs-parser"

"@types/yargs@npm:17.0.33":
  locations:
    - "node_modules/@types/yargs"

"@typescript-eslint/eslint-plugin@virtual:8264c989147b56e71c127665c32e44703a48be8af06fefe4fc694818f4c016bc39c798825b171079430351d5d4d1720df1b03c981d8c1809145138c9af731252#npm:8.33.1":
  locations:
    - "node_modules/@typescript-eslint/eslint-plugin"

"@typescript-eslint/parser@virtual:8264c989147b56e71c127665c32e44703a48be8af06fefe4fc694818f4c016bc39c798825b171079430351d5d4d1720df1b03c981d8c1809145138c9af731252#npm:8.33.1":
  locations:
    - "node_modules/@typescript-eslint/parser"

"@typescript-eslint/project-service@virtual:84aa7059e05ced4efa069d41643704df9d26c3192b24ea7d96a65bdb8e29305a9637414043ce736ef949b9a99febe667b38eb93498e73608f41b89c9821deac2#npm:8.33.1":
  locations:
    - "node_modules/@typescript-eslint/project-service"

"@typescript-eslint/scope-manager@npm:8.33.1":
  locations:
    - "node_modules/@typescript-eslint/scope-manager"

"@typescript-eslint/tsconfig-utils@virtual:84aa7059e05ced4efa069d41643704df9d26c3192b24ea7d96a65bdb8e29305a9637414043ce736ef949b9a99febe667b38eb93498e73608f41b89c9821deac2#npm:8.33.1":
  locations:
    - "node_modules/@typescript-eslint/tsconfig-utils"

"@typescript-eslint/type-utils@virtual:ae7dc19168e695f372c232c0679bd419529b68755b53c5424a347599db526eeb0bdf67cae7f7b7230dde90715aa96b6a521b0a967a46d1db316408b04ee3cc2b#npm:8.33.1":
  locations:
    - "node_modules/@typescript-eslint/type-utils"

"@typescript-eslint/types@npm:8.33.1":
  locations:
    - "node_modules/@typescript-eslint/types"

"@typescript-eslint/typescript-estree@virtual:065fe9cb1bc55659ccf05ebfa5262377537a77e8a3294badecf0882c7ebe9bcec3712cb8a8450761604afe2c3a54fa77f10f10c173acf5af0f771ac6290c201e#npm:8.33.1":
  locations:
    - "node_modules/@typescript-eslint/typescript-estree"

"@typescript-eslint/utils@virtual:8264c989147b56e71c127665c32e44703a48be8af06fefe4fc694818f4c016bc39c798825b171079430351d5d4d1720df1b03c981d8c1809145138c9af731252#npm:8.33.1":
  locations:
    - "node_modules/@typescript-eslint/utils"

"@typescript-eslint/visitor-keys@npm:8.33.1":
  locations:
    - "node_modules/@typescript-eslint/visitor-keys"

"@ungap/structured-clone@npm:1.3.0":
  locations:
    - "node_modules/@ungap/structured-clone"

"@unrs/resolver-binding-win32-x64-msvc@npm:1.7.11":
  locations:
    - "node_modules/@unrs/resolver-binding-win32-x64-msvc"

"@vitejs/plugin-react@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:4.5.1":
  locations:
    - "node_modules/@vitejs/plugin-react"
  aliases:
    - "virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:4.5.1"

"@vitejs/plugin-react@virtual:43581a1c353e0b1af27c89d9f01c3a62597f988edb0c8de0e48f0d417a1c39e73a5a227adadafabc6a2802ffec1ba9f301a2376289e9b13bf416a0379ad610b2#npm:4.5.1":
  locations:
    - "node_modules/@astrojs/react/node_modules/@vitejs/plugin-react"

"@webassemblyjs/ast@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/ast"

"@webassemblyjs/floating-point-hex-parser@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/floating-point-hex-parser"

"@webassemblyjs/helper-api-error@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/helper-api-error"

"@webassemblyjs/helper-buffer@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/helper-buffer"

"@webassemblyjs/helper-numbers@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/helper-numbers"

"@webassemblyjs/helper-wasm-bytecode@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/helper-wasm-bytecode"

"@webassemblyjs/helper-wasm-section@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/helper-wasm-section"

"@webassemblyjs/ieee754@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/ieee754"

"@webassemblyjs/leb128@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/leb128"

"@webassemblyjs/utf8@npm:1.13.2":
  locations:
    - "node_modules/@webassemblyjs/utf8"

"@webassemblyjs/wasm-edit@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/wasm-edit"

"@webassemblyjs/wasm-gen@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/wasm-gen"

"@webassemblyjs/wasm-opt@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/wasm-opt"

"@webassemblyjs/wasm-parser@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/wasm-parser"

"@webassemblyjs/wast-printer@npm:1.14.1":
  locations:
    - "node_modules/@webassemblyjs/wast-printer"

"@xmldom/xmldom@npm:0.9.8":
  locations:
    - "node_modules/@xmldom/xmldom"

"@xtuc/ieee754@npm:1.2.0":
  locations:
    - "node_modules/@xtuc/ieee754"

"@xtuc/long@npm:4.2.2":
  locations:
    - "node_modules/@xtuc/long"

"abbrev@npm:1.1.1":
  locations:
    - "node_modules/abbrev"

"abbrev@npm:2.0.0":
  locations:
    - "node_modules/js-beautify/node_modules/abbrev"

"abbrev@npm:3.0.1":
  locations:
    - "node_modules/node-gyp/node_modules/abbrev"

"accepts@npm:1.3.8":
  locations:
    - "node_modules/accepts"

"acorn-jsx@virtual:a50722a5a9326b6a5f12350c494c4db3aa0f4caeac45e3e9e5fe071da20014ecfe738fe2ebe2c9c98abae81a4ea86b42f56d776b3bd5ec37f9ad3670c242b242#npm:5.3.2":
  locations:
    - "node_modules/acorn-jsx"

"acorn-walk@npm:8.3.4":
  locations:
    - "node_modules/acorn-walk"

"acorn@npm:4.0.13":
  locations:
    - "node_modules/gulp-sourcemaps/node_modules/acorn"

"acorn@npm:7.4.1":
  locations:
    - "node_modules/is-expression/node_modules/acorn"

"acorn@npm:8.15.0":
  locations:
    - "node_modules/acorn"

"adm-zip@npm:0.5.16":
  locations:
    - "node_modules/adm-zip"

"agent-base@npm:6.0.2":
  locations:
    - "node_modules/https-proxy-agent/node_modules/agent-base"

"agent-base@npm:7.1.3":
  locations:
    - "node_modules/agent-base"

"ajv-formats@virtual:7d68b1c0fde37300f56685f7bb4c28ebea1b0104d72a9753a9c1cd828a7af871eef630afc629d50afb995ee91b4816b63a5a2727399876aa4a4f0405da35dc08#npm:2.1.1":
  locations:
    - "node_modules/ajv-formats"

"ajv-formats@virtual:f2f0682292e7d1a898e81372a5e1acc3370e012545b0bfa3e29df25e89e63ee5d8d02ca77967518a41a562a4df0a219c519c08a92f59cc9c825fb45b26ef849e#npm:2.1.1":
  locations:
    - "node_modules/@angular-devkit/core/node_modules/ajv-formats"

"ajv-keywords@virtual:7d68b1c0fde37300f56685f7bb4c28ebea1b0104d72a9753a9c1cd828a7af871eef630afc629d50afb995ee91b4816b63a5a2727399876aa4a4f0405da35dc08#npm:5.1.0":
  locations:
    - "node_modules/ajv-keywords"

"ajv-keywords@virtual:f2b36937f163b579815d3163513b3330d7a31aaf0599eea66346382b8838395c613f4204e9809cc2ff6bba09c17ab0c34b37deadcb147de7e2f5e535d6ccc245#npm:3.5.2":
  locations:
    - "node_modules/schema-utils/node_modules/ajv-keywords"

"ajv@npm:6.12.6":
  locations:
    - "node_modules/schema-utils/node_modules/ajv"
    - "node_modules/eslint/node_modules/ajv"
    - "node_modules/@eslint/eslintrc/node_modules/ajv"

"ajv@npm:8.12.0":
  locations:
    - "node_modules/@angular-devkit/core/node_modules/ajv"

"ajv@npm:8.17.1":
  locations:
    - "node_modules/ajv"

"alce@npm:1.2.0":
  locations:
    - "node_modules/alce"

"ansi-align@npm:3.0.1":
  locations:
    - "node_modules/ansi-align"

"ansi-colors@npm:4.1.3":
  locations:
    - "node_modules/ansi-colors"

"ansi-escapes@npm:4.3.2":
  locations:
    - "node_modules/ansi-escapes"

"ansi-escapes@npm:7.0.0":
  locations:
    - "node_modules/log-update/node_modules/ansi-escapes"

"ansi-regex@npm:2.1.1":
  locations:
    - "node_modules/ansi-regex"

"ansi-regex@npm:5.0.1":
  locations:
    - "node_modules/strip-ansi/node_modules/ansi-regex"
    - "node_modules/strip-ansi-cjs/node_modules/ansi-regex"

"ansi-regex@npm:6.1.0":
  locations:
    - "node_modules/wrap-ansi/node_modules/ansi-regex"
    - "node_modules/widest-line/node_modules/ansi-regex"
    - "node_modules/log-update/node_modules/ansi-regex"
    - "node_modules/cli-truncate/node_modules/ansi-regex"
    - "node_modules/boxen/node_modules/ansi-regex"
    - "node_modules/@isaacs/cliui/node_modules/ansi-regex"

"ansi-styles@npm:2.2.1":
  locations:
    - "node_modules/module/node_modules/ansi-styles"

"ansi-styles@npm:4.3.0":
  locations:
    - "node_modules/ansi-styles"

"ansi-styles@npm:5.2.0":
  locations:
    - "node_modules/pretty-format/node_modules/ansi-styles"

"ansi-styles@npm:6.2.1":
  locations:
    - "node_modules/wrap-ansi/node_modules/ansi-styles"
    - "node_modules/slice-ansi/node_modules/ansi-styles"
    - "node_modules/log-update/node_modules/ansi-styles"
    - "node_modules/@isaacs/cliui/node_modules/ansi-styles"

"ansis@npm:3.17.0":
  locations:
    - "node_modules/ansis"

"any-promise@npm:1.3.0":
  locations:
    - "node_modules/any-promise"

"anymatch@npm:3.1.3":
  locations:
    - "node_modules/anymatch"

"app-root-path@npm:3.1.0":
  locations:
    - "node_modules/app-root-path"

"append-field@npm:1.0.0":
  locations:
    - "node_modules/append-field"

"aproba@npm:2.0.0":
  locations:
    - "node_modules/aproba"

"are-we-there-yet@npm:2.0.0":
  locations:
    - "node_modules/are-we-there-yet"

"arg@npm:4.1.3":
  locations:
    - "node_modules/arg"

"arg@npm:5.0.2":
  locations:
    - "node_modules/tailwindcss/node_modules/arg"

"argparse@npm:1.0.10":
  locations:
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/argparse"

"argparse@npm:2.0.1":
  locations:
    - "node_modules/argparse"

"aria-query@npm:5.3.2":
  locations:
    - "node_modules/aria-query"

"arr-diff@npm:2.0.0":
  locations:
    - "node_modules/arr-diff"

"arr-flatten@npm:1.1.0":
  locations:
    - "node_modules/arr-flatten"

"array-buffer-byte-length@npm:1.0.2":
  locations:
    - "node_modules/array-buffer-byte-length"

"array-flatten@npm:1.1.1":
  locations:
    - "node_modules/array-flatten"

"array-includes@npm:3.1.9":
  locations:
    - "node_modules/array-includes"

"array-iterate@npm:2.0.1":
  locations:
    - "node_modules/array-iterate"

"array-timsort@npm:1.0.3":
  locations:
    - "node_modules/array-timsort"

"array-unique@npm:0.2.1":
  locations:
    - "node_modules/array-unique"

"array.prototype.findlast@npm:1.2.5":
  locations:
    - "node_modules/array.prototype.findlast"

"array.prototype.findlastindex@npm:1.2.6":
  locations:
    - "node_modules/array.prototype.findlastindex"

"array.prototype.flat@npm:1.3.3":
  locations:
    - "node_modules/array.prototype.flat"

"array.prototype.flatmap@npm:1.3.3":
  locations:
    - "node_modules/array.prototype.flatmap"

"array.prototype.tosorted@npm:1.1.4":
  locations:
    - "node_modules/array.prototype.tosorted"

"arraybuffer.prototype.slice@npm:1.0.4":
  locations:
    - "node_modules/arraybuffer.prototype.slice"

"asap@npm:2.0.6":
  locations:
    - "node_modules/asap"

"assert-never@npm:1.4.0":
  locations:
    - "node_modules/assert-never"

"ast-types-flow@npm:0.0.8":
  locations:
    - "node_modules/ast-types-flow"

"astro-eslint-parser@npm:1.2.2":
  locations:
    - "node_modules/astro-eslint-parser"

"astro@npm:5.9.1":
  locations:
    - "node_modules/astro"

"astrojs-compiler-sync@virtual:c29538ab7decee0a049826ce4ae1afa7fb7087668f58eb4593d69afeafba1a714360d24f6f7d3dc844cc9653a92b6a0b9eab1be021b316ece2230c248a4f609e#npm:1.1.1":
  locations:
    - "node_modules/astrojs-compiler-sync"

"async-function@npm:1.0.0":
  locations:
    - "node_modules/async-function"

"async@npm:3.2.6":
  locations:
    - "node_modules/async"

"asynckit@npm:0.4.0":
  locations:
    - "node_modules/asynckit"

"atob@npm:2.1.2":
  locations:
    - "node_modules/atob"

"available-typed-arrays@npm:1.0.7":
  locations:
    - "node_modules/available-typed-arrays"

"aws-sdk@npm:2.1692.0":
  locations:
    - "node_modules/aws-sdk"

"axe-core@npm:4.10.3":
  locations:
    - "node_modules/axe-core"

"axios@npm:1.9.0":
  locations:
    - "node_modules/axios"

"axobject-query@npm:4.1.0":
  locations:
    - "node_modules/axobject-query"

"babel-dead-code-elimination@npm:1.0.10":
  locations:
    - "node_modules/babel-dead-code-elimination"

"babel-jest@virtual:353c408ca656e340a4089a9683de2b414c08e003d33109cd94e414b86a23524237b614b171e1fb56eb4f57d641441d6802b4dc7615497a4e6ca86d850141ef9a#npm:29.7.0":
  locations:
    - "node_modules/babel-jest"

"babel-plugin-istanbul@npm:6.1.1":
  locations:
    - "node_modules/babel-plugin-istanbul"

"babel-plugin-jest-hoist@npm:29.6.3":
  locations:
    - "node_modules/babel-plugin-jest-hoist"

"babel-plugin-macros@npm:3.1.0":
  locations:
    - "node_modules/babel-plugin-macros"

"babel-preset-current-node-syntax@virtual:15ef0a4ad61c166598c4d195dc64a0b7270b186e9a584ea25871b4181189fa5a61a49aa37f6bcda6ffed25499ff900f1a33224b0c22868c8eb1eaf1dd4f0dc11#npm:1.1.0":
  locations:
    - "node_modules/babel-preset-current-node-syntax"
  aliases:
    - "virtual:8908a3ce51d8fccbe3f8dac1fe445d9ee4f68cfdcee48a3077b008a0bb801d6d4fa9c2f4543578ca69326fd19991687206df13bf27c5c02bda7fccb2341af7b8#npm:1.1.0"

"babel-preset-jest@virtual:810cc3ccc73c92baaf2f050e2f9448e54ff77fa2e0c1416427716e53b3d3cb1a74140ce931968cb3ff3e49e0761c12eb0f00aa03de2d74d7658282c36784018c#npm:29.6.3":
  locations:
    - "node_modules/babel-preset-jest"

"babel-walk@npm:3.0.0-canary-5":
  locations:
    - "node_modules/babel-walk"

"bail@npm:2.0.2":
  locations:
    - "node_modules/bail"

"balanced-match@npm:1.0.2":
  locations:
    - "node_modules/balanced-match"

"base-64@npm:1.0.0":
  locations:
    - "node_modules/base-64"

"base64-arraybuffer@npm:1.0.2":
  locations:
    - "node_modules/base64-arraybuffer"

"base64-js@npm:1.5.1":
  locations:
    - "node_modules/base64-js"

"bcrypt@npm:5.1.1":
  locations:
    - "node_modules/bcrypt"

"binary-extensions@npm:2.3.0":
  locations:
    - "node_modules/binary-extensions"

"bindings@npm:1.5.0":
  locations:
    - "node_modules/bindings"

"bl@npm:4.1.0":
  locations:
    - "node_modules/bl"

"blob-to-buffer@npm:1.2.9":
  locations:
    - "node_modules/blob-to-buffer"

"body-parser@npm:1.20.3":
  locations:
    - "node_modules/body-parser"

"boolbase@npm:1.0.0":
  locations:
    - "node_modules/boolbase"

"boxen@npm:8.0.1":
  locations:
    - "node_modules/boxen"

"brace-expansion@npm:1.1.11":
  locations:
    - "node_modules/minimatch/node_modules/brace-expansion"

"brace-expansion@npm:2.0.1":
  locations:
    - "node_modules/brace-expansion"

"braces@npm:1.8.5":
  locations:
    - "node_modules/glob-stream/node_modules/braces"

"braces@npm:3.0.3":
  locations:
    - "node_modules/braces"

"brotli@npm:1.3.3":
  locations:
    - "node_modules/brotli"

"browserslist@npm:4.25.0":
  locations:
    - "node_modules/browserslist"

"bs-logger@npm:0.2.6":
  locations:
    - "node_modules/bs-logger"

"bser@npm:2.1.1":
  locations:
    - "node_modules/bser"

"buffer-builder@npm:0.2.0":
  locations:
    - "node_modules/buffer-builder"

"buffer-from@npm:0.1.2":
  locations:
    - "node_modules/html-tokenize/node_modules/buffer-from"

"buffer-from@npm:1.1.2":
  locations:
    - "node_modules/buffer-from"

"buffer@npm:4.9.2":
  locations:
    - "node_modules/buffer"

"buffer@npm:5.7.1":
  locations:
    - "node_modules/bl/node_modules/buffer"

"buffer@npm:6.0.3":
  locations:
    - "node_modules/typeorm/node_modules/buffer"

"busboy@npm:1.6.0":
  locations:
    - "node_modules/busboy"

"bytes@npm:3.1.2":
  locations:
    - "node_modules/bytes"

"cacache@npm:19.0.1":
  locations:
    - "node_modules/cacache"

"call-bind-apply-helpers@npm:1.0.2":
  locations:
    - "node_modules/call-bind-apply-helpers"

"call-bind@npm:1.0.8":
  locations:
    - "node_modules/call-bind"

"call-bound@npm:1.0.4":
  locations:
    - "node_modules/call-bound"

"callsites@npm:3.1.0":
  locations:
    - "node_modules/callsites"

"camel-case@npm:3.0.0":
  locations:
    - "node_modules/camel-case"

"camelcase-css@npm:2.0.1":
  locations:
    - "node_modules/camelcase-css"

"camelcase@npm:2.1.1":
  locations:
    - "node_modules/camelcase"

"camelcase@npm:3.0.0":
  locations:
    - "node_modules/module/node_modules/yargs-parser/node_modules/camelcase"

"camelcase@npm:5.3.1":
  locations:
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/camelcase"

"camelcase@npm:6.3.0":
  locations:
    - "node_modules/jest-validate/node_modules/camelcase"

"camelcase@npm:8.0.0":
  locations:
    - "node_modules/boxen/node_modules/camelcase"

"camelize@npm:1.0.1":
  locations:
    - "node_modules/camelize"

"caniuse-lite@npm:1.0.30001721":
  locations:
    - "node_modules/caniuse-lite"

"ccount@npm:2.0.1":
  locations:
    - "node_modules/ccount"

"chalk@npm:1.1.3":
  locations:
    - "node_modules/module/node_modules/chalk"

"chalk@npm:3.0.0":
  locations:
    - "node_modules/fixpack/node_modules/chalk"

"chalk@npm:4.1.2":
  locations:
    - "node_modules/chalk"

"chalk@npm:5.4.1":
  locations:
    - "node_modules/lint-staged/node_modules/chalk"
    - "node_modules/inquirer/node_modules/chalk"
    - "node_modules/boxen/node_modules/chalk"

"char-regex@npm:1.0.2":
  locations:
    - "node_modules/char-regex"

"character-entities-html4@npm:2.1.0":
  locations:
    - "node_modules/character-entities-html4"

"character-entities-legacy@npm:3.0.0":
  locations:
    - "node_modules/character-entities-legacy"

"character-entities@npm:2.0.2":
  locations:
    - "node_modules/character-entities"

"character-parser@npm:2.2.0":
  locations:
    - "node_modules/character-parser"

"chardet@npm:0.7.0":
  locations:
    - "node_modules/chardet"

"cheerio-select@npm:2.1.0":
  locations:
    - "node_modules/cheerio-select"

"cheerio@npm:1.0.0-rc.12":
  locations:
    - "node_modules/cheerio"

"chokidar@npm:3.6.0":
  locations:
    - "node_modules/chokidar"

"chokidar@npm:4.0.3":
  locations:
    - "node_modules/unstorage/node_modules/chokidar"

"chownr@npm:2.0.0":
  locations:
    - "node_modules/chownr"

"chownr@npm:3.0.0":
  locations:
    - "node_modules/tar/node_modules/chownr"

"chrome-trace-event@npm:1.0.4":
  locations:
    - "node_modules/chrome-trace-event"

"ci-info@npm:3.9.0":
  locations:
    - "node_modules/ci-info"

"ci-info@npm:4.2.0":
  locations:
    - "node_modules/astro/node_modules/ci-info"
    - "node_modules/@astrojs/telemetry/node_modules/ci-info"

"cjs-module-lexer@npm:1.4.3":
  locations:
    - "node_modules/cjs-module-lexer"

"class-transformer@npm:0.5.1":
  locations:
    - "node_modules/class-transformer"

"class-validator@npm:0.14.2":
  locations:
    - "node_modules/class-validator"

"clean-css@npm:4.2.4":
  locations:
    - "node_modules/clean-css"

"cli-boxes@npm:3.0.0":
  locations:
    - "node_modules/cli-boxes"

"cli-cursor@npm:3.1.0":
  locations:
    - "node_modules/cli-cursor"

"cli-cursor@npm:5.0.0":
  locations:
    - "node_modules/log-update/node_modules/cli-cursor"

"cli-spinners@npm:2.9.2":
  locations:
    - "node_modules/cli-spinners"

"cli-table3@npm:0.6.5":
  locations:
    - "node_modules/cli-table3"

"cli-truncate@npm:4.0.0":
  locations:
    - "node_modules/cli-truncate"

"cli-width@npm:3.0.0":
  locations:
    - "node_modules/@nestjs/cli/node_modules/cli-width"

"cli-width@npm:4.1.0":
  locations:
    - "node_modules/cli-width"

"client-only@npm:0.0.1":
  locations:
    - "node_modules/client-only"

"cliui@npm:3.2.0":
  locations:
    - "node_modules/cliui"

"cliui@npm:8.0.1":
  locations:
    - "node_modules/yargs/node_modules/cliui"

"clone-stats@npm:0.0.1":
  locations:
    - "node_modules/clone-stats"

"clone@npm:1.0.4":
  locations:
    - "node_modules/clone"

"clone@npm:2.1.2":
  locations:
    - "node_modules/fontkit/node_modules/clone"

"clsx@npm:1.2.1":
  locations:
    - "node_modules/react-draggable/node_modules/clsx"

"clsx@npm:2.1.1":
  locations:
    - "node_modules/clsx"

"co@npm:4.6.0":
  locations:
    - "node_modules/co"

"code-point-at@npm:1.1.0":
  locations:
    - "node_modules/code-point-at"

"collect-v8-coverage@npm:1.0.2":
  locations:
    - "node_modules/collect-v8-coverage"

"color-convert@npm:1.9.3":
  locations:
    - "node_modules/color/node_modules/color-convert"

"color-convert@npm:2.0.1":
  locations:
    - "node_modules/color-convert"

"color-name@npm:1.1.3":
  locations:
    - "node_modules/color/node_modules/color-name"

"color-name@npm:1.1.4":
  locations:
    - "node_modules/color-name"

"color-string@npm:1.9.1":
  locations:
    - "node_modules/color-string"

"color-support@npm:1.1.3":
  locations:
    - "node_modules/color-support"

"color@npm:3.2.1":
  locations:
    - "node_modules/color"

"color@npm:4.2.3":
  locations:
    - "node_modules/sharp/node_modules/color"

"colorette@npm:2.0.20":
  locations:
    - "node_modules/colorette"

"colorjs.io@npm:0.5.2":
  locations:
    - "node_modules/colorjs.io"

"colorspace@npm:1.1.4":
  locations:
    - "node_modules/colorspace"

"combined-stream@npm:1.0.8":
  locations:
    - "node_modules/combined-stream"

"comma-separated-tokens@npm:2.0.3":
  locations:
    - "node_modules/comma-separated-tokens"

"commander@npm:10.0.1":
  locations:
    - "node_modules/commander"

"commander@npm:14.0.0":
  locations:
    - "node_modules/lint-staged/node_modules/commander"

"commander@npm:2.20.3":
  locations:
    - "node_modules/terser/node_modules/commander"
    - "node_modules/html-minifier/node_modules/commander"

"commander@npm:4.1.1":
  locations:
    - "node_modules/sucrase/node_modules/commander"
    - "node_modules/@nestjs/cli/node_modules/commander"

"commander@npm:6.2.1":
  locations:
    - "node_modules/juice/node_modules/commander"

"comment-json@npm:4.2.5":
  locations:
    - "node_modules/comment-json"

"common-ancestor-path@npm:1.0.1":
  locations:
    - "node_modules/common-ancestor-path"

"component-emitter@npm:1.3.1":
  locations:
    - "node_modules/component-emitter"

"concat-map@npm:0.0.1":
  locations:
    - "node_modules/concat-map"

"concat-stream@npm:1.5.1":
  locations:
    - "node_modules/concat-stream"

"concat-stream@npm:2.0.0":
  locations:
    - "node_modules/multer/node_modules/concat-stream"

"config-chain@npm:1.1.13":
  locations:
    - "node_modules/config-chain"

"consola@npm:2.15.3":
  locations:
    - "node_modules/consola"

"console-control-strings@npm:1.1.0":
  locations:
    - "node_modules/console-control-strings"

"constantinople@npm:4.0.1":
  locations:
    - "node_modules/constantinople"

"content-disposition@npm:0.5.4":
  locations:
    - "node_modules/content-disposition"

"content-type@npm:1.0.5":
  locations:
    - "node_modules/content-type"

"convert-source-map@npm:1.9.0":
  locations:
    - "node_modules/gulp-sourcemaps/node_modules/convert-source-map"
    - "node_modules/@emotion/babel-plugin/node_modules/convert-source-map"

"convert-source-map@npm:2.0.0":
  locations:
    - "node_modules/convert-source-map"

"cookie-es@npm:1.2.2":
  locations:
    - "node_modules/cookie-es"

"cookie-signature@npm:1.0.6":
  locations:
    - "node_modules/cookie-signature"

"cookie@npm:0.7.1":
  locations:
    - "node_modules/express/node_modules/cookie"

"cookie@npm:1.0.2":
  locations:
    - "node_modules/cookie"

"cookiejar@npm:2.1.4":
  locations:
    - "node_modules/cookiejar"

"core-util-is@npm:1.0.3":
  locations:
    - "node_modules/core-util-is"

"cors@npm:2.8.5":
  locations:
    - "node_modules/cors"

"cosmiconfig@npm:7.1.0":
  locations:
    - "node_modules/cosmiconfig"

"cosmiconfig@virtual:0a123c628a76799bf3bce2b667fc9919ff00b9fef639abb2e43d92b438fd8ffba9aa4c60a8e3c4e20e28b5d9dbacced7878a79c19ff53b14c6a91856f6216870#npm:8.3.6":
  locations:
    - "node_modules/@nestjs/cli/node_modules/cosmiconfig"

"create-jest@npm:29.7.0":
  locations:
    - "node_modules/create-jest"

"create-require@npm:1.1.1":
  locations:
    - "node_modules/create-require"

"crelt@npm:1.0.6":
  locations:
    - "node_modules/crelt"

"cron@npm:3.2.1":
  locations:
    - "node_modules/cron"

"cross-fetch@npm:3.2.0":
  locations:
    - "node_modules/cross-fetch"

"cross-spawn@npm:6.0.6":
  locations:
    - "node_modules/execa/node_modules/cross-spawn"

"cross-spawn@npm:7.0.6":
  locations:
    - "node_modules/cross-spawn"

"crossws@npm:0.3.5":
  locations:
    - "node_modules/crossws"

"css-box-model@npm:1.2.1":
  locations:
    - "node_modules/css-box-model"

"css-color-keywords@npm:1.0.0":
  locations:
    - "node_modules/css-color-keywords"

"css-line-break@npm:2.1.0":
  locations:
    - "node_modules/css-line-break"

"css-select@npm:5.1.0":
  locations:
    - "node_modules/css-select"

"css-to-react-native@npm:3.2.0":
  locations:
    - "node_modules/css-to-react-native"

"css-tree@npm:3.1.0":
  locations:
    - "node_modules/css-tree"

"css-what@npm:6.1.0":
  locations:
    - "node_modules/css-what"

"css@npm:2.2.4":
  locations:
    - "node_modules/css"

"cssesc@npm:3.0.0":
  locations:
    - "node_modules/cssesc"

"csstype@npm:3.1.3":
  locations:
    - "node_modules/csstype"

"csv-parser@npm:3.2.0":
  locations:
    - "node_modules/csv-parser"

"csv-stringify@npm:6.5.2":
  locations:
    - "node_modules/csv-stringify"

"culori@npm:4.0.1":
  locations:
    - "node_modules/culori"

"d3-array@npm:3.2.4":
  locations:
    - "node_modules/d3-array"

"d3-color@npm:3.1.0":
  locations:
    - "node_modules/d3-color"

"d3-delaunay@npm:6.0.4":
  locations:
    - "node_modules/d3-delaunay"

"d3-format@npm:3.1.0":
  locations:
    - "node_modules/d3-format"

"d3-interpolate@npm:3.0.1":
  locations:
    - "node_modules/d3-interpolate"

"d3-path@npm:3.1.0":
  locations:
    - "node_modules/d3-path"

"d3-scale@npm:4.0.2":
  locations:
    - "node_modules/d3-scale"

"d3-shape@npm:3.2.0":
  locations:
    - "node_modules/d3-shape"

"d3-time-format@npm:4.1.0":
  locations:
    - "node_modules/d3-time-format"

"d3-time@npm:3.1.0":
  locations:
    - "node_modules/d3-time"

"damerau-levenshtein@npm:1.0.8":
  locations:
    - "node_modules/damerau-levenshtein"

"data-view-buffer@npm:1.0.2":
  locations:
    - "node_modules/data-view-buffer"

"data-view-byte-length@npm:1.0.2":
  locations:
    - "node_modules/data-view-byte-length"

"data-view-byte-offset@npm:1.0.1":
  locations:
    - "node_modules/data-view-byte-offset"

"dayjs@npm:1.11.13":
  locations:
    - "node_modules/dayjs"

"debug-fabulous@npm:0.0.4":
  locations:
    - "node_modules/debug-fabulous"

"debug@virtual:1ff4b5f90832ba0a9c93ba1223af226e44ba70c1126a3740d93562b97bc36544e896a5e95908196f7458713e6a6089a34bfc67362fc6df7fa093bd06c878be47#npm:4.4.1":
  locations:
    - "node_modules/typeorm/node_modules/debug"
    - "node_modules/superagent/node_modules/debug"
    - "node_modules/socks-proxy-agent/node_modules/debug"
    - "node_modules/micromark/node_modules/debug"
    - "node_modules/lint-staged/node_modules/debug"
    - "node_modules/istanbul-lib-source-maps/node_modules/debug"
    - "node_modules/https-proxy-agent/node_modules/debug"
    - "node_modules/http-proxy-agent/node_modules/debug"
    - "node_modules/eslint/node_modules/debug"
    - "node_modules/eslint-import-resolver-typescript/node_modules/debug"
    - "node_modules/astro/node_modules/debug"
    - "node_modules/astro-eslint-parser/node_modules/debug"
    - "node_modules/@typescript-eslint/typescript-estree/node_modules/debug"
    - "node_modules/@typescript-eslint/type-utils/node_modules/debug"
    - "node_modules/@typescript-eslint/project-service/node_modules/debug"
    - "node_modules/@typescript-eslint/parser/node_modules/debug"
    - "node_modules/@tokenizer/inflate/node_modules/debug"
    - "node_modules/@npmcli/agent/node_modules/debug"
    - "node_modules/@humanwhocodes/config-array/node_modules/debug"
    - "node_modules/@eslint/eslintrc/node_modules/debug"
    - "node_modules/@babel/traverse/node_modules/debug"
    - "node_modules/@babel/core/node_modules/debug"
    - "node_modules/@astrojs/telemetry/node_modules/debug"

"debug@virtual:96ae22c60301efd863de91c4ec8466760c91798bc6984361771d597c6d303a622340cc9acb0f6fdb46dbb7bc0133aee65314168f69f2cb314a47527d822b0781#npm:2.6.9":
  locations:
    - "node_modules/debug"

"debug@virtual:c1df2a191bb744c70b450e4b47633a3ed90bfe993a93ee6b47b7766c17bd2e1308edd5c836b4847f25f83fcdce38a10b55d236c310d8f496dcb6d5afea2be399#npm:3.2.7":
  locations:
    - "node_modules/jsftp/node_modules/debug"
    - "node_modules/eslint-plugin-import/node_modules/debug"
    - "node_modules/eslint-module-utils/node_modules/debug"
    - "node_modules/eslint-import-resolver-node/node_modules/debug"

"decamelize@npm:1.2.0":
  locations:
    - "node_modules/decamelize"

"decode-named-character-reference@npm:1.1.0":
  locations:
    - "node_modules/decode-named-character-reference"

"decode-uri-component@npm:0.2.2":
  locations:
    - "node_modules/decode-uri-component"

"decode-uri-component@npm:0.4.1":
  locations:
    - "node_modules/query-string/node_modules/decode-uri-component"

"dedent@virtual:f7679858c638e2e5ade31901dd2b1e5007918fdc7d84fefb11f4200f46ba2e43b9d662fb793507b517bb1e725144e51f6d68f60f9f6100fd52144f042f58f0bc#npm:1.6.0":
  locations:
    - "node_modules/dedent"

"deep-extend@npm:0.6.0":
  locations:
    - "node_modules/deep-extend"

"deep-is@npm:0.1.4":
  locations:
    - "node_modules/deep-is"

"deepmerge@npm:4.3.1":
  locations:
    - "node_modules/deepmerge"

"defaults@npm:1.0.4":
  locations:
    - "node_modules/defaults"

"define-data-property@npm:1.1.4":
  locations:
    - "node_modules/define-data-property"

"define-properties@npm:1.2.1":
  locations:
    - "node_modules/define-properties"

"defu@npm:6.1.4":
  locations:
    - "node_modules/defu"

"delaunator@npm:5.0.1":
  locations:
    - "node_modules/delaunator"

"delayed-stream@npm:1.0.0":
  locations:
    - "node_modules/delayed-stream"

"delegates@npm:1.0.0":
  locations:
    - "node_modules/delegates"

"depd@npm:2.0.0":
  locations:
    - "node_modules/depd"

"dequal@npm:2.0.3":
  locations:
    - "node_modules/dequal"

"destr@npm:2.0.5":
  locations:
    - "node_modules/destr"

"destroy@npm:1.2.0":
  locations:
    - "node_modules/destroy"

"detect-indent@npm:6.1.0":
  locations:
    - "node_modules/detect-indent"

"detect-libc@npm:2.0.4":
  locations:
    - "node_modules/detect-libc"

"detect-newline@npm:2.1.0":
  locations:
    - "node_modules/gulp-sourcemaps/node_modules/detect-newline"

"detect-newline@npm:3.1.0":
  locations:
    - "node_modules/detect-newline"

"detect-node@npm:2.1.0":
  locations:
    - "node_modules/detect-node"

"deterministic-object-hash@npm:2.0.2":
  locations:
    - "node_modules/deterministic-object-hash"

"devalue@npm:5.1.1":
  locations:
    - "node_modules/devalue"

"devlop@npm:1.1.0":
  locations:
    - "node_modules/devlop"

"dezalgo@npm:1.0.4":
  locations:
    - "node_modules/dezalgo"

"dfa@npm:1.2.0":
  locations:
    - "node_modules/dfa"

"didyoumean@npm:1.2.2":
  locations:
    - "node_modules/didyoumean"

"diff-sequences@npm:29.6.3":
  locations:
    - "node_modules/diff-sequences"

"diff@npm:4.0.2":
  locations:
    - "node_modules/diff"

"diff@npm:5.2.0":
  locations:
    - "node_modules/astro/node_modules/diff"

"diff@npm:7.0.0":
  locations:
    - "node_modules/@tanstack/router-utils/node_modules/diff"

"display-notification@npm:2.0.0":
  locations:
    - "node_modules/display-notification"

"dlv@npm:1.1.3":
  locations:
    - "node_modules/dlv"

"dnd-core@npm:16.0.1":
  locations:
    - "node_modules/dnd-core"

"doctrine@npm:2.1.0":
  locations:
    - "node_modules/doctrine"

"doctrine@npm:3.0.0":
  locations:
    - "node_modules/eslint/node_modules/doctrine"

"doctypes@npm:1.1.0":
  locations:
    - "node_modules/doctypes"

"dom-helpers@npm:5.2.1":
  locations:
    - "node_modules/dom-helpers"

"dom-serializer@npm:1.4.1":
  locations:
    - "node_modules/web-resource-inliner/node_modules/dom-serializer"

"dom-serializer@npm:2.0.0":
  locations:
    - "node_modules/dom-serializer"

"domelementtype@npm:2.3.0":
  locations:
    - "node_modules/domelementtype"

"domhandler@npm:3.3.0":
  locations:
    - "node_modules/web-resource-inliner/node_modules/htmlparser2/node_modules/domhandler"

"domhandler@npm:4.3.1":
  locations:
    - "node_modules/web-resource-inliner/node_modules/domhandler"

"domhandler@npm:5.0.3":
  locations:
    - "node_modules/domhandler"

"domutils@npm:2.8.0":
  locations:
    - "node_modules/web-resource-inliner/node_modules/domutils"

"domutils@npm:3.2.2":
  locations:
    - "node_modules/domutils"

"dotenv-expand@npm:10.0.0":
  locations:
    - "node_modules/dotenv-expand"

"dotenv@npm:16.4.5":
  locations:
    - "node_modules/dotenv"

"dotenv@npm:16.5.0":
  locations:
    - "node_modules/typeorm/node_modules/dotenv"

"dset@npm:3.1.4":
  locations:
    - "node_modules/dset"

"dunder-proto@npm:1.0.1":
  locations:
    - "node_modules/dunder-proto"

"duplexer2@npm:0.1.4":
  locations:
    - "node_modules/duplexer2"

"duplexer@npm:0.1.2":
  locations:
    - "node_modules/duplexer"

"duplexify@npm:3.7.1":
  locations:
    - "node_modules/duplexify"

"eastasianwidth@npm:0.2.0":
  locations:
    - "node_modules/eastasianwidth"

"editorconfig@npm:1.0.4":
  locations:
    - "node_modules/editorconfig"

"ee-first@npm:1.1.1":
  locations:
    - "node_modules/ee-first"

"ejs@npm:3.1.10":
  locations:
    - "node_modules/ejs"

"electron-to-chromium@npm:1.5.165":
  locations:
    - "node_modules/electron-to-chromium"

"emittery@npm:0.13.1":
  locations:
    - "node_modules/emittery"

"emoji-regex@npm:10.4.0":
  locations:
    - "node_modules/wrap-ansi/node_modules/emoji-regex"
    - "node_modules/widest-line/node_modules/emoji-regex"
    - "node_modules/cli-truncate/node_modules/emoji-regex"
    - "node_modules/boxen/node_modules/emoji-regex"

"emoji-regex@npm:8.0.0":
  locations:
    - "node_modules/string-width/node_modules/emoji-regex"
    - "node_modules/string-width-cjs/node_modules/emoji-regex"

"emoji-regex@npm:9.2.2":
  locations:
    - "node_modules/emoji-regex"

"enabled@npm:2.0.0":
  locations:
    - "node_modules/enabled"

"encodeurl@npm:1.0.2":
  locations:
    - "node_modules/send/node_modules/encodeurl"

"encodeurl@npm:2.0.0":
  locations:
    - "node_modules/encodeurl"

"encoding-japanese@npm:2.2.0":
  locations:
    - "node_modules/encoding-japanese"

"encoding@npm:0.1.13":
  locations:
    - "node_modules/encoding"

"end-of-stream@npm:1.4.4":
  locations:
    - "node_modules/end-of-stream"

"enhanced-resolve@npm:5.18.1":
  locations:
    - "node_modules/enhanced-resolve"

"entities@npm:2.2.0":
  locations:
    - "node_modules/web-resource-inliner/node_modules/entities"

"entities@npm:4.5.0":
  locations:
    - "node_modules/entities"

"entities@npm:6.0.1":
  locations:
    - "node_modules/parse5/node_modules/entities"
    - "node_modules/astro-eslint-parser/node_modules/entities"

"env-paths@npm:2.2.1":
  locations:
    - "node_modules/env-paths"

"environment@npm:1.1.0":
  locations:
    - "node_modules/environment"

"err-code@npm:2.0.3":
  locations:
    - "node_modules/err-code"

"error-ex@npm:1.3.2":
  locations:
    - "node_modules/error-ex"

"es-abstract@npm:1.24.0":
  locations:
    - "node_modules/es-abstract"

"es-define-property@npm:1.0.1":
  locations:
    - "node_modules/es-define-property"

"es-errors@npm:1.3.0":
  locations:
    - "node_modules/es-errors"

"es-iterator-helpers@npm:1.2.1":
  locations:
    - "node_modules/es-iterator-helpers"

"es-module-lexer@npm:1.7.0":
  locations:
    - "node_modules/es-module-lexer"

"es-object-atoms@npm:1.1.1":
  locations:
    - "node_modules/es-object-atoms"

"es-set-tostringtag@npm:2.1.0":
  locations:
    - "node_modules/es-set-tostringtag"

"es-shim-unscopables@npm:1.1.0":
  locations:
    - "node_modules/es-shim-unscopables"

"es-to-primitive@npm:1.3.0":
  locations:
    - "node_modules/es-to-primitive"

"es-toolkit@npm:1.39.3":
  locations:
    - "node_modules/es-toolkit"

"esbuild@npm:0.21.5":
  locations:
    - "node_modules/vite/node_modules/esbuild"

"esbuild@npm:0.25.5":
  locations:
    - "node_modules/esbuild"

"escalade@npm:3.2.0":
  locations:
    - "node_modules/escalade"

"escape-goat@npm:3.0.0":
  locations:
    - "node_modules/escape-goat"

"escape-html@npm:1.0.3":
  locations:
    - "node_modules/escape-html"

"escape-string-applescript@npm:1.0.0":
  locations:
    - "node_modules/escape-string-applescript"

"escape-string-regexp@npm:1.0.5":
  locations:
    - "node_modules/module/node_modules/escape-string-regexp"
    - "node_modules/figures/node_modules/escape-string-regexp"

"escape-string-regexp@npm:2.0.0":
  locations:
    - "node_modules/stack-utils/node_modules/escape-string-regexp"

"escape-string-regexp@npm:4.0.0":
  locations:
    - "node_modules/escape-string-regexp"

"escape-string-regexp@npm:5.0.0":
  locations:
    - "node_modules/mdast-util-find-and-replace/node_modules/escape-string-regexp"

"eslint-compat-utils@virtual:48fea4e37df6d3de91c6b02bae48bf10ef97fe1c3f1b7ac5ee3ec3d1a2f3d705a9cb27aad926049495222f07681fb466c6c382687aaf800b0f9c6735a3e2e940#npm:0.6.5":
  locations:
    - "node_modules/eslint-compat-utils"

"eslint-config-next@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:15.0.1":
  locations:
    - "node_modules/eslint-config-next"

"eslint-config-prettier@virtual:7500e53e5794f26bce60f75d5f75f5411d77123ef1b5430b10e6a6b95d69cf9f113b9a177237f485c5cc0d748dffc13299adf0cf03f6e1ab049a65027d84ebff#npm:10.1.5":
  locations:
    - "node_modules/eslint-config-prettier"

"eslint-import-resolver-node@npm:0.3.9":
  locations:
    - "node_modules/eslint-import-resolver-node"

"eslint-import-resolver-typescript@virtual:82efb915828844d6e0b369e803aaa43cd0c34aa5b9bd875df702a9dc13d183897d25046b49999a299eb5ec621fc261b1ec815f1549c5392436e1c19d30b5abc1#npm:3.10.1":
  locations:
    - "node_modules/eslint-import-resolver-typescript"

"eslint-module-utils@virtual:985cc03d277bcff14d323eb1a42c2a9ad532a5071d69be7926a2b371892bc0de1c6950cad00d12c322d127351a8b90a7fe83abfebef57c0f691e1e8d5f98096f#npm:2.12.0":
  locations:
    - "node_modules/eslint-module-utils"

"eslint-plugin-astro@virtual:f076ded31fccc59d4f9e6ced6349f34297f2dd657b8275061704bbe29a11a614ffe29c030a051fc61b74e50142507a1ec03a156876f30c45951e0e1e6cc4795e#npm:1.3.1":
  locations:
    - "node_modules/eslint-plugin-astro"

"eslint-plugin-import@virtual:82efb915828844d6e0b369e803aaa43cd0c34aa5b9bd875df702a9dc13d183897d25046b49999a299eb5ec621fc261b1ec815f1549c5392436e1c19d30b5abc1#npm:2.31.0":
  locations:
    - "node_modules/eslint-plugin-import"

"eslint-plugin-jsx-a11y@virtual:82efb915828844d6e0b369e803aaa43cd0c34aa5b9bd875df702a9dc13d183897d25046b49999a299eb5ec621fc261b1ec815f1549c5392436e1c19d30b5abc1#npm:6.10.2":
  locations:
    - "node_modules/eslint-plugin-jsx-a11y"

"eslint-plugin-prettier@virtual:7500e53e5794f26bce60f75d5f75f5411d77123ef1b5430b10e6a6b95d69cf9f113b9a177237f485c5cc0d748dffc13299adf0cf03f6e1ab049a65027d84ebff#npm:5.4.1":
  locations:
    - "node_modules/eslint-plugin-prettier"

"eslint-plugin-react-hooks@virtual:7500e53e5794f26bce60f75d5f75f5411d77123ef1b5430b10e6a6b95d69cf9f113b9a177237f485c5cc0d748dffc13299adf0cf03f6e1ab049a65027d84ebff#npm:5.2.0":
  locations:
    - "node_modules/eslint-plugin-react-hooks"
  aliases:
    - "virtual:82efb915828844d6e0b369e803aaa43cd0c34aa5b9bd875df702a9dc13d183897d25046b49999a299eb5ec621fc261b1ec815f1549c5392436e1c19d30b5abc1#npm:5.2.0"

"eslint-plugin-react-refresh@virtual:7500e53e5794f26bce60f75d5f75f5411d77123ef1b5430b10e6a6b95d69cf9f113b9a177237f485c5cc0d748dffc13299adf0cf03f6e1ab049a65027d84ebff#npm:0.4.20":
  locations:
    - "node_modules/eslint-plugin-react-refresh"

"eslint-plugin-react@virtual:82efb915828844d6e0b369e803aaa43cd0c34aa5b9bd875df702a9dc13d183897d25046b49999a299eb5ec621fc261b1ec815f1549c5392436e1c19d30b5abc1#npm:7.37.5":
  locations:
    - "node_modules/eslint-plugin-react"

"eslint-scope@npm:5.1.1":
  locations:
    - "node_modules/webpack/node_modules/eslint-scope"

"eslint-scope@npm:7.2.2":
  locations:
    - "node_modules/eslint-scope"

"eslint-scope@npm:8.3.0":
  locations:
    - "node_modules/astro-eslint-parser/node_modules/eslint-scope"

"eslint-visitor-keys@npm:3.4.3":
  locations:
    - "node_modules/eslint-visitor-keys"

"eslint-visitor-keys@npm:4.2.0":
  locations:
    - "node_modules/astro-eslint-parser/node_modules/eslint-visitor-keys"
    - "node_modules/@typescript-eslint/visitor-keys/node_modules/eslint-visitor-keys"

"eslint@npm:8.57.1":
  locations:
    - "node_modules/eslint"

"espree@npm:10.3.0":
  locations:
    - "node_modules/astro-eslint-parser/node_modules/espree"

"espree@npm:9.6.1":
  locations:
    - "node_modules/espree"

"esprima@npm:1.2.5":
  locations:
    - "node_modules/alce/node_modules/esprima"

"esprima@npm:4.0.1":
  locations:
    - "node_modules/esprima"

"esquery@npm:1.6.0":
  locations:
    - "node_modules/esquery"

"esrecurse@npm:4.3.0":
  locations:
    - "node_modules/esrecurse"

"estraverse@npm:1.9.3":
  locations:
    - "node_modules/alce/node_modules/estraverse"

"estraverse@npm:4.3.0":
  locations:
    - "node_modules/webpack/node_modules/estraverse"

"estraverse@npm:5.3.0":
  locations:
    - "node_modules/estraverse"

"estree-walker@npm:2.0.2":
  locations:
    - "node_modules/@rollup/pluginutils/node_modules/estree-walker"

"estree-walker@npm:3.0.3":
  locations:
    - "node_modules/estree-walker"

"esutils@npm:2.0.3":
  locations:
    - "node_modules/esutils"

"etag@npm:1.8.1":
  locations:
    - "node_modules/etag"

"eventemitter2@npm:6.4.9":
  locations:
    - "node_modules/eventemitter2"

"eventemitter3@npm:5.0.1":
  locations:
    - "node_modules/eventemitter3"

"events@npm:1.1.1":
  locations:
    - "node_modules/events"

"events@npm:3.3.0":
  locations:
    - "node_modules/webpack/node_modules/events"

"execa@npm:0.10.0":
  locations:
    - "node_modules/execa"

"execa@npm:5.1.1":
  locations:
    - "node_modules/jest-changed-files/node_modules/execa"

"exit@npm:0.1.2":
  locations:
    - "node_modules/exit"

"expand-brackets@npm:0.1.5":
  locations:
    - "node_modules/expand-brackets"

"expand-range@npm:1.8.2":
  locations:
    - "node_modules/expand-range"

"expect@npm:29.7.0":
  locations:
    - "node_modules/expect"

"exponential-backoff@npm:3.1.2":
  locations:
    - "node_modules/exponential-backoff"

"express@npm:4.21.2":
  locations:
    - "node_modules/express"

"extend-object@npm:1.0.0":
  locations:
    - "node_modules/extend-object"

"extend-shallow@npm:2.0.1":
  locations:
    - "node_modules/extend-shallow"

"extend@npm:3.0.2":
  locations:
    - "node_modules/extend"

"external-editor@npm:3.1.0":
  locations:
    - "node_modules/external-editor"

"extglob@npm:0.3.2":
  locations:
    - "node_modules/extglob"

"fast-deep-equal@npm:3.1.3":
  locations:
    - "node_modules/fast-deep-equal"

"fast-diff@npm:1.3.0":
  locations:
    - "node_modules/fast-diff"

"fast-glob@npm:3.3.1":
  locations:
    - "node_modules/@next/eslint-plugin-next/node_modules/fast-glob"

"fast-glob@npm:3.3.3":
  locations:
    - "node_modules/fast-glob"

"fast-json-stable-stringify@npm:2.1.0":
  locations:
    - "node_modules/fast-json-stable-stringify"

"fast-levenshtein@npm:2.0.6":
  locations:
    - "node_modules/fast-levenshtein"

"fast-safe-stringify@npm:2.1.1":
  locations:
    - "node_modules/fast-safe-stringify"

"fast-uri@npm:3.0.6":
  locations:
    - "node_modules/fast-uri"

"fastq@npm:1.19.1":
  locations:
    - "node_modules/fastq"

"fb-watchman@npm:2.0.2":
  locations:
    - "node_modules/fb-watchman"

"fdir@virtual:d4e4bcf80e67f9de0540c123c7c4882e34dce6a8ba807a0a834f267f9132ee6bd264e69a49c6203aa89877ed3a5a5d633bfa002384881be452cc3a2d2fbcce0b#npm:6.4.5":
  locations:
    - "node_modules/fdir"

"fecha@npm:4.2.3":
  locations:
    - "node_modules/fecha"

"fflate@npm:0.8.2":
  locations:
    - "node_modules/fflate"

"figures@npm:3.2.0":
  locations:
    - "node_modules/figures"

"file-entry-cache@npm:6.0.1":
  locations:
    - "node_modules/file-entry-cache"

"file-type@npm:20.4.1":
  locations:
    - "node_modules/file-type"

"file-uri-to-path@npm:1.0.0":
  locations:
    - "node_modules/file-uri-to-path"

"filelist@npm:1.0.4":
  locations:
    - "node_modules/filelist"

"filename-regex@npm:2.0.1":
  locations:
    - "node_modules/filename-regex"

"fill-range@npm:2.2.4":
  locations:
    - "node_modules/expand-range/node_modules/fill-range"

"fill-range@npm:7.1.1":
  locations:
    - "node_modules/fill-range"

"filter-obj@npm:5.1.0":
  locations:
    - "node_modules/filter-obj"

"finalhandler@npm:1.3.1":
  locations:
    - "node_modules/finalhandler"

"find-root@npm:1.1.0":
  locations:
    - "node_modules/find-root"

"find-up@npm:1.1.2":
  locations:
    - "node_modules/find-up"

"find-up@npm:4.1.0":
  locations:
    - "node_modules/pkg-dir/node_modules/find-up"
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/find-up"

"find-up@npm:5.0.0":
  locations:
    - "node_modules/eslint/node_modules/find-up"

"first-chunk-stream@npm:1.0.0":
  locations:
    - "node_modules/first-chunk-stream"

"fixpack@npm:4.0.0":
  locations:
    - "node_modules/fixpack"

"flat-cache@npm:3.2.0":
  locations:
    - "node_modules/flat-cache"

"flatted@npm:3.3.3":
  locations:
    - "node_modules/flatted"

"flattie@npm:1.1.1":
  locations:
    - "node_modules/flattie"

"fn.name@npm:1.1.0":
  locations:
    - "node_modules/fn.name"

"follow-redirects@virtual:007b36cf567c5c0b0b89b54b5fd89ec08f3a2a884d74d5c6d450b18bf5cf65f53734a0c801662b1c2259c9afe08585fed46b3182771fef81b1aa08fab6405199#npm:1.15.9":
  locations:
    - "node_modules/follow-redirects"

"fontace@npm:0.3.0":
  locations:
    - "node_modules/fontace"

"fontkit@npm:2.0.4":
  locations:
    - "node_modules/fontkit"

"for-each@npm:0.3.5":
  locations:
    - "node_modules/for-each"

"for-in@npm:1.0.2":
  locations:
    - "node_modules/for-in"

"for-own@npm:0.1.5":
  locations:
    - "node_modules/for-own"

"foreground-child@npm:3.3.1":
  locations:
    - "node_modules/foreground-child"

"fork-ts-checker-webpack-plugin@virtual:27d6a4f677023da2b7228beb18e0dc4d1e1d463ea5073fc0f39e927d08377f976d9992ac83c3ceb8276fa5af4d91302d6d195ad7c22daff89a91b254c544b70e#npm:9.0.2":
  locations:
    - "node_modules/@nestjs/cli/node_modules/fork-ts-checker-webpack-plugin"

"form-data@npm:4.0.3":
  locations:
    - "node_modules/form-data"

"formidable@npm:3.5.4":
  locations:
    - "node_modules/formidable"

"forwarded@npm:0.2.0":
  locations:
    - "node_modules/forwarded"

"framer-motion@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:11.18.2":
  locations:
    - "node_modules/framer-motion"

"fresh@npm:0.5.2":
  locations:
    - "node_modules/fresh"

"fs-extra@npm:10.1.0":
  locations:
    - "node_modules/fs-extra"

"fs-minipass@npm:2.1.0":
  locations:
    - "node_modules/fs-minipass"

"fs-minipass@npm:3.0.3":
  locations:
    - "node_modules/cacache/node_modules/fs-minipass"

"fs-monkey@npm:1.0.6":
  locations:
    - "node_modules/fs-monkey"

"fs.realpath@npm:1.0.0":
  locations:
    - "node_modules/fs.realpath"

"ftp-response-parser@npm:1.0.1":
  locations:
    - "node_modules/ftp-response-parser"

"function-bind@npm:1.1.2":
  locations:
    - "node_modules/function-bind"

"function.prototype.name@npm:1.1.8":
  locations:
    - "node_modules/function.prototype.name"

"functions-have-names@npm:1.2.3":
  locations:
    - "node_modules/functions-have-names"

"gauge@npm:3.0.2":
  locations:
    - "node_modules/gauge"

"gensync@npm:1.0.0-beta.2":
  locations:
    - "node_modules/gensync"

"get-caller-file@npm:2.0.5":
  locations:
    - "node_modules/get-caller-file"

"get-east-asian-width@npm:1.3.0":
  locations:
    - "node_modules/get-east-asian-width"

"get-intrinsic@npm:1.3.0":
  locations:
    - "node_modules/get-intrinsic"

"get-package-type@npm:0.1.0":
  locations:
    - "node_modules/get-package-type"

"get-port@npm:5.1.1":
  locations:
    - "node_modules/get-port"

"get-proto@npm:1.0.1":
  locations:
    - "node_modules/get-proto"

"get-stream@npm:3.0.0":
  locations:
    - "node_modules/get-stream"

"get-stream@npm:6.0.1":
  locations:
    - "node_modules/jest-changed-files/node_modules/get-stream"

"get-symbol-description@npm:1.1.0":
  locations:
    - "node_modules/get-symbol-description"

"get-tsconfig@npm:4.10.1":
  locations:
    - "node_modules/get-tsconfig"

"github-slugger@npm:2.0.0":
  locations:
    - "node_modules/github-slugger"

"glob-base@npm:0.3.0":
  locations:
    - "node_modules/glob-base"

"glob-parent@npm:2.0.0":
  locations:
    - "node_modules/glob-base/node_modules/glob-parent"

"glob-parent@npm:3.1.0":
  locations:
    - "node_modules/glob-stream/node_modules/glob-parent"

"glob-parent@npm:5.1.2":
  locations:
    - "node_modules/glob-parent"

"glob-parent@npm:6.0.2":
  locations:
    - "node_modules/tailwindcss/node_modules/glob-parent"
    - "node_modules/eslint/node_modules/glob-parent"

"glob-stream@npm:5.3.5":
  locations:
    - "node_modules/glob-stream"

"glob-to-regexp@npm:0.4.1":
  locations:
    - "node_modules/glob-to-regexp"

"glob@npm:10.3.12":
  locations:
    - "node_modules/@nestjs-modules/mailer/node_modules/glob"

"glob@npm:10.4.5":
  locations:
    - "node_modules/glob"

"glob@npm:5.0.15":
  locations:
    - "node_modules/glob-stream/node_modules/glob"

"glob@npm:7.2.3":
  locations:
    - "node_modules/test-exclude/node_modules/glob"
    - "node_modules/rimraf/node_modules/glob"
    - "node_modules/jest-runtime/node_modules/glob"
    - "node_modules/jest-config/node_modules/glob"
    - "node_modules/@jest/reporters/node_modules/glob"
    - "node_modules/@jest/core/node_modules/glob"

"globals@npm:11.12.0":
  locations:
    - "node_modules/@babel/traverse/node_modules/globals"

"globals@npm:13.24.0":
  locations:
    - "node_modules/eslint/node_modules/globals"
    - "node_modules/@eslint/eslintrc/node_modules/globals"

"globals@npm:15.15.0":
  locations:
    - "node_modules/globals"

"globalthis@npm:1.0.4":
  locations:
    - "node_modules/globalthis"

"goober@virtual:2c56ffa55019c5277340e4b4a37b70909270a400d761c43aad083bc768ba99eef413508f5c1712b2547ec71e9082ede6aaf1a98e79fc804af591670626f78bc6#npm:2.1.16":
  locations:
    - "node_modules/goober"

"gopd@npm:1.2.0":
  locations:
    - "node_modules/gopd"

"graceful-fs@npm:4.2.11":
  locations:
    - "node_modules/graceful-fs"

"graphemer@npm:1.4.0":
  locations:
    - "node_modules/graphemer"

"gsap@npm:3.13.0":
  locations:
    - "node_modules/gsap"

"gulp-sourcemaps@npm:1.12.1":
  locations:
    - "node_modules/gulp-sourcemaps"

"h3@npm:1.15.3":
  locations:
    - "node_modules/h3"

"handlebars@npm:4.7.8":
  locations:
    - "node_modules/handlebars"

"has-ansi@npm:2.0.0":
  locations:
    - "node_modules/has-ansi"

"has-bigints@npm:1.1.0":
  locations:
    - "node_modules/has-bigints"

"has-flag@npm:4.0.0":
  locations:
    - "node_modules/has-flag"

"has-own-prop@npm:2.0.0":
  locations:
    - "node_modules/has-own-prop"

"has-property-descriptors@npm:1.0.2":
  locations:
    - "node_modules/has-property-descriptors"

"has-proto@npm:1.2.0":
  locations:
    - "node_modules/has-proto"

"has-symbols@npm:1.1.0":
  locations:
    - "node_modules/has-symbols"

"has-tostringtag@npm:1.0.2":
  locations:
    - "node_modules/has-tostringtag"

"has-unicode@npm:2.0.1":
  locations:
    - "node_modules/has-unicode"

"hasown@npm:2.0.2":
  locations:
    - "node_modules/hasown"

"hast-util-from-html@npm:2.0.3":
  locations:
    - "node_modules/hast-util-from-html"

"hast-util-from-parse5@npm:8.0.3":
  locations:
    - "node_modules/hast-util-from-parse5"

"hast-util-is-element@npm:3.0.0":
  locations:
    - "node_modules/hast-util-is-element"

"hast-util-parse-selector@npm:4.0.0":
  locations:
    - "node_modules/hast-util-parse-selector"

"hast-util-raw@npm:9.1.0":
  locations:
    - "node_modules/hast-util-raw"

"hast-util-to-html@npm:9.0.5":
  locations:
    - "node_modules/hast-util-to-html"

"hast-util-to-parse5@npm:8.0.0":
  locations:
    - "node_modules/hast-util-to-parse5"

"hast-util-to-text@npm:4.0.2":
  locations:
    - "node_modules/hast-util-to-text"

"hast-util-whitespace@npm:3.0.0":
  locations:
    - "node_modules/hast-util-whitespace"

"hastscript@npm:9.0.1":
  locations:
    - "node_modules/hastscript"

"he@npm:1.2.0":
  locations:
    - "node_modules/he"

"hoek@npm:4.3.1":
  locations:
    - "node_modules/hoek"

"hoek@npm:5.0.4":
  locations:
    - "node_modules/joi/node_modules/hoek"

"hoek@npm:6.1.3":
  locations:
    - "node_modules/topo/node_modules/hoek"

"hoist-non-react-statics@npm:3.3.2":
  locations:
    - "node_modules/hoist-non-react-statics"

"hosted-git-info@npm:2.8.9":
  locations:
    - "node_modules/hosted-git-info"

"html-escaper@npm:2.0.2":
  locations:
    - "node_modules/html-escaper"

"html-escaper@npm:3.0.3":
  locations:
    - "node_modules/astro/node_modules/html-escaper"

"html-minifier@npm:4.0.0":
  locations:
    - "node_modules/html-minifier"

"html-parse-stringify@npm:3.0.1":
  locations:
    - "node_modules/html-parse-stringify"

"html-to-text@npm:9.0.5":
  locations:
    - "node_modules/html-to-text"

"html-tokenize@npm:2.0.1":
  locations:
    - "node_modules/html-tokenize"

"html-void-elements@npm:3.0.0":
  locations:
    - "node_modules/html-void-elements"

"html2canvas@npm:1.4.1":
  locations:
    - "node_modules/html2canvas"

"htmlparser2@npm:5.0.1":
  locations:
    - "node_modules/web-resource-inliner/node_modules/htmlparser2"

"htmlparser2@npm:8.0.2":
  locations:
    - "node_modules/htmlparser2"

"htmlparser2@npm:9.1.0":
  locations:
    - "node_modules/mjml-parser-xml/node_modules/htmlparser2"

"http-cache-semantics@npm:4.2.0":
  locations:
    - "node_modules/http-cache-semantics"

"http-errors@npm:2.0.0":
  locations:
    - "node_modules/http-errors"

"http-proxy-agent@npm:7.0.2":
  locations:
    - "node_modules/http-proxy-agent"

"https-proxy-agent@npm:5.0.1":
  locations:
    - "node_modules/https-proxy-agent"

"https-proxy-agent@npm:7.0.6":
  locations:
    - "node_modules/@npmcli/agent/node_modules/https-proxy-agent"

"human-signals@npm:2.1.0":
  locations:
    - "node_modules/human-signals"

"husky@npm:9.1.7":
  locations:
    - "node_modules/husky"

"i18next-browser-languagedetector@npm:8.1.0":
  locations:
    - "node_modules/i18next-browser-languagedetector"

"i18next@npm:23.16.8":
  locations:
    - "apps/design-editor/node_modules/i18next"

"i18next@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:24.2.3":
  locations:
    - "node_modules/i18next"

"i18next@virtual:f076ded31fccc59d4f9e6ced6349f34297f2dd657b8275061704bbe29a11a614ffe29c030a051fc61b74e50142507a1ec03a156876f30c45951e0e1e6cc4795e#npm:25.2.1":
  locations:
    - "apps/ssg/node_modules/i18next"

"iconv-lite@npm:0.4.24":
  locations:
    - "node_modules/raw-body/node_modules/iconv-lite"
    - "node_modules/external-editor/node_modules/iconv-lite"
    - "node_modules/body-parser/node_modules/iconv-lite"

"iconv-lite@npm:0.6.3":
  locations:
    - "node_modules/iconv-lite"

"ieee754@npm:1.1.13":
  locations:
    - "node_modules/aws-sdk/node_modules/ieee754"

"ieee754@npm:1.2.1":
  locations:
    - "node_modules/ieee754"

"ignore@npm:5.3.2":
  locations:
    - "node_modules/ignore"

"ignore@npm:7.0.5":
  locations:
    - "node_modules/@typescript-eslint/eslint-plugin/node_modules/ignore"

"immer@npm:10.1.1":
  locations:
    - "node_modules/immer"

"immutable@npm:5.1.2":
  locations:
    - "node_modules/immutable"

"import-fresh@npm:3.3.1":
  locations:
    - "node_modules/import-fresh"

"import-local@npm:3.2.0":
  locations:
    - "node_modules/import-local"

"import-meta-resolve@npm:4.1.0":
  locations:
    - "node_modules/import-meta-resolve"

"imurmurhash@npm:0.1.4":
  locations:
    - "node_modules/imurmurhash"

"inflight@npm:1.0.6":
  locations:
    - "node_modules/inflight"

"inherits@npm:2.0.4":
  locations:
    - "node_modules/inherits"

"ini@npm:1.3.8":
  locations:
    - "node_modules/ini"

"inquirer@npm:8.2.6":
  locations:
    - "node_modules/@nestjs/cli/node_modules/inquirer"

"inquirer@npm:9.2.15":
  locations:
    - "node_modules/inquirer"

"internal-slot@npm:1.1.0":
  locations:
    - "node_modules/internal-slot"

"internmap@npm:2.0.3":
  locations:
    - "node_modules/internmap"

"invert-kv@npm:1.0.0":
  locations:
    - "node_modules/invert-kv"

"ip-address@npm:9.0.5":
  locations:
    - "node_modules/ip-address"

"ipaddr.js@npm:1.9.1":
  locations:
    - "node_modules/ipaddr.js"

"iron-webcrypto@npm:1.2.1":
  locations:
    - "node_modules/iron-webcrypto"

"is-arguments@npm:1.2.0":
  locations:
    - "node_modules/is-arguments"

"is-array-buffer@npm:3.0.5":
  locations:
    - "node_modules/is-array-buffer"

"is-arrayish@npm:0.2.1":
  locations:
    - "node_modules/is-arrayish"

"is-arrayish@npm:0.3.2":
  locations:
    - "node_modules/simple-swizzle/node_modules/is-arrayish"

"is-async-function@npm:2.1.1":
  locations:
    - "node_modules/is-async-function"

"is-bigint@npm:1.1.0":
  locations:
    - "node_modules/is-bigint"

"is-binary-path@npm:2.1.0":
  locations:
    - "node_modules/is-binary-path"

"is-boolean-object@npm:1.2.2":
  locations:
    - "node_modules/is-boolean-object"

"is-buffer@npm:1.1.6":
  locations:
    - "node_modules/is-buffer"

"is-bun-module@npm:2.0.0":
  locations:
    - "node_modules/is-bun-module"

"is-callable@npm:1.2.7":
  locations:
    - "node_modules/is-callable"

"is-core-module@npm:2.16.1":
  locations:
    - "node_modules/is-core-module"

"is-data-view@npm:1.0.2":
  locations:
    - "node_modules/is-data-view"

"is-date-object@npm:1.1.0":
  locations:
    - "node_modules/is-date-object"

"is-docker@npm:2.2.1":
  locations:
    - "node_modules/is-docker"

"is-docker@npm:3.0.0":
  locations:
    - "node_modules/is-inside-container/node_modules/is-docker"
    - "node_modules/@astrojs/telemetry/node_modules/is-docker"

"is-dotfile@npm:1.0.3":
  locations:
    - "node_modules/is-dotfile"

"is-equal-shallow@npm:0.1.3":
  locations:
    - "node_modules/is-equal-shallow"

"is-expression@npm:4.0.0":
  locations:
    - "node_modules/is-expression"

"is-extendable@npm:0.1.1":
  locations:
    - "node_modules/is-extendable"

"is-extglob@npm:1.0.0":
  locations:
    - "node_modules/is-extglob"

"is-extglob@npm:2.1.1":
  locations:
    - "node_modules/is-glob/node_modules/is-extglob"
    - "node_modules/glob-stream/node_modules/is-glob/node_modules/is-extglob"

"is-finalizationregistry@npm:1.1.1":
  locations:
    - "node_modules/is-finalizationregistry"

"is-fullwidth-code-point@npm:1.0.0":
  locations:
    - "node_modules/module/node_modules/is-fullwidth-code-point"
    - "node_modules/cliui/node_modules/is-fullwidth-code-point"

"is-fullwidth-code-point@npm:3.0.0":
  locations:
    - "node_modules/string-width/node_modules/is-fullwidth-code-point"
    - "node_modules/string-width-cjs/node_modules/is-fullwidth-code-point"

"is-fullwidth-code-point@npm:4.0.0":
  locations:
    - "node_modules/is-fullwidth-code-point"

"is-fullwidth-code-point@npm:5.0.0":
  locations:
    - "node_modules/log-update/node_modules/is-fullwidth-code-point"

"is-generator-fn@npm:2.1.0":
  locations:
    - "node_modules/is-generator-fn"

"is-generator-function@npm:1.1.0":
  locations:
    - "node_modules/is-generator-function"

"is-glob@npm:2.0.1":
  locations:
    - "node_modules/parse-glob/node_modules/is-glob"
    - "node_modules/glob-stream/node_modules/micromatch/node_modules/is-glob"
    - "node_modules/glob-base/node_modules/is-glob"

"is-glob@npm:3.1.0":
  locations:
    - "node_modules/glob-stream/node_modules/is-glob"

"is-glob@npm:4.0.3":
  locations:
    - "node_modules/is-glob"

"is-inside-container@npm:1.0.0":
  locations:
    - "node_modules/is-inside-container"

"is-interactive@npm:1.0.0":
  locations:
    - "node_modules/is-interactive"

"is-map@npm:2.0.3":
  locations:
    - "node_modules/is-map"

"is-negative-zero@npm:2.0.3":
  locations:
    - "node_modules/is-negative-zero"

"is-number-object@npm:1.1.1":
  locations:
    - "node_modules/is-number-object"

"is-number@npm:2.1.0":
  locations:
    - "node_modules/expand-range/node_modules/is-number"

"is-number@npm:4.0.0":
  locations:
    - "node_modules/randomatic/node_modules/is-number"

"is-number@npm:7.0.0":
  locations:
    - "node_modules/is-number"

"is-path-inside@npm:3.0.3":
  locations:
    - "node_modules/is-path-inside"

"is-plain-obj@npm:4.1.0":
  locations:
    - "node_modules/is-plain-obj"

"is-posix-bracket@npm:0.1.1":
  locations:
    - "node_modules/is-posix-bracket"

"is-primitive@npm:2.0.0":
  locations:
    - "node_modules/is-primitive"

"is-promise@npm:2.2.2":
  locations:
    - "node_modules/is-promise"

"is-regex@npm:1.2.1":
  locations:
    - "node_modules/is-regex"

"is-set@npm:2.0.3":
  locations:
    - "node_modules/is-set"

"is-shared-array-buffer@npm:1.0.4":
  locations:
    - "node_modules/is-shared-array-buffer"

"is-stream@npm:1.1.0":
  locations:
    - "node_modules/is-stream"

"is-stream@npm:2.0.1":
  locations:
    - "node_modules/winston/node_modules/is-stream"
    - "node_modules/jest-changed-files/node_modules/is-stream"

"is-string@npm:1.1.1":
  locations:
    - "node_modules/is-string"

"is-symbol@npm:1.1.1":
  locations:
    - "node_modules/is-symbol"

"is-typed-array@npm:1.1.15":
  locations:
    - "node_modules/is-typed-array"

"is-unicode-supported@npm:0.1.0":
  locations:
    - "node_modules/is-unicode-supported"

"is-utf8@npm:0.2.1":
  locations:
    - "node_modules/is-utf8"

"is-valid-glob@npm:0.3.0":
  locations:
    - "node_modules/is-valid-glob"

"is-weakmap@npm:2.0.2":
  locations:
    - "node_modules/is-weakmap"

"is-weakref@npm:1.1.1":
  locations:
    - "node_modules/is-weakref"

"is-weakset@npm:2.0.4":
  locations:
    - "node_modules/is-weakset"

"is-wsl@npm:2.2.0":
  locations:
    - "node_modules/is-wsl"

"is-wsl@npm:3.1.0":
  locations:
    - "node_modules/@astrojs/telemetry/node_modules/is-wsl"

"isarray@npm:0.0.1":
  locations:
    - "node_modules/html-tokenize/node_modules/isarray"
    - "node_modules/glob-stream/node_modules/isarray"
    - "node_modules/ftp-response-parser/node_modules/isarray"

"isarray@npm:1.0.0":
  locations:
    - "node_modules/isarray"

"isarray@npm:2.0.5":
  locations:
    - "node_modules/which-builtin-type/node_modules/isarray"
    - "node_modules/safe-push-apply/node_modules/isarray"
    - "node_modules/safe-array-concat/node_modules/isarray"

"isemail@npm:3.2.0":
  locations:
    - "node_modules/isemail"

"isexe@npm:2.0.0":
  locations:
    - "node_modules/isexe"

"isexe@npm:3.1.1":
  locations:
    - "node_modules/node-gyp/node_modules/isexe"

"isobject@npm:2.1.0":
  locations:
    - "node_modules/isobject"

"istanbul-lib-coverage@npm:3.2.2":
  locations:
    - "node_modules/istanbul-lib-coverage"

"istanbul-lib-instrument@npm:5.2.1":
  locations:
    - "node_modules/istanbul-lib-instrument"

"istanbul-lib-instrument@npm:6.0.3":
  locations:
    - "node_modules/@jest/reporters/node_modules/istanbul-lib-instrument"

"istanbul-lib-report@npm:3.0.1":
  locations:
    - "node_modules/istanbul-lib-report"

"istanbul-lib-source-maps@npm:4.0.1":
  locations:
    - "node_modules/istanbul-lib-source-maps"

"istanbul-reports@npm:3.1.7":
  locations:
    - "node_modules/istanbul-reports"

"iterare@npm:1.2.1":
  locations:
    - "node_modules/iterare"

"iterator.prototype@npm:1.1.5":
  locations:
    - "node_modules/iterator.prototype"

"its-fine@virtual:7c10321826407f5fb71fbc4a7dd5504185efb590c9f9a855a6b5199aa3bf6995a4af3f68dd20977533d459fd0800ef4f6520727c17e788b16ab8dd5bc0548357#npm:1.2.5":
  locations:
    - "node_modules/its-fine"

"jackspeak@npm:2.3.6":
  locations:
    - "node_modules/@nestjs-modules/mailer/node_modules/jackspeak"

"jackspeak@npm:3.4.3":
  locations:
    - "node_modules/jackspeak"

"jake@npm:10.9.2":
  locations:
    - "node_modules/jake"

"javascript-natural-sort@npm:0.7.1":
  locations:
    - "node_modules/javascript-natural-sort"

"jest-changed-files@npm:29.7.0":
  locations:
    - "node_modules/jest-changed-files"

"jest-circus@npm:29.7.0":
  locations:
    - "node_modules/jest-circus"

"jest-cli@virtual:13717d5e3be58e58db99845f8389bd23f9e8812cf3a7a6214540f88a9ed068937b46fa880de071f494de7405e6288f36805d637be582dea12a6ddf39bd422fc6#npm:29.7.0":
  locations:
    - "node_modules/jest-cli"

"jest-config@virtual:3a6a7b993b4c5b60edc037a265ed4617431cf4c75aee76d6fbd0f2ca65ea68cee61c092e9bd306baebd90cc377234b4a525791e6755ee4d2193076de2c2bdfed#npm:29.7.0":
  locations:
    - "node_modules/jest-config"

"jest-config@virtual:6f44d8c5cc1cd67367c0e1e1e117bda0efcf068436ba4049b2acd4b39393c79511b57b682b4ba11597f827d3c46467f6452b4497d0e9fd685c7c49a2b5fe08a7#npm:29.7.0":
  locations:
    - "node_modules/@jest/core/node_modules/jest-config"

"jest-diff@npm:29.7.0":
  locations:
    - "node_modules/jest-diff"

"jest-docblock@npm:29.7.0":
  locations:
    - "node_modules/jest-docblock"

"jest-each@npm:29.7.0":
  locations:
    - "node_modules/jest-each"

"jest-environment-node@npm:29.7.0":
  locations:
    - "node_modules/jest-environment-node"

"jest-get-type@npm:29.6.3":
  locations:
    - "node_modules/jest-get-type"

"jest-haste-map@npm:29.7.0":
  locations:
    - "node_modules/jest-haste-map"

"jest-leak-detector@npm:29.7.0":
  locations:
    - "node_modules/jest-leak-detector"

"jest-matcher-utils@npm:29.7.0":
  locations:
    - "node_modules/jest-matcher-utils"

"jest-message-util@npm:29.7.0":
  locations:
    - "node_modules/jest-message-util"

"jest-mock@npm:29.7.0":
  locations:
    - "node_modules/jest-mock"

"jest-pnp-resolver@virtual:5c36f0eefbce78ee308fab92b5dcd29e2b0b70713b50365f0168be5bb1facc6582106f851a083d72bbb13e26d984e8612da5ed4b2bae83649e73e7b1ce19525b#npm:1.2.3":
  locations:
    - "node_modules/jest-pnp-resolver"

"jest-regex-util@npm:29.6.3":
  locations:
    - "node_modules/jest-regex-util"

"jest-resolve-dependencies@npm:29.7.0":
  locations:
    - "node_modules/jest-resolve-dependencies"

"jest-resolve@npm:29.7.0":
  locations:
    - "node_modules/jest-resolve"

"jest-runner@npm:29.7.0":
  locations:
    - "node_modules/jest-runner"

"jest-runtime@npm:29.7.0":
  locations:
    - "node_modules/jest-runtime"

"jest-snapshot@npm:29.7.0":
  locations:
    - "node_modules/jest-snapshot"

"jest-util@npm:29.7.0":
  locations:
    - "node_modules/jest-util"

"jest-validate@npm:29.7.0":
  locations:
    - "node_modules/jest-validate"

"jest-watcher@npm:29.7.0":
  locations:
    - "node_modules/jest-watcher"

"jest-worker@npm:27.5.1":
  locations:
    - "node_modules/terser-webpack-plugin/node_modules/jest-worker"

"jest-worker@npm:29.7.0":
  locations:
    - "node_modules/jest-worker"

"jest@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:29.7.0":
  locations:
    - "node_modules/jest"

"jiti@npm:1.21.7":
  locations:
    - "node_modules/jiti"

"jmespath@npm:0.16.0":
  locations:
    - "node_modules/jmespath"

"joi@npm:13.7.0":
  locations:
    - "node_modules/joi"

"jose@npm:5.10.0":
  locations:
    - "node_modules/jose"

"js-beautify@npm:1.15.4":
  locations:
    - "node_modules/js-beautify"

"js-cookie@npm:3.0.5":
  locations:
    - "node_modules/js-cookie"

"js-stringify@npm:1.0.2":
  locations:
    - "node_modules/js-stringify"

"js-tokens@npm:4.0.0":
  locations:
    - "node_modules/js-tokens"

"js-yaml@npm:3.14.1":
  locations:
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/js-yaml"

"js-yaml@npm:4.1.0":
  locations:
    - "node_modules/js-yaml"

"jsbn@npm:1.1.0":
  locations:
    - "node_modules/jsbn"

"jsesc@npm:3.1.0":
  locations:
    - "node_modules/jsesc"

"jsftp@npm:2.1.3":
  locations:
    - "node_modules/jsftp"

"json-buffer@npm:3.0.1":
  locations:
    - "node_modules/json-buffer"

"json-parse-even-better-errors@npm:2.3.1":
  locations:
    - "node_modules/json-parse-even-better-errors"

"json-schema-traverse@npm:0.4.1":
  locations:
    - "node_modules/schema-utils/node_modules/json-schema-traverse"
    - "node_modules/eslint/node_modules/json-schema-traverse"
    - "node_modules/@eslint/eslintrc/node_modules/json-schema-traverse"

"json-schema-traverse@npm:1.0.0":
  locations:
    - "node_modules/json-schema-traverse"

"json-stable-stringify-without-jsonify@npm:1.0.1":
  locations:
    - "node_modules/json-stable-stringify-without-jsonify"

"json5@npm:1.0.2":
  locations:
    - "node_modules/eslint-plugin-import/node_modules/json5"

"json5@npm:2.2.3":
  locations:
    - "node_modules/json5"

"jsonc-parser@npm:3.2.1":
  locations:
    - "node_modules/jsonc-parser"

"jsonc-parser@npm:3.3.1":
  locations:
    - "node_modules/@nestjs/schematics/node_modules/jsonc-parser"
    - "node_modules/@nestjs/cli/node_modules/jsonc-parser"

"jsonfile@npm:6.1.0":
  locations:
    - "node_modules/jsonfile"

"jstransformer@npm:1.0.0":
  locations:
    - "node_modules/jstransformer"

"jsx-ast-utils@npm:3.3.5":
  locations:
    - "node_modules/jsx-ast-utils"

"juice@npm:10.0.1":
  locations:
    - "node_modules/juice"

"keyv@npm:4.5.4":
  locations:
    - "node_modules/keyv"

"kind-of@npm:3.2.2":
  locations:
    - "node_modules/kind-of"

"kind-of@npm:6.0.3":
  locations:
    - "node_modules/randomatic/node_modules/kind-of"

"kleur@npm:3.0.3":
  locations:
    - "node_modules/kleur"

"kleur@npm:4.1.5":
  locations:
    - "node_modules/astro/node_modules/kleur"

"konva@npm:9.3.6":
  locations:
    - "node_modules/konva"

"kuler@npm:2.0.0":
  locations:
    - "node_modules/kuler"

"language-subtag-registry@npm:0.3.23":
  locations:
    - "node_modules/language-subtag-registry"

"language-tags@npm:1.0.9":
  locations:
    - "node_modules/language-tags"

"lazy-debug-legacy@virtual:96ae22c60301efd863de91c4ec8466760c91798bc6984361771d597c6d303a622340cc9acb0f6fdb46dbb7bc0133aee65314168f69f2cb314a47527d822b0781#npm:0.0.1":
  locations:
    - "node_modules/lazy-debug-legacy"

"lazystream@npm:1.0.1":
  locations:
    - "node_modules/lazystream"

"lcid@npm:1.0.0":
  locations:
    - "node_modules/lcid"

"leac@npm:0.6.0":
  locations:
    - "node_modules/leac"

"leven@npm:3.1.0":
  locations:
    - "node_modules/leven"

"levn@npm:0.4.1":
  locations:
    - "node_modules/levn"

"libbase64@npm:1.3.0":
  locations:
    - "node_modules/libbase64"

"libmime@npm:5.3.6":
  locations:
    - "node_modules/libmime"

"libphonenumber-js@npm:1.12.9":
  locations:
    - "node_modules/libphonenumber-js"

"libqp@npm:2.1.1":
  locations:
    - "node_modules/libqp"

"lilconfig@npm:3.1.3":
  locations:
    - "node_modules/lilconfig"

"lines-and-columns@npm:1.2.4":
  locations:
    - "node_modules/lines-and-columns"

"linkify-it@npm:5.0.0":
  locations:
    - "node_modules/linkify-it"

"linkifyjs@npm:4.3.1":
  locations:
    - "node_modules/linkifyjs"

"lint-staged@npm:16.1.0":
  locations:
    - "node_modules/lint-staged"

"liquidjs@npm:10.21.1":
  locations:
    - "node_modules/liquidjs"

"listr2@npm:8.3.3":
  locations:
    - "node_modules/listr2"

"load-json-file@npm:1.1.0":
  locations:
    - "node_modules/load-json-file"

"loader-runner@npm:4.3.0":
  locations:
    - "node_modules/loader-runner"

"locate-path@npm:5.0.0":
  locations:
    - "node_modules/pkg-dir/node_modules/locate-path"
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/locate-path"

"locate-path@npm:6.0.0":
  locations:
    - "node_modules/locate-path"

"lodash._reinterpolate@npm:3.0.0":
  locations:
    - "node_modules/lodash._reinterpolate"

"lodash.assign@npm:4.2.0":
  locations:
    - "node_modules/lodash.assign"

"lodash.assigninwith@npm:4.2.0":
  locations:
    - "node_modules/lodash.assigninwith"

"lodash.isequal@npm:4.5.0":
  locations:
    - "node_modules/lodash.isequal"

"lodash.keys@npm:4.2.0":
  locations:
    - "node_modules/lodash.keys"

"lodash.memoize@npm:4.1.2":
  locations:
    - "node_modules/lodash.memoize"

"lodash.merge@npm:4.6.2":
  locations:
    - "node_modules/lodash.merge"

"lodash.rest@npm:4.0.5":
  locations:
    - "node_modules/lodash.rest"

"lodash.template@npm:4.2.4":
  locations:
    - "node_modules/lodash.template"

"lodash.templatesettings@npm:4.2.0":
  locations:
    - "node_modules/lodash.templatesettings"

"lodash.throttle@npm:4.1.1":
  locations:
    - "node_modules/lodash.throttle"

"lodash.tostring@npm:4.1.4":
  locations:
    - "node_modules/lodash.tostring"

"lodash@npm:4.17.21":
  locations:
    - "node_modules/lodash"

"log-symbols@npm:4.1.0":
  locations:
    - "node_modules/log-symbols"

"log-update@npm:6.1.0":
  locations:
    - "node_modules/log-update"

"logform@npm:2.7.0":
  locations:
    - "node_modules/logform"

"longest-streak@npm:3.1.0":
  locations:
    - "node_modules/longest-streak"

"loose-envify@npm:1.4.0":
  locations:
    - "node_modules/loose-envify"

"lower-case@npm:1.1.4":
  locations:
    - "node_modules/lower-case"

"lru-cache@npm:10.4.3":
  locations:
    - "node_modules/lru-cache"

"lru-cache@npm:5.1.1":
  locations:
    - "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache"

"luxon@npm:3.5.0":
  locations:
    - "node_modules/luxon"

"magic-string@npm:0.30.17":
  locations:
    - "node_modules/astro/node_modules/magic-string"

"magic-string@npm:0.30.8":
  locations:
    - "node_modules/magic-string"

"magicast@npm:0.3.5":
  locations:
    - "node_modules/magicast"

"mailparser@npm:3.7.3":
  locations:
    - "node_modules/mailparser"

"mailsplit@npm:5.4.3":
  locations:
    - "node_modules/mailsplit"

"make-dir@npm:3.1.0":
  locations:
    - "node_modules/make-dir"

"make-dir@npm:4.0.0":
  locations:
    - "node_modules/istanbul-lib-report/node_modules/make-dir"

"make-error@npm:1.3.6":
  locations:
    - "node_modules/make-error"

"make-fetch-happen@npm:14.0.3":
  locations:
    - "node_modules/make-fetch-happen"

"makeerror@npm:1.0.12":
  locations:
    - "node_modules/makeerror"

"map-stream@npm:0.0.6":
  locations:
    - "node_modules/map-stream"

"markdown-it@npm:14.1.0":
  locations:
    - "node_modules/markdown-it"

"markdown-table@npm:3.0.4":
  locations:
    - "node_modules/markdown-table"

"math-intrinsics@npm:1.1.0":
  locations:
    - "node_modules/math-intrinsics"

"math-random@npm:1.0.4":
  locations:
    - "node_modules/math-random"

"mdast-util-definitions@npm:6.0.0":
  locations:
    - "node_modules/mdast-util-definitions"

"mdast-util-find-and-replace@npm:3.0.2":
  locations:
    - "node_modules/mdast-util-find-and-replace"

"mdast-util-from-markdown@npm:2.0.2":
  locations:
    - "node_modules/mdast-util-from-markdown"

"mdast-util-gfm-autolink-literal@npm:2.0.1":
  locations:
    - "node_modules/mdast-util-gfm-autolink-literal"

"mdast-util-gfm-footnote@npm:2.1.0":
  locations:
    - "node_modules/mdast-util-gfm-footnote"

"mdast-util-gfm-strikethrough@npm:2.0.0":
  locations:
    - "node_modules/mdast-util-gfm-strikethrough"

"mdast-util-gfm-table@npm:2.0.0":
  locations:
    - "node_modules/mdast-util-gfm-table"

"mdast-util-gfm-task-list-item@npm:2.0.0":
  locations:
    - "node_modules/mdast-util-gfm-task-list-item"

"mdast-util-gfm@npm:3.1.0":
  locations:
    - "node_modules/mdast-util-gfm"

"mdast-util-phrasing@npm:4.1.0":
  locations:
    - "node_modules/mdast-util-phrasing"

"mdast-util-to-hast@npm:13.2.0":
  locations:
    - "node_modules/mdast-util-to-hast"

"mdast-util-to-markdown@npm:2.1.2":
  locations:
    - "node_modules/mdast-util-to-markdown"

"mdast-util-to-string@npm:4.0.0":
  locations:
    - "node_modules/mdast-util-to-string"

"mdn-data@npm:2.12.2":
  locations:
    - "node_modules/mdn-data"

"mdurl@npm:2.0.0":
  locations:
    - "node_modules/mdurl"

"media-typer@npm:0.3.0":
  locations:
    - "node_modules/media-typer"

"memfs@npm:3.5.3":
  locations:
    - "node_modules/memfs"

"memoize-one@npm:5.2.1":
  locations:
    - "node_modules/memoize-one"

"memoize-one@npm:6.0.0":
  locations:
    - "node_modules/react-select/node_modules/memoize-one"

"mensch@npm:0.3.4":
  locations:
    - "node_modules/mensch"

"merge-descriptors@npm:1.0.3":
  locations:
    - "node_modules/merge-descriptors"

"merge-stream@npm:1.0.1":
  locations:
    - "node_modules/vinyl-fs/node_modules/merge-stream"

"merge-stream@npm:2.0.0":
  locations:
    - "node_modules/merge-stream"

"merge2@npm:1.4.1":
  locations:
    - "node_modules/merge2"

"methods@npm:1.1.2":
  locations:
    - "node_modules/methods"

"micromark-core-commonmark@npm:2.0.3":
  locations:
    - "node_modules/micromark-core-commonmark"

"micromark-extension-gfm-autolink-literal@npm:2.1.0":
  locations:
    - "node_modules/micromark-extension-gfm-autolink-literal"

"micromark-extension-gfm-footnote@npm:2.1.0":
  locations:
    - "node_modules/micromark-extension-gfm-footnote"

"micromark-extension-gfm-strikethrough@npm:2.1.0":
  locations:
    - "node_modules/micromark-extension-gfm-strikethrough"

"micromark-extension-gfm-table@npm:2.1.1":
  locations:
    - "node_modules/micromark-extension-gfm-table"

"micromark-extension-gfm-tagfilter@npm:2.0.0":
  locations:
    - "node_modules/micromark-extension-gfm-tagfilter"

"micromark-extension-gfm-task-list-item@npm:2.1.0":
  locations:
    - "node_modules/micromark-extension-gfm-task-list-item"

"micromark-extension-gfm@npm:3.0.0":
  locations:
    - "node_modules/micromark-extension-gfm"

"micromark-factory-destination@npm:2.0.1":
  locations:
    - "node_modules/micromark-factory-destination"

"micromark-factory-label@npm:2.0.1":
  locations:
    - "node_modules/micromark-factory-label"

"micromark-factory-space@npm:2.0.1":
  locations:
    - "node_modules/micromark-factory-space"

"micromark-factory-title@npm:2.0.1":
  locations:
    - "node_modules/micromark-factory-title"

"micromark-factory-whitespace@npm:2.0.1":
  locations:
    - "node_modules/micromark-factory-whitespace"

"micromark-util-character@npm:2.1.1":
  locations:
    - "node_modules/micromark-util-character"

"micromark-util-chunked@npm:2.0.1":
  locations:
    - "node_modules/micromark-util-chunked"

"micromark-util-classify-character@npm:2.0.1":
  locations:
    - "node_modules/micromark-util-classify-character"

"micromark-util-combine-extensions@npm:2.0.1":
  locations:
    - "node_modules/micromark-util-combine-extensions"

"micromark-util-decode-numeric-character-reference@npm:2.0.2":
  locations:
    - "node_modules/micromark-util-decode-numeric-character-reference"

"micromark-util-decode-string@npm:2.0.1":
  locations:
    - "node_modules/micromark-util-decode-string"

"micromark-util-encode@npm:2.0.1":
  locations:
    - "node_modules/micromark-util-encode"

"micromark-util-html-tag-name@npm:2.0.1":
  locations:
    - "node_modules/micromark-util-html-tag-name"

"micromark-util-normalize-identifier@npm:2.0.1":
  locations:
    - "node_modules/micromark-util-normalize-identifier"

"micromark-util-resolve-all@npm:2.0.1":
  locations:
    - "node_modules/micromark-util-resolve-all"

"micromark-util-sanitize-uri@npm:2.0.1":
  locations:
    - "node_modules/micromark-util-sanitize-uri"

"micromark-util-subtokenize@npm:2.1.0":
  locations:
    - "node_modules/micromark-util-subtokenize"

"micromark-util-symbol@npm:2.0.1":
  locations:
    - "node_modules/micromark-util-symbol"

"micromark-util-types@npm:2.0.2":
  locations:
    - "node_modules/micromark-util-types"

"micromark@npm:4.0.2":
  locations:
    - "node_modules/micromark"

"micromatch@npm:2.3.11":
  locations:
    - "node_modules/glob-stream/node_modules/micromatch"

"micromatch@npm:4.0.8":
  locations:
    - "node_modules/micromatch"

"mime-db@npm:1.52.0":
  locations:
    - "node_modules/mime-db"

"mime-types@npm:2.1.35":
  locations:
    - "node_modules/mime-types"

"mime@npm:1.6.0":
  locations:
    - "node_modules/send/node_modules/mime"

"mime@npm:2.6.0":
  locations:
    - "node_modules/mime"

"mimic-fn@npm:2.1.0":
  locations:
    - "node_modules/mimic-fn"

"mimic-function@npm:5.0.1":
  locations:
    - "node_modules/mimic-function"

"minimatch@npm:3.1.2":
  locations:
    - "node_modules/minimatch"

"minimatch@npm:5.1.6":
  locations:
    - "node_modules/filelist/node_modules/minimatch"

"minimatch@npm:9.0.1":
  locations:
    - "node_modules/editorconfig/node_modules/minimatch"

"minimatch@npm:9.0.5":
  locations:
    - "node_modules/mjml-cli/node_modules/minimatch"
    - "node_modules/glob/node_modules/minimatch"
    - "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch"
    - "node_modules/@nestjs-modules/mailer/node_modules/minimatch"

"minimist@npm:1.2.8":
  locations:
    - "node_modules/minimist"

"minipass-collect@npm:2.0.1":
  locations:
    - "node_modules/minipass-collect"

"minipass-fetch@npm:4.0.1":
  locations:
    - "node_modules/minipass-fetch"

"minipass-flush@npm:1.0.5":
  locations:
    - "node_modules/minipass-flush"

"minipass-pipeline@npm:1.2.4":
  locations:
    - "node_modules/minipass-pipeline"

"minipass-sized@npm:1.0.3":
  locations:
    - "node_modules/minipass-sized"

"minipass@npm:3.3.6":
  locations:
    - "node_modules/minipass-sized/node_modules/minipass"
    - "node_modules/minipass-pipeline/node_modules/minipass"
    - "node_modules/minipass-flush/node_modules/minipass"
    - "node_modules/fs-minipass/node_modules/minipass"
    - "node_modules/@mapbox/node-pre-gyp/node_modules/minizlib/node_modules/minipass"

"minipass@npm:5.0.0":
  locations:
    - "node_modules/@mapbox/node-pre-gyp/node_modules/minipass"

"minipass@npm:7.1.2":
  locations:
    - "node_modules/minipass"

"minizlib@npm:2.1.2":
  locations:
    - "node_modules/@mapbox/node-pre-gyp/node_modules/minizlib"

"minizlib@npm:3.0.2":
  locations:
    - "node_modules/minizlib"

"mjml-accordion@npm:4.15.3":
  locations:
    - "node_modules/mjml-accordion"

"mjml-body@npm:4.15.3":
  locations:
    - "node_modules/mjml-body"

"mjml-button@npm:4.15.3":
  locations:
    - "node_modules/mjml-button"

"mjml-carousel@npm:4.15.3":
  locations:
    - "node_modules/mjml-carousel"

"mjml-cli@npm:4.15.3":
  locations:
    - "node_modules/mjml-cli"

"mjml-column@npm:4.15.3":
  locations:
    - "node_modules/mjml-column"

"mjml-core@npm:4.15.3":
  locations:
    - "node_modules/mjml-core"

"mjml-divider@npm:4.15.3":
  locations:
    - "node_modules/mjml-divider"

"mjml-group@npm:4.15.3":
  locations:
    - "node_modules/mjml-group"

"mjml-head-attributes@npm:4.15.3":
  locations:
    - "node_modules/mjml-head-attributes"

"mjml-head-breakpoint@npm:4.15.3":
  locations:
    - "node_modules/mjml-head-breakpoint"

"mjml-head-font@npm:4.15.3":
  locations:
    - "node_modules/mjml-head-font"

"mjml-head-html-attributes@npm:4.15.3":
  locations:
    - "node_modules/mjml-head-html-attributes"

"mjml-head-preview@npm:4.15.3":
  locations:
    - "node_modules/mjml-head-preview"

"mjml-head-style@npm:4.15.3":
  locations:
    - "node_modules/mjml-head-style"

"mjml-head-title@npm:4.15.3":
  locations:
    - "node_modules/mjml-head-title"

"mjml-head@npm:4.15.3":
  locations:
    - "node_modules/mjml-head"

"mjml-hero@npm:4.15.3":
  locations:
    - "node_modules/mjml-hero"

"mjml-image@npm:4.15.3":
  locations:
    - "node_modules/mjml-image"

"mjml-migrate@npm:4.15.3":
  locations:
    - "node_modules/mjml-migrate"

"mjml-navbar@npm:4.15.3":
  locations:
    - "node_modules/mjml-navbar"

"mjml-parser-xml@npm:4.15.3":
  locations:
    - "node_modules/mjml-parser-xml"

"mjml-preset-core@npm:4.15.3":
  locations:
    - "node_modules/mjml-preset-core"

"mjml-raw@npm:4.15.3":
  locations:
    - "node_modules/mjml-raw"

"mjml-section@npm:4.15.3":
  locations:
    - "node_modules/mjml-section"

"mjml-social@npm:4.15.3":
  locations:
    - "node_modules/mjml-social"

"mjml-spacer@npm:4.15.3":
  locations:
    - "node_modules/mjml-spacer"

"mjml-table@npm:4.15.3":
  locations:
    - "node_modules/mjml-table"

"mjml-text@npm:4.15.3":
  locations:
    - "node_modules/mjml-text"

"mjml-validator@npm:4.15.3":
  locations:
    - "node_modules/mjml-validator"

"mjml-wrapper@npm:4.15.3":
  locations:
    - "node_modules/mjml-wrapper"

"mjml@npm:4.15.3":
  locations:
    - "node_modules/mjml"

"mkdirp@npm:0.5.6":
  locations:
    - "node_modules/mkdirp"

"mkdirp@npm:1.0.4":
  locations:
    - "node_modules/@mapbox/node-pre-gyp/node_modules/mkdirp"

"mkdirp@npm:3.0.1":
  locations:
    - "node_modules/tar/node_modules/mkdirp"

"module@npm:1.2.5":
  locations:
    - "node_modules/module"

"motion-dom@npm:11.18.1":
  locations:
    - "node_modules/motion-dom"

"motion-utils@npm:11.18.1":
  locations:
    - "node_modules/motion-utils"

"mrmime@npm:2.0.1":
  locations:
    - "node_modules/mrmime"

"ms@npm:2.0.0":
  locations:
    - "node_modules/debug/node_modules/ms"

"ms@npm:2.1.3":
  locations:
    - "node_modules/ms"

"multer@npm:2.0.1":
  locations:
    - "node_modules/multer"

"multipipe@npm:1.0.2":
  locations:
    - "node_modules/multipipe"

"mute-stream@npm:0.0.8":
  locations:
    - "node_modules/@nestjs/cli/node_modules/mute-stream"

"mute-stream@npm:1.0.0":
  locations:
    - "node_modules/mute-stream"

"mz@npm:2.7.0":
  locations:
    - "node_modules/mz"

"nan@npm:2.23.0":
  locations:
    - "node_modules/nan"

"nano-spawn@npm:1.0.2":
  locations:
    - "node_modules/nano-spawn"

"nanoid@npm:3.3.11":
  locations:
    - "node_modules/nanoid"

"napi-postinstall@npm:0.2.4":
  locations:
    - "node_modules/napi-postinstall"

"natural-compare@npm:1.4.0":
  locations:
    - "node_modules/natural-compare"

"negotiator@npm:0.6.3":
  locations:
    - "node_modules/accepts/node_modules/negotiator"

"negotiator@npm:1.0.0":
  locations:
    - "node_modules/negotiator"

"neo-async@npm:2.6.2":
  locations:
    - "node_modules/neo-async"

"neotraverse@npm:0.6.18":
  locations:
    - "node_modules/neotraverse"

"next-redux-wrapper@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:8.1.0":
  locations:
    - "node_modules/next-redux-wrapper"

"next-transpile-modules@npm:10.0.1":
  locations:
    - "node_modules/next-transpile-modules"

"next@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:15.0.0":
  locations:
    - "node_modules/next"

"nextgen-bindup@workspace:.":
  locations:
    - ""
  bin:
    "node_modules/astro":
      "vite": "vite/bin/vite.js"
    "node_modules/@babel/core":
      "semver": "semver/bin/semver.js"
    "node_modules/@babel/helper-compilation-targets":
      "semver": "semver/bin/semver.js"
    "node_modules/@astrojs/telemetry":
      "is-docker": "is-docker/cli.js"
    "node_modules/@astrojs/react":
      "vite": "vite/bin/vite.js"
    "node_modules/aws-sdk":
      "uuid": "uuid/dist/bin/uuid"
    "node_modules/eslint-plugin-import":
      "semver": "semver/bin/semver.js"
      "json5": "json5/lib/cli.js"
    "node_modules/@nestjs/cli":
      "tsc": "typescript/bin/tsc"
      "tsserver": "typescript/bin/tsserver"
    "node_modules/@nestjs/schedule":
      "uuid": "uuid/dist/esm/bin/uuid"
    "node_modules/@nestjs/typeorm":
      "uuid": "uuid/dist/bin/uuid"
    "node_modules/@nestjs-modules/mailer":
      "glob": "glob/dist/esm/bin.mjs"
    "node_modules/preview-email":
      "uuid": "uuid/dist/bin/uuid"
    "node_modules/vite":
      "esbuild": "esbuild/bin/esbuild"
    "node_modules/@mapbox/node-pre-gyp":
      "mkdirp": "mkdirp/bin/cmd.js"
    "node_modules/gulp-sourcemaps":
      "acorn": "acorn/bin/acorn"
    "node_modules/make-dir":
      "semver": "semver/bin/semver.js"
    "node_modules/istanbul-lib-instrument":
      "semver": "semver/bin/semver.js"
    "node_modules/eslint-plugin-react":
      "semver": "semver/bin/semver.js"
      "resolve": "resolve/bin/resolve"
    "node_modules/is-inside-container":
      "is-docker": "is-docker/cli.js"
    "node_modules/node-gyp":
      "nopt": "nopt/bin/nopt.js"
      "node-which": "which/bin/which.js"
    "node_modules/send":
      "mime": "mime/cli.js"
    "node_modules/@istanbuljs/load-nyc-config":
      "js-yaml": "js-yaml/bin/js-yaml.js"
    "node_modules/tar":
      "mkdirp": "mkdirp/dist/cjs/src/bin.js"
    "node_modules/normalize-package-data":
      "semver": "semver/bin/semver"
    "node_modules/execa":
      "semver": "semver/bin/semver"
      "which": "which/bin/which"
    "node_modules/js-beautify":
      "nopt": "nopt/bin/nopt.js"
    "node_modules/alce":
      "esparse": "esprima/bin/esparse.js"
      "esvalidate": "esprima/bin/esvalidate.js"
    "node_modules/is-expression":
      "acorn": "acorn/bin/acorn"
    ".":
      "esbuild": "esbuild/bin/esbuild"
      "eslint": "eslint/bin/eslint.js"
      "tailwind": "tailwindcss/lib/cli.js"
      "tailwindcss": "tailwindcss/lib/cli.js"
      "typeorm": "typeorm/cli.js"
      "typeorm-ts-node-commonjs": "typeorm/cli-ts-node-commonjs.js"
      "typeorm-ts-node-esm": "typeorm/cli-ts-node-esm.js"
      "lint-staged": "lint-staged/bin/lint-staged.js"
      "astro": "astro/astro.js"
      "parser": "@babel/parser/bin/babel-parser.js"
      "husky": "husky/bin.js"
      "prettier": "prettier/bin/prettier.cjs"
      "tsc": "typescript/bin/tsc"
      "tsserver": "typescript/bin/tsserver"
      "eslint-config-prettier": "eslint-config-prettier/bin/cli.js"
      "js-yaml": "js-yaml/bin/js-yaml.js"
      "pidtree": "pidtree/bin/pidtree.js"
      "yaml": "yaml/bin.mjs"
      "csv-parser": "csv-parser/bin/csv-parser"
      "module": "module/dist/cli.js"
      "xml2json": "xml2json/bin/xml2json"
      "nest": "@nestjs/cli/bin/nest.js"
      "mjml-cli": "mjml-cli/bin/mjml"
      "glob": "glob/dist/esm/bin.mjs"
      "sucrase": "sucrase/bin/sucrase"
      "sucrase-node": "sucrase/bin/sucrase-node"
      "ts-node": "ts-node/dist/bin.js"
      "ts-node-cwd": "ts-node/dist/bin-cwd.js"
      "ts-node-esm": "ts-node/dist/bin-esm.js"
      "ts-node-script": "ts-node/dist/bin-script.js"
      "ts-node-transpile-only": "ts-node/dist/bin-transpile.js"
      "ts-script": "ts-node/dist/bin-script-deprecated.js"
      "jest": "jest-cli/bin/jest.js"
      "ts-jest": "ts-jest/cli.js"
      "schematics": "@angular-devkit/schematics-cli/bin/schematics.js"
      "uuid": "uuid/dist/esm/bin/uuid"
      "vite": "vite/bin/vite.js"
      "sass": "sass-embedded/dist/bin/sass.js"
      "next": "next/dist/bin/next"
      "node-which": "which/bin/node-which"
      "acorn": "acorn/bin/acorn"
      "jsesc": "jsesc/bin/jsesc"
      "node-pre-gyp": "@mapbox/node-pre-gyp/bin/node-pre-gyp"
      "html-tokenize": "html-tokenize/bin/cmd.js"
      "window-size": "window-size/cli.js"
      "json5": "json5/lib/cli.js"
      "opencollective": "@nuxtjs/opencollective/bin/opencollective.js"
      "ejs": "ejs/bin/cli.js"
      "handlebars": "handlebars/bin/handlebars"
      "liquidjs": "liquidjs/bin/liquid.js"
      "liquid": "liquidjs/bin/liquid.js"
      "mjml": "mjml/bin/mjml"
      "tree-kill": "tree-kill/cli.js"
      "webpack": "webpack/bin/webpack.js"
      "semver": "semver/bin/semver.js"
      "sha.js": "sha.js/bin.js"
      "import-local-fixture": "import-local/fixtures/cli.js"
      "rimraf": "rimraf/bin.js"
      "loose-envify": "loose-envify/cli.js"
      "rollup": "rollup/dist/bin/rollup"
      "nanoid": "nanoid/bin/nanoid.cjs"
      "is-inside-container": "is-inside-container/cli.js"
      "cssesc": "cssesc/bin/cssesc"
      "tsconfck": "tsconfck/bin/tsconfck.js"
      "jiti": "jiti/bin/jiti.js"
      "resolve": "resolve/bin/resolve"
      "nopt": "nopt/bin/nopt.js"
      "node-gyp": "node-gyp/bin/node-gyp.js"
      "mkdirp": "mkdirp/bin/cmd.js"
      "mime": "mime/cli.js"
      "jake": "jake/bin/cli.js"
      "uglifyjs": "uglify-js/bin/uglifyjs"
      "migrate": "mjml-migrate/lib/cli.js"
      "fixpack": "fixpack/bin/fixpack"
      "browserslist": "browserslist/cli.js"
      "esparse": "esprima/bin/esparse.js"
      "esvalidate": "esprima/bin/esvalidate.js"
      "create-jest": "create-jest/bin/create-jest.js"
      "tsx": "tsx/dist/cli.mjs"
      "markdown-it": "markdown-it/bin/markdown-it.mjs"
      "html-minifier": "html-minifier/cli.js"
      "terser": "terser/bin/terser"
      "css-beautify": "js-beautify/js/bin/css-beautify.js"
      "html-beautify": "js-beautify/js/bin/html-beautify.js"
      "js-beautify": "js-beautify/js/bin/js-beautify.js"
      "juice": "juice/bin/juice"
      "rc": "rc/cli.js"
      "he": "he/bin/he"
      "tlds": "tlds/bin.js"
      "is-docker": "is-docker/cli.js"
      "update-browserslist-db": "update-browserslist-db/cli.js"
      "napi-postinstall": "napi-postinstall/lib/cli.js"
      "color-support": "color-support/bin.js"
      "atob": "atob/bin/atob.js"

"nice-try@npm:1.0.5":
  locations:
    - "node_modules/nice-try"

"nlcst-to-string@npm:4.0.0":
  locations:
    - "node_modules/nlcst-to-string"

"no-case@npm:2.3.2":
  locations:
    - "node_modules/no-case"

"node-abort-controller@npm:3.1.1":
  locations:
    - "node_modules/node-abort-controller"

"node-addon-api@npm:5.1.0":
  locations:
    - "node_modules/node-addon-api"

"node-emoji@npm:1.11.0":
  locations:
    - "node_modules/node-emoji"

"node-expat@npm:2.4.1":
  locations:
    - "node_modules/node-expat"

"node-fetch-native@npm:1.6.6":
  locations:
    - "node_modules/node-fetch-native"

"node-fetch@virtual:5547f15a2bb3d361d141532d43f94523f31e9edfe533f8367b3e26e300194e2978be03f56c09e100afcfee4c02b7fbe13c6ffcf58c613b457a86da522a2979f2#npm:2.7.0":
  locations:
    - "node_modules/node-fetch"

"node-gyp@npm:11.2.0":
  locations:
    - "node_modules/node-gyp"

"node-int64@npm:0.4.0":
  locations:
    - "node_modules/node-int64"

"node-mock-http@npm:1.0.0":
  locations:
    - "node_modules/node-mock-http"

"node-releases@npm:2.0.19":
  locations:
    - "node_modules/node-releases"

"nodemailer@npm:6.10.1":
  locations:
    - "node_modules/preview-email/node_modules/nodemailer"

"nodemailer@npm:7.0.3":
  locations:
    - "node_modules/nodemailer"

"nopt@npm:5.0.0":
  locations:
    - "node_modules/nopt"

"nopt@npm:7.2.1":
  locations:
    - "node_modules/js-beautify/node_modules/nopt"

"nopt@npm:8.1.0":
  locations:
    - "node_modules/node-gyp/node_modules/nopt"

"normalize-package-data@npm:2.5.0":
  locations:
    - "node_modules/normalize-package-data"

"normalize-path@npm:2.1.1":
  locations:
    - "node_modules/glob-stream/node_modules/normalize-path"
    - "node_modules/@gulp-sourcemaps/map-sources/node_modules/normalize-path"

"normalize-path@npm:3.0.0":
  locations:
    - "node_modules/normalize-path"

"npm-run-path@npm:2.0.2":
  locations:
    - "node_modules/npm-run-path"

"npm-run-path@npm:4.0.1":
  locations:
    - "node_modules/jest-changed-files/node_modules/npm-run-path"

"npmlog@npm:5.0.1":
  locations:
    - "node_modules/npmlog"

"nth-check@npm:2.1.1":
  locations:
    - "node_modules/nth-check"

"number-is-nan@npm:1.0.1":
  locations:
    - "node_modules/number-is-nan"

"object-assign@npm:4.1.0":
  locations:
    - "node_modules/debug-fabulous/node_modules/object-assign"

"object-assign@npm:4.1.1":
  locations:
    - "node_modules/object-assign"

"object-hash@npm:3.0.0":
  locations:
    - "node_modules/object-hash"

"object-inspect@npm:1.13.4":
  locations:
    - "node_modules/object-inspect"

"object-keys@npm:0.4.0":
  locations:
    - "node_modules/html-tokenize/node_modules/object-keys"

"object-keys@npm:1.1.1":
  locations:
    - "node_modules/object-keys"

"object.assign@npm:4.1.7":
  locations:
    - "node_modules/object.assign"

"object.entries@npm:1.1.9":
  locations:
    - "node_modules/object.entries"

"object.fromentries@npm:2.0.8":
  locations:
    - "node_modules/object.fromentries"

"object.groupby@npm:1.0.3":
  locations:
    - "node_modules/object.groupby"

"object.omit@npm:2.0.1":
  locations:
    - "node_modules/object.omit"

"object.values@npm:1.2.1":
  locations:
    - "node_modules/object.values"

"ofetch@npm:1.4.1":
  locations:
    - "node_modules/ofetch"

"ohash@npm:2.0.11":
  locations:
    - "node_modules/ohash"

"on-finished@npm:2.4.1":
  locations:
    - "node_modules/on-finished"

"once@npm:1.4.0":
  locations:
    - "node_modules/once"

"one-time@npm:1.0.0":
  locations:
    - "node_modules/one-time"

"onetime@npm:5.1.2":
  locations:
    - "node_modules/onetime"

"onetime@npm:7.0.0":
  locations:
    - "node_modules/restore-cursor/node_modules/onetime"

"oniguruma-parser@npm:0.12.1":
  locations:
    - "node_modules/oniguruma-parser"

"oniguruma-to-es@npm:4.3.3":
  locations:
    - "node_modules/oniguruma-to-es"

"open@npm:7.4.2":
  locations:
    - "node_modules/open"

"optionator@npm:0.9.4":
  locations:
    - "node_modules/optionator"

"ora@npm:5.4.1":
  locations:
    - "node_modules/ora"

"ordered-read-streams@npm:0.3.0":
  locations:
    - "node_modules/ordered-read-streams"

"orderedmap@npm:2.1.1":
  locations:
    - "node_modules/orderedmap"

"os-homedir@npm:1.0.2":
  locations:
    - "node_modules/os-homedir"

"os-locale@npm:1.4.0":
  locations:
    - "node_modules/os-locale"

"os-tmpdir@npm:1.0.2":
  locations:
    - "node_modules/os-tmpdir"

"own-keys@npm:1.0.1":
  locations:
    - "node_modules/own-keys"

"p-event@npm:4.2.0":
  locations:
    - "node_modules/p-event"

"p-finally@npm:1.0.0":
  locations:
    - "node_modules/p-finally"

"p-limit@npm:2.3.0":
  locations:
    - "node_modules/pkg-dir/node_modules/p-limit"
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/p-limit"

"p-limit@npm:3.1.0":
  locations:
    - "node_modules/p-limit"

"p-limit@npm:6.2.0":
  locations:
    - "node_modules/astro/node_modules/p-limit"

"p-locate@npm:4.1.0":
  locations:
    - "node_modules/pkg-dir/node_modules/p-locate"
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/p-locate"

"p-locate@npm:5.0.0":
  locations:
    - "node_modules/p-locate"

"p-map@npm:7.0.3":
  locations:
    - "node_modules/p-map"

"p-queue@npm:8.1.0":
  locations:
    - "node_modules/p-queue"

"p-timeout@npm:3.2.0":
  locations:
    - "node_modules/p-timeout"

"p-timeout@npm:6.1.4":
  locations:
    - "node_modules/p-queue/node_modules/p-timeout"

"p-try@npm:2.2.0":
  locations:
    - "node_modules/p-try"

"p-wait-for@npm:3.2.0":
  locations:
    - "node_modules/p-wait-for"

"package-json-from-dist@npm:1.0.1":
  locations:
    - "node_modules/package-json-from-dist"

"package-manager-detector@npm:1.3.0":
  locations:
    - "node_modules/package-manager-detector"

"pako@npm:0.2.9":
  locations:
    - "node_modules/pako"

"papaparse@npm:5.5.3":
  locations:
    - "node_modules/papaparse"

"param-case@npm:2.1.1":
  locations:
    - "node_modules/param-case"

"parent-module@npm:1.0.1":
  locations:
    - "node_modules/parent-module"

"parse-glob@npm:3.0.4":
  locations:
    - "node_modules/parse-glob"

"parse-json@npm:2.2.0":
  locations:
    - "node_modules/load-json-file/node_modules/parse-json"

"parse-json@npm:5.2.0":
  locations:
    - "node_modules/parse-json"

"parse-latin@npm:7.0.0":
  locations:
    - "node_modules/parse-latin"

"parse-listing@npm:1.1.3":
  locations:
    - "node_modules/parse-listing"

"parse5-htmlparser2-tree-adapter@npm:7.1.0":
  locations:
    - "node_modules/parse5-htmlparser2-tree-adapter"

"parse5@npm:7.3.0":
  locations:
    - "node_modules/parse5"

"parseley@npm:0.12.1":
  locations:
    - "node_modules/parseley"

"parseurl@npm:1.3.3":
  locations:
    - "node_modules/parseurl"

"path-dirname@npm:1.0.2":
  locations:
    - "node_modules/path-dirname"

"path-exists@npm:2.1.0":
  locations:
    - "node_modules/find-up/node_modules/path-exists"

"path-exists@npm:4.0.0":
  locations:
    - "node_modules/path-exists"

"path-is-absolute@npm:1.0.1":
  locations:
    - "node_modules/path-is-absolute"

"path-key@npm:2.0.1":
  locations:
    - "node_modules/npm-run-path/node_modules/path-key"
    - "node_modules/execa/node_modules/path-key"

"path-key@npm:3.1.1":
  locations:
    - "node_modules/path-key"

"path-parse@npm:1.0.7":
  locations:
    - "node_modules/path-parse"

"path-scurry@npm:1.11.1":
  locations:
    - "node_modules/path-scurry"

"path-to-regexp@npm:0.1.12":
  locations:
    - "node_modules/express/node_modules/path-to-regexp"

"path-to-regexp@npm:3.3.0":
  locations:
    - "node_modules/path-to-regexp"

"path-type@npm:1.1.0":
  locations:
    - "node_modules/read-pkg/node_modules/path-type"

"path-type@npm:4.0.0":
  locations:
    - "node_modules/path-type"

"peberminta@npm:0.9.0":
  locations:
    - "node_modules/peberminta"

"pg-cloudflare@npm:1.2.5":
  locations:
    - "node_modules/pg-cloudflare"

"pg-connection-string@npm:2.9.0":
  locations:
    - "node_modules/pg-connection-string"

"pg-int8@npm:1.0.1":
  locations:
    - "node_modules/pg-int8"

"pg-pool@virtual:c442e6e9545f415335aafec7417c275dc58811af280a0aeb49f7d1996947ae86fdf8e840717c5ef93fc995f8766ebc46c7d91a97a79efa5931bafc8245017570#npm:3.10.0":
  locations:
    - "node_modules/pg-pool"

"pg-protocol@npm:1.10.0":
  locations:
    - "node_modules/pg-protocol"

"pg-types@npm:2.2.0":
  locations:
    - "node_modules/pg-types"

"pg@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:8.16.0":
  locations:
    - "node_modules/pg"

"pgpass@npm:1.0.5":
  locations:
    - "node_modules/pgpass"

"picocolors@npm:1.1.1":
  locations:
    - "node_modules/picocolors"

"picomatch@npm:2.3.1":
  locations:
    - "node_modules/readdirp/node_modules/picomatch"
    - "node_modules/micromatch/node_modules/picomatch"
    - "node_modules/jest-util/node_modules/picomatch"
    - "node_modules/anymatch/node_modules/picomatch"

"picomatch@npm:4.0.1":
  locations:
    - "node_modules/@angular-devkit/core/node_modules/picomatch"

"picomatch@npm:4.0.2":
  locations:
    - "node_modules/picomatch"

"pidtree@npm:0.6.0":
  locations:
    - "node_modules/pidtree"

"pify@npm:2.3.0":
  locations:
    - "node_modules/pify"

"pinkie-promise@npm:2.0.1":
  locations:
    - "node_modules/pinkie-promise"

"pinkie@npm:2.0.4":
  locations:
    - "node_modules/pinkie"

"pirates@npm:4.0.7":
  locations:
    - "node_modules/pirates"

"pkg-conf@npm:1.1.3":
  locations:
    - "node_modules/pkg-conf"

"pkg-dir@npm:4.2.0":
  locations:
    - "node_modules/pkg-dir"

"pluralize@npm:8.0.0":
  locations:
    - "node_modules/pluralize"

"possible-typed-array-names@npm:1.1.0":
  locations:
    - "node_modules/possible-typed-array-names"

"postcss-import@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:15.1.0":
  locations:
    - "node_modules/postcss-import"

"postcss-js@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:4.0.1":
  locations:
    - "node_modules/postcss-js"

"postcss-load-config@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:4.0.2":
  locations:
    - "node_modules/postcss-load-config"

"postcss-nested@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:6.2.0":
  locations:
    - "node_modules/postcss-nested"

"postcss-selector-parser@npm:6.1.2":
  locations:
    - "node_modules/postcss-selector-parser"

"postcss-selector-parser@npm:7.1.0":
  locations:
    - "node_modules/eslint-plugin-astro/node_modules/postcss-selector-parser"

"postcss-value-parser@npm:4.2.0":
  locations:
    - "node_modules/postcss-value-parser"

"postcss@npm:8.4.31":
  locations:
    - "node_modules/next/node_modules/postcss"

"postcss@npm:8.4.49":
  locations:
    - "node_modules/styled-components/node_modules/postcss"

"postcss@npm:8.5.4":
  locations:
    - "node_modules/postcss"

"postgres-array@npm:2.0.0":
  locations:
    - "node_modules/postgres-array"

"postgres-bytea@npm:1.0.0":
  locations:
    - "node_modules/postgres-bytea"

"postgres-date@npm:1.0.7":
  locations:
    - "node_modules/postgres-date"

"postgres-interval@npm:1.2.0":
  locations:
    - "node_modules/postgres-interval"

"prelude-ls@npm:1.2.1":
  locations:
    - "node_modules/prelude-ls"

"preserve@npm:0.2.0":
  locations:
    - "node_modules/preserve"

"prettier-linter-helpers@npm:1.0.0":
  locations:
    - "node_modules/prettier-linter-helpers"

"prettier-plugin-astro@npm:0.14.1":
  locations:
    - "node_modules/prettier-plugin-astro"

"prettier@npm:3.5.3":
  locations:
    - "node_modules/prettier"

"pretty-format@npm:29.7.0":
  locations:
    - "node_modules/pretty-format"

"preview-email@npm:3.1.0":
  locations:
    - "node_modules/preview-email"

"prismjs@npm:1.30.0":
  locations:
    - "node_modules/prismjs"

"proc-log@npm:5.0.0":
  locations:
    - "node_modules/proc-log"

"process-nextick-args@npm:1.0.7":
  locations:
    - "node_modules/process-nextick-args"

"process-nextick-args@npm:2.0.1":
  locations:
    - "node_modules/readable-stream/node_modules/process-nextick-args"

"promise-retry@npm:2.0.1":
  locations:
    - "node_modules/promise-retry"

"promise@npm:7.3.1":
  locations:
    - "node_modules/promise"

"prompts@npm:2.4.2":
  locations:
    - "node_modules/prompts"

"prop-types@npm:15.7.2":
  locations:
    - "node_modules/react-filerobot-image-editor/node_modules/prop-types"

"prop-types@npm:15.8.1":
  locations:
    - "node_modules/prop-types"

"property-expr@npm:2.0.6":
  locations:
    - "node_modules/property-expr"

"property-information@npm:6.5.0":
  locations:
    - "node_modules/hast-util-to-parse5/node_modules/property-information"

"property-information@npm:7.1.0":
  locations:
    - "node_modules/property-information"

"prosemirror-changeset@npm:2.3.1":
  locations:
    - "node_modules/prosemirror-changeset"

"prosemirror-collab@npm:1.3.1":
  locations:
    - "node_modules/prosemirror-collab"

"prosemirror-commands@npm:1.7.1":
  locations:
    - "node_modules/prosemirror-commands"

"prosemirror-dropcursor@npm:1.8.2":
  locations:
    - "node_modules/prosemirror-dropcursor"

"prosemirror-gapcursor@npm:1.3.2":
  locations:
    - "node_modules/prosemirror-gapcursor"

"prosemirror-history@npm:1.4.1":
  locations:
    - "node_modules/prosemirror-history"

"prosemirror-inputrules@npm:1.5.0":
  locations:
    - "node_modules/prosemirror-inputrules"

"prosemirror-keymap@npm:1.2.3":
  locations:
    - "node_modules/prosemirror-keymap"

"prosemirror-markdown@npm:1.13.2":
  locations:
    - "node_modules/prosemirror-markdown"

"prosemirror-menu@npm:1.2.5":
  locations:
    - "node_modules/prosemirror-menu"

"prosemirror-model@npm:1.25.1":
  locations:
    - "node_modules/prosemirror-model"

"prosemirror-schema-basic@npm:1.2.4":
  locations:
    - "node_modules/prosemirror-schema-basic"

"prosemirror-schema-list@npm:1.5.1":
  locations:
    - "node_modules/prosemirror-schema-list"

"prosemirror-state@npm:1.4.3":
  locations:
    - "node_modules/prosemirror-state"

"prosemirror-tables@npm:1.7.1":
  locations:
    - "node_modules/prosemirror-tables"

"prosemirror-trailing-node@virtual:cce07e9b83156d99683ebe3ef2ba8701a44e84f2b574ac1069883d6e58995d0b9e0e27a61e6dbfc2970bb35eab239384592f2f45401bde2c6c1ef99abe7a5a68#npm:3.0.0":
  locations:
    - "node_modules/prosemirror-trailing-node"

"prosemirror-transform@npm:1.10.4":
  locations:
    - "node_modules/prosemirror-transform"

"prosemirror-view@npm:1.40.0":
  locations:
    - "node_modules/prosemirror-view"

"proto-list@npm:1.2.4":
  locations:
    - "node_modules/proto-list"

"proxy-addr@npm:2.0.7":
  locations:
    - "node_modules/proxy-addr"

"proxy-from-env@npm:1.1.0":
  locations:
    - "node_modules/proxy-from-env"

"pug-attrs@npm:3.0.0":
  locations:
    - "node_modules/pug-attrs"

"pug-code-gen@npm:3.0.3":
  locations:
    - "node_modules/pug-code-gen"

"pug-error@npm:2.1.0":
  locations:
    - "node_modules/pug-error"

"pug-filters@npm:4.0.0":
  locations:
    - "node_modules/pug-filters"

"pug-lexer@npm:5.0.1":
  locations:
    - "node_modules/pug-lexer"

"pug-linker@npm:4.0.0":
  locations:
    - "node_modules/pug-linker"

"pug-load@npm:3.0.0":
  locations:
    - "node_modules/pug-load"

"pug-parser@npm:6.0.0":
  locations:
    - "node_modules/pug-parser"

"pug-runtime@npm:3.0.1":
  locations:
    - "node_modules/pug-runtime"

"pug-strip-comments@npm:2.0.0":
  locations:
    - "node_modules/pug-strip-comments"

"pug-walk@npm:2.0.0":
  locations:
    - "node_modules/pug-walk"

"pug@npm:3.0.3":
  locations:
    - "node_modules/pug"

"punycode.js@npm:2.3.1":
  locations:
    - "node_modules/punycode.js"

"punycode@npm:1.3.2":
  locations:
    - "node_modules/url/node_modules/punycode"

"punycode@npm:2.3.1":
  locations:
    - "node_modules/punycode"

"pure-rand@npm:6.1.0":
  locations:
    - "node_modules/pure-rand"

"qs@npm:6.13.0":
  locations:
    - "node_modules/express/node_modules/qs"
    - "node_modules/body-parser/node_modules/qs"

"qs@npm:6.14.0":
  locations:
    - "node_modules/qs"

"query-string@npm:9.2.0":
  locations:
    - "node_modules/query-string"

"querystring@npm:0.2.0":
  locations:
    - "node_modules/querystring"

"queue-microtask@npm:1.2.3":
  locations:
    - "node_modules/queue-microtask"

"radix3@npm:1.1.2":
  locations:
    - "node_modules/radix3"

"raf-schd@npm:4.0.3":
  locations:
    - "node_modules/raf-schd"

"randomatic@npm:3.1.1":
  locations:
    - "node_modules/randomatic"

"randombytes@npm:2.1.0":
  locations:
    - "node_modules/randombytes"

"range-parser@npm:1.2.1":
  locations:
    - "node_modules/range-parser"

"raw-body@npm:2.5.2":
  locations:
    - "node_modules/raw-body"

"rc@npm:1.2.8":
  locations:
    - "node_modules/rc"

"react-beautiful-dnd@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:13.1.1":
  locations:
    - "node_modules/react-beautiful-dnd"

"react-best-gradient-color-picker@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:3.0.14":
  locations:
    - "node_modules/react-best-gradient-color-picker"

"react-colorful@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:5.6.1":
  locations:
    - "node_modules/react-colorful"

"react-complex-tree@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:2.4.6":
  locations:
    - "node_modules/react-complex-tree"

"react-dnd-html5-backend@npm:16.0.1":
  locations:
    - "node_modules/react-dnd-html5-backend"

"react-dnd@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:16.0.1":
  locations:
    - "node_modules/react-dnd"

"react-dom@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:18.3.1":
  locations:
    - "node_modules/react-dom"

"react-draggable@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:4.4.6":
  locations:
    - "node_modules/react-draggable"

"react-filerobot-image-editor@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:4.9.1":
  locations:
    - "node_modules/react-filerobot-image-editor"

"react-hook-form@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:7.57.0":
  locations:
    - "node_modules/react-hook-form"

"react-i18next@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:15.5.2":
  locations:
    - "node_modules/react-i18next"

"react-i18next@virtual:f076ded31fccc59d4f9e6ced6349f34297f2dd657b8275061704bbe29a11a614ffe29c030a051fc61b74e50142507a1ec03a156876f30c45951e0e1e6cc4795e#npm:15.5.2":
  locations:
    - "apps/ssg/node_modules/react-i18next"

"react-i18next@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:15.5.2":
  locations:
    - "apps/design-editor/node_modules/react-i18next"

"react-intersection-observer@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:9.16.0":
  locations:
    - "node_modules/react-intersection-observer"

"react-is@npm:16.13.1":
  locations:
    - "node_modules/react-is"

"react-is@npm:17.0.2":
  locations:
    - "node_modules/react-beautiful-dnd/node_modules/react-is"

"react-is@npm:18.3.1":
  locations:
    - "node_modules/pretty-format/node_modules/react-is"

"react-is@npm:19.1.0":
  locations:
    - "node_modules/@mui/x-internals/node_modules/react-is"
    - "node_modules/@mui/x-date-pickers/node_modules/react-is"
    - "node_modules/@mui/x-charts/node_modules/react-is"
    - "node_modules/@mui/utils/node_modules/react-is"
    - "node_modules/@mui/material/node_modules/react-is"

"react-konva@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:18.2.10":
  locations:
    - "node_modules/react-konva"

"react-material-symbols@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:4.4.0":
  locations:
    - "node_modules/react-material-symbols"

"react-reconciler@virtual:7c10321826407f5fb71fbc4a7dd5504185efb590c9f9a855a6b5199aa3bf6995a4af3f68dd20977533d459fd0800ef4f6520727c17e788b16ab8dd5bc0548357#npm:0.29.2":
  locations:
    - "node_modules/react-reconciler"

"react-redux@virtual:25b987bad67b27fa1da609fb29920d19161906534174c31f7a5763f03d8088ec0edf4c87d96fd1070a4fe21dd8ab99b8d5ad4059867c84f94b7124f5a0b6e7b5#npm:7.2.9":
  locations:
    - "node_modules/react-beautiful-dnd/node_modules/react-redux"

"react-redux@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:9.2.0":
  locations:
    - "node_modules/react-redux"

"react-refresh@npm:0.17.0":
  locations:
    - "node_modules/react-refresh"

"react-router-dom@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:7.6.2":
  locations:
    - "node_modules/react-router-dom"

"react-router@virtual:e1c3437082c9e12bab11d6eeda99b264c51301a8dc143d8954a74156689407ecef8947821ba5b1c25103de2364322f944d49af0fba33716567ce0ba5981a3ae1#npm:7.6.2":
  locations:
    - "node_modules/react-router"

"react-select@virtual:5d63d3102ca7e616987152c5c8e5bbf6ac34a973d26ccba43db1d35b323787a442bcb9b578e2db51660247b50c1693d2f1af70721af3780dcbd9e4e53747564d#npm:5.10.1":
  locations:
    - "node_modules/react-select"

"react-transition-group@virtual:8b7c244d6a7e6d8ae9814fa74b3d1d35270987acf177442262f1c7b634d6f55b3d454030170da205794d6b37258530e48419d1ae0665df48caeb2b4480e60eac#npm:4.4.5":
  locations:
    - "node_modules/react-transition-group"

"react@npm:18.3.1":
  locations:
    - "node_modules/react"

"read-cache@npm:1.0.0":
  locations:
    - "node_modules/read-cache"

"read-pkg-up@npm:1.0.1":
  locations:
    - "node_modules/read-pkg-up"

"read-pkg@npm:1.1.0":
  locations:
    - "node_modules/read-pkg"

"readable-stream@npm:1.0.34":
  locations:
    - "node_modules/html-tokenize/node_modules/readable-stream"
    - "node_modules/glob-stream/node_modules/readable-stream"

"readable-stream@npm:1.1.14":
  locations:
    - "node_modules/ftp-response-parser/node_modules/readable-stream"

"readable-stream@npm:2.0.6":
  locations:
    - "node_modules/concat-stream/node_modules/readable-stream"

"readable-stream@npm:2.3.8":
  locations:
    - "node_modules/readable-stream"

"readable-stream@npm:3.6.2":
  locations:
    - "node_modules/winston/node_modules/readable-stream"
    - "node_modules/winston-transport/node_modules/readable-stream"
    - "node_modules/multer/node_modules/readable-stream"
    - "node_modules/bl/node_modules/readable-stream"
    - "node_modules/are-we-there-yet/node_modules/readable-stream"

"readdirp@npm:3.6.0":
  locations:
    - "node_modules/readdirp"

"readdirp@npm:4.1.2":
  locations:
    - "node_modules/unstorage/node_modules/readdirp"

"redux-thunk@virtual:c12a494b7a15cd47c9485a01c6add24b6b2e6de86fea79f7a3135f31d702fdc15ebd2b501e553df26f491581f7a53c00f01b36f63b3551c540f02d33ea221575#npm:3.1.0":
  locations:
    - "node_modules/redux-thunk"

"redux@npm:4.2.1":
  locations:
    - "node_modules/react-beautiful-dnd/node_modules/redux"
    - "node_modules/dnd-core/node_modules/redux"
    - "node_modules/@types/react-redux/node_modules/redux"

"redux@npm:5.0.1":
  locations:
    - "node_modules/redux"

"reflect-metadata@npm:0.2.2":
  locations:
    - "node_modules/reflect-metadata"

"reflect.getprototypeof@npm:1.0.10":
  locations:
    - "node_modules/reflect.getprototypeof"

"regex-cache@npm:0.4.4":
  locations:
    - "node_modules/regex-cache"

"regex-recursion@npm:6.0.2":
  locations:
    - "node_modules/regex-recursion"

"regex-utilities@npm:2.3.0":
  locations:
    - "node_modules/regex-utilities"

"regex@npm:6.0.1":
  locations:
    - "node_modules/regex"

"regexp.prototype.flags@npm:1.5.4":
  locations:
    - "node_modules/regexp.prototype.flags"

"rehype-parse@npm:9.0.1":
  locations:
    - "node_modules/rehype-parse"

"rehype-raw@npm:7.0.0":
  locations:
    - "node_modules/rehype-raw"

"rehype-stringify@npm:10.0.1":
  locations:
    - "node_modules/rehype-stringify"

"rehype@npm:13.0.2":
  locations:
    - "node_modules/rehype"

"relateurl@npm:0.2.7":
  locations:
    - "node_modules/relateurl"

"remark-gfm@npm:4.0.1":
  locations:
    - "node_modules/remark-gfm"

"remark-parse@npm:11.0.0":
  locations:
    - "node_modules/remark-parse"

"remark-rehype@npm:11.1.2":
  locations:
    - "node_modules/remark-rehype"

"remark-smartypants@npm:3.0.2":
  locations:
    - "node_modules/remark-smartypants"

"remark-stringify@npm:11.0.0":
  locations:
    - "node_modules/remark-stringify"

"remove-trailing-separator@npm:1.1.0":
  locations:
    - "node_modules/remove-trailing-separator"

"repeat-element@npm:1.1.4":
  locations:
    - "node_modules/repeat-element"

"repeat-string@npm:1.6.1":
  locations:
    - "node_modules/repeat-string"

"replace-ext@npm:0.0.1":
  locations:
    - "node_modules/replace-ext"

"require-directory@npm:2.1.1":
  locations:
    - "node_modules/require-directory"

"require-from-string@npm:2.0.2":
  locations:
    - "node_modules/require-from-string"

"require-main-filename@npm:1.0.1":
  locations:
    - "node_modules/require-main-filename"

"reselect@npm:5.1.1":
  locations:
    - "node_modules/reselect"

"resolve-cwd@npm:3.0.0":
  locations:
    - "node_modules/resolve-cwd"

"resolve-from@npm:4.0.0":
  locations:
    - "node_modules/import-fresh/node_modules/resolve-from"

"resolve-from@npm:5.0.0":
  locations:
    - "node_modules/resolve-from"

"resolve-pkg-maps@npm:1.0.0":
  locations:
    - "node_modules/resolve-pkg-maps"

"resolve-url@npm:0.2.1":
  locations:
    - "node_modules/resolve-url"

"resolve.exports@npm:2.0.3":
  locations:
    - "node_modules/resolve.exports"

"resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d":
  locations:
    - "node_modules/resolve"

"resolve@patch:resolve@npm%3A2.0.0-next.5#optional!builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d":
  locations:
    - "node_modules/eslint-plugin-react/node_modules/resolve"

"restore-cursor@npm:3.1.0":
  locations:
    - "node_modules/cli-cursor/node_modules/restore-cursor"

"restore-cursor@npm:5.1.0":
  locations:
    - "node_modules/restore-cursor"

"restructure@npm:3.0.2":
  locations:
    - "node_modules/restructure"

"retext-latin@npm:4.0.0":
  locations:
    - "node_modules/retext-latin"

"retext-smartypants@npm:6.2.0":
  locations:
    - "node_modules/retext-smartypants"

"retext-stringify@npm:4.0.0":
  locations:
    - "node_modules/retext-stringify"

"retext@npm:9.0.0":
  locations:
    - "node_modules/retext"

"retry@npm:0.12.0":
  locations:
    - "node_modules/retry"

"reusify@npm:1.1.0":
  locations:
    - "node_modules/reusify"

"rfdc@npm:1.4.1":
  locations:
    - "node_modules/rfdc"

"rimraf@npm:3.0.2":
  locations:
    - "node_modules/rimraf"

"robust-predicates@npm:3.0.2":
  locations:
    - "node_modules/robust-predicates"

"rollup@npm:4.42.0":
  locations:
    - "node_modules/rollup"

"rope-sequence@npm:1.3.4":
  locations:
    - "node_modules/rope-sequence"

"run-applescript@npm:3.2.0":
  locations:
    - "node_modules/run-applescript"

"run-async@npm:2.4.1":
  locations:
    - "node_modules/@nestjs/cli/node_modules/run-async"

"run-async@npm:3.0.0":
  locations:
    - "node_modules/run-async"

"run-parallel@npm:1.2.0":
  locations:
    - "node_modules/run-parallel"

"rxjs@npm:7.8.1":
  locations:
    - "node_modules/@angular-devkit/schematics/node_modules/rxjs"
    - "node_modules/@angular-devkit/core/node_modules/rxjs"

"rxjs@npm:7.8.2":
  locations:
    - "node_modules/rxjs"

"s.color@npm:0.0.15":
  locations:
    - "node_modules/s.color"

"safe-array-concat@npm:1.1.3":
  locations:
    - "node_modules/safe-array-concat"

"safe-buffer@npm:5.1.2":
  locations:
    - "node_modules/readable-stream/node_modules/safe-buffer"

"safe-buffer@npm:5.2.1":
  locations:
    - "node_modules/safe-buffer"

"safe-push-apply@npm:1.0.0":
  locations:
    - "node_modules/safe-push-apply"

"safe-regex-test@npm:1.1.0":
  locations:
    - "node_modules/safe-regex-test"

"safe-stable-stringify@npm:2.5.0":
  locations:
    - "node_modules/safe-stable-stringify"

"safer-buffer@npm:2.1.2":
  locations:
    - "node_modules/safer-buffer"

"sass-embedded-win32-x64@npm:1.89.1":
  locations:
    - "node_modules/sass-embedded-win32-x64"

"sass-embedded@npm:1.89.1":
  locations:
    - "node_modules/sass-embedded"

"sass-formatter@npm:0.7.9":
  locations:
    - "node_modules/sass-formatter"

"sax@npm:1.2.1":
  locations:
    - "node_modules/sax"

"sax@npm:1.4.1":
  locations:
    - "node_modules/xml2js/node_modules/sax"

"scheduler@npm:0.23.2":
  locations:
    - "node_modules/scheduler"

"schema-utils@npm:3.3.0":
  locations:
    - "node_modules/schema-utils"

"schema-utils@npm:4.3.2":
  locations:
    - "node_modules/terser-webpack-plugin/node_modules/schema-utils"

"selderee@npm:0.11.0":
  locations:
    - "node_modules/selderee"

"semver@npm:5.7.2":
  locations:
    - "node_modules/normalize-package-data/node_modules/semver"
    - "node_modules/execa/node_modules/semver"

"semver@npm:6.3.1":
  locations:
    - "node_modules/make-dir/node_modules/semver"
    - "node_modules/istanbul-lib-instrument/node_modules/semver"
    - "node_modules/eslint-plugin-react/node_modules/semver"
    - "node_modules/eslint-plugin-import/node_modules/semver"
    - "node_modules/@babel/helper-compilation-targets/node_modules/semver"
    - "node_modules/@babel/core/node_modules/semver"

"semver@npm:7.7.2":
  locations:
    - "node_modules/semver"

"send@npm:0.19.0":
  locations:
    - "node_modules/send"

"serialize-javascript@npm:6.0.2":
  locations:
    - "node_modules/serialize-javascript"

"seroval-plugins@virtual:1c773c1f26d71d986b26447997b3958a4d29559ea5dcfe9a9a41e80cc585e996a918a37b5825dc415898cc2ef960310246958e917fc8fc558110fccc331f1674#npm:1.3.2":
  locations:
    - "node_modules/seroval-plugins"

"seroval@npm:1.3.2":
  locations:
    - "node_modules/seroval"

"serve-static@npm:1.16.2":
  locations:
    - "node_modules/serve-static"

"set-blocking@npm:2.0.0":
  locations:
    - "node_modules/set-blocking"

"set-cookie-parser@npm:2.7.1":
  locations:
    - "node_modules/set-cookie-parser"

"set-function-length@npm:1.2.2":
  locations:
    - "node_modules/set-function-length"

"set-function-name@npm:2.0.2":
  locations:
    - "node_modules/set-function-name"

"set-proto@npm:1.0.0":
  locations:
    - "node_modules/set-proto"

"setprototypeof@npm:1.2.0":
  locations:
    - "node_modules/setprototypeof"

"sha.js@npm:2.4.11":
  locations:
    - "node_modules/sha.js"

"shallowequal@npm:1.1.0":
  locations:
    - "node_modules/shallowequal"

"sharp@npm:0.33.5":
  locations:
    - "node_modules/sharp"

"shebang-command@npm:1.2.0":
  locations:
    - "node_modules/execa/node_modules/shebang-command"

"shebang-command@npm:2.0.0":
  locations:
    - "node_modules/shebang-command"

"shebang-regex@npm:1.0.0":
  locations:
    - "node_modules/execa/node_modules/shebang-regex"

"shebang-regex@npm:3.0.0":
  locations:
    - "node_modules/shebang-regex"

"shiki@npm:3.6.0":
  locations:
    - "node_modules/shiki"

"side-channel-list@npm:1.0.0":
  locations:
    - "node_modules/side-channel-list"

"side-channel-map@npm:1.0.1":
  locations:
    - "node_modules/side-channel-map"

"side-channel-weakmap@npm:1.0.2":
  locations:
    - "node_modules/side-channel-weakmap"

"side-channel@npm:1.1.0":
  locations:
    - "node_modules/side-channel"

"signal-exit@npm:3.0.7":
  locations:
    - "node_modules/signal-exit"

"signal-exit@npm:4.1.0":
  locations:
    - "node_modules/restore-cursor/node_modules/signal-exit"
    - "node_modules/foreground-child/node_modules/signal-exit"

"simple-swizzle@npm:0.2.2":
  locations:
    - "node_modules/simple-swizzle"

"sisteransi@npm:1.0.5":
  locations:
    - "node_modules/sisteransi"

"slash@npm:3.0.0":
  locations:
    - "node_modules/slash"

"slice-ansi@npm:5.0.0":
  locations:
    - "node_modules/slice-ansi"

"slice-ansi@npm:7.1.0":
  locations:
    - "node_modules/log-update/node_modules/slice-ansi"

"slick@npm:1.12.2":
  locations:
    - "node_modules/slick"

"smart-buffer@npm:4.2.0":
  locations:
    - "node_modules/smart-buffer"

"smol-toml@npm:1.3.4":
  locations:
    - "node_modules/smol-toml"

"socks-proxy-agent@npm:8.0.5":
  locations:
    - "node_modules/socks-proxy-agent"

"socks@npm:2.8.4":
  locations:
    - "node_modules/socks"

"solid-js@npm:1.9.7":
  locations:
    - "node_modules/solid-js"

"source-map-js@npm:1.2.1":
  locations:
    - "node_modules/source-map-js"

"source-map-resolve@npm:0.5.3":
  locations:
    - "node_modules/source-map-resolve"

"source-map-support@npm:0.5.13":
  locations:
    - "node_modules/jest-runner/node_modules/source-map-support"

"source-map-support@npm:0.5.21":
  locations:
    - "node_modules/source-map-support"

"source-map-url@npm:0.4.1":
  locations:
    - "node_modules/source-map-url"

"source-map@npm:0.5.7":
  locations:
    - "node_modules/@emotion/babel-plugin/node_modules/source-map"

"source-map@npm:0.6.1":
  locations:
    - "node_modules/source-map"

"source-map@npm:0.7.4":
  locations:
    - "node_modules/ts-loader/node_modules/source-map"
    - "node_modules/@angular-devkit/core/node_modules/source-map"

"space-separated-tokens@npm:2.0.2":
  locations:
    - "node_modules/space-separated-tokens"

"spdx-correct@npm:3.2.0":
  locations:
    - "node_modules/spdx-correct"

"spdx-exceptions@npm:2.5.0":
  locations:
    - "node_modules/spdx-exceptions"

"spdx-expression-parse@npm:3.0.1":
  locations:
    - "node_modules/spdx-expression-parse"

"spdx-license-ids@npm:3.0.21":
  locations:
    - "node_modules/spdx-license-ids"

"split-on-first@npm:3.0.0":
  locations:
    - "node_modules/split-on-first"

"split2@npm:4.2.0":
  locations:
    - "node_modules/split2"

"sprintf-js@npm:1.0.3":
  locations:
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/sprintf-js"

"sprintf-js@npm:1.1.3":
  locations:
    - "node_modules/sprintf-js"

"sql-highlight@npm:6.1.0":
  locations:
    - "node_modules/sql-highlight"

"ssri@npm:12.0.0":
  locations:
    - "node_modules/ssri"

"stable-hash@npm:0.0.5":
  locations:
    - "node_modules/stable-hash"

"stack-trace@npm:0.0.10":
  locations:
    - "node_modules/stack-trace"

"stack-utils@npm:2.0.6":
  locations:
    - "node_modules/stack-utils"

"statuses@npm:2.0.1":
  locations:
    - "node_modules/statuses"

"stop-iteration-iterator@npm:1.1.0":
  locations:
    - "node_modules/stop-iteration-iterator"

"stream-combiner@npm:0.2.2":
  locations:
    - "node_modules/stream-combiner"

"stream-shift@npm:1.0.3":
  locations:
    - "node_modules/stream-shift"

"streamsearch@npm:1.1.0":
  locations:
    - "node_modules/streamsearch"

"string-argv@npm:0.3.2":
  locations:
    - "node_modules/string-argv"

"string-length@npm:4.0.2":
  locations:
    - "node_modules/string-length"

"string-width@npm:1.0.2":
  locations:
    - "node_modules/module/node_modules/string-width"
    - "node_modules/cliui/node_modules/string-width"

"string-width@npm:4.2.3":
  locations:
    - "node_modules/string-width-cjs"
    - "node_modules/string-width"

"string-width@npm:5.1.2":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/string-width"

"string-width@npm:7.2.0":
  locations:
    - "node_modules/wrap-ansi/node_modules/string-width"
    - "node_modules/widest-line/node_modules/string-width"
    - "node_modules/cli-truncate/node_modules/string-width"
    - "node_modules/boxen/node_modules/string-width"

"string.fromcodepoint@npm:0.2.1":
  locations:
    - "node_modules/string.fromcodepoint"

"string.prototype.includes@npm:2.0.1":
  locations:
    - "node_modules/string.prototype.includes"

"string.prototype.matchall@npm:4.0.12":
  locations:
    - "node_modules/string.prototype.matchall"

"string.prototype.repeat@npm:1.0.0":
  locations:
    - "node_modules/string.prototype.repeat"

"string.prototype.trim@npm:1.2.10":
  locations:
    - "node_modules/string.prototype.trim"

"string.prototype.trimend@npm:1.0.9":
  locations:
    - "node_modules/string.prototype.trimend"

"string.prototype.trimstart@npm:1.0.8":
  locations:
    - "node_modules/string.prototype.trimstart"

"string_decoder@npm:0.10.31":
  locations:
    - "node_modules/string_decoder"

"string_decoder@npm:1.1.1":
  locations:
    - "node_modules/readable-stream/node_modules/string_decoder"

"string_decoder@npm:1.3.0":
  locations:
    - "node_modules/winston/node_modules/string_decoder"
    - "node_modules/winston-transport/node_modules/string_decoder"
    - "node_modules/multer/node_modules/string_decoder"
    - "node_modules/bl/node_modules/string_decoder"
    - "node_modules/are-we-there-yet/node_modules/string_decoder"

"stringify-entities@npm:4.0.4":
  locations:
    - "node_modules/stringify-entities"

"strip-ansi@npm:3.0.1":
  locations:
    - "node_modules/module/node_modules/strip-ansi"
    - "node_modules/cliui/node_modules/strip-ansi"

"strip-ansi@npm:6.0.1":
  locations:
    - "node_modules/strip-ansi-cjs"
    - "node_modules/strip-ansi"

"strip-ansi@npm:7.1.0":
  locations:
    - "node_modules/wrap-ansi/node_modules/strip-ansi"
    - "node_modules/widest-line/node_modules/strip-ansi"
    - "node_modules/log-update/node_modules/strip-ansi"
    - "node_modules/cli-truncate/node_modules/strip-ansi"
    - "node_modules/boxen/node_modules/strip-ansi"
    - "node_modules/@isaacs/cliui/node_modules/strip-ansi"

"strip-bom-stream@npm:1.0.0":
  locations:
    - "node_modules/strip-bom-stream"

"strip-bom@npm:2.0.0":
  locations:
    - "node_modules/strip-bom"

"strip-bom@npm:3.0.0":
  locations:
    - "node_modules/tsconfig-paths/node_modules/strip-bom"
    - "node_modules/eslint-plugin-import/node_modules/strip-bom"

"strip-bom@npm:4.0.0":
  locations:
    - "node_modules/jest-runtime/node_modules/strip-bom"

"strip-eof@npm:1.0.0":
  locations:
    - "node_modules/strip-eof"

"strip-final-newline@npm:2.0.0":
  locations:
    - "node_modules/strip-final-newline"

"strip-json-comments@npm:2.0.1":
  locations:
    - "node_modules/rc/node_modules/strip-json-comments"

"strip-json-comments@npm:3.1.1":
  locations:
    - "node_modules/strip-json-comments"

"stripe@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:18.2.1":
  locations:
    - "node_modules/stripe"

"strtok3@npm:10.3.1":
  locations:
    - "node_modules/strtok3"

"styled-components@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:6.1.18":
  locations:
    - "node_modules/styled-components"

"styled-jsx@virtual:d10caaa3f525e599ac27d5359eb63cd651c1ebdfeb52a2e19dd2c1f3c283b95eefab8f546f18f735da377d93ab2b85f8f52af04ef92ae4334b209d5b06163e57#npm:5.1.6":
  locations:
    - "node_modules/styled-jsx"

"stylis@npm:4.2.0":
  locations:
    - "node_modules/stylis"

"stylis@npm:4.3.2":
  locations:
    - "node_modules/styled-components/node_modules/stylis"

"sucrase@npm:3.35.0":
  locations:
    - "node_modules/sucrase"

"suf-log@npm:2.5.3":
  locations:
    - "node_modules/suf-log"

"superagent@npm:10.2.1":
  locations:
    - "node_modules/superagent"

"supertest@npm:7.1.1":
  locations:
    - "node_modules/supertest"

"supports-color@npm:2.0.0":
  locations:
    - "node_modules/module/node_modules/supports-color"

"supports-color@npm:7.2.0":
  locations:
    - "node_modules/supports-color"

"supports-color@npm:8.1.1":
  locations:
    - "node_modules/terser-webpack-plugin/node_modules/supports-color"
    - "node_modules/sass-embedded/node_modules/supports-color"
    - "node_modules/jest-worker/node_modules/supports-color"

"supports-preserve-symlinks-flag@npm:1.0.0":
  locations:
    - "node_modules/supports-preserve-symlinks-flag"

"symbol-observable@npm:4.0.0":
  locations:
    - "node_modules/symbol-observable"

"symbol@npm:0.2.3":
  locations:
    - "node_modules/symbol"

"sync-child-process@npm:1.0.2":
  locations:
    - "node_modules/sync-child-process"

"sync-message-port@npm:1.1.3":
  locations:
    - "node_modules/sync-message-port"

"synckit@npm:0.11.8":
  locations:
    - "node_modules/synckit"

"tailwindcss@npm:3.4.17":
  locations:
    - "node_modules/tailwindcss"

"tapable@npm:2.2.2":
  locations:
    - "node_modules/tapable"

"tar@npm:6.2.1":
  locations:
    - "node_modules/@mapbox/node-pre-gyp/node_modules/tar"

"tar@npm:7.4.3":
  locations:
    - "node_modules/tar"

"terser-webpack-plugin@virtual:68dcd4e608d50045be250163d1f39c81f3aa9a940ca436ffcd629aa89b215ce4cb01deba0401ffabdcf0b9a55103432d58d1f37dd3588cf5fa4b3074829fc074#npm:5.3.14":
  locations:
    - "node_modules/terser-webpack-plugin"

"terser@npm:5.41.0":
  locations:
    - "node_modules/terser"

"test-exclude@npm:6.0.0":
  locations:
    - "node_modules/test-exclude"

"text-hex@npm:1.0.0":
  locations:
    - "node_modules/text-hex"

"text-segmentation@npm:1.0.3":
  locations:
    - "node_modules/text-segmentation"

"text-table@npm:0.2.0":
  locations:
    - "node_modules/text-table"

"thenify-all@npm:1.6.0":
  locations:
    - "node_modules/thenify-all"

"thenify@npm:3.3.1":
  locations:
    - "node_modules/thenify"

"through2-filter@npm:2.0.0":
  locations:
    - "node_modules/vinyl-fs/node_modules/through2-filter"

"through2-filter@npm:3.0.0":
  locations:
    - "node_modules/through2-filter"

"through2@npm:0.4.2":
  locations:
    - "node_modules/html-tokenize/node_modules/through2"

"through2@npm:0.6.5":
  locations:
    - "node_modules/glob-stream/node_modules/through2"

"through2@npm:2.0.5":
  locations:
    - "node_modules/through2"

"through@npm:2.3.8":
  locations:
    - "node_modules/through"

"tildify@npm:1.2.0":
  locations:
    - "node_modules/tildify"

"tiny-case@npm:1.0.3":
  locations:
    - "node_modules/tiny-case"

"tiny-inflate@npm:1.0.3":
  locations:
    - "node_modules/tiny-inflate"

"tiny-invariant@npm:1.3.3":
  locations:
    - "node_modules/tiny-invariant"

"tiny-warning@npm:1.0.3":
  locations:
    - "node_modules/tiny-warning"

"tinycolor2@npm:1.4.2":
  locations:
    - "node_modules/tinycolor2"

"tinyexec@npm:0.3.2":
  locations:
    - "node_modules/tinyexec"

"tinyglobby@npm:0.2.14":
  locations:
    - "node_modules/tinyglobby"

"tippy.js@npm:6.3.7":
  locations:
    - "node_modules/tippy.js"

"tiptap-extension-resize-image@virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:1.2.2":
  locations:
    - "node_modules/tiptap-extension-resize-image"

"tlds@npm:1.259.0":
  locations:
    - "node_modules/tlds"

"tmp@npm:0.0.33":
  locations:
    - "node_modules/tmp"

"tmpl@npm:1.0.5":
  locations:
    - "node_modules/tmpl"

"to-absolute-glob@npm:0.1.1":
  locations:
    - "node_modules/to-absolute-glob"

"to-regex-range@npm:5.0.1":
  locations:
    - "node_modules/to-regex-range"

"toidentifier@npm:1.0.1":
  locations:
    - "node_modules/toidentifier"

"token-stream@npm:1.0.0":
  locations:
    - "node_modules/token-stream"

"token-types@npm:6.0.0":
  locations:
    - "node_modules/token-types"

"topo@npm:3.0.3":
  locations:
    - "node_modules/topo"

"toposort@npm:2.0.2":
  locations:
    - "node_modules/toposort"

"tr46@npm:0.0.3":
  locations:
    - "node_modules/tr46"

"tree-kill@npm:1.2.2":
  locations:
    - "node_modules/tree-kill"

"trim-lines@npm:3.0.1":
  locations:
    - "node_modules/trim-lines"

"triple-beam@npm:1.4.1":
  locations:
    - "node_modules/triple-beam"

"trough@npm:2.2.0":
  locations:
    - "node_modules/trough"

"ts-api-utils@virtual:ae7dc19168e695f372c232c0679bd419529b68755b53c5424a347599db526eeb0bdf67cae7f7b7230dde90715aa96b6a521b0a967a46d1db316408b04ee3cc2b#npm:2.1.0":
  locations:
    - "node_modules/ts-api-utils"

"ts-interface-checker@npm:0.1.13":
  locations:
    - "node_modules/ts-interface-checker"

"ts-jest@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:29.3.4":
  locations:
    - "node_modules/ts-jest"

"ts-loader@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:9.5.2":
  locations:
    - "node_modules/ts-loader"

"ts-node@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:10.9.2":
  locations:
    - "node_modules/ts-node"

"tsconfck@virtual:f5dea88454b79084bdc2a6366b33908e4411963c25103ab86a651561afb3c9fba8ccb4b0f1aabc8be31aa47d42aa6c371afd88f99665c741faa6c744a2123906#npm:3.1.6":
  locations:
    - "node_modules/tsconfck"

"tsconfig-paths-webpack-plugin@npm:4.2.0":
  locations:
    - "node_modules/tsconfig-paths-webpack-plugin"

"tsconfig-paths@npm:3.15.0":
  locations:
    - "node_modules/eslint-plugin-import/node_modules/tsconfig-paths"

"tsconfig-paths@npm:4.2.0":
  locations:
    - "node_modules/tsconfig-paths"

"tslib@npm:2.6.2":
  locations:
    - "node_modules/styled-components/node_modules/tslib"

"tslib@npm:2.8.1":
  locations:
    - "node_modules/tslib"

"tsx@npm:4.19.4":
  locations:
    - "node_modules/tsx"

"type-check@npm:0.4.0":
  locations:
    - "node_modules/type-check"

"type-detect@npm:4.0.8":
  locations:
    - "node_modules/type-detect"

"type-fest@npm:0.20.2":
  locations:
    - "node_modules/eslint/node_modules/type-fest"
    - "node_modules/@eslint/eslintrc/node_modules/type-fest"

"type-fest@npm:0.21.3":
  locations:
    - "node_modules/ansi-escapes/node_modules/type-fest"

"type-fest@npm:2.19.0":
  locations:
    - "node_modules/yup/node_modules/type-fest"

"type-fest@npm:4.41.0":
  locations:
    - "node_modules/type-fest"

"type-is@npm:1.6.18":
  locations:
    - "node_modules/type-is"

"typed-array-buffer@npm:1.0.3":
  locations:
    - "node_modules/typed-array-buffer"

"typed-array-byte-length@npm:1.0.3":
  locations:
    - "node_modules/typed-array-byte-length"

"typed-array-byte-offset@npm:1.0.4":
  locations:
    - "node_modules/typed-array-byte-offset"

"typed-array-length@npm:1.0.7":
  locations:
    - "node_modules/typed-array-length"

"typedarray@npm:0.0.6":
  locations:
    - "node_modules/multer/node_modules/typedarray"

"typedarray@npm:0.0.7":
  locations:
    - "node_modules/typedarray"

"typeorm@virtual:6f64a54703f58570ddd243cd79da364e84b4f31cacedc539e7f751f6b68a278b6419a68f4f1675ce738bea506f19dd3069a7eb3f4fc1af5498c3c15c471d6087#npm:0.3.24":
  locations:
    - "node_modules/typeorm"

"typescript-eslint@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:8.33.1":
  locations:
    - "node_modules/typescript-eslint"

"typescript@patch:typescript@npm%3A5.7.2#optional!builtin<compat/typescript>::version=5.7.2&hash=29ae49":
  locations:
    - "node_modules/@nestjs/cli/node_modules/typescript"

"typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=29ae49":
  locations:
    - "node_modules/typescript"

"uc.micro@npm:2.1.0":
  locations:
    - "node_modules/uc.micro"

"ufo@npm:1.6.1":
  locations:
    - "node_modules/ufo"

"uglify-js@npm:3.19.3":
  locations:
    - "node_modules/uglify-js"

"uid@npm:2.0.2":
  locations:
    - "node_modules/uid"

"uint8array-extras@npm:1.4.0":
  locations:
    - "node_modules/uint8array-extras"

"ultrahtml@npm:1.6.0":
  locations:
    - "node_modules/ultrahtml"

"unbox-primitive@npm:1.1.0":
  locations:
    - "node_modules/unbox-primitive"

"uncrypto@npm:0.1.3":
  locations:
    - "node_modules/uncrypto"

"undici-types@npm:6.21.0":
  locations:
    - "node_modules/undici-types"

"unescape-js@npm:1.1.4":
  locations:
    - "node_modules/unescape-js"

"unescape@npm:1.0.1":
  locations:
    - "node_modules/unescape"

"unicode-properties@npm:1.4.1":
  locations:
    - "node_modules/unicode-properties"

"unicode-trie@npm:2.0.0":
  locations:
    - "node_modules/unicode-trie"

"unified@npm:11.0.5":
  locations:
    - "node_modules/unified"

"unifont@npm:0.5.0":
  locations:
    - "node_modules/unifont"

"unique-filename@npm:4.0.0":
  locations:
    - "node_modules/unique-filename"

"unique-slug@npm:5.0.0":
  locations:
    - "node_modules/unique-slug"

"unique-stream@npm:2.3.1":
  locations:
    - "node_modules/unique-stream"

"unist-util-find-after@npm:5.0.0":
  locations:
    - "node_modules/unist-util-find-after"

"unist-util-is@npm:6.0.0":
  locations:
    - "node_modules/unist-util-is"

"unist-util-modify-children@npm:4.0.0":
  locations:
    - "node_modules/unist-util-modify-children"

"unist-util-position@npm:5.0.0":
  locations:
    - "node_modules/unist-util-position"

"unist-util-remove-position@npm:5.0.0":
  locations:
    - "node_modules/unist-util-remove-position"

"unist-util-stringify-position@npm:4.0.0":
  locations:
    - "node_modules/unist-util-stringify-position"

"unist-util-visit-children@npm:3.0.0":
  locations:
    - "node_modules/unist-util-visit-children"

"unist-util-visit-parents@npm:6.0.1":
  locations:
    - "node_modules/unist-util-visit-parents"

"unist-util-visit@npm:5.0.0":
  locations:
    - "node_modules/unist-util-visit"

"universalify@npm:2.0.1":
  locations:
    - "node_modules/universalify"

"unorm@npm:1.6.0":
  locations:
    - "node_modules/unorm"

"unpipe@npm:1.0.0":
  locations:
    - "node_modules/unpipe"

"unplugin@npm:2.3.5":
  locations:
    - "node_modules/unplugin"

"unrs-resolver@npm:1.7.11":
  locations:
    - "node_modules/unrs-resolver"

"unstorage@virtual:f5dea88454b79084bdc2a6366b33908e4411963c25103ab86a651561afb3c9fba8ccb4b0f1aabc8be31aa47d42aa6c371afd88f99665c741faa6c744a2123906#npm:1.16.0":
  locations:
    - "node_modules/unstorage"

"update-browserslist-db@virtual:869551b10809a3e2ff53d630c33d6910e8604641d5ce93f662d5bcd7e2e4637ea42af7b06094f62db452de748bfc21441444c7b5d57adbed850dd1fa2aa08c28#npm:1.1.3":
  locations:
    - "node_modules/update-browserslist-db"

"upper-case@npm:1.1.3":
  locations:
    - "node_modules/upper-case"

"uri-js@npm:4.4.1":
  locations:
    - "node_modules/uri-js"

"urix@npm:0.1.0":
  locations:
    - "node_modules/urix"

"url@npm:0.10.3":
  locations:
    - "node_modules/url"

"use-isomorphic-layout-effect@virtual:8d76f9b5467f829e7433ba420571608b6031d7f2d14cd70616bf60067d9c6389758cdb929f4155f7c0421dbb17fbacf9afcfa956ad4b16f4f170d436b0cb7bc3#npm:1.2.1":
  locations:
    - "node_modules/use-isomorphic-layout-effect"

"use-memo-one@virtual:25b987bad67b27fa1da609fb29920d19161906534174c31f7a5763f03d8088ec0edf4c87d96fd1070a4fe21dd8ab99b8d5ad4059867c84f94b7124f5a0b6e7b5#npm:1.1.3":
  locations:
    - "node_modules/use-memo-one"

"use-sync-external-store@virtual:7839d029689bb9e2cfbc09332c1435aff547791daa4b53581f2eebf60279edbe56ca84b25d123db80b6839734f89c238e59ea23c6885b7ec5a14f5b2c55644ce#npm:1.5.0":
  locations:
    - "node_modules/use-sync-external-store"

"util-deprecate@npm:1.0.2":
  locations:
    - "node_modules/util-deprecate"

"util@npm:0.12.5":
  locations:
    - "node_modules/util"

"utils-merge@npm:1.0.1":
  locations:
    - "node_modules/utils-merge"

"utrie@npm:1.0.2":
  locations:
    - "node_modules/utrie"

"uuid@npm:11.0.3":
  locations:
    - "node_modules/@nestjs/schedule/node_modules/uuid"

"uuid@npm:11.1.0":
  locations:
    - "node_modules/uuid"

"uuid@npm:8.0.0":
  locations:
    - "node_modules/aws-sdk/node_modules/uuid"

"uuid@npm:9.0.1":
  locations:
    - "node_modules/preview-email/node_modules/uuid"
    - "node_modules/@nestjs/typeorm/node_modules/uuid"

"v8-compile-cache-lib@npm:3.0.1":
  locations:
    - "node_modules/v8-compile-cache-lib"

"v8-to-istanbul@npm:9.3.0":
  locations:
    - "node_modules/v8-to-istanbul"

"vali-date@npm:1.0.0":
  locations:
    - "node_modules/vali-date"

"valid-data-url@npm:3.0.1":
  locations:
    - "node_modules/valid-data-url"

"validate-npm-package-license@npm:3.0.4":
  locations:
    - "node_modules/validate-npm-package-license"

"validator@npm:13.15.15":
  locations:
    - "node_modules/validator"

"varint@npm:6.0.0":
  locations:
    - "node_modules/varint"

"vary@npm:1.1.2":
  locations:
    - "node_modules/vary"

"vfile-location@npm:5.0.3":
  locations:
    - "node_modules/vfile-location"

"vfile-message@npm:4.0.2":
  locations:
    - "node_modules/vfile-message"

"vfile@npm:6.0.3":
  locations:
    - "node_modules/vfile"

"vinyl-fs@npm:2.4.3":
  locations:
    - "node_modules/vinyl-fs"

"vinyl@npm:1.2.0":
  locations:
    - "node_modules/vinyl"

"vite@virtual:3d20061fc21149799468b13d716b58a27e247a53bf9dca0a27ce1356450e3bf701c63553670d4cb813ba12fb0b82fe8d46421b33290018857083e562a143e347#npm:5.4.19":
  locations:
    - "node_modules/vite"
  aliases:
    - "virtual:f70d5ac4c69bbceaf1a20e7f4d46e1be5f4bdc0c5f7163fb76aa386d4986238ed6a8b165bacfba026b04cadc57c1ab304bef5767965c149bdd306987c4a593a1#npm:5.4.19"

"vite@virtual:f5dea88454b79084bdc2a6366b33908e4411963c25103ab86a651561afb3c9fba8ccb4b0f1aabc8be31aa47d42aa6c371afd88f99665c741faa6c744a2123906#npm:6.3.5":
  locations:
    - "node_modules/astro/node_modules/vite"
    - "node_modules/@astrojs/react/node_modules/vite"

"vitefu@virtual:f5dea88454b79084bdc2a6366b33908e4411963c25103ab86a651561afb3c9fba8ccb4b0f1aabc8be31aa47d42aa6c371afd88f99665c741faa6c744a2123906#npm:1.0.6":
  locations:
    - "node_modules/astro/node_modules/vitefu"

"void-elements@npm:3.1.0":
  locations:
    - "node_modules/void-elements"

"w3c-keyname@npm:2.2.8":
  locations:
    - "node_modules/w3c-keyname"

"walker@npm:1.0.8":
  locations:
    - "node_modules/walker"

"watchpack@npm:2.4.4":
  locations:
    - "node_modules/watchpack"

"wcwidth@npm:1.0.1":
  locations:
    - "node_modules/wcwidth"

"web-namespaces@npm:2.0.1":
  locations:
    - "node_modules/web-namespaces"

"web-resource-inliner@npm:6.0.1":
  locations:
    - "node_modules/web-resource-inliner"

"webidl-conversions@npm:3.0.1":
  locations:
    - "node_modules/webidl-conversions"

"webpack-node-externals@npm:3.0.0":
  locations:
    - "node_modules/webpack-node-externals"

"webpack-sources@npm:3.3.2":
  locations:
    - "node_modules/webpack-sources"

"webpack-virtual-modules@npm:0.6.2":
  locations:
    - "node_modules/webpack-virtual-modules"

"webpack@virtual:27d6a4f677023da2b7228beb18e0dc4d1e1d463ea5073fc0f39e927d08377f976d9992ac83c3ceb8276fa5af4d91302d6d195ad7c22daff89a91b254c544b70e#npm:5.97.1":
  locations:
    - "node_modules/webpack"

"whatwg-url@npm:5.0.0":
  locations:
    - "node_modules/whatwg-url"

"which-boxed-primitive@npm:1.1.1":
  locations:
    - "node_modules/which-boxed-primitive"

"which-builtin-type@npm:1.2.1":
  locations:
    - "node_modules/which-builtin-type"

"which-collection@npm:1.0.2":
  locations:
    - "node_modules/which-collection"

"which-pm-runs@npm:1.1.0":
  locations:
    - "node_modules/which-pm-runs"

"which-typed-array@npm:1.1.19":
  locations:
    - "node_modules/which-typed-array"

"which@npm:1.3.1":
  locations:
    - "node_modules/execa/node_modules/which"

"which@npm:2.0.2":
  locations:
    - "node_modules/which"

"which@npm:5.0.0":
  locations:
    - "node_modules/node-gyp/node_modules/which"

"wide-align@npm:1.1.5":
  locations:
    - "node_modules/wide-align"

"widest-line@npm:5.0.0":
  locations:
    - "node_modules/widest-line"

"window-size@npm:0.2.0":
  locations:
    - "node_modules/window-size"

"winston-transport@npm:4.9.0":
  locations:
    - "node_modules/winston-transport"

"winston@npm:3.17.0":
  locations:
    - "node_modules/winston"

"with@npm:7.0.2":
  locations:
    - "node_modules/with"

"word-wrap@npm:1.2.5":
  locations:
    - "node_modules/word-wrap"

"wordwrap@npm:1.0.0":
  locations:
    - "node_modules/wordwrap"

"wrap-ansi@npm:2.1.0":
  locations:
    - "node_modules/cliui/node_modules/wrap-ansi"

"wrap-ansi@npm:6.2.0":
  locations:
    - "node_modules/inquirer/node_modules/wrap-ansi"
    - "node_modules/@nestjs/cli/node_modules/wrap-ansi"

"wrap-ansi@npm:7.0.0":
  locations:
    - "node_modules/yargs/node_modules/wrap-ansi"
    - "node_modules/wrap-ansi-cjs"

"wrap-ansi@npm:8.1.0":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/wrap-ansi"

"wrap-ansi@npm:9.0.0":
  locations:
    - "node_modules/wrap-ansi"

"wrappy@npm:1.0.2":
  locations:
    - "node_modules/wrappy"

"write-file-atomic@npm:4.0.2":
  locations:
    - "node_modules/write-file-atomic"

"ws@virtual:49731040f388d2036af44ec61b98b988af87a9e8e75bacacc84138888e23f552558a3793189cbd337c1f28274795eb66d4dd3d3baf7cf822ca06b3e9adb29603#npm:8.18.2":
  locations:
    - "node_modules/ws"

"xml2js@npm:0.6.2":
  locations:
    - "node_modules/xml2js"

"xml2json@npm:0.12.0":
  locations:
    - "node_modules/xml2json"

"xmlbuilder@npm:11.0.1":
  locations:
    - "node_modules/xmlbuilder"

"xtend@npm:2.1.2":
  locations:
    - "node_modules/html-tokenize/node_modules/xtend"

"xtend@npm:4.0.2":
  locations:
    - "node_modules/xtend"

"xxhash-wasm@npm:1.1.0":
  locations:
    - "node_modules/xxhash-wasm"

"y18n@npm:3.2.2":
  locations:
    - "node_modules/y18n"

"y18n@npm:5.0.8":
  locations:
    - "node_modules/yargs/node_modules/y18n"

"yallist@npm:3.1.1":
  locations:
    - "node_modules/@babel/helper-compilation-targets/node_modules/yallist"

"yallist@npm:4.0.0":
  locations:
    - "node_modules/yallist"

"yallist@npm:5.0.0":
  locations:
    - "node_modules/tar/node_modules/yallist"

"yaml@npm:1.10.2":
  locations:
    - "node_modules/cosmiconfig/node_modules/yaml"

"yaml@npm:2.8.0":
  locations:
    - "node_modules/yaml"

"yargs-parser@npm:2.4.1":
  locations:
    - "node_modules/module/node_modules/yargs-parser"

"yargs-parser@npm:21.1.1":
  locations:
    - "node_modules/yargs-parser"

"yargs@npm:17.7.2":
  locations:
    - "node_modules/yargs"

"yargs@npm:4.6.0":
  locations:
    - "node_modules/module/node_modules/yargs"

"yn@npm:3.1.1":
  locations:
    - "node_modules/yn"

"yocto-queue@npm:0.1.0":
  locations:
    - "node_modules/yocto-queue"

"yocto-queue@npm:1.2.1":
  locations:
    - "node_modules/astro/node_modules/yocto-queue"

"yocto-spinner@npm:0.2.3":
  locations:
    - "node_modules/yocto-spinner"

"yoctocolors@npm:2.1.1":
  locations:
    - "node_modules/yoctocolors"

"yup@npm:1.6.1":
  locations:
    - "node_modules/yup"

"zod-to-json-schema@virtual:f5dea88454b79084bdc2a6366b33908e4411963c25103ab86a651561afb3c9fba8ccb4b0f1aabc8be31aa47d42aa6c371afd88f99665c741faa6c744a2123906#npm:3.24.5":
  locations:
    - "node_modules/zod-to-json-schema"

"zod-to-ts@virtual:f5dea88454b79084bdc2a6366b33908e4411963c25103ab86a651561afb3c9fba8ccb4b0f1aabc8be31aa47d42aa6c371afd88f99665c741faa6c744a2123906#npm:1.2.0":
  locations:
    - "node_modules/zod-to-ts"

"zod@npm:3.25.56":
  locations:
    - "node_modules/zod"

"zwitch@npm:2.0.4":
  locations:
    - "node_modules/zwitch"
