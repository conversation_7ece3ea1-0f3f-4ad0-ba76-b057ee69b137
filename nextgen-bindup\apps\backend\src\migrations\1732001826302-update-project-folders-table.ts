import { MigrationInterface, QueryRunner, TableFore<PERSON><PERSON><PERSON> } from 'typeorm';

export class UpdateProjectFoldersTable1732001826302
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}project_folders`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createForeignKey(
      this.TABLE_NAME,
      new TableForeignKey({
        columnNames: ['projectId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'projects',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey(
      this.TABLE_NAME,
      'FK_project_folders_projectId',
    );
  }
}
