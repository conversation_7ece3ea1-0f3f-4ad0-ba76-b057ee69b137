"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FontsetController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../auth/auth.guard");
const fontset_service_1 = require("./fontset.service");
const fontset_entity_1 = require("./entities/fontset.entity");
let FontsetController = class FontsetController {
    constructor(fontsetService) {
        this.fontsetService = fontsetService;
    }
    async create(assetEntity) {
        return await this.fontsetService.create(assetEntity);
    }
    async update(fontsetId, data) {
        return await this.fontsetService.update(+fontsetId, data);
    }
    async getById(fontsetId) {
        return await this.fontsetService.findById(+fontsetId);
    }
    async getByProjectId(projectId) {
        return await this.fontsetService.findByProjectId(+projectId);
    }
    async getBySiteId(projectId, siteId) {
        return await this.fontsetService.findBySiteId(+projectId, +siteId);
    }
    async delete(fontsetId) {
        return await this.fontsetService.delete(+fontsetId);
    }
};
exports.FontsetController = FontsetController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [fontset_entity_1.FontsetEntity]),
    __metadata("design:returntype", Promise)
], FontsetController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('update/:fontsetId'),
    __param(0, (0, common_1.Param)('fontsetId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], FontsetController.prototype, "update", null);
__decorate([
    (0, common_1.Get)('one/:fontsetId'),
    __param(0, (0, common_1.Param)('fontsetId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FontsetController.prototype, "getById", null);
__decorate([
    (0, common_1.Get)('project/:projectId'),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FontsetController.prototype, "getByProjectId", null);
__decorate([
    (0, common_1.Get)('site/:projectId/:siteId'),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], FontsetController.prototype, "getBySiteId", null);
__decorate([
    (0, common_1.Delete)(':fontsetId'),
    __param(0, (0, common_1.Param)('fontsetId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FontsetController.prototype, "delete", null);
exports.FontsetController = FontsetController = __decorate([
    (0, common_1.Controller)('fontset'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [fontset_service_1.FontsetService])
], FontsetController);
//# sourceMappingURL=fontset.controller.js.map