import { forwardRef, Module } from '@nestjs/common';
import { VersionHistoryController } from './version-history.controller';
import { VersionHistoryService } from './version-history.service';
import { PageModule } from 'src/page/page.module';
import { SiteModule } from 'src/site/site.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SiteEntity } from 'src/site/entities/site.entity';
import { PageEntity } from 'src/page/entities/page.entity';
import { SiteVersionEntity } from './entities/site-version.entity';
import { PageVersionEntity } from './entities/page-version.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([SiteEntity]),
    TypeOrmModule.forFeature([PageEntity]),
    TypeOrmModule.forFeature([SiteVersionEntity]),
    TypeOrmModule.forFeature([PageVersionEntity]),
    forwardRef(() => PageModule),
    SiteModule,
  ],
  controllers: [VersionHistoryController],
  providers: [VersionHistoryService],
  exports: [VersionHistoryService],
})
export class VersionHistoryModule {}
