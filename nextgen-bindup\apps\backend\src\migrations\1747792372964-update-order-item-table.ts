import { MigrationInterface, QueryRunner, TableForeignKey } from 'typeorm';

export class UpdateOrderItemTable1747792372964 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}order_items`;
  TABLE_NAME_PRODUCT: string = `${process.env.ENTITY_PREFIX || ''}products`;
  FOREIGN_KEY_NAME = 'FK_PRODUCT_ID_ON_ORDER_ITEMS';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createForeignKey(
      this.TABLE_NAME,
      new TableForeignKey({
        columnNames: ['productId'],
        referencedColumnNames: ['id'],
        referencedTableName: this.TABLE_NAME_PRODUCT,
        onDelete: 'SET NULL',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // remove foreign key
    await queryRunner.dropForeignKey(this.TABLE_NAME, this.FOREIGN_KEY_NAME);
  }
}
