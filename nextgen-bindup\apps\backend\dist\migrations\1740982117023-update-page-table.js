"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePageTable1740982117023 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePageTable1740982117023 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}pages`;
    }
    async up(queryRunner) {
        const column = new typeorm_1.TableColumn({
            name: 'isDeleted',
            type: 'boolean',
            default: false,
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, column);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'isDeleted');
    }
}
exports.UpdatePageTable1740982117023 = UpdatePageTable1740982117023;
//# sourceMappingURL=1740982117023-update-page-table.js.map