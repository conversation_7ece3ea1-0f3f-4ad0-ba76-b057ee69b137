import { SiteService } from './site.service';
import { SiteEntity } from './entities/site.entity';
export declare class SiteController {
    private readonly siteService;
    constructor(siteService: SiteService);
    create(siteEntity: SiteEntity): Promise<SiteEntity>;
    update(siteId: string, siteData: Partial<SiteEntity>): Promise<SiteEntity>;
    getUrl(siteId: string): Promise<string>;
    getById(siteId: string): Promise<SiteEntity>;
    getByProjectId(projectId: string): Promise<SiteEntity[]>;
    getByProjectFolderId(projectId: string, projectFolderId: string): Promise<SiteEntity[]>;
    getByIds(data: {
        ids: number[];
    }): Promise<SiteEntity[]>;
    delete(siteId: string): Promise<boolean>;
    clone(siteId: string): Promise<SiteEntity>;
}
