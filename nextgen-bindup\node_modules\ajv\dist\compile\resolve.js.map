{"version": 3, "file": "resolve.js", "sourceRoot": "", "sources": ["../../lib/compile/resolve.ts"], "names": [], "mappings": ";;;AAGA,iCAA+B;AAC/B,yCAAwC;AACxC,iDAAgD;AAKhD,2CAA2C;AAC3C,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC;IAC7B,MAAM;IACN,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,eAAe;IACf,eAAe;IACf,UAAU;IACV,UAAU;IACV,SAAS;IACT,SAAS;IACT,aAAa;IACb,YAAY;IACZ,UAAU;IACV,MAAM;IACN,OAAO;CACR,CAAC,CAAA;AAEF,SAAgB,SAAS,CAAC,MAAiB,EAAE,QAA0B,IAAI;IACzE,IAAI,OAAO,MAAM,IAAI,SAAS;QAAE,OAAO,IAAI,CAAA;IAC3C,IAAI,KAAK,KAAK,IAAI;QAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IAC1C,IAAI,CAAC,KAAK;QAAE,OAAO,KAAK,CAAA;IACxB,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK,CAAA;AACnC,CAAC;AALD,8BAKC;AAED,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC;IAC3B,MAAM;IACN,eAAe;IACf,kBAAkB;IAClB,aAAa;IACb,gBAAgB;CACjB,CAAC,CAAA;AAEF,SAAS,MAAM,CAAC,MAAuB;IACrC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QACzB,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;YAAE,OAAO,IAAI,CAAA;QACtC,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;QACvB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;YAAE,OAAO,IAAI,CAAA;QACvD,IAAI,OAAO,GAAG,IAAI,QAAQ,IAAI,MAAM,CAAC,GAAG,CAAC;YAAE,OAAO,IAAI,CAAA;IACxD,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAS,SAAS,CAAC,MAAuB;IACxC,IAAI,KAAK,GAAG,CAAC,CAAA;IACb,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QACzB,IAAI,GAAG,KAAK,MAAM;YAAE,OAAO,QAAQ,CAAA;QACnC,KAAK,EAAE,CAAA;QACP,IAAI,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC;YAAE,SAAQ;QACrC,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,QAAQ,EAAE,CAAC;YACnC,IAAA,eAAQ,EAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;QAC3D,CAAC;QACD,IAAI,KAAK,KAAK,QAAQ;YAAE,OAAO,QAAQ,CAAA;IACzC,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAgB,WAAW,CAAC,QAAqB,EAAE,EAAE,GAAG,EAAE,EAAE,SAAmB;IAC7E,IAAI,SAAS,KAAK,KAAK;QAAE,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC,CAAA;IAC7C,MAAM,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;IAC5B,OAAO,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;AAClC,CAAC;AAJD,kCAIC;AAED,SAAgB,YAAY,CAAC,QAAqB,EAAE,CAAe;IACjE,MAAM,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;IACxC,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;AACvC,CAAC;AAHD,oCAGC;AAED,MAAM,mBAAmB,GAAG,OAAO,CAAA;AACnC,SAAgB,WAAW,CAAC,EAAsB;IAChD,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;AACtD,CAAC;AAFD,kCAEC;AAED,SAAgB,UAAU,CAAC,QAAqB,EAAE,MAAc,EAAE,EAAU;IAC1E,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC,CAAA;IACpB,OAAO,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;AACrC,CAAC;AAHD,gCAGC;AAED,MAAM,MAAM,GAAG,uBAAuB,CAAA;AAEtC,SAAgB,aAAa,CAAY,MAAiB,EAAE,MAAc;IACxE,IAAI,OAAO,MAAM,IAAI,SAAS;QAAE,OAAO,EAAE,CAAA;IACzC,MAAM,EAAC,QAAQ,EAAE,WAAW,EAAC,GAAG,IAAI,CAAC,IAAI,CAAA;IACzC,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,CAAA;IACrD,MAAM,OAAO,GAAmC,EAAC,EAAE,EAAE,KAAK,EAAC,CAAA;IAC3D,MAAM,UAAU,GAAG,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;IACzD,MAAM,SAAS,GAAc,EAAE,CAAA;IAC/B,MAAM,UAAU,GAAgB,IAAI,GAAG,EAAE,CAAA;IAEzC,QAAQ,CAAC,MAAM,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,aAAa,EAAE,EAAE;QACnE,IAAI,aAAa,KAAK,SAAS;YAAE,OAAM;QACvC,MAAM,QAAQ,GAAG,UAAU,GAAG,OAAO,CAAA;QACrC,IAAI,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,CAAA;QACxC,IAAI,OAAO,GAAG,CAAC,QAAQ,CAAC,IAAI,QAAQ;YAAE,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAA;QACpF,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAA;QACjC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,CAAA;QACxC,OAAO,CAAC,OAAO,CAAC,GAAG,WAAW,CAAA;QAE9B,SAAS,MAAM,CAAY,GAAW;YACpC,6DAA6D;YAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAA;YAC9C,GAAG,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;YACjE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC;gBAAE,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC5C,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YACnB,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC7B,IAAI,OAAO,QAAQ,IAAI,QAAQ;gBAAE,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC/D,IAAI,OAAO,QAAQ,IAAI,QAAQ,EAAE,CAAC;gBAChC,gBAAgB,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;YAC7C,CAAC;iBAAM,IAAI,GAAG,KAAK,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oBACnB,gBAAgB,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAA;oBAC1C,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;gBACtB,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAA;gBAC3B,CAAC;YACH,CAAC;YACD,OAAO,GAAG,CAAA;QACZ,CAAC;QAED,SAAS,SAAS,CAAY,MAAe;YAC3C,IAAI,OAAO,MAAM,IAAI,QAAQ,EAAE,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBAAE,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,GAAG,CAAC,CAAA;gBACvE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM,EAAE,CAAC,CAAA;YACjC,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,OAAO,SAAS,CAAA;IAEhB,SAAS,gBAAgB,CAAC,IAAe,EAAE,IAA2B,EAAE,GAAW;QACjF,IAAI,IAAI,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;YAAE,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA;IACnE,CAAC;IAED,SAAS,QAAQ,CAAC,GAAW;QAC3B,OAAO,IAAI,KAAK,CAAC,cAAc,GAAG,oCAAoC,CAAC,CAAA;IACzE,CAAC;AACH,CAAC;AAxDD,sCAwDC"}