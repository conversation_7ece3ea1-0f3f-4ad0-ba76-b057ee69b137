import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UserInfoEntity } from './entities/user-info.entity';
import { Repository } from 'typeorm';

@Injectable()
export class UserInfoService {
  @InjectRepository(UserInfoEntity)
  readonly userInfoRepo: Repository<UserInfoEntity>;

  constructor() {}

  async updateRecently(userId: string, siteId: number): Promise<boolean> {
    const user = await this.userInfoRepo.findOneBy({ userId });
    if (!user) throw new Error('api.error.user_not_found');

    if (!user.recentlySite) user.recentlySite = [];
    const index = user.recentlySite.indexOf(siteId);
    if (index >= 0) {
      user.recentlySite.splice(index, 1);
    }

    user.recentlySite.unshift(siteId);
    while (user.recentlySite.length > 10) user.recentlySite.pop();

    await this.userInfoRepo.update(
      { userId },
      { recentlySite: user.recentlySite },
    );

    return true;
  }

  async findByUserId(userId: string): Promise<UserInfoEntity | null> {
    return await this.userInfoRepo.findOneBy({ userId });
  }

  async update(
    userId: string,
    data: Partial<UserInfoEntity>,
  ): Promise<UserInfoEntity> {
    const userInfo = await this.userInfoRepo.findOneBy({ userId });
    if (!userInfo) throw new Error('api.error.user_not_found');

    await this.userInfoRepo.update({ userId }, data);
    return { ...userInfo, ...data };
  }
}
