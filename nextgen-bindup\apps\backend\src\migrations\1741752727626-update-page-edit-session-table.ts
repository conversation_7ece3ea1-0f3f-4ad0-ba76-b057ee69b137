import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableUnique,
} from 'typeorm';

export class UpdatePageEditSessionTable1741752727626
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}page_edit_sessions`;
  CONSTRAINT_NAME = 'unique_user_site_session';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}
        DROP CONSTRAINT IF EXISTS ${this.CONSTRAINT_NAME};
      `);

    await queryRunner.query(
      `DELETE FROM ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
    await queryRunner.changeColumn(
      this.TABLE_NAME,
      'userId',
      new TableColumn({
        name: 'userId',
        type: 'varchar',
        length: '36',
        isNullable: false,
      }),
    );

    await queryRunner.createUniqueConstraint(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
      new TableUnique({
        name: this.CONSTRAINT_NAME,
        columnNames: ['userId', 'pageId', 'siteId', 'sessionId'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}
        DROP CONSTRAINT IF EXISTS ${this.CONSTRAINT_NAME};
      `);

    await queryRunner.changeColumn(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
      'userId',
      new TableColumn({
        name: 'userId',
        type: 'integer',
        isNullable: false,
      }),
    );

    await queryRunner.createUniqueConstraint(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
      new TableUnique({
        name: this.CONSTRAINT_NAME,
        columnNames: ['userId', 'pageId', 'siteId', 'sessionId'],
      }),
    );
  }
}
