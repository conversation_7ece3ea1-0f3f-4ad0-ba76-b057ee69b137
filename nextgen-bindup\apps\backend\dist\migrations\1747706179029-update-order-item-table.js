"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderItemTable1747706179029 = void 0;
const typeorm_1 = require("typeorm");
class UpdateOrderItemTable1747706179029 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}order_items`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'attributes',
            type: 'jsonb',
            isNullable: true,
        }));
    }
    async down(queryRunner) { }
}
exports.UpdateOrderItemTable1747706179029 = UpdateOrderItemTable1747706179029;
//# sourceMappingURL=1747706179029-update-order-item-table.js.map