"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobType = exports.Industry = exports.Occupation = exports.PREFECTURE_LIST = exports.CountryCode = exports.Sex = exports.UserType = void 0;
var UserType;
(function (UserType) {
    UserType[UserType["INDIVIDUAL"] = 1] = "INDIVIDUAL";
    UserType[UserType["CORPORATION"] = 2] = "CORPORATION";
})(UserType || (exports.UserType = UserType = {}));
var Sex;
(function (Sex) {
    Sex[Sex["MALE"] = 1] = "MALE";
    Sex[Sex["FEMALE"] = 2] = "FEMALE";
    Sex[Sex["OTHER"] = 3] = "OTHER";
})(Sex || (exports.Sex = Sex = {}));
var CountryCode;
(function (CountryCode) {
    CountryCode[CountryCode["JAPAN"] = 1] = "JAPAN";
})(CountryCode || (exports.CountryCode = CountryCode = {}));
exports.PREFECTURE_LIST = {
    0: [],
    1: [
        { code: 'HOKKAIDO', value: 1 },
        { code: 'AOMORI', value: 2 },
        { code: 'IWATE', value: 3 },
        { code: 'MIYAGI', value: 4 },
        { code: 'AKITA', value: 5 },
        { code: 'YAMAGATA', value: 6 },
        { code: 'FUKUSHIMA', value: 7 },
        { code: 'IBARAKI', value: 8 },
        { code: 'TOCHIGI', value: 9 },
        { code: 'GUNMA', value: 10 },
        { code: 'SAITAMA', value: 11 },
        { code: 'CHIBA', value: 12 },
        { code: 'TOKYO', value: 13 },
        { code: 'KANAGAWA', value: 14 },
        { code: 'YAMANASHI', value: 15 },
        { code: 'NAGANO', value: 17 },
        { code: 'NIIGATA', value: 18 },
        { code: 'TOYAMA', value: 19 },
        { code: 'ISHIKAWA', value: 20 },
        { code: 'FUKUI', value: 21 },
        { code: 'SHIZUOKA', value: 22 },
        { code: 'AICHI', value: 23 },
        { code: 'GIFU', value: 24 },
        { code: 'MIE', value: 25 },
        { code: 'SHIGA', value: 26 },
        { code: 'KYOTO', value: 27 },
        { code: 'OSAKA', value: 28 },
        { code: 'HYOGO', value: 29 },
        { code: 'NARA', value: 30 },
        { code: 'WAKAYAMA', value: 31 },
        { code: 'SHIMANE', value: 32 },
        { code: 'TOTTORI', value: 33 },
        { code: 'OKAYAMA', value: 34 },
        { code: 'HIROSHIMA', value: 35 },
        { code: 'YAMAGUCHI', value: 36 },
        { code: 'KAGAWA', value: 37 },
        { code: 'TOKUSHIMA', value: 38 },
        { code: 'KOCHI', value: 39 },
        { code: 'EHIME', value: 40 },
        { code: 'FUKUOKA', value: 41 },
        { code: 'SAGA', value: 42 },
        { code: 'NAGASAKI', value: 43 },
        { code: 'OITA', value: 44 },
        { code: 'KUMAMOTO', value: 45 },
        { code: 'MIYAZAKI', value: 46 },
        { code: 'KAGOSHIMA', value: 47 },
        { code: 'OKINAWA', value: 48 },
    ],
};
var Occupation;
(function (Occupation) {
    Occupation[Occupation["NONE"] = 0] = "NONE";
    Occupation[Occupation["COMPANY_EMPLOYEE"] = 1] = "COMPANY_EMPLOYEE";
    Occupation[Occupation["CIVIL_SERVANT"] = 2] = "CIVIL_SERVANT";
    Occupation[Occupation["PROFESSOR_LECTURER"] = 3] = "PROFESSOR_LECTURER";
    Occupation[Occupation["STUDENT"] = 4] = "STUDENT";
    Occupation[Occupation["FULLTIME_HOUSEWIFE"] = 5] = "FULLTIME_HOUSEWIFE";
    Occupation[Occupation["SELF_EMPLOYED"] = 9] = "SELF_EMPLOYED";
    Occupation[Occupation["UNEMPLOYED"] = 10] = "UNEMPLOYED";
    Occupation[Occupation["OTHER"] = 11] = "OTHER";
    Occupation[Occupation["DESIGNER"] = 12] = "DESIGNER";
})(Occupation || (exports.Occupation = Occupation = {}));
var Industry;
(function (Industry) {
    Industry[Industry["NONE"] = 0] = "NONE";
    Industry[Industry["DESIGN_INDUSTRY"] = 1] = "DESIGN_INDUSTRY";
    Industry[Industry["CREATIVE_INDUSTRY"] = 2] = "CREATIVE_INDUSTRY";
    Industry[Industry["MANUFACTURING"] = 3] = "MANUFACTURING";
    Industry[Industry["RETAIL"] = 4] = "RETAIL";
    Industry[Industry["CONSTRUCTION"] = 5] = "CONSTRUCTION";
    Industry[Industry["INFORMATION_AND_COMMUNICATIONS"] = 6] = "INFORMATION_AND_COMMUNICATIONS";
    Industry[Industry["FOOD_AND_BEVERAGE_INDUSTRY"] = 7] = "FOOD_AND_BEVERAGE_INDUSTRY";
    Industry[Industry["LEISURE_AND_TOURISM"] = 8] = "LEISURE_AND_TOURISM";
    Industry[Industry["FINANCE_AND_REAL_ESTATE"] = 9] = "FINANCE_AND_REAL_ESTATE";
    Industry[Industry["PROFESSIONAL"] = 10] = "PROFESSIONAL";
    Industry[Industry["EDUCATION"] = 11] = "EDUCATION";
    Industry[Industry["MEDICAL_AND_WELFARE"] = 12] = "MEDICAL_AND_WELFARE";
    Industry[Industry["BEAUTY"] = 13] = "BEAUTY";
    Industry[Industry["AGRICULTURE_FORESTRY_AND_FISHING"] = 14] = "AGRICULTURE_FORESTRY_AND_FISHING";
    Industry[Industry["CIVIL_SERVANT"] = 15] = "CIVIL_SERVANT";
    Industry[Industry["OTHER"] = 16] = "OTHER";
})(Industry || (exports.Industry = Industry = {}));
var JobType;
(function (JobType) {
    JobType[JobType["NONE"] = 0] = "NONE";
    JobType[JobType["EXECUTIVE"] = 1] = "EXECUTIVE";
    JobType[JobType["COMPANY_OFFICER"] = 2] = "COMPANY_OFFICER";
    JobType[JobType["SELF_EMPLOYED"] = 3] = "SELF_EMPLOYED";
    JobType[JobType["WEB_MANAGER_APPROVAL_PERSON"] = 4] = "WEB_MANAGER_APPROVAL_PERSON";
    JobType[JobType["WEB_MANAGER_NONDECISION_MAKER"] = 5] = "WEB_MANAGER_NONDECISION_MAKER";
    JobType[JobType["SALES"] = 6] = "SALES";
    JobType[JobType["PUBLIC_RELATIONS_AND_PROMOTION"] = 7] = "PUBLIC_RELATIONS_AND_PROMOTION";
    JobType[JobType["ADMINISTRATION"] = 8] = "ADMINISTRATION";
    JobType[JobType["MARKETING"] = 9] = "MARKETING";
    JobType[JobType["TEACHING_PROFESSION"] = 10] = "TEACHING_PROFESSION";
    JobType[JobType["PROFESSIONAL_WEB_DESIGNER"] = 11] = "PROFESSIONAL_WEB_DESIGNER";
    JobType[JobType["PROFESSIONAL_DESIGNER"] = 12] = "PROFESSIONAL_DESIGNER";
    JobType[JobType["PROFESSIONAL_GRAPHIC_DESIGNER"] = 13] = "PROFESSIONAL_GRAPHIC_DESIGNER";
    JobType[JobType["PROFESSIONAL_ILLUSTRATOR"] = 14] = "PROFESSIONAL_ILLUSTRATOR";
    JobType[JobType["PROFESSIONAL_PHOTOGRAPHER"] = 15] = "PROFESSIONAL_PHOTOGRAPHER";
    JobType[JobType["PROFESSIONAL_OCCUPATION_MUSICIAN"] = 16] = "PROFESSIONAL_OCCUPATION_MUSICIAN";
    JobType[JobType["PROFESSIONAL_ENGINEER"] = 17] = "PROFESSIONAL_ENGINEER";
    JobType[JobType["PROFESSIONAL_OTHER"] = 18] = "PROFESSIONAL_OTHER";
    JobType[JobType["HOUSEWIFE"] = 19] = "HOUSEWIFE";
    JobType[JobType["STUDENT"] = 20] = "STUDENT";
    JobType[JobType["PARTTIME_WORKER"] = 21] = "PARTTIME_WORKER";
    JobType[JobType["UNEMPLOYED"] = 22] = "UNEMPLOYED";
    JobType[JobType["OTHER"] = 23] = "OTHER";
})(JobType || (exports.JobType = JobType = {}));
//# sourceMappingURL=user-info.enum.js.map