"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserInfoTable1738652412290 = void 0;
const typeorm_1 = require("typeorm");
class CreateUserInfoTable1738652412290 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}user_info`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'userId',
                    type: 'varchar',
                    length: '36',
                    isPrimary: true,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'type',
                    type: 'smallint',
                    isNullable: false,
                    default: '1',
                },
                {
                    name: 'avatar',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'company',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'lastNameHira',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'firstNameHira',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'sex',
                    type: 'smallint',
                    isNullable: true,
                },
                {
                    name: 'countryCode',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'postalCode',
                    type: 'varchar',
                    length: '10',
                    isNullable: true,
                },
                {
                    name: 'prefecture',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'municipalities',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'street',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'building',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'phone',
                    type: 'varchar',
                    length: '50',
                    isNullable: true,
                },
                {
                    name: 'fax',
                    type: 'varchar',
                    length: '50',
                    isNullable: true,
                },
                {
                    name: 'occupation',
                    type: 'smallint',
                    isNullable: true,
                },
                {
                    name: 'industry',
                    type: 'smallint',
                    isNullable: true,
                },
                {
                    name: 'jobType',
                    type: 'smallint',
                    isNullable: true,
                },
                {
                    name: 'birthday',
                    type: 'date',
                    isNullable: true,
                },
                {
                    name: 'receiveNewsletter',
                    type: 'boolean',
                    isNullable: true,
                },
                {
                    name: 'receiveSupport',
                    type: 'boolean',
                    isNullable: true,
                },
                {
                    name: 'receiveDirectMail',
                    type: 'boolean',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
        await queryRunner.query(`alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
    async down(queryRunner) {
        await queryRunner.query(`alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateUserInfoTable1738652412290 = CreateUserInfoTable1738652412290;
//# sourceMappingURL=1738652412290-create-user-info-table.js.map