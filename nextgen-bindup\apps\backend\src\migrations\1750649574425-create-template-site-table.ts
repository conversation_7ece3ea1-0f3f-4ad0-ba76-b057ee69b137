import { SiteStatus } from 'src/site/types/site.type';
import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateTemplateSitesTable1750649574425
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}template_sites`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isGenerated: true,
            generationStrategy: 'increment',
            isPrimary: true,
          },
          {
            name: 'templateId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'managementName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'smallint',
            isNullable: false,
            default: `'${SiteStatus.DRAFT}'`,
          },
          {
            name: 'url',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isSearch',
            type: 'boolean',
            isNullable: true,
          },
          {
            name: 'thumb',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'headCode',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'bodyCode',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isArchived',
            type: 'boolean',
            isNullable: true,
          },
          {
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
