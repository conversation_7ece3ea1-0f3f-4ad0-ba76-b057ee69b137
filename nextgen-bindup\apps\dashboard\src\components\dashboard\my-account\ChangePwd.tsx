import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionActions,
  AccordionDetails,
  AccordionSummary,
  Button,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { getErrMsg } from '@nextgen-bindup/common/utility';
import { userInfoService } from '../../../services/user-info.service';

export const ChangePwd: FC<{
  onUpdate: () => void;
}> = ({ onUpdate }) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errorMsg, setErrorMsg] = useState<string>('');
  const [errorField, setErrorField] = useState<
    '' | 'currentPwd' | 'newPwd' | 'retypePwd'
  >('');
  const [currentPwd, setCurrentPwd] = useState<string>('');
  const [newPwd, setNewPwd] = useState<string>('');
  const [retypePwd, setRetypePwd] = useState<string>('');

  const onSave = async () => {
    setIsLoading(true);
    setErrorMsg('');
    setErrorField('');

    try {
      if (!validate()) return;

      await userInfoService.changePassword({
        currentPwd: currentPwd,
        newPassword: newPwd,
      });

      onUpdate();
    } catch (e) {
      setErrorMsg(t(getErrMsg(e)));
    } finally {
      setIsLoading(false);
    }
  };

  const validate = () => {
    if (!currentPwd) {
      setErrorField('currentPwd');
      setErrorMsg('myaccount.change_pwd.error.current_pwd_required');
      return false;
    }

    if (!newPwd) {
      setErrorField('newPwd');
      setErrorMsg('myaccount.change_pwd.error.new_pwd_required');
      return false;
    }

    if (newPwd.length < 6 || newPwd.length > 16) {
      setErrorField('newPwd');
      setErrorMsg('myaccount.change_pwd.error.new_pwd_length');
      return false;
    }

    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d_-]{6,16}$/;
    if (!regex.test(newPwd)) {
      setErrorField('newPwd');
      setErrorMsg('myaccount.change_pwd.error.new_pwd_invalid');
      return false;
    }

    if (!retypePwd) {
      setErrorField('retypePwd');
      setErrorMsg('myaccount.change_pwd.error.retype_pwd_required');
      return false;
    }

    if (newPwd !== retypePwd) {
      setErrorField('retypePwd');
      setErrorMsg('myaccount.change_pwd.error.retype_pwd_not_match');
      return false;
    }

    return true;
  };

  return (
    <Accordion defaultExpanded sx={{ width: '100%' }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="h5" color="info">
          {t('myaccount.change_pwd.title')}
        </Typography>
      </AccordionSummary>

      <AccordionDetails>
        <Stack spacing={3}>
          {/* Current Password */}
          <TextField
            label={t('myaccount.change_pwd.current_pwd')}
            variant="outlined"
            size="small"
            required
            type="password"
            value={currentPwd}
            error={errorField === 'currentPwd'}
            onChange={e => {
              setCurrentPwd(e.target.value);
            }}
          />

          {/* New Password */}
          <TextField
            label={t('myaccount.change_pwd.new_pwd')}
            helperText={t('myaccount.change_pwd.new_pwd_note')}
            variant="outlined"
            size="small"
            required
            type="password"
            value={newPwd}
            error={errorField === 'newPwd'}
            onChange={e => {
              setNewPwd(e.target.value);
            }}
          />

          {/* Retype new password */}
          <TextField
            label={t('myaccount.change_pwd.retype_pwd')}
            variant="outlined"
            size="small"
            required
            type="password"
            value={retypePwd}
            error={errorField === 'retypePwd'}
            onChange={e => {
              setRetypePwd(e.target.value);
            }}
          />
        </Stack>
      </AccordionDetails>

      <AccordionActions sx={{ marginBottom: '0.5rem' }}>
        <Typography variant="body1" color="error">
          {t(errorMsg)}
        </Typography>

        <Button
          size="large"
          color="info"
          variant="contained"
          onClick={onSave}
          loading={isLoading}
          loadingPosition="start"
        >
          {t('common.btn_save')}
        </Button>
      </AccordionActions>
    </Accordion>
  );
};
