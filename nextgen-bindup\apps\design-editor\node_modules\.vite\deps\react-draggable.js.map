{"version": 3, "sources": ["../../../../../node_modules/clsx/dist/clsx.js", "../../../../../node_modules/react-draggable/build/cjs/utils/shims.js", "../../../../../node_modules/react-draggable/build/cjs/utils/getPrefix.js", "../../../../../node_modules/react-draggable/build/cjs/utils/domFns.js", "../../../../../node_modules/react-draggable/build/cjs/utils/positionFns.js", "../../../../../node_modules/react-draggable/build/cjs/utils/log.js", "../../../../../node_modules/react-draggable/build/cjs/DraggableCore.js", "../../../../../node_modules/react-draggable/build/cjs/Draggable.js", "../../../../../node_modules/react-draggable/build/cjs/cjs.js"], "sourcesContent": ["function r(e){var o,t,f=\"\";if(\"string\"==typeof e||\"number\"==typeof e)f+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var n=e.length;for(o=0;o<n;o++)e[o]&&(t=r(e[o]))&&(f&&(f+=\" \"),f+=t)}else for(t in e)e[t]&&(f&&(f+=\" \"),f+=t);return f}function e(){for(var e,o,t=0,f=\"\",n=arguments.length;t<n;t++)(e=arguments[t])&&(o=r(e))&&(f&&(f+=\" \"),f+=o);return f}module.exports=e,module.exports.clsx=e;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.dontSetMe = dontSetMe;\nexports.findInArray = findInArray;\nexports.int = int;\nexports.isFunction = isFunction;\nexports.isNum = isNum;\n// @credits https://gist.github.com/rogozhnikoff/a43cfed27c41e4e68cdc\nfunction findInArray(array /*: Array<any> | TouchList*/, callback /*: Function*/) /*: any*/{\n  for (let i = 0, length = array.length; i < length; i++) {\n    if (callback.apply(callback, [array[i], i, array])) return array[i];\n  }\n}\nfunction isFunction(func /*: any*/) /*: boolean %checks*/{\n  // $FlowIgnore[method-unbinding]\n  return typeof func === 'function' || Object.prototype.toString.call(func) === '[object Function]';\n}\nfunction isNum(num /*: any*/) /*: boolean %checks*/{\n  return typeof num === 'number' && !isNaN(num);\n}\nfunction int(a /*: string*/) /*: number*/{\n  return parseInt(a, 10);\n}\nfunction dontSetMe(props /*: Object*/, propName /*: string*/, componentName /*: string*/) /*: ?Error*/{\n  if (props[propName]) {\n    return new Error(`Invalid prop ${propName} passed to ${componentName} - do not set this, set it on the child.`);\n  }\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.browserPrefixToKey = browserPrefixToKey;\nexports.browserPrefixToStyle = browserPrefixToStyle;\nexports.default = void 0;\nexports.getPrefix = getPrefix;\nconst prefixes = ['Moz', 'Webkit', 'O', 'ms'];\nfunction getPrefix() /*: string*/{\n  let prop /*: string*/ = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'transform';\n  // Ensure we're running in an environment where there is actually a global\n  // `window` obj\n  if (typeof window === 'undefined') return '';\n\n  // If we're in a pseudo-browser server-side environment, this access\n  // path may not exist, so bail out if it doesn't.\n  const style = window.document?.documentElement?.style;\n  if (!style) return '';\n  if (prop in style) return '';\n  for (let i = 0; i < prefixes.length; i++) {\n    if (browserPrefixToKey(prop, prefixes[i]) in style) return prefixes[i];\n  }\n  return '';\n}\nfunction browserPrefixToKey(prop /*: string*/, prefix /*: string*/) /*: string*/{\n  return prefix ? `${prefix}${kebabToTitleCase(prop)}` : prop;\n}\nfunction browserPrefixToStyle(prop /*: string*/, prefix /*: string*/) /*: string*/{\n  return prefix ? `-${prefix.toLowerCase()}-${prop}` : prop;\n}\nfunction kebabToTitleCase(str /*: string*/) /*: string*/{\n  let out = '';\n  let shouldCapitalize = true;\n  for (let i = 0; i < str.length; i++) {\n    if (shouldCapitalize) {\n      out += str[i].toUpperCase();\n      shouldCapitalize = false;\n    } else if (str[i] === '-') {\n      shouldCapitalize = true;\n    } else {\n      out += str[i];\n    }\n  }\n  return out;\n}\n\n// Default export is the prefix itself, like 'Moz', 'Webkit', etc\n// Note that you may have to re-test for certain things; for instance, Chrome 50\n// can handle unprefixed `transform`, but not unprefixed `user-select`\nvar _default = exports.default = (getPrefix() /*: string*/);", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.addClassName = addClassName;\nexports.addEvent = addEvent;\nexports.addUserSelectStyles = addUserSelectStyles;\nexports.createCSSTransform = createCSSTransform;\nexports.createSVGTransform = createSVGTransform;\nexports.getTouch = getTouch;\nexports.getTouchIdentifier = getTouchIdentifier;\nexports.getTranslation = getTranslation;\nexports.innerHeight = innerHeight;\nexports.innerWidth = innerWidth;\nexports.matchesSelector = matchesSelector;\nexports.matchesSelectorAndParentsTo = matchesSelectorAndParentsTo;\nexports.offsetXYFromParent = offsetXYFromParent;\nexports.outerHeight = outerHeight;\nexports.outerWidth = outerWidth;\nexports.removeClassName = removeClassName;\nexports.removeEvent = removeEvent;\nexports.scheduleRemoveUserSelectStyles = scheduleRemoveUserSelectStyles;\nvar _shims = require(\"./shims\");\nvar _getPrefix = _interopRequireWildcard(require(\"./getPrefix\"));\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n/*:: import type {ControlPosition, PositionOffsetControlPosition, MouseTouchEvent} from './types';*/\nlet matchesSelectorFunc = '';\nfunction matchesSelector(el /*: Node*/, selector /*: string*/) /*: boolean*/{\n  if (!matchesSelectorFunc) {\n    matchesSelectorFunc = (0, _shims.findInArray)(['matches', 'webkitMatchesSelector', 'mozMatchesSelector', 'msMatchesSelector', 'oMatchesSelector'], function (method) {\n      // $FlowIgnore: Doesn't think elements are indexable\n      return (0, _shims.isFunction)(el[method]);\n    });\n  }\n\n  // Might not be found entirely (not an Element?) - in that case, bail\n  // $FlowIgnore: Doesn't think elements are indexable\n  if (!(0, _shims.isFunction)(el[matchesSelectorFunc])) return false;\n\n  // $FlowIgnore: Doesn't think elements are indexable\n  return el[matchesSelectorFunc](selector);\n}\n\n// Works up the tree to the draggable itself attempting to match selector.\nfunction matchesSelectorAndParentsTo(el /*: Node*/, selector /*: string*/, baseNode /*: Node*/) /*: boolean*/{\n  let node = el;\n  do {\n    if (matchesSelector(node, selector)) return true;\n    if (node === baseNode) return false;\n    // $FlowIgnore[incompatible-type]\n    node = node.parentNode;\n  } while (node);\n  return false;\n}\nfunction addEvent(el /*: ?Node*/, event /*: string*/, handler /*: Function*/, inputOptions /*: Object*/) /*: void*/{\n  if (!el) return;\n  const options = {\n    capture: true,\n    ...inputOptions\n  };\n  // $FlowIgnore[method-unbinding]\n  if (el.addEventListener) {\n    el.addEventListener(event, handler, options);\n  } else if (el.attachEvent) {\n    el.attachEvent('on' + event, handler);\n  } else {\n    // $FlowIgnore: Doesn't think elements are indexable\n    el['on' + event] = handler;\n  }\n}\nfunction removeEvent(el /*: ?Node*/, event /*: string*/, handler /*: Function*/, inputOptions /*: Object*/) /*: void*/{\n  if (!el) return;\n  const options = {\n    capture: true,\n    ...inputOptions\n  };\n  // $FlowIgnore[method-unbinding]\n  if (el.removeEventListener) {\n    el.removeEventListener(event, handler, options);\n  } else if (el.detachEvent) {\n    el.detachEvent('on' + event, handler);\n  } else {\n    // $FlowIgnore: Doesn't think elements are indexable\n    el['on' + event] = null;\n  }\n}\nfunction outerHeight(node /*: HTMLElement*/) /*: number*/{\n  // This is deliberately excluding margin for our calculations, since we are using\n  // offsetTop which is including margin. See getBoundPosition\n  let height = node.clientHeight;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  height += (0, _shims.int)(computedStyle.borderTopWidth);\n  height += (0, _shims.int)(computedStyle.borderBottomWidth);\n  return height;\n}\nfunction outerWidth(node /*: HTMLElement*/) /*: number*/{\n  // This is deliberately excluding margin for our calculations, since we are using\n  // offsetLeft which is including margin. See getBoundPosition\n  let width = node.clientWidth;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  width += (0, _shims.int)(computedStyle.borderLeftWidth);\n  width += (0, _shims.int)(computedStyle.borderRightWidth);\n  return width;\n}\nfunction innerHeight(node /*: HTMLElement*/) /*: number*/{\n  let height = node.clientHeight;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  height -= (0, _shims.int)(computedStyle.paddingTop);\n  height -= (0, _shims.int)(computedStyle.paddingBottom);\n  return height;\n}\nfunction innerWidth(node /*: HTMLElement*/) /*: number*/{\n  let width = node.clientWidth;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  width -= (0, _shims.int)(computedStyle.paddingLeft);\n  width -= (0, _shims.int)(computedStyle.paddingRight);\n  return width;\n}\n/*:: interface EventWithOffset {\n  clientX: number, clientY: number\n}*/\n// Get from offsetParent\nfunction offsetXYFromParent(evt /*: EventWithOffset*/, offsetParent /*: HTMLElement*/, scale /*: number*/) /*: ControlPosition*/{\n  const isBody = offsetParent === offsetParent.ownerDocument.body;\n  const offsetParentRect = isBody ? {\n    left: 0,\n    top: 0\n  } : offsetParent.getBoundingClientRect();\n  const x = (evt.clientX + offsetParent.scrollLeft - offsetParentRect.left) / scale;\n  const y = (evt.clientY + offsetParent.scrollTop - offsetParentRect.top) / scale;\n  return {\n    x,\n    y\n  };\n}\nfunction createCSSTransform(controlPos /*: ControlPosition*/, positionOffset /*: PositionOffsetControlPosition*/) /*: Object*/{\n  const translation = getTranslation(controlPos, positionOffset, 'px');\n  return {\n    [(0, _getPrefix.browserPrefixToKey)('transform', _getPrefix.default)]: translation\n  };\n}\nfunction createSVGTransform(controlPos /*: ControlPosition*/, positionOffset /*: PositionOffsetControlPosition*/) /*: string*/{\n  const translation = getTranslation(controlPos, positionOffset, '');\n  return translation;\n}\nfunction getTranslation(_ref /*:: */, positionOffset /*: PositionOffsetControlPosition*/, unitSuffix /*: string*/) /*: string*/{\n  let {\n    x,\n    y\n  } /*: ControlPosition*/ = _ref /*: ControlPosition*/;\n  let translation = `translate(${x}${unitSuffix},${y}${unitSuffix})`;\n  if (positionOffset) {\n    const defaultX = `${typeof positionOffset.x === 'string' ? positionOffset.x : positionOffset.x + unitSuffix}`;\n    const defaultY = `${typeof positionOffset.y === 'string' ? positionOffset.y : positionOffset.y + unitSuffix}`;\n    translation = `translate(${defaultX}, ${defaultY})` + translation;\n  }\n  return translation;\n}\nfunction getTouch(e /*: MouseTouchEvent*/, identifier /*: number*/) /*: ?{clientX: number, clientY: number}*/{\n  return e.targetTouches && (0, _shims.findInArray)(e.targetTouches, t => identifier === t.identifier) || e.changedTouches && (0, _shims.findInArray)(e.changedTouches, t => identifier === t.identifier);\n}\nfunction getTouchIdentifier(e /*: MouseTouchEvent*/) /*: ?number*/{\n  if (e.targetTouches && e.targetTouches[0]) return e.targetTouches[0].identifier;\n  if (e.changedTouches && e.changedTouches[0]) return e.changedTouches[0].identifier;\n}\n\n// User-select Hacks:\n//\n// Useful for preventing blue highlights all over everything when dragging.\n\n// Note we're passing `document` b/c we could be iframed\nfunction addUserSelectStyles(doc /*: ?Document*/) {\n  if (!doc) return;\n  let styleEl = doc.getElementById('react-draggable-style-el');\n  if (!styleEl) {\n    styleEl = doc.createElement('style');\n    styleEl.type = 'text/css';\n    styleEl.id = 'react-draggable-style-el';\n    styleEl.innerHTML = '.react-draggable-transparent-selection *::-moz-selection {all: inherit;}\\n';\n    styleEl.innerHTML += '.react-draggable-transparent-selection *::selection {all: inherit;}\\n';\n    doc.getElementsByTagName('head')[0].appendChild(styleEl);\n  }\n  if (doc.body) addClassName(doc.body, 'react-draggable-transparent-selection');\n}\nfunction scheduleRemoveUserSelectStyles(doc /*: ?Document*/) {\n  // Prevent a possible \"forced reflow\"\n  if (window.requestAnimationFrame) {\n    window.requestAnimationFrame(() => {\n      removeUserSelectStyles(doc);\n    });\n  } else {\n    removeUserSelectStyles(doc);\n  }\n}\nfunction removeUserSelectStyles(doc /*: ?Document*/) {\n  if (!doc) return;\n  try {\n    if (doc.body) removeClassName(doc.body, 'react-draggable-transparent-selection');\n    // $FlowIgnore: IE\n    if (doc.selection) {\n      // $FlowIgnore: IE\n      doc.selection.empty();\n    } else {\n      // Remove selection caused by scroll, unless it's a focused input\n      // (we use doc.defaultView in case we're in an iframe)\n      const selection = (doc.defaultView || window).getSelection();\n      if (selection && selection.type !== 'Caret') {\n        selection.removeAllRanges();\n      }\n    }\n  } catch (e) {\n    // probably IE\n  }\n}\nfunction addClassName(el /*: HTMLElement*/, className /*: string*/) {\n  if (el.classList) {\n    el.classList.add(className);\n  } else {\n    if (!el.className.match(new RegExp(`(?:^|\\\\s)${className}(?!\\\\S)`))) {\n      el.className += ` ${className}`;\n    }\n  }\n}\nfunction removeClassName(el /*: HTMLElement*/, className /*: string*/) {\n  if (el.classList) {\n    el.classList.remove(className);\n  } else {\n    el.className = el.className.replace(new RegExp(`(?:^|\\\\s)${className}(?!\\\\S)`, 'g'), '');\n  }\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.canDragX = canDragX;\nexports.canDragY = canDragY;\nexports.createCoreData = createCoreData;\nexports.createDraggableData = createDraggableData;\nexports.getBoundPosition = getBoundPosition;\nexports.getControlPosition = getControlPosition;\nexports.snapToGrid = snapToGrid;\nvar _shims = require(\"./shims\");\nvar _domFns = require(\"./domFns\");\n/*:: import type Draggable from '../Draggable';*/\n/*:: import type {Bounds, ControlPosition, DraggableData, MouseTouchEvent} from './types';*/\n/*:: import type DraggableCore from '../DraggableCore';*/\nfunction getBoundPosition(draggable /*: Draggable*/, x /*: number*/, y /*: number*/) /*: [number, number]*/{\n  // If no bounds, short-circuit and move on\n  if (!draggable.props.bounds) return [x, y];\n\n  // Clone new bounds\n  let {\n    bounds\n  } = draggable.props;\n  bounds = typeof bounds === 'string' ? bounds : cloneBounds(bounds);\n  const node = findDOMNode(draggable);\n  if (typeof bounds === 'string') {\n    const {\n      ownerDocument\n    } = node;\n    const ownerWindow = ownerDocument.defaultView;\n    let boundNode;\n    if (bounds === 'parent') {\n      boundNode = node.parentNode;\n    } else {\n      // Flow assigns the wrong return type (Node) for getRootNode(),\n      // so we cast it to one of the correct types (Element).\n      // The others are Document and ShadowRoot.\n      // All three implement querySelector() so it's safe to call.\n      const rootNode = ((node.getRootNode() /*: any*/) /*: Element*/);\n      boundNode = rootNode.querySelector(bounds);\n    }\n    if (!(boundNode instanceof ownerWindow.HTMLElement)) {\n      throw new Error('Bounds selector \"' + bounds + '\" could not find an element.');\n    }\n    const boundNodeEl /*: HTMLElement*/ = boundNode; // for Flow, can't seem to refine correctly\n    const nodeStyle = ownerWindow.getComputedStyle(node);\n    const boundNodeStyle = ownerWindow.getComputedStyle(boundNodeEl);\n    // Compute bounds. This is a pain with padding and offsets but this gets it exactly right.\n    bounds = {\n      left: -node.offsetLeft + (0, _shims.int)(boundNodeStyle.paddingLeft) + (0, _shims.int)(nodeStyle.marginLeft),\n      top: -node.offsetTop + (0, _shims.int)(boundNodeStyle.paddingTop) + (0, _shims.int)(nodeStyle.marginTop),\n      right: (0, _domFns.innerWidth)(boundNodeEl) - (0, _domFns.outerWidth)(node) - node.offsetLeft + (0, _shims.int)(boundNodeStyle.paddingRight) - (0, _shims.int)(nodeStyle.marginRight),\n      bottom: (0, _domFns.innerHeight)(boundNodeEl) - (0, _domFns.outerHeight)(node) - node.offsetTop + (0, _shims.int)(boundNodeStyle.paddingBottom) - (0, _shims.int)(nodeStyle.marginBottom)\n    };\n  }\n\n  // Keep x and y below right and bottom limits...\n  if ((0, _shims.isNum)(bounds.right)) x = Math.min(x, bounds.right);\n  if ((0, _shims.isNum)(bounds.bottom)) y = Math.min(y, bounds.bottom);\n\n  // But above left and top limits.\n  if ((0, _shims.isNum)(bounds.left)) x = Math.max(x, bounds.left);\n  if ((0, _shims.isNum)(bounds.top)) y = Math.max(y, bounds.top);\n  return [x, y];\n}\nfunction snapToGrid(grid /*: [number, number]*/, pendingX /*: number*/, pendingY /*: number*/) /*: [number, number]*/{\n  const x = Math.round(pendingX / grid[0]) * grid[0];\n  const y = Math.round(pendingY / grid[1]) * grid[1];\n  return [x, y];\n}\nfunction canDragX(draggable /*: Draggable*/) /*: boolean*/{\n  return draggable.props.axis === 'both' || draggable.props.axis === 'x';\n}\nfunction canDragY(draggable /*: Draggable*/) /*: boolean*/{\n  return draggable.props.axis === 'both' || draggable.props.axis === 'y';\n}\n\n// Get {x, y} positions from event.\nfunction getControlPosition(e /*: MouseTouchEvent*/, touchIdentifier /*: ?number*/, draggableCore /*: DraggableCore*/) /*: ?ControlPosition*/{\n  const touchObj = typeof touchIdentifier === 'number' ? (0, _domFns.getTouch)(e, touchIdentifier) : null;\n  if (typeof touchIdentifier === 'number' && !touchObj) return null; // not the right touch\n  const node = findDOMNode(draggableCore);\n  // User can provide an offsetParent if desired.\n  const offsetParent = draggableCore.props.offsetParent || node.offsetParent || node.ownerDocument.body;\n  return (0, _domFns.offsetXYFromParent)(touchObj || e, offsetParent, draggableCore.props.scale);\n}\n\n// Create an data object exposed by <DraggableCore>'s events\nfunction createCoreData(draggable /*: DraggableCore*/, x /*: number*/, y /*: number*/) /*: DraggableData*/{\n  const isStart = !(0, _shims.isNum)(draggable.lastX);\n  const node = findDOMNode(draggable);\n  if (isStart) {\n    // If this is our first move, use the x and y as last coords.\n    return {\n      node,\n      deltaX: 0,\n      deltaY: 0,\n      lastX: x,\n      lastY: y,\n      x,\n      y\n    };\n  } else {\n    // Otherwise calculate proper values.\n    return {\n      node,\n      deltaX: x - draggable.lastX,\n      deltaY: y - draggable.lastY,\n      lastX: draggable.lastX,\n      lastY: draggable.lastY,\n      x,\n      y\n    };\n  }\n}\n\n// Create an data exposed by <Draggable>'s events\nfunction createDraggableData(draggable /*: Draggable*/, coreData /*: DraggableData*/) /*: DraggableData*/{\n  const scale = draggable.props.scale;\n  return {\n    node: coreData.node,\n    x: draggable.state.x + coreData.deltaX / scale,\n    y: draggable.state.y + coreData.deltaY / scale,\n    deltaX: coreData.deltaX / scale,\n    deltaY: coreData.deltaY / scale,\n    lastX: draggable.state.x,\n    lastY: draggable.state.y\n  };\n}\n\n// A lot faster than stringify/parse\nfunction cloneBounds(bounds /*: Bounds*/) /*: Bounds*/{\n  return {\n    left: bounds.left,\n    top: bounds.top,\n    right: bounds.right,\n    bottom: bounds.bottom\n  };\n}\nfunction findDOMNode(draggable /*: Draggable | DraggableCore*/) /*: HTMLElement*/{\n  const node = draggable.findDOMNode();\n  if (!node) {\n    throw new Error('<DraggableCore>: Unmounted during event!');\n  }\n  // $FlowIgnore we can't assert on HTMLElement due to tests... FIXME\n  return node;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = log;\n/*eslint no-console:0*/\nfunction log() {\n  if (undefined) console.log(...arguments);\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _domFns = require(\"./utils/domFns\");\nvar _positionFns = require(\"./utils/positionFns\");\nvar _shims = require(\"./utils/shims\");\nvar _log = _interopRequireDefault(require(\"./utils/log\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/*:: import type {EventHandler, MouseTouchEvent} from './utils/types';*/\n/*:: import type {Element as ReactElement} from 'react';*/\n// Simple abstraction for dragging events names.\nconst eventsFor = {\n  touch: {\n    start: 'touchstart',\n    move: 'touchmove',\n    stop: 'touchend'\n  },\n  mouse: {\n    start: 'mousedown',\n    move: 'mousemove',\n    stop: 'mouseup'\n  }\n};\n\n// Default to mouse events.\nlet dragEventFor = eventsFor.mouse;\n/*:: export type DraggableData = {\n  node: HTMLElement,\n  x: number, y: number,\n  deltaX: number, deltaY: number,\n  lastX: number, lastY: number,\n};*/\n/*:: export type DraggableEventHandler = (e: MouseEvent, data: DraggableData) => void | false;*/\n/*:: export type ControlPosition = {x: number, y: number};*/\n/*:: export type PositionOffsetControlPosition = {x: number|string, y: number|string};*/\n/*:: export type DraggableCoreDefaultProps = {\n  allowAnyClick: boolean,\n  allowMobileScroll: boolean,\n  disabled: boolean,\n  enableUserSelectHack: boolean,\n  onStart: DraggableEventHandler,\n  onDrag: DraggableEventHandler,\n  onStop: DraggableEventHandler,\n  onMouseDown: (e: MouseEvent) => void,\n  scale: number,\n};*/\n/*:: export type DraggableCoreProps = {\n  ...DraggableCoreDefaultProps,\n  cancel: string,\n  children: ReactElement<any>,\n  offsetParent: HTMLElement,\n  grid: [number, number],\n  handle: string,\n  nodeRef?: ?React.ElementRef<any>,\n};*/\n//\n// Define <DraggableCore>.\n//\n// <DraggableCore> is for advanced usage of <Draggable>. It maintains minimal internal state so it can\n// work well with libraries that require more control over the element.\n//\n\nclass DraggableCore extends React.Component /*:: <DraggableCoreProps>*/{\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"dragging\", false);\n    // Used while dragging to determine deltas.\n    _defineProperty(this, \"lastX\", NaN);\n    _defineProperty(this, \"lastY\", NaN);\n    _defineProperty(this, \"touchIdentifier\", null);\n    _defineProperty(this, \"mounted\", false);\n    _defineProperty(this, \"handleDragStart\", e => {\n      // Make it possible to attach event handlers on top of this one.\n      this.props.onMouseDown(e);\n\n      // Only accept left-clicks.\n      if (!this.props.allowAnyClick && typeof e.button === 'number' && e.button !== 0) return false;\n\n      // Get nodes. Be sure to grab relative document (could be iframed)\n      const thisNode = this.findDOMNode();\n      if (!thisNode || !thisNode.ownerDocument || !thisNode.ownerDocument.body) {\n        throw new Error('<DraggableCore> not mounted on DragStart!');\n      }\n      const {\n        ownerDocument\n      } = thisNode;\n\n      // Short circuit if handle or cancel prop was provided and selector doesn't match.\n      if (this.props.disabled || !(e.target instanceof ownerDocument.defaultView.Node) || this.props.handle && !(0, _domFns.matchesSelectorAndParentsTo)(e.target, this.props.handle, thisNode) || this.props.cancel && (0, _domFns.matchesSelectorAndParentsTo)(e.target, this.props.cancel, thisNode)) {\n        return;\n      }\n\n      // Prevent scrolling on mobile devices, like ipad/iphone.\n      // Important that this is after handle/cancel.\n      if (e.type === 'touchstart' && !this.props.allowMobileScroll) e.preventDefault();\n\n      // Set touch identifier in component state if this is a touch event. This allows us to\n      // distinguish between individual touches on multitouch screens by identifying which\n      // touchpoint was set to this element.\n      const touchIdentifier = (0, _domFns.getTouchIdentifier)(e);\n      this.touchIdentifier = touchIdentifier;\n\n      // Get the current drag point from the event. This is used as the offset.\n      const position = (0, _positionFns.getControlPosition)(e, touchIdentifier, this);\n      if (position == null) return; // not possible but satisfies flow\n      const {\n        x,\n        y\n      } = position;\n\n      // Create an event object with all the data parents need to make a decision here.\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n      (0, _log.default)('DraggableCore: handleDragStart: %j', coreEvent);\n\n      // Call event handler. If it returns explicit false, cancel.\n      (0, _log.default)('calling', this.props.onStart);\n      const shouldUpdate = this.props.onStart(e, coreEvent);\n      if (shouldUpdate === false || this.mounted === false) return;\n\n      // Add a style to the body to disable user-select. This prevents text from\n      // being selected all over the page.\n      if (this.props.enableUserSelectHack) (0, _domFns.addUserSelectStyles)(ownerDocument);\n\n      // Initiate dragging. Set the current x and y as offsets\n      // so we know how much we've moved during the drag. This allows us\n      // to drag elements around even if they have been moved, without issue.\n      this.dragging = true;\n      this.lastX = x;\n      this.lastY = y;\n\n      // Add events to the document directly so we catch when the user's mouse/touch moves outside of\n      // this element. We use different events depending on whether or not we have detected that this\n      // is a touch-capable device.\n      (0, _domFns.addEvent)(ownerDocument, dragEventFor.move, this.handleDrag);\n      (0, _domFns.addEvent)(ownerDocument, dragEventFor.stop, this.handleDragStop);\n    });\n    _defineProperty(this, \"handleDrag\", e => {\n      // Get the current drag point from the event. This is used as the offset.\n      const position = (0, _positionFns.getControlPosition)(e, this.touchIdentifier, this);\n      if (position == null) return;\n      let {\n        x,\n        y\n      } = position;\n\n      // Snap to grid if prop has been provided\n      if (Array.isArray(this.props.grid)) {\n        let deltaX = x - this.lastX,\n          deltaY = y - this.lastY;\n        [deltaX, deltaY] = (0, _positionFns.snapToGrid)(this.props.grid, deltaX, deltaY);\n        if (!deltaX && !deltaY) return; // skip useless drag\n        x = this.lastX + deltaX, y = this.lastY + deltaY;\n      }\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n      (0, _log.default)('DraggableCore: handleDrag: %j', coreEvent);\n\n      // Call event handler. If it returns explicit false, trigger end.\n      const shouldUpdate = this.props.onDrag(e, coreEvent);\n      if (shouldUpdate === false || this.mounted === false) {\n        try {\n          // $FlowIgnore\n          this.handleDragStop(new MouseEvent('mouseup'));\n        } catch (err) {\n          // Old browsers\n          const event = ((document.createEvent('MouseEvents') /*: any*/) /*: MouseTouchEvent*/);\n          // I see why this insanity was deprecated\n          // $FlowIgnore\n          event.initMouseEvent('mouseup', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);\n          this.handleDragStop(event);\n        }\n        return;\n      }\n      this.lastX = x;\n      this.lastY = y;\n    });\n    _defineProperty(this, \"handleDragStop\", e => {\n      if (!this.dragging) return;\n      const position = (0, _positionFns.getControlPosition)(e, this.touchIdentifier, this);\n      if (position == null) return;\n      let {\n        x,\n        y\n      } = position;\n\n      // Snap to grid if prop has been provided\n      if (Array.isArray(this.props.grid)) {\n        let deltaX = x - this.lastX || 0;\n        let deltaY = y - this.lastY || 0;\n        [deltaX, deltaY] = (0, _positionFns.snapToGrid)(this.props.grid, deltaX, deltaY);\n        x = this.lastX + deltaX, y = this.lastY + deltaY;\n      }\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n\n      // Call event handler\n      const shouldContinue = this.props.onStop(e, coreEvent);\n      if (shouldContinue === false || this.mounted === false) return false;\n      const thisNode = this.findDOMNode();\n      if (thisNode) {\n        // Remove user-select hack\n        if (this.props.enableUserSelectHack) (0, _domFns.scheduleRemoveUserSelectStyles)(thisNode.ownerDocument);\n      }\n      (0, _log.default)('DraggableCore: handleDragStop: %j', coreEvent);\n\n      // Reset the el.\n      this.dragging = false;\n      this.lastX = NaN;\n      this.lastY = NaN;\n      if (thisNode) {\n        // Remove event handlers\n        (0, _log.default)('DraggableCore: Removing handlers');\n        (0, _domFns.removeEvent)(thisNode.ownerDocument, dragEventFor.move, this.handleDrag);\n        (0, _domFns.removeEvent)(thisNode.ownerDocument, dragEventFor.stop, this.handleDragStop);\n      }\n    });\n    _defineProperty(this, \"onMouseDown\", e => {\n      dragEventFor = eventsFor.mouse; // on touchscreen laptops we could switch back to mouse\n\n      return this.handleDragStart(e);\n    });\n    _defineProperty(this, \"onMouseUp\", e => {\n      dragEventFor = eventsFor.mouse;\n      return this.handleDragStop(e);\n    });\n    // Same as onMouseDown (start drag), but now consider this a touch device.\n    _defineProperty(this, \"onTouchStart\", e => {\n      // We're on a touch device now, so change the event handlers\n      dragEventFor = eventsFor.touch;\n      return this.handleDragStart(e);\n    });\n    _defineProperty(this, \"onTouchEnd\", e => {\n      // We're on a touch device now, so change the event handlers\n      dragEventFor = eventsFor.touch;\n      return this.handleDragStop(e);\n    });\n  }\n  componentDidMount() {\n    this.mounted = true;\n    // Touch handlers must be added with {passive: false} to be cancelable.\n    // https://developers.google.com/web/updates/2017/01/scrolling-intervention\n    const thisNode = this.findDOMNode();\n    if (thisNode) {\n      (0, _domFns.addEvent)(thisNode, eventsFor.touch.start, this.onTouchStart, {\n        passive: false\n      });\n    }\n  }\n  componentWillUnmount() {\n    this.mounted = false;\n    // Remove any leftover event handlers. Remove both touch and mouse handlers in case\n    // some browser quirk caused a touch event to fire during a mouse move, or vice versa.\n    const thisNode = this.findDOMNode();\n    if (thisNode) {\n      const {\n        ownerDocument\n      } = thisNode;\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.mouse.move, this.handleDrag);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.touch.move, this.handleDrag);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.mouse.stop, this.handleDragStop);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.touch.stop, this.handleDragStop);\n      (0, _domFns.removeEvent)(thisNode, eventsFor.touch.start, this.onTouchStart, {\n        passive: false\n      });\n      if (this.props.enableUserSelectHack) (0, _domFns.scheduleRemoveUserSelectStyles)(ownerDocument);\n    }\n  }\n\n  // React Strict Mode compatibility: if `nodeRef` is passed, we will use it instead of trying to find\n  // the underlying DOM node ourselves. See the README for more information.\n  findDOMNode() /*: ?HTMLElement*/{\n    return this.props?.nodeRef ? this.props?.nodeRef?.current : _reactDom.default.findDOMNode(this);\n  }\n  render() /*: React.Element<any>*/{\n    // Reuse the child provided\n    // This makes it flexible to use whatever element is wanted (div, ul, etc)\n    return /*#__PURE__*/React.cloneElement(React.Children.only(this.props.children), {\n      // Note: mouseMove handler is attached to document so it will still function\n      // when the user drags quickly and leaves the bounds of the element.\n      onMouseDown: this.onMouseDown,\n      onMouseUp: this.onMouseUp,\n      // onTouchStart is added on `componentDidMount` so they can be added with\n      // {passive: false}, which allows it to cancel. See\n      // https://developers.google.com/web/updates/2017/01/scrolling-intervention\n      onTouchEnd: this.onTouchEnd\n    });\n  }\n}\nexports.default = DraggableCore;\n_defineProperty(DraggableCore, \"displayName\", 'DraggableCore');\n_defineProperty(DraggableCore, \"propTypes\", {\n  /**\n   * `allowAnyClick` allows dragging using any mouse button.\n   * By default, we only accept the left button.\n   *\n   * Defaults to `false`.\n   */\n  allowAnyClick: _propTypes.default.bool,\n  /**\n   * `allowMobileScroll` turns off cancellation of the 'touchstart' event\n   * on mobile devices. Only enable this if you are having trouble with click\n   * events. Prefer using 'handle' / 'cancel' instead.\n   *\n   * Defaults to `false`.\n   */\n  allowMobileScroll: _propTypes.default.bool,\n  children: _propTypes.default.node.isRequired,\n  /**\n   * `disabled`, if true, stops the <Draggable> from dragging. All handlers,\n   * with the exception of `onMouseDown`, will not fire.\n   */\n  disabled: _propTypes.default.bool,\n  /**\n   * By default, we add 'user-select:none' attributes to the document body\n   * to prevent ugly text selection during drag. If this is causing problems\n   * for your app, set this to `false`.\n   */\n  enableUserSelectHack: _propTypes.default.bool,\n  /**\n   * `offsetParent`, if set, uses the passed DOM node to compute drag offsets\n   * instead of using the parent node.\n   */\n  offsetParent: function (props /*: DraggableCoreProps*/, propName /*: $Keys<DraggableCoreProps>*/) {\n    if (props[propName] && props[propName].nodeType !== 1) {\n      throw new Error('Draggable\\'s offsetParent must be a DOM Node.');\n    }\n  },\n  /**\n   * `grid` specifies the x and y that dragging should snap to.\n   */\n  grid: _propTypes.default.arrayOf(_propTypes.default.number),\n  /**\n   * `handle` specifies a selector to be used as the handle that initiates drag.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *         return (\n   *            <Draggable handle=\".handle\">\n   *              <div>\n   *                  <div className=\"handle\">Click me to drag</div>\n   *                  <div>This is some other content</div>\n   *              </div>\n   *           </Draggable>\n   *         );\n   *       }\n   *   });\n   * ```\n   */\n  handle: _propTypes.default.string,\n  /**\n   * `cancel` specifies a selector to be used to prevent drag initialization.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *           return(\n   *               <Draggable cancel=\".cancel\">\n   *                   <div>\n   *                     <div className=\"cancel\">You can't drag from here</div>\n   *                     <div>Dragging here works fine</div>\n   *                   </div>\n   *               </Draggable>\n   *           );\n   *       }\n   *   });\n   * ```\n   */\n  cancel: _propTypes.default.string,\n  /* If running in React Strict mode, ReactDOM.findDOMNode() is deprecated.\n   * Unfortunately, in order for <Draggable> to work properly, we need raw access\n   * to the underlying DOM node. If you want to avoid the warning, pass a `nodeRef`\n   * as in this example:\n   *\n   * function MyComponent() {\n   *   const nodeRef = React.useRef(null);\n   *   return (\n   *     <Draggable nodeRef={nodeRef}>\n   *       <div ref={nodeRef}>Example Target</div>\n   *     </Draggable>\n   *   );\n   * }\n   *\n   * This can be used for arbitrarily nested components, so long as the ref ends up\n   * pointing to the actual child DOM node and not a custom component.\n   */\n  nodeRef: _propTypes.default.object,\n  /**\n   * Called when dragging starts.\n   * If this function returns the boolean false, dragging will be canceled.\n   */\n  onStart: _propTypes.default.func,\n  /**\n   * Called while dragging.\n   * If this function returns the boolean false, dragging will be canceled.\n   */\n  onDrag: _propTypes.default.func,\n  /**\n   * Called when dragging stops.\n   * If this function returns the boolean false, the drag will remain active.\n   */\n  onStop: _propTypes.default.func,\n  /**\n   * A workaround option which can be passed if onMouseDown needs to be accessed,\n   * since it'll always be blocked (as there is internal use of onMouseDown)\n   */\n  onMouseDown: _propTypes.default.func,\n  /**\n   * `scale`, if set, applies scaling while dragging an element\n   */\n  scale: _propTypes.default.number,\n  /**\n   * These properties should be defined on the child, not here.\n   */\n  className: _shims.dontSetMe,\n  style: _shims.dontSetMe,\n  transform: _shims.dontSetMe\n});\n_defineProperty(DraggableCore, \"defaultProps\", {\n  allowAnyClick: false,\n  // by default only accept left click\n  allowMobileScroll: false,\n  disabled: false,\n  enableUserSelectHack: true,\n  onStart: function () {},\n  onDrag: function () {},\n  onStop: function () {},\n  onMouseDown: function () {},\n  scale: 1\n});", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"DraggableCore\", {\n  enumerable: true,\n  get: function () {\n    return _DraggableCore.default;\n  }\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _clsx = require(\"clsx\");\nvar _domFns = require(\"./utils/domFns\");\nvar _positionFns = require(\"./utils/positionFns\");\nvar _shims = require(\"./utils/shims\");\nvar _DraggableCore = _interopRequireDefault(require(\"./DraggableCore\"));\nvar _log = _interopRequireDefault(require(\"./utils/log\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); } /*:: import type {ControlPosition, PositionOffsetControlPosition, DraggableCoreProps, DraggableCoreDefaultProps} from './DraggableCore';*/\n/*:: import type {Bounds, DraggableEventHandler} from './utils/types';*/\n/*:: import type {Element as ReactElement} from 'react';*/\n/*:: type DraggableState = {\n  dragging: boolean,\n  dragged: boolean,\n  x: number, y: number,\n  slackX: number, slackY: number,\n  isElementSVG: boolean,\n  prevPropsPosition: ?ControlPosition,\n};*/\n/*:: export type DraggableDefaultProps = {\n  ...DraggableCoreDefaultProps,\n  axis: 'both' | 'x' | 'y' | 'none',\n  bounds: Bounds | string | false,\n  defaultClassName: string,\n  defaultClassNameDragging: string,\n  defaultClassNameDragged: string,\n  defaultPosition: ControlPosition,\n  scale: number,\n};*/\n/*:: export type DraggableProps = {\n  ...DraggableCoreProps,\n  ...DraggableDefaultProps,\n  positionOffset: PositionOffsetControlPosition,\n  position: ControlPosition,\n};*/\n//\n// Define <Draggable>\n//\nclass Draggable extends React.Component /*:: <DraggableProps, DraggableState>*/{\n  // React 16.3+\n  // Arity (props, state)\n  static getDerivedStateFromProps(_ref /*:: */, _ref2 /*:: */) /*: ?Partial<DraggableState>*/{\n    let {\n      position\n    } /*: DraggableProps*/ = _ref /*: DraggableProps*/;\n    let {\n      prevPropsPosition\n    } /*: DraggableState*/ = _ref2 /*: DraggableState*/;\n    // Set x/y if a new position is provided in props that is different than the previous.\n    if (position && (!prevPropsPosition || position.x !== prevPropsPosition.x || position.y !== prevPropsPosition.y)) {\n      (0, _log.default)('Draggable: getDerivedStateFromProps %j', {\n        position,\n        prevPropsPosition\n      });\n      return {\n        x: position.x,\n        y: position.y,\n        prevPropsPosition: {\n          ...position\n        }\n      };\n    }\n    return null;\n  }\n  constructor(props /*: DraggableProps*/) {\n    super(props);\n    _defineProperty(this, \"onDragStart\", (e, coreData) => {\n      (0, _log.default)('Draggable: onDragStart: %j', coreData);\n\n      // Short-circuit if user's callback killed it.\n      const shouldStart = this.props.onStart(e, (0, _positionFns.createDraggableData)(this, coreData));\n      // Kills start event on core as well, so move handlers are never bound.\n      if (shouldStart === false) return false;\n      this.setState({\n        dragging: true,\n        dragged: true\n      });\n    });\n    _defineProperty(this, \"onDrag\", (e, coreData) => {\n      if (!this.state.dragging) return false;\n      (0, _log.default)('Draggable: onDrag: %j', coreData);\n      const uiData = (0, _positionFns.createDraggableData)(this, coreData);\n      const newState = {\n        x: uiData.x,\n        y: uiData.y,\n        slackX: 0,\n        slackY: 0\n      };\n\n      // Keep within bounds.\n      if (this.props.bounds) {\n        // Save original x and y.\n        const {\n          x,\n          y\n        } = newState;\n\n        // Add slack to the values used to calculate bound position. This will ensure that if\n        // we start removing slack, the element won't react to it right away until it's been\n        // completely removed.\n        newState.x += this.state.slackX;\n        newState.y += this.state.slackY;\n\n        // Get bound position. This will ceil/floor the x and y within the boundaries.\n        const [newStateX, newStateY] = (0, _positionFns.getBoundPosition)(this, newState.x, newState.y);\n        newState.x = newStateX;\n        newState.y = newStateY;\n\n        // Recalculate slack by noting how much was shaved by the boundPosition handler.\n        newState.slackX = this.state.slackX + (x - newState.x);\n        newState.slackY = this.state.slackY + (y - newState.y);\n\n        // Update the event we fire to reflect what really happened after bounds took effect.\n        uiData.x = newState.x;\n        uiData.y = newState.y;\n        uiData.deltaX = newState.x - this.state.x;\n        uiData.deltaY = newState.y - this.state.y;\n      }\n\n      // Short-circuit if user's callback killed it.\n      const shouldUpdate = this.props.onDrag(e, uiData);\n      if (shouldUpdate === false) return false;\n      this.setState(newState);\n    });\n    _defineProperty(this, \"onDragStop\", (e, coreData) => {\n      if (!this.state.dragging) return false;\n\n      // Short-circuit if user's callback killed it.\n      const shouldContinue = this.props.onStop(e, (0, _positionFns.createDraggableData)(this, coreData));\n      if (shouldContinue === false) return false;\n      (0, _log.default)('Draggable: onDragStop: %j', coreData);\n      const newState /*: Partial<DraggableState>*/ = {\n        dragging: false,\n        slackX: 0,\n        slackY: 0\n      };\n\n      // If this is a controlled component, the result of this operation will be to\n      // revert back to the old position. We expect a handler on `onDragStop`, at the least.\n      const controlled = Boolean(this.props.position);\n      if (controlled) {\n        const {\n          x,\n          y\n        } = this.props.position;\n        newState.x = x;\n        newState.y = y;\n      }\n      this.setState(newState);\n    });\n    this.state = {\n      // Whether or not we are currently dragging.\n      dragging: false,\n      // Whether or not we have been dragged before.\n      dragged: false,\n      // Current transform x and y.\n      x: props.position ? props.position.x : props.defaultPosition.x,\n      y: props.position ? props.position.y : props.defaultPosition.y,\n      prevPropsPosition: {\n        ...props.position\n      },\n      // Used for compensating for out-of-bounds drags\n      slackX: 0,\n      slackY: 0,\n      // Can only determine if SVG after mounting\n      isElementSVG: false\n    };\n    if (props.position && !(props.onDrag || props.onStop)) {\n      // eslint-disable-next-line no-console\n      console.warn('A `position` was applied to this <Draggable>, without drag handlers. This will make this ' + 'component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the ' + '`position` of this element.');\n    }\n  }\n  componentDidMount() {\n    // Check to see if the element passed is an instanceof SVGElement\n    if (typeof window.SVGElement !== 'undefined' && this.findDOMNode() instanceof window.SVGElement) {\n      this.setState({\n        isElementSVG: true\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.dragging) {\n      this.setState({\n        dragging: false\n      }); // prevents invariant if unmounted while dragging\n    }\n  }\n\n  // React Strict Mode compatibility: if `nodeRef` is passed, we will use it instead of trying to find\n  // the underlying DOM node ourselves. See the README for more information.\n  findDOMNode() /*: ?HTMLElement*/{\n    return this.props?.nodeRef?.current ?? _reactDom.default.findDOMNode(this);\n  }\n  render() /*: ReactElement<any>*/{\n    const {\n      axis,\n      bounds,\n      children,\n      defaultPosition,\n      defaultClassName,\n      defaultClassNameDragging,\n      defaultClassNameDragged,\n      position,\n      positionOffset,\n      scale,\n      ...draggableCoreProps\n    } = this.props;\n    let style = {};\n    let svgTransform = null;\n\n    // If this is controlled, we don't want to move it - unless it's dragging.\n    const controlled = Boolean(position);\n    const draggable = !controlled || this.state.dragging;\n    const validPosition = position || defaultPosition;\n    const transformOpts = {\n      // Set left if horizontal drag is enabled\n      x: (0, _positionFns.canDragX)(this) && draggable ? this.state.x : validPosition.x,\n      // Set top if vertical drag is enabled\n      y: (0, _positionFns.canDragY)(this) && draggable ? this.state.y : validPosition.y\n    };\n\n    // If this element was SVG, we use the `transform` attribute.\n    if (this.state.isElementSVG) {\n      svgTransform = (0, _domFns.createSVGTransform)(transformOpts, positionOffset);\n    } else {\n      // Add a CSS transform to move the element around. This allows us to move the element around\n      // without worrying about whether or not it is relatively or absolutely positioned.\n      // If the item you are dragging already has a transform set, wrap it in a <span> so <Draggable>\n      // has a clean slate.\n      style = (0, _domFns.createCSSTransform)(transformOpts, positionOffset);\n    }\n\n    // Mark with class while dragging\n    const className = (0, _clsx.clsx)(children.props.className || '', defaultClassName, {\n      [defaultClassNameDragging]: this.state.dragging,\n      [defaultClassNameDragged]: this.state.dragged\n    });\n\n    // Reuse the child provided\n    // This makes it flexible to use whatever element is wanted (div, ul, etc)\n    return /*#__PURE__*/React.createElement(_DraggableCore.default, _extends({}, draggableCoreProps, {\n      onStart: this.onDragStart,\n      onDrag: this.onDrag,\n      onStop: this.onDragStop\n    }), /*#__PURE__*/React.cloneElement(React.Children.only(children), {\n      className: className,\n      style: {\n        ...children.props.style,\n        ...style\n      },\n      transform: svgTransform\n    }));\n  }\n}\nexports.default = Draggable;\n_defineProperty(Draggable, \"displayName\", 'Draggable');\n_defineProperty(Draggable, \"propTypes\", {\n  // Accepts all props <DraggableCore> accepts.\n  ..._DraggableCore.default.propTypes,\n  /**\n   * `axis` determines which axis the draggable can move.\n   *\n   *  Note that all callbacks will still return data as normal. This only\n   *  controls flushing to the DOM.\n   *\n   * 'both' allows movement horizontally and vertically.\n   * 'x' limits movement to horizontal axis.\n   * 'y' limits movement to vertical axis.\n   * 'none' limits all movement.\n   *\n   * Defaults to 'both'.\n   */\n  axis: _propTypes.default.oneOf(['both', 'x', 'y', 'none']),\n  /**\n   * `bounds` determines the range of movement available to the element.\n   * Available values are:\n   *\n   * 'parent' restricts movement within the Draggable's parent node.\n   *\n   * Alternatively, pass an object with the following properties, all of which are optional:\n   *\n   * {left: LEFT_BOUND, right: RIGHT_BOUND, bottom: BOTTOM_BOUND, top: TOP_BOUND}\n   *\n   * All values are in px.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *         return (\n   *            <Draggable bounds={{right: 300, bottom: 300}}>\n   *              <div>Content</div>\n   *           </Draggable>\n   *         );\n   *       }\n   *   });\n   * ```\n   */\n  bounds: _propTypes.default.oneOfType([_propTypes.default.shape({\n    left: _propTypes.default.number,\n    right: _propTypes.default.number,\n    top: _propTypes.default.number,\n    bottom: _propTypes.default.number\n  }), _propTypes.default.string, _propTypes.default.oneOf([false])]),\n  defaultClassName: _propTypes.default.string,\n  defaultClassNameDragging: _propTypes.default.string,\n  defaultClassNameDragged: _propTypes.default.string,\n  /**\n   * `defaultPosition` specifies the x and y that the dragged item should start at\n   *\n   * Example:\n   *\n   * ```jsx\n   *      let App = React.createClass({\n   *          render: function () {\n   *              return (\n   *                  <Draggable defaultPosition={{x: 25, y: 25}}>\n   *                      <div>I start with transformX: 25px and transformY: 25px;</div>\n   *                  </Draggable>\n   *              );\n   *          }\n   *      });\n   * ```\n   */\n  defaultPosition: _propTypes.default.shape({\n    x: _propTypes.default.number,\n    y: _propTypes.default.number\n  }),\n  positionOffset: _propTypes.default.shape({\n    x: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.string]),\n    y: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.string])\n  }),\n  /**\n   * `position`, if present, defines the current position of the element.\n   *\n   *  This is similar to how form elements in React work - if no `position` is supplied, the component\n   *  is uncontrolled.\n   *\n   * Example:\n   *\n   * ```jsx\n   *      let App = React.createClass({\n   *          render: function () {\n   *              return (\n   *                  <Draggable position={{x: 25, y: 25}}>\n   *                      <div>I start with transformX: 25px and transformY: 25px;</div>\n   *                  </Draggable>\n   *              );\n   *          }\n   *      });\n   * ```\n   */\n  position: _propTypes.default.shape({\n    x: _propTypes.default.number,\n    y: _propTypes.default.number\n  }),\n  /**\n   * These properties should be defined on the child, not here.\n   */\n  className: _shims.dontSetMe,\n  style: _shims.dontSetMe,\n  transform: _shims.dontSetMe\n});\n_defineProperty(Draggable, \"defaultProps\", {\n  ..._DraggableCore.default.defaultProps,\n  axis: 'both',\n  bounds: false,\n  defaultClassName: 'react-draggable',\n  defaultClassNameDragging: 'react-draggable-dragging',\n  defaultClassNameDragged: 'react-draggable-dragged',\n  defaultPosition: {\n    x: 0,\n    y: 0\n  },\n  scale: 1\n});", "\"use strict\";\n\nconst {\n  default: Draggable,\n  DraggableCore\n} = require('./Draggable');\n\n// Previous versions of this lib exported <Draggable> as the root export. As to no-// them, or TypeScript, we export *both* as the root and as 'default'.\n// See https://github.com/mzabriskie/react-draggable/pull/254\n// and https://github.com/mzabriskie/react-draggable/issues/266\nmodule.exports = Draggable;\nmodule.exports.default = Draggable;\nmodule.exports.DraggableCore = DraggableCore;"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAAA,aAAS,EAAEA,IAAE;AAAC,UAAI,GAAE,GAAE,IAAE;AAAG,UAAG,YAAU,OAAOA,MAAG,YAAU,OAAOA,GAAE,MAAGA;AAAA,eAAU,YAAU,OAAOA,GAAE,KAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,YAAI,IAAEA,GAAE;AAAO,aAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAAA,GAAE,CAAC,MAAI,IAAE,EAAEA,GAAE,CAAC,CAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAA,MAAE,MAAM,MAAI,KAAKA,GAAE,CAAAA,GAAE,CAAC,MAAI,MAAI,KAAG,MAAK,KAAG;AAAG,aAAO;AAAA,IAAC;AAAC,aAAS,IAAG;AAAC,eAAQA,IAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,QAAO,IAAE,GAAE,IAAI,EAACA,KAAE,UAAU,CAAC,OAAK,IAAE,EAAEA,EAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAG,aAAO;AAAA,IAAC;AAAC,WAAO,UAAQ,GAAE,OAAO,QAAQ,OAAK;AAAA;AAAA;;;ACA3Y;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,YAAY;AACpB,YAAQ,cAAc;AACtB,YAAQ,MAAM;AACd,YAAQ,aAAa;AACrB,YAAQ,QAAQ;AAEhB,aAAS,YAAY,OAAoC,UAAkC;AACzF,eAAS,IAAI,GAAG,SAAS,MAAM,QAAQ,IAAI,QAAQ,KAAK;AACtD,YAAI,SAAS,MAAM,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,EAAG,QAAO,MAAM,CAAC;AAAA,MACpE;AAAA,IACF;AACA,aAAS,WAAW,MAAqC;AAEvD,aAAO,OAAO,SAAS,cAAc,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM;AAAA,IAChF;AACA,aAAS,MAAM,KAAoC;AACjD,aAAO,OAAO,QAAQ,YAAY,CAAC,MAAM,GAAG;AAAA,IAC9C;AACA,aAAS,IAAI,GAA4B;AACvC,aAAO,SAAS,GAAG,EAAE;AAAA,IACvB;AACA,aAAS,UAAU,OAAoB,UAAuB,eAAwC;AACpG,UAAI,MAAM,QAAQ,GAAG;AACnB,eAAO,IAAI,MAAM,gBAAgB,QAAQ,cAAc,aAAa,0CAA0C;AAAA,MAChH;AAAA,IACF;AAAA;AAAA;;;AC9BA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,qBAAqB;AAC7B,YAAQ,uBAAuB;AAC/B,YAAQ,UAAU;AAClB,YAAQ,YAAY;AACpB,QAAM,WAAW,CAAC,OAAO,UAAU,KAAK,IAAI;AAC5C,aAAS,YAAwB;AAVjC;AAWE,UAAI,OAAoB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAG5F,UAAI,OAAO,WAAW,YAAa,QAAO;AAI1C,YAAM,SAAQ,kBAAO,aAAP,mBAAiB,oBAAjB,mBAAkC;AAChD,UAAI,CAAC,MAAO,QAAO;AACnB,UAAI,QAAQ,MAAO,QAAO;AAC1B,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,mBAAmB,MAAM,SAAS,CAAC,CAAC,KAAK,MAAO,QAAO,SAAS,CAAC;AAAA,MACvE;AACA,aAAO;AAAA,IACT;AACA,aAAS,mBAAmB,MAAmB,QAAiC;AAC9E,aAAO,SAAS,GAAG,MAAM,GAAG,iBAAiB,IAAI,CAAC,KAAK;AAAA,IACzD;AACA,aAAS,qBAAqB,MAAmB,QAAiC;AAChF,aAAO,SAAS,IAAI,OAAO,YAAY,CAAC,IAAI,IAAI,KAAK;AAAA,IACvD;AACA,aAAS,iBAAiB,KAA8B;AACtD,UAAI,MAAM;AACV,UAAI,mBAAmB;AACvB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,kBAAkB;AACpB,iBAAO,IAAI,CAAC,EAAE,YAAY;AAC1B,6BAAmB;AAAA,QACrB,WAAW,IAAI,CAAC,MAAM,KAAK;AACzB,6BAAmB;AAAA,QACrB,OAAO;AACL,iBAAO,IAAI,CAAC;AAAA,QACd;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAKA,QAAI,WAAW,QAAQ,UAAW,UAAU;AAAA;AAAA;;;ACnD5C;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,eAAe;AACvB,YAAQ,WAAW;AACnB,YAAQ,sBAAsB;AAC9B,YAAQ,qBAAqB;AAC7B,YAAQ,qBAAqB;AAC7B,YAAQ,WAAW;AACnB,YAAQ,qBAAqB;AAC7B,YAAQ,iBAAiB;AACzB,YAAQ,cAAc;AACtB,YAAQ,aAAa;AACrB,YAAQ,kBAAkB;AAC1B,YAAQ,8BAA8B;AACtC,YAAQ,qBAAqB;AAC7B,YAAQ,cAAc;AACtB,YAAQ,aAAa;AACrB,YAAQ,kBAAkB;AAC1B,YAAQ,cAAc;AACtB,YAAQ,iCAAiC;AACzC,QAAI,SAAS;AACb,QAAI,aAAa,wBAAwB,mBAAsB;AAC/D,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AAErmB,QAAI,sBAAsB;AAC1B,aAAS,gBAAgB,IAAe,UAAoC;AAC1E,UAAI,CAAC,qBAAqB;AACxB,+BAAuB,GAAG,OAAO,aAAa,CAAC,WAAW,yBAAyB,sBAAsB,qBAAqB,kBAAkB,GAAG,SAAU,QAAQ;AAEnK,kBAAQ,GAAG,OAAO,YAAY,GAAG,MAAM,CAAC;AAAA,QAC1C,CAAC;AAAA,MACH;AAIA,UAAI,EAAE,GAAG,OAAO,YAAY,GAAG,mBAAmB,CAAC,EAAG,QAAO;AAG7D,aAAO,GAAG,mBAAmB,EAAE,QAAQ;AAAA,IACzC;AAGA,aAAS,4BAA4B,IAAe,UAAuB,UAAkC;AAC3G,UAAI,OAAO;AACX,SAAG;AACD,YAAI,gBAAgB,MAAM,QAAQ,EAAG,QAAO;AAC5C,YAAI,SAAS,SAAU,QAAO;AAE9B,eAAO,KAAK;AAAA,MACd,SAAS;AACT,aAAO;AAAA,IACT;AACA,aAAS,SAAS,IAAgB,OAAoB,SAAwB,cAAqC;AACjH,UAAI,CAAC,GAAI;AACT,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,GAAG;AAAA,MACL;AAEA,UAAI,GAAG,kBAAkB;AACvB,WAAG,iBAAiB,OAAO,SAAS,OAAO;AAAA,MAC7C,WAAW,GAAG,aAAa;AACzB,WAAG,YAAY,OAAO,OAAO,OAAO;AAAA,MACtC,OAAO;AAEL,WAAG,OAAO,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AACA,aAAS,YAAY,IAAgB,OAAoB,SAAwB,cAAqC;AACpH,UAAI,CAAC,GAAI;AACT,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,GAAG;AAAA,MACL;AAEA,UAAI,GAAG,qBAAqB;AAC1B,WAAG,oBAAoB,OAAO,SAAS,OAAO;AAAA,MAChD,WAAW,GAAG,aAAa;AACzB,WAAG,YAAY,OAAO,OAAO,OAAO;AAAA,MACtC,OAAO;AAEL,WAAG,OAAO,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AACA,aAAS,YAAY,MAAoC;AAGvD,UAAI,SAAS,KAAK;AAClB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,iBAAW,GAAG,OAAO,KAAK,cAAc,cAAc;AACtD,iBAAW,GAAG,OAAO,KAAK,cAAc,iBAAiB;AACzD,aAAO;AAAA,IACT;AACA,aAAS,WAAW,MAAoC;AAGtD,UAAI,QAAQ,KAAK;AACjB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,gBAAU,GAAG,OAAO,KAAK,cAAc,eAAe;AACtD,gBAAU,GAAG,OAAO,KAAK,cAAc,gBAAgB;AACvD,aAAO;AAAA,IACT;AACA,aAAS,YAAY,MAAoC;AACvD,UAAI,SAAS,KAAK;AAClB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,iBAAW,GAAG,OAAO,KAAK,cAAc,UAAU;AAClD,iBAAW,GAAG,OAAO,KAAK,cAAc,aAAa;AACrD,aAAO;AAAA,IACT;AACA,aAAS,WAAW,MAAoC;AACtD,UAAI,QAAQ,KAAK;AACjB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,gBAAU,GAAG,OAAO,KAAK,cAAc,WAAW;AAClD,gBAAU,GAAG,OAAO,KAAK,cAAc,YAAY;AACnD,aAAO;AAAA,IACT;AAKA,aAAS,mBAAmB,KAA2B,cAAgC,OAAyC;AAC9H,YAAM,SAAS,iBAAiB,aAAa,cAAc;AAC3D,YAAM,mBAAmB,SAAS;AAAA,QAChC,MAAM;AAAA,QACN,KAAK;AAAA,MACP,IAAI,aAAa,sBAAsB;AACvC,YAAM,KAAK,IAAI,UAAU,aAAa,aAAa,iBAAiB,QAAQ;AAC5E,YAAM,KAAK,IAAI,UAAU,aAAa,YAAY,iBAAiB,OAAO;AAC1E,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,aAAS,mBAAmB,YAAkC,gBAAgE;AAC5H,YAAM,cAAc,eAAe,YAAY,gBAAgB,IAAI;AACnE,aAAO;AAAA,QACL,EAAE,GAAG,WAAW,oBAAoB,aAAa,WAAW,OAAO,CAAC,GAAG;AAAA,MACzE;AAAA,IACF;AACA,aAAS,mBAAmB,YAAkC,gBAAgE;AAC5H,YAAM,cAAc,eAAe,YAAY,gBAAgB,EAAE;AACjE,aAAO;AAAA,IACT;AACA,aAAS,eAAe,MAAc,gBAAoD,YAAqC;AAC7H,UAAI;AAAA,QACF;AAAA,QACA;AAAA,MACF,IAA0B;AAC1B,UAAI,cAAc,aAAa,CAAC,GAAG,UAAU,IAAI,CAAC,GAAG,UAAU;AAC/D,UAAI,gBAAgB;AAClB,cAAM,WAAW,GAAG,OAAO,eAAe,MAAM,WAAW,eAAe,IAAI,eAAe,IAAI,UAAU;AAC3G,cAAM,WAAW,GAAG,OAAO,eAAe,MAAM,WAAW,eAAe,IAAI,eAAe,IAAI,UAAU;AAC3G,sBAAc,aAAa,QAAQ,KAAK,QAAQ,MAAM;AAAA,MACxD;AACA,aAAO;AAAA,IACT;AACA,aAAS,SAAS,GAAyB,YAAkE;AAC3G,aAAO,EAAE,kBAAkB,GAAG,OAAO,aAAa,EAAE,eAAe,OAAK,eAAe,EAAE,UAAU,KAAK,EAAE,mBAAmB,GAAG,OAAO,aAAa,EAAE,gBAAgB,OAAK,eAAe,EAAE,UAAU;AAAA,IACxM;AACA,aAAS,mBAAmB,GAAsC;AAChE,UAAI,EAAE,iBAAiB,EAAE,cAAc,CAAC,EAAG,QAAO,EAAE,cAAc,CAAC,EAAE;AACrE,UAAI,EAAE,kBAAkB,EAAE,eAAe,CAAC,EAAG,QAAO,EAAE,eAAe,CAAC,EAAE;AAAA,IAC1E;AAOA,aAAS,oBAAoB,KAAqB;AAChD,UAAI,CAAC,IAAK;AACV,UAAI,UAAU,IAAI,eAAe,0BAA0B;AAC3D,UAAI,CAAC,SAAS;AACZ,kBAAU,IAAI,cAAc,OAAO;AACnC,gBAAQ,OAAO;AACf,gBAAQ,KAAK;AACb,gBAAQ,YAAY;AACpB,gBAAQ,aAAa;AACrB,YAAI,qBAAqB,MAAM,EAAE,CAAC,EAAE,YAAY,OAAO;AAAA,MACzD;AACA,UAAI,IAAI,KAAM,cAAa,IAAI,MAAM,uCAAuC;AAAA,IAC9E;AACA,aAAS,+BAA+B,KAAqB;AAE3D,UAAI,OAAO,uBAAuB;AAChC,eAAO,sBAAsB,MAAM;AACjC,iCAAuB,GAAG;AAAA,QAC5B,CAAC;AAAA,MACH,OAAO;AACL,+BAAuB,GAAG;AAAA,MAC5B;AAAA,IACF;AACA,aAAS,uBAAuB,KAAqB;AACnD,UAAI,CAAC,IAAK;AACV,UAAI;AACF,YAAI,IAAI,KAAM,iBAAgB,IAAI,MAAM,uCAAuC;AAE/E,YAAI,IAAI,WAAW;AAEjB,cAAI,UAAU,MAAM;AAAA,QACtB,OAAO;AAGL,gBAAM,aAAa,IAAI,eAAe,QAAQ,aAAa;AAC3D,cAAI,aAAa,UAAU,SAAS,SAAS;AAC3C,sBAAU,gBAAgB;AAAA,UAC5B;AAAA,QACF;AAAA,MACF,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AACA,aAAS,aAAa,IAAsB,WAAwB;AAClE,UAAI,GAAG,WAAW;AAChB,WAAG,UAAU,IAAI,SAAS;AAAA,MAC5B,OAAO;AACL,YAAI,CAAC,GAAG,UAAU,MAAM,IAAI,OAAO,YAAY,SAAS,SAAS,CAAC,GAAG;AACnE,aAAG,aAAa,IAAI,SAAS;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AACA,aAAS,gBAAgB,IAAsB,WAAwB;AACrE,UAAI,GAAG,WAAW;AAChB,WAAG,UAAU,OAAO,SAAS;AAAA,MAC/B,OAAO;AACL,WAAG,YAAY,GAAG,UAAU,QAAQ,IAAI,OAAO,YAAY,SAAS,WAAW,GAAG,GAAG,EAAE;AAAA,MACzF;AAAA,IACF;AAAA;AAAA;;;ACtOA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,WAAW;AACnB,YAAQ,WAAW;AACnB,YAAQ,iBAAiB;AACzB,YAAQ,sBAAsB;AAC9B,YAAQ,mBAAmB;AAC3B,YAAQ,qBAAqB;AAC7B,YAAQ,aAAa;AACrB,QAAI,SAAS;AACb,QAAI,UAAU;AAId,aAAS,iBAAiB,WAA2B,GAAgB,GAAsC;AAEzG,UAAI,CAAC,UAAU,MAAM,OAAQ,QAAO,CAAC,GAAG,CAAC;AAGzC,UAAI;AAAA,QACF;AAAA,MACF,IAAI,UAAU;AACd,eAAS,OAAO,WAAW,WAAW,SAAS,YAAY,MAAM;AACjE,YAAM,OAAO,YAAY,SAAS;AAClC,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,cAAc,cAAc;AAClC,YAAI;AACJ,YAAI,WAAW,UAAU;AACvB,sBAAY,KAAK;AAAA,QACnB,OAAO;AAKL,gBAAM,WAAa,KAAK,YAAY;AACpC,sBAAY,SAAS,cAAc,MAAM;AAAA,QAC3C;AACA,YAAI,EAAE,qBAAqB,YAAY,cAAc;AACnD,gBAAM,IAAI,MAAM,sBAAsB,SAAS,8BAA8B;AAAA,QAC/E;AACA,cAAM,cAAgC;AACtC,cAAM,YAAY,YAAY,iBAAiB,IAAI;AACnD,cAAM,iBAAiB,YAAY,iBAAiB,WAAW;AAE/D,iBAAS;AAAA,UACP,MAAM,CAAC,KAAK,cAAc,GAAG,OAAO,KAAK,eAAe,WAAW,KAAK,GAAG,OAAO,KAAK,UAAU,UAAU;AAAA,UAC3G,KAAK,CAAC,KAAK,aAAa,GAAG,OAAO,KAAK,eAAe,UAAU,KAAK,GAAG,OAAO,KAAK,UAAU,SAAS;AAAA,UACvG,QAAQ,GAAG,QAAQ,YAAY,WAAW,KAAK,GAAG,QAAQ,YAAY,IAAI,IAAI,KAAK,cAAc,GAAG,OAAO,KAAK,eAAe,YAAY,KAAK,GAAG,OAAO,KAAK,UAAU,WAAW;AAAA,UACpL,SAAS,GAAG,QAAQ,aAAa,WAAW,KAAK,GAAG,QAAQ,aAAa,IAAI,IAAI,KAAK,aAAa,GAAG,OAAO,KAAK,eAAe,aAAa,KAAK,GAAG,OAAO,KAAK,UAAU,YAAY;AAAA,QAC1L;AAAA,MACF;AAGA,WAAK,GAAG,OAAO,OAAO,OAAO,KAAK,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,KAAK;AACjE,WAAK,GAAG,OAAO,OAAO,OAAO,MAAM,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,MAAM;AAGnE,WAAK,GAAG,OAAO,OAAO,OAAO,IAAI,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,IAAI;AAC/D,WAAK,GAAG,OAAO,OAAO,OAAO,GAAG,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,GAAG;AAC7D,aAAO,CAAC,GAAG,CAAC;AAAA,IACd;AACA,aAAS,WAAW,MAA6B,UAAuB,UAA6C;AACnH,YAAM,IAAI,KAAK,MAAM,WAAW,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AACjD,YAAM,IAAI,KAAK,MAAM,WAAW,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AACjD,aAAO,CAAC,GAAG,CAAC;AAAA,IACd;AACA,aAAS,SAAS,WAAwC;AACxD,aAAO,UAAU,MAAM,SAAS,UAAU,UAAU,MAAM,SAAS;AAAA,IACrE;AACA,aAAS,SAAS,WAAwC;AACxD,aAAO,UAAU,MAAM,SAAS,UAAU,UAAU,MAAM,SAAS;AAAA,IACrE;AAGA,aAAS,mBAAmB,GAAyB,iBAA+B,eAAyD;AAC3I,YAAM,WAAW,OAAO,oBAAoB,YAAY,GAAG,QAAQ,UAAU,GAAG,eAAe,IAAI;AACnG,UAAI,OAAO,oBAAoB,YAAY,CAAC,SAAU,QAAO;AAC7D,YAAM,OAAO,YAAY,aAAa;AAEtC,YAAM,eAAe,cAAc,MAAM,gBAAgB,KAAK,gBAAgB,KAAK,cAAc;AACjG,cAAQ,GAAG,QAAQ,oBAAoB,YAAY,GAAG,cAAc,cAAc,MAAM,KAAK;AAAA,IAC/F;AAGA,aAAS,eAAe,WAA+B,GAAgB,GAAmC;AACxG,YAAM,UAAU,EAAE,GAAG,OAAO,OAAO,UAAU,KAAK;AAClD,YAAM,OAAO,YAAY,SAAS;AAClC,UAAI,SAAS;AAEX,eAAO;AAAA,UACL;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,MACF,OAAO;AAEL,eAAO;AAAA,UACL;AAAA,UACA,QAAQ,IAAI,UAAU;AAAA,UACtB,QAAQ,IAAI,UAAU;AAAA,UACtB,OAAO,UAAU;AAAA,UACjB,OAAO,UAAU;AAAA,UACjB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,aAAS,oBAAoB,WAA2B,UAAiD;AACvG,YAAM,QAAQ,UAAU,MAAM;AAC9B,aAAO;AAAA,QACL,MAAM,SAAS;AAAA,QACf,GAAG,UAAU,MAAM,IAAI,SAAS,SAAS;AAAA,QACzC,GAAG,UAAU,MAAM,IAAI,SAAS,SAAS;AAAA,QACzC,QAAQ,SAAS,SAAS;AAAA,QAC1B,QAAQ,SAAS,SAAS;AAAA,QAC1B,OAAO,UAAU,MAAM;AAAA,QACvB,OAAO,UAAU,MAAM;AAAA,MACzB;AAAA,IACF;AAGA,aAAS,YAAY,QAAiC;AACpD,aAAO;AAAA,QACL,MAAM,OAAO;AAAA,QACb,KAAK,OAAO;AAAA,QACZ,OAAO,OAAO;AAAA,QACd,QAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AACA,aAAS,YAAY,WAA4D;AAC/E,YAAM,OAAO,UAAU,YAAY;AACnC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC5D;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACpJA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,aAAS,MAAM;AACb,UAAI,OAAW,SAAQ,IAAI,GAAG,SAAS;AAAA,IACzC;AAAA;AAAA;;;ACTA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,YAAY,uBAAuB,mBAAoB;AAC3D,QAAI,UAAU;AACd,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,QAAI,OAAO,uBAAuB,aAAsB;AACxD,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AACrmB,aAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,cAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,IAAG;AACnL,aAAS,eAAe,GAAG;AAAE,UAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,aAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAA,IAAI;AAC1G,aAAS,aAAa,GAAG,GAAG;AAAE,UAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AAAG,UAAI,IAAI,EAAE,OAAO,WAAW;AAAG,UAAI,WAAW,GAAG;AAAE,YAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,YAAI,YAAY,OAAO,EAAG,QAAO;AAAG,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAA,IAAG;AAIvT,QAAM,YAAY;AAAA,MAChB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAGA,QAAI,eAAe,UAAU;AAqC7B,QAAM,gBAAN,cAA4B,MAAM,UAAqC;AAAA,MACrE,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,wBAAgB,MAAM,YAAY,KAAK;AAEvC,wBAAgB,MAAM,SAAS,GAAG;AAClC,wBAAgB,MAAM,SAAS,GAAG;AAClC,wBAAgB,MAAM,mBAAmB,IAAI;AAC7C,wBAAgB,MAAM,WAAW,KAAK;AACtC,wBAAgB,MAAM,mBAAmB,OAAK;AAE5C,eAAK,MAAM,YAAY,CAAC;AAGxB,cAAI,CAAC,KAAK,MAAM,iBAAiB,OAAO,EAAE,WAAW,YAAY,EAAE,WAAW,EAAG,QAAO;AAGxF,gBAAM,WAAW,KAAK,YAAY;AAClC,cAAI,CAAC,YAAY,CAAC,SAAS,iBAAiB,CAAC,SAAS,cAAc,MAAM;AACxE,kBAAM,IAAI,MAAM,2CAA2C;AAAA,UAC7D;AACA,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AAGJ,cAAI,KAAK,MAAM,YAAY,EAAE,EAAE,kBAAkB,cAAc,YAAY,SAAS,KAAK,MAAM,UAAU,EAAE,GAAG,QAAQ,6BAA6B,EAAE,QAAQ,KAAK,MAAM,QAAQ,QAAQ,KAAK,KAAK,MAAM,WAAW,GAAG,QAAQ,6BAA6B,EAAE,QAAQ,KAAK,MAAM,QAAQ,QAAQ,GAAG;AACjS;AAAA,UACF;AAIA,cAAI,EAAE,SAAS,gBAAgB,CAAC,KAAK,MAAM,kBAAmB,GAAE,eAAe;AAK/E,gBAAM,mBAAmB,GAAG,QAAQ,oBAAoB,CAAC;AACzD,eAAK,kBAAkB;AAGvB,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,iBAAiB,IAAI;AAC9E,cAAI,YAAY,KAAM;AACtB,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAC7D,WAAC,GAAG,KAAK,SAAS,sCAAsC,SAAS;AAGjE,WAAC,GAAG,KAAK,SAAS,WAAW,KAAK,MAAM,OAAO;AAC/C,gBAAM,eAAe,KAAK,MAAM,QAAQ,GAAG,SAAS;AACpD,cAAI,iBAAiB,SAAS,KAAK,YAAY,MAAO;AAItD,cAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,qBAAqB,aAAa;AAKnF,eAAK,WAAW;AAChB,eAAK,QAAQ;AACb,eAAK,QAAQ;AAKb,WAAC,GAAG,QAAQ,UAAU,eAAe,aAAa,MAAM,KAAK,UAAU;AACvE,WAAC,GAAG,QAAQ,UAAU,eAAe,aAAa,MAAM,KAAK,cAAc;AAAA,QAC7E,CAAC;AACD,wBAAgB,MAAM,cAAc,OAAK;AAEvC,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,KAAK,iBAAiB,IAAI;AACnF,cAAI,YAAY,KAAM;AACtB,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,cAAI,MAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAClC,gBAAI,SAAS,IAAI,KAAK,OACpB,SAAS,IAAI,KAAK;AACpB,aAAC,QAAQ,MAAM,KAAK,GAAG,aAAa,YAAY,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/E,gBAAI,CAAC,UAAU,CAAC,OAAQ;AACxB,gBAAI,KAAK,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AAAA,UAC5C;AACA,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAC7D,WAAC,GAAG,KAAK,SAAS,iCAAiC,SAAS;AAG5D,gBAAM,eAAe,KAAK,MAAM,OAAO,GAAG,SAAS;AACnD,cAAI,iBAAiB,SAAS,KAAK,YAAY,OAAO;AACpD,gBAAI;AAEF,mBAAK,eAAe,IAAI,WAAW,SAAS,CAAC;AAAA,YAC/C,SAAS,KAAK;AAEZ,oBAAM,QAAU,SAAS,YAAY,aAAa;AAGlD,oBAAM,eAAe,WAAW,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,OAAO,OAAO,OAAO,GAAG,IAAI;AACtG,mBAAK,eAAe,KAAK;AAAA,YAC3B;AACA;AAAA,UACF;AACA,eAAK,QAAQ;AACb,eAAK,QAAQ;AAAA,QACf,CAAC;AACD,wBAAgB,MAAM,kBAAkB,OAAK;AAC3C,cAAI,CAAC,KAAK,SAAU;AACpB,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,KAAK,iBAAiB,IAAI;AACnF,cAAI,YAAY,KAAM;AACtB,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,cAAI,MAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAClC,gBAAI,SAAS,IAAI,KAAK,SAAS;AAC/B,gBAAI,SAAS,IAAI,KAAK,SAAS;AAC/B,aAAC,QAAQ,MAAM,KAAK,GAAG,aAAa,YAAY,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/E,gBAAI,KAAK,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AAAA,UAC5C;AACA,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAG7D,gBAAM,iBAAiB,KAAK,MAAM,OAAO,GAAG,SAAS;AACrD,cAAI,mBAAmB,SAAS,KAAK,YAAY,MAAO,QAAO;AAC/D,gBAAM,WAAW,KAAK,YAAY;AAClC,cAAI,UAAU;AAEZ,gBAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,gCAAgC,SAAS,aAAa;AAAA,UACzG;AACA,WAAC,GAAG,KAAK,SAAS,qCAAqC,SAAS;AAGhE,eAAK,WAAW;AAChB,eAAK,QAAQ;AACb,eAAK,QAAQ;AACb,cAAI,UAAU;AAEZ,aAAC,GAAG,KAAK,SAAS,kCAAkC;AACpD,aAAC,GAAG,QAAQ,aAAa,SAAS,eAAe,aAAa,MAAM,KAAK,UAAU;AACnF,aAAC,GAAG,QAAQ,aAAa,SAAS,eAAe,aAAa,MAAM,KAAK,cAAc;AAAA,UACzF;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,eAAe,OAAK;AACxC,yBAAe,UAAU;AAEzB,iBAAO,KAAK,gBAAgB,CAAC;AAAA,QAC/B,CAAC;AACD,wBAAgB,MAAM,aAAa,OAAK;AACtC,yBAAe,UAAU;AACzB,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,CAAC;AAED,wBAAgB,MAAM,gBAAgB,OAAK;AAEzC,yBAAe,UAAU;AACzB,iBAAO,KAAK,gBAAgB,CAAC;AAAA,QAC/B,CAAC;AACD,wBAAgB,MAAM,cAAc,OAAK;AAEvC,yBAAe,UAAU;AACzB,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,UAAU;AAGf,cAAM,WAAW,KAAK,YAAY;AAClC,YAAI,UAAU;AACZ,WAAC,GAAG,QAAQ,UAAU,UAAU,UAAU,MAAM,OAAO,KAAK,cAAc;AAAA,YACxE,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,aAAK,UAAU;AAGf,cAAM,WAAW,KAAK,YAAY;AAClC,YAAI,UAAU;AACZ,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,UAAU;AAC7E,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,UAAU;AAC7E,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,cAAc;AACjF,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,cAAc;AACjF,WAAC,GAAG,QAAQ,aAAa,UAAU,UAAU,MAAM,OAAO,KAAK,cAAc;AAAA,YAC3E,SAAS;AAAA,UACX,CAAC;AACD,cAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,gCAAgC,aAAa;AAAA,QAChG;AAAA,MACF;AAAA;AAAA;AAAA,MAIA,cAAgC;AAtRlC;AAuRI,iBAAO,UAAK,UAAL,mBAAY,YAAU,gBAAK,UAAL,mBAAY,YAAZ,mBAAqB,UAAU,UAAU,QAAQ,YAAY,IAAI;AAAA,MAChG;AAAA,MACA,SAAiC;AAG/B,eAAoB,MAAM,aAAa,MAAM,SAAS,KAAK,KAAK,MAAM,QAAQ,GAAG;AAAA;AAAA;AAAA,UAG/E,aAAa,KAAK;AAAA,UAClB,WAAW,KAAK;AAAA;AAAA;AAAA;AAAA,UAIhB,YAAY,KAAK;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AACA,YAAQ,UAAU;AAClB,oBAAgB,eAAe,eAAe,eAAe;AAC7D,oBAAgB,eAAe,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAO1C,eAAe,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQlC,mBAAmB,WAAW,QAAQ;AAAA,MACtC,UAAU,WAAW,QAAQ,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,MAKlC,UAAU,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAM7B,sBAAsB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAKzC,cAAc,SAAU,OAAgC,UAA0C;AAChG,YAAI,MAAM,QAAQ,KAAK,MAAM,QAAQ,EAAE,aAAa,GAAG;AACrD,gBAAM,IAAI,MAAM,8CAA+C;AAAA,QACjE;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAIA,MAAM,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqB1D,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqB3B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkB3B,SAAS,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK5B,SAAS,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK5B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK3B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK3B,aAAa,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIhC,OAAO,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAI1B,WAAW,OAAO;AAAA,MAClB,OAAO,OAAO;AAAA,MACd,WAAW,OAAO;AAAA,IACpB,CAAC;AACD,oBAAgB,eAAe,gBAAgB;AAAA,MAC7C,eAAe;AAAA;AAAA,MAEf,mBAAmB;AAAA,MACnB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,SAAS,WAAY;AAAA,MAAC;AAAA,MACtB,QAAQ,WAAY;AAAA,MAAC;AAAA,MACrB,QAAQ,WAAY;AAAA,MAAC;AAAA,MACrB,aAAa,WAAY;AAAA,MAAC;AAAA,MAC1B,OAAO;AAAA,IACT,CAAC;AAAA;AAAA;;;ACzbD;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,YAAY;AAAA,MACZ,KAAK,WAAY;AACf,eAAO,eAAe;AAAA,MACxB;AAAA,IACF,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,YAAY,uBAAuB,mBAAoB;AAC3D,QAAI,QAAQ;AACZ,QAAI,UAAU;AACd,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,QAAI,iBAAiB,uBAAuB,uBAA0B;AACtE,QAAI,OAAO,uBAAuB,aAAsB;AACxD,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AACrmB,aAAS,WAAW;AAAE,aAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AAAE,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,cAAI,IAAI,UAAU,CAAC;AAAG,mBAAS,KAAK,EAAG,EAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAAI;AAAE,eAAO;AAAA,MAAG,GAAG,SAAS,MAAM,MAAM,SAAS;AAAA,IAAG;AACnR,aAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,cAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,IAAG;AACnL,aAAS,eAAe,GAAG;AAAE,UAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,aAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAA,IAAI;AAC1G,aAAS,aAAa,GAAG,GAAG;AAAE,UAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AAAG,UAAI,IAAI,EAAE,OAAO,WAAW;AAAG,UAAI,WAAW,GAAG;AAAE,YAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,YAAI,YAAY,OAAO,EAAG,QAAO;AAAG,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAA,IAAG;AA8BvT,QAAM,YAAN,cAAwB,MAAM,UAAiD;AAAA;AAAA;AAAA,MAG7E,OAAO,yBAAyB,MAAc,OAA6C;AACzF,YAAI;AAAA,UACF;AAAA,QACF,IAAyB;AACzB,YAAI;AAAA,UACF;AAAA,QACF,IAAyB;AAEzB,YAAI,aAAa,CAAC,qBAAqB,SAAS,MAAM,kBAAkB,KAAK,SAAS,MAAM,kBAAkB,IAAI;AAChH,WAAC,GAAG,KAAK,SAAS,0CAA0C;AAAA,YAC1D;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,YACL,GAAG,SAAS;AAAA,YACZ,GAAG,SAAS;AAAA,YACZ,mBAAmB;AAAA,cACjB,GAAG;AAAA,YACL;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,YAAY,OAA4B;AACtC,cAAM,KAAK;AACX,wBAAgB,MAAM,eAAe,CAAC,GAAG,aAAa;AACpD,WAAC,GAAG,KAAK,SAAS,8BAA8B,QAAQ;AAGxD,gBAAM,cAAc,KAAK,MAAM,QAAQ,IAAI,GAAG,aAAa,qBAAqB,MAAM,QAAQ,CAAC;AAE/F,cAAI,gBAAgB,MAAO,QAAO;AAClC,eAAK,SAAS;AAAA,YACZ,UAAU;AAAA,YACV,SAAS;AAAA,UACX,CAAC;AAAA,QACH,CAAC;AACD,wBAAgB,MAAM,UAAU,CAAC,GAAG,aAAa;AAC/C,cAAI,CAAC,KAAK,MAAM,SAAU,QAAO;AACjC,WAAC,GAAG,KAAK,SAAS,yBAAyB,QAAQ;AACnD,gBAAM,UAAU,GAAG,aAAa,qBAAqB,MAAM,QAAQ;AACnE,gBAAM,WAAW;AAAA,YACf,GAAG,OAAO;AAAA,YACV,GAAG,OAAO;AAAA,YACV,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAGA,cAAI,KAAK,MAAM,QAAQ;AAErB,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI;AAKJ,qBAAS,KAAK,KAAK,MAAM;AACzB,qBAAS,KAAK,KAAK,MAAM;AAGzB,kBAAM,CAAC,WAAW,SAAS,KAAK,GAAG,aAAa,kBAAkB,MAAM,SAAS,GAAG,SAAS,CAAC;AAC9F,qBAAS,IAAI;AACb,qBAAS,IAAI;AAGb,qBAAS,SAAS,KAAK,MAAM,UAAU,IAAI,SAAS;AACpD,qBAAS,SAAS,KAAK,MAAM,UAAU,IAAI,SAAS;AAGpD,mBAAO,IAAI,SAAS;AACpB,mBAAO,IAAI,SAAS;AACpB,mBAAO,SAAS,SAAS,IAAI,KAAK,MAAM;AACxC,mBAAO,SAAS,SAAS,IAAI,KAAK,MAAM;AAAA,UAC1C;AAGA,gBAAM,eAAe,KAAK,MAAM,OAAO,GAAG,MAAM;AAChD,cAAI,iBAAiB,MAAO,QAAO;AACnC,eAAK,SAAS,QAAQ;AAAA,QACxB,CAAC;AACD,wBAAgB,MAAM,cAAc,CAAC,GAAG,aAAa;AACnD,cAAI,CAAC,KAAK,MAAM,SAAU,QAAO;AAGjC,gBAAM,iBAAiB,KAAK,MAAM,OAAO,IAAI,GAAG,aAAa,qBAAqB,MAAM,QAAQ,CAAC;AACjG,cAAI,mBAAmB,MAAO,QAAO;AACrC,WAAC,GAAG,KAAK,SAAS,6BAA6B,QAAQ;AACvD,gBAAM,WAAyC;AAAA,YAC7C,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAIA,gBAAM,aAAa,QAAQ,KAAK,MAAM,QAAQ;AAC9C,cAAI,YAAY;AACd,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI,KAAK,MAAM;AACf,qBAAS,IAAI;AACb,qBAAS,IAAI;AAAA,UACf;AACA,eAAK,SAAS,QAAQ;AAAA,QACxB,CAAC;AACD,aAAK,QAAQ;AAAA;AAAA,UAEX,UAAU;AAAA;AAAA,UAEV,SAAS;AAAA;AAAA,UAET,GAAG,MAAM,WAAW,MAAM,SAAS,IAAI,MAAM,gBAAgB;AAAA,UAC7D,GAAG,MAAM,WAAW,MAAM,SAAS,IAAI,MAAM,gBAAgB;AAAA,UAC7D,mBAAmB;AAAA,YACjB,GAAG,MAAM;AAAA,UACX;AAAA;AAAA,UAEA,QAAQ;AAAA,UACR,QAAQ;AAAA;AAAA,UAER,cAAc;AAAA,QAChB;AACA,YAAI,MAAM,YAAY,EAAE,MAAM,UAAU,MAAM,SAAS;AAErD,kBAAQ,KAAK,2NAAqO;AAAA,QACpP;AAAA,MACF;AAAA,MACA,oBAAoB;AAElB,YAAI,OAAO,OAAO,eAAe,eAAe,KAAK,YAAY,aAAa,OAAO,YAAY;AAC/F,eAAK,SAAS;AAAA,YACZ,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,YAAI,KAAK,MAAM,UAAU;AACvB,eAAK,SAAS;AAAA,YACZ,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACF;AAAA;AAAA;AAAA,MAIA,cAAgC;AAhNlC;AAiNI,iBAAO,gBAAK,UAAL,mBAAY,YAAZ,mBAAqB,YAAW,UAAU,QAAQ,YAAY,IAAI;AAAA,MAC3E;AAAA,MACA,SAAgC;AAC9B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACL,IAAI,KAAK;AACT,YAAI,QAAQ,CAAC;AACb,YAAI,eAAe;AAGnB,cAAM,aAAa,QAAQ,QAAQ;AACnC,cAAM,YAAY,CAAC,cAAc,KAAK,MAAM;AAC5C,cAAM,gBAAgB,YAAY;AAClC,cAAM,gBAAgB;AAAA;AAAA,UAEpB,IAAI,GAAG,aAAa,UAAU,IAAI,KAAK,YAAY,KAAK,MAAM,IAAI,cAAc;AAAA;AAAA,UAEhF,IAAI,GAAG,aAAa,UAAU,IAAI,KAAK,YAAY,KAAK,MAAM,IAAI,cAAc;AAAA,QAClF;AAGA,YAAI,KAAK,MAAM,cAAc;AAC3B,0BAAgB,GAAG,QAAQ,oBAAoB,eAAe,cAAc;AAAA,QAC9E,OAAO;AAKL,mBAAS,GAAG,QAAQ,oBAAoB,eAAe,cAAc;AAAA,QACvE;AAGA,cAAM,aAAa,GAAG,MAAM,MAAM,SAAS,MAAM,aAAa,IAAI,kBAAkB;AAAA,UAClF,CAAC,wBAAwB,GAAG,KAAK,MAAM;AAAA,UACvC,CAAC,uBAAuB,GAAG,KAAK,MAAM;AAAA,QACxC,CAAC;AAID,eAAoB,MAAM,cAAc,eAAe,SAAS,SAAS,CAAC,GAAG,oBAAoB;AAAA,UAC/F,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,QACf,CAAC,GAAgB,MAAM,aAAa,MAAM,SAAS,KAAK,QAAQ,GAAG;AAAA,UACjE;AAAA,UACA,OAAO;AAAA,YACL,GAAG,SAAS,MAAM;AAAA,YAClB,GAAG;AAAA,UACL;AAAA,UACA,WAAW;AAAA,QACb,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,YAAQ,UAAU;AAClB,oBAAgB,WAAW,eAAe,WAAW;AACrD,oBAAgB,WAAW,aAAa;AAAA;AAAA,MAEtC,GAAG,eAAe,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAc1B,MAAM,WAAW,QAAQ,MAAM,CAAC,QAAQ,KAAK,KAAK,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA2BzD,QAAQ,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,MAAM;AAAA,QAC7D,MAAM,WAAW,QAAQ;AAAA,QACzB,OAAO,WAAW,QAAQ;AAAA,QAC1B,KAAK,WAAW,QAAQ;AAAA,QACxB,QAAQ,WAAW,QAAQ;AAAA,MAC7B,CAAC,GAAG,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAAA,MACjE,kBAAkB,WAAW,QAAQ;AAAA,MACrC,0BAA0B,WAAW,QAAQ;AAAA,MAC7C,yBAAyB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkB5C,iBAAiB,WAAW,QAAQ,MAAM;AAAA,QACxC,GAAG,WAAW,QAAQ;AAAA,QACtB,GAAG,WAAW,QAAQ;AAAA,MACxB,CAAC;AAAA,MACD,gBAAgB,WAAW,QAAQ,MAAM;AAAA,QACvC,GAAG,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC;AAAA,QACtF,GAAG,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC;AAAA,MACxF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqBD,UAAU,WAAW,QAAQ,MAAM;AAAA,QACjC,GAAG,WAAW,QAAQ;AAAA,QACtB,GAAG,WAAW,QAAQ;AAAA,MACxB,CAAC;AAAA;AAAA;AAAA;AAAA,MAID,WAAW,OAAO;AAAA,MAClB,OAAO,OAAO;AAAA,MACd,WAAW,OAAO;AAAA,IACpB,CAAC;AACD,oBAAgB,WAAW,gBAAgB;AAAA,MACzC,GAAG,eAAe,QAAQ;AAAA,MAC1B,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,0BAA0B;AAAA,MAC1B,yBAAyB;AAAA,MACzB,iBAAiB;AAAA,QACf,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA,OAAO;AAAA,IACT,CAAC;AAAA;AAAA;;;AC1YD;AAAA;AAEA,QAAM;AAAA,MACJ,SAAS;AAAA,MACT;AAAA,IACF,IAAI;AAKJ,WAAO,UAAU;AACjB,WAAO,QAAQ,UAAU;AACzB,WAAO,QAAQ,gBAAgB;AAAA;AAAA;", "names": ["e", "e", "t", "e", "t", "e", "t"]}