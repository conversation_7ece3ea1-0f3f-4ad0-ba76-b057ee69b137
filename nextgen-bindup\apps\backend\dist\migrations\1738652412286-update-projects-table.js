"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateProjectsTable1738652412286 = void 0;
const typeorm_1 = require("typeorm");
class UpdateProjectsTable1738652412286 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}projects`;
    }
    async up(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'userId');
        const userIdColumn = new typeorm_1.TableColumn({
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, userIdColumn);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'userId');
        const userIdColumn = new typeorm_1.TableColumn({
            name: 'userId',
            type: 'integer',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, userIdColumn);
    }
}
exports.UpdateProjectsTable1738652412286 = UpdateProjectsTable1738652412286;
//# sourceMappingURL=1738652412286-update-projects-table.js.map