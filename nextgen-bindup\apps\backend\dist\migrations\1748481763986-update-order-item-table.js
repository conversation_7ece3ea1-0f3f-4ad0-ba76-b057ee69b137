"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderItemTable1748481763986 = void 0;
const typeorm_1 = require("typeorm");
class UpdateOrderItemTable1748481763986 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}order_items`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'displayPrice',
            type: 'bigint',
            isNullable: false,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'salePrice',
            type: 'bigint',
            isNullable: true,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'productType',
            type: 'varchar',
            length: '10',
            isNullable: false,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'displayPrice');
        await queryRunner.dropColumn(this.TABLE_NAME, 'salePrice');
        await queryRunner.dropColumn(this.TABLE_NAME, 'productType');
    }
}
exports.UpdateOrderItemTable1748481763986 = UpdateOrderItemTable1748481763986;
//# sourceMappingURL=1748481763986-update-order-item-table.js.map