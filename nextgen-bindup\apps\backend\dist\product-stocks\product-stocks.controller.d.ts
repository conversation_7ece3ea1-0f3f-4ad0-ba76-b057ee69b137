import { ProductStocksService } from './product-stocks.service';
import { CartItem, CheckStockResponse } from './dto/product-stock.dto';
export declare class ProductStocksController {
    private readonly productStockService;
    constructor(productStockService: ProductStocksService);
    getAll(siteId: string, productId: string): Promise<import("./entities/product-stock.entity").ProductStockEntity[]>;
    checkStock(siteId: string, { cartItems }: {
        cartItems: CartItem[];
    }): Promise<CheckStockResponse>;
}
