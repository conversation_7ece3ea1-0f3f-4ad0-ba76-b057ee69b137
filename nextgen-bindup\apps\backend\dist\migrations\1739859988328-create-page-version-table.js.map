{"version": 3, "file": "1739859988328-create-page-version-table.js", "sourceRoot": "", "sources": ["../../src/migrations/1739859988328-create-page-version-table.ts"], "names": [], "mappings": ";;;AAAA,uDAAgE;AAChE,qCAAiE;AAEjE,MAAa,8BAA8B;IAA3C;QACE,eAAU,GAAW,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,eAAe,CAAC;IA0JzE,CAAC;IAxJQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,WAAW,CAC3B,IAAI,eAAK,CAAC;YACR,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe;YACnC,IAAI,EAAE,IAAI,CAAC,UAAU;YACrB,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,IAAI;oBACjB,kBAAkB,EAAE,WAAW;iBAChC;gBACD;oBACE,IAAI,EAAE,eAAe;oBACrB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,IAAI,oBAAQ,CAAC,IAAI,GAAG;iBAC9B;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,OAAO;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,UAAU;oBAChB,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,IAAI,sBAAU,CAAC,KAAK,GAAG;iBACjC;gBACD;oBACE,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,OAAO;iBACjB;gBACD;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,OAAO;iBACjB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,aAAa;oBACnB,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,sBAAsB;iBAChC;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,aAAa;oBACnB,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,sBAAsB;iBAChC;gBACD;oBACE,IAAI,EAAE,kBAAkB;oBACxB,IAAI,EAAE,aAAa;oBACnB,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,sBAAsB;iBAChC;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,OAAO;oBACb,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,IAAI;iBACd;aACF;SACF,CAAC,EACF,IAAI,CACL,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,SAAS,CACzB,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,EAAE,CACpD,CAAC;IACJ,CAAC;CACF;AA3JD,wEA2JC"}