import { DnsRecordEntity } from './entities/dns-record.entity';
import { Repository } from 'typeorm';
export declare class DnsRecordService {
    readonly dnsRecordRepo: Repository<DnsRecordEntity>;
    create(data: DnsRecordEntity): Promise<DnsRecordEntity>;
    update(id: number, data: Partial<DnsRecordEntity>): Promise<DnsRecordEntity>;
    delete(id: number): Promise<boolean>;
    findBySiteId(siteId: number): Promise<DnsRecordEntity[]>;
    findByProjectId(projectId: number): Promise<DnsRecordEntity[]>;
}
