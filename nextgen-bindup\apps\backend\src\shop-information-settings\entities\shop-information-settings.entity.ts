import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ColorTheme, TaxMode, TaxRegulation } from '../enums';

@Entity('shop_information_settings', { schema: process.env.DATABASE_SCHEMA })
export class ShopInformationSettingEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @Column({
    name: 'isMaintenance',
    type: 'boolean',
    nullable: false,
  })
  isMaintenance: boolean;

  @Column({
    name: 'shopName',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  shopName: string;

  @Column({
    name: 'logoImages',
    type: 'text',
    nullable: true,
    array: true,
  })
  logoImages: string[];

  @Column({
    name: 'colorTheme',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  colorTheme: ColorTheme;

  @Column({
    name: 'shopUrl',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  shopUrl: string;

  @Column({
    name: 'isSetupGuide',
    type: 'boolean',
    nullable: false,
  })
  isSetupGuide: boolean;

  @Column({
    name: 'guideUrl',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  guideUrl: string;

  @Column({
    name: 'email',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  email: string;

  @Column({
    name: 'isAddPrivacy',
    type: 'boolean',
    nullable: false,
  })
  isAddPrivacy: boolean;

  @Column({
    name: 'privacyUrl',
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  privacyUrl: string;

  @Column({
    name: 'taxMode',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  taxMode: TaxMode;

  @Column({
    name: 'taxRate',
    type: 'bigint',
    nullable: true,
  })
  taxRate: number;

  @Column({
    name: 'taxRegulation',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  taxRegulation: TaxRegulation;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
