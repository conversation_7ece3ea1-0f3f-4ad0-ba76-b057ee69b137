import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateOrderTable1748424195394 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}orders`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    // drop project_id column if it exists
    await queryRunner.query(`
      ALTER TABLE IF EXISTS "${this.TABLE_NAME}"
      DROP COLUMN IF EXISTS "projectId"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Recreate project_id column if it was dropped
    await queryRunner.query(`
      ALTER TABLE IF EXISTS "${this.TABLE_NAME}"
      ADD COLUMN "projectId" integer
    `);
  }
}
