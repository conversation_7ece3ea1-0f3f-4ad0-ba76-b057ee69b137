"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnhandledExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../../logger/logger.service");
let UnhandledExceptionFilter = class UnhandledExceptionFilter {
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    catch(exception, host) {
        try {
            const ctx = host.switchToHttp();
            const response = ctx.getResponse();
            const status = exception instanceof common_1.HttpException
                ? exception.getStatus()
                : common_1.HttpStatus.BAD_REQUEST;
            if (response.locals.requestLog) {
                this.loggerService.error(response.locals.shop, exception.message, {
                    meta: response.locals.requestLog,
                });
                response.locals.requestLog = undefined;
            }
            else {
                this.loggerService.error(response.locals.shop, exception.message);
            }
            let responseMsg = 'api.error.bad_bequest';
            if (exception.type === 'ValidateException' ||
                exception.type === 'AppException') {
                responseMsg = exception.message;
            }
            console.error('Unhandled exception:', exception);
            response.status(status).json({ data: responseMsg });
        }
        catch { }
    }
};
exports.UnhandledExceptionFilter = UnhandledExceptionFilter;
exports.UnhandledExceptionFilter = UnhandledExceptionFilter = __decorate([
    (0, common_1.Catch)(),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], UnhandledExceptionFilter);
//# sourceMappingURL=unhandled-exception.filter.js.map