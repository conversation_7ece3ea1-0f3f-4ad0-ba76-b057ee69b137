"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionHistoryService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const page_entity_1 = require("../page/entities/page.entity");
const page_version_entity_1 = require("./entities/page-version.entity");
const site_version_entity_1 = require("./entities/site-version.entity");
const typeorm_2 = require("typeorm");
const site_entity_1 = require("../site/entities/site.entity");
let VersionHistoryService = class VersionHistoryService {
    constructor(pageRepository, siteRepository, siteVersionRepository, pageVersionRepository) {
        this.pageRepository = pageRepository;
        this.siteRepository = siteRepository;
        this.siteVersionRepository = siteVersionRepository;
        this.pageVersionRepository = pageVersionRepository;
    }
    async deleteSiteVersion(siteVersionId) {
        const siteVersion = await this.siteVersionRepository.findOne({
            where: { id: siteVersionId },
        });
        if (!siteVersion) {
            throw new Error('Site version not found');
        }
        await this.siteVersionRepository.delete(siteVersionId);
        await this.pageVersionRepository.delete({
            siteVersionId: siteVersionId,
        });
    }
    async updateVersionName(siteVersionId, versionName) {
        const siteVersion = await this.siteVersionRepository.findOne({
            where: { id: siteVersionId },
        });
        if (!siteVersion) {
            throw new Error('Site version not found');
        }
        siteVersion.versionName = versionName;
        await this.siteVersionRepository.save(siteVersion);
    }
    async backupSite(siteId, name) {
        const site = await this.siteRepository.findOne({ where: { id: siteId } });
        if (!site) {
            throw new Error('Site not found');
        }
        const newVersion = new site_version_entity_1.SiteVersionEntity();
        newVersion.siteId = siteId;
        newVersion.projectId = site.projectId;
        newVersion.projectFolderId = site.projectFolderId;
        newVersion.managementName = site.managementName;
        newVersion.status = site.status;
        newVersion.url = site.url;
        newVersion.title = site.title;
        newVersion.description = site.description;
        newVersion.isSearch = site.isSearch;
        newVersion.thumb = site.thumb;
        newVersion.headCode = site.headCode;
        newVersion.bodyCode = site.bodyCode;
        newVersion.createdAt = site.createdAt;
        newVersion.updatedAt = site.updatedAt;
        newVersion.isArchived = site.isArchived;
        newVersion.versionName = name;
        newVersion.userId = site.userId;
        const savedSiteVersion = await this.siteVersionRepository.save(newVersion);
        const pages = await this.pageRepository.find({
            where: {
                projectId: site.projectId,
                siteId: siteId,
                isDeleted: (0, typeorm_2.In)([false, null]),
            },
        });
        if (!pages.length) {
            return savedSiteVersion;
        }
        const pageVersions = pages.map(page => {
            const pageVersion = new page_version_entity_1.PageVersionEntity();
            pageVersion.siteVersionId = savedSiteVersion.id;
            pageVersion.pageId = page.id;
            pageVersion.type = page.type;
            pageVersion.parentId = page.parentId;
            pageVersion.projectId = page.projectId;
            pageVersion.siteId = page.siteId;
            pageVersion.datasource = page.datasource;
            pageVersion.name = page.name;
            pageVersion.components = page.components;
            pageVersion.ts = page.ts;
            pageVersion.status = page.status;
            pageVersion.url = page.url;
            pageVersion.title = page.title;
            pageVersion.description = page.description;
            pageVersion.isSearch = page.isSearch;
            pageVersion.thumb = page.thumb;
            pageVersion.headCode = page.headCode;
            pageVersion.bodyCode = page.bodyCode;
            pageVersion.isPrivate = page.isPrivate;
            pageVersion.isHome = page.isHome;
            pageVersion.children = page.children;
            pageVersion.createdAt = page.createdAt;
            pageVersion.updatedAt = page.updatedAt;
            pageVersion.userId = page.userId;
            return pageVersion;
        });
        await this.pageVersionRepository.save(pageVersions);
        return savedSiteVersion;
    }
    async restore(siteId, siteVersionId) {
        const siteVersion = await this.siteVersionRepository.findOne({
            where: { id: siteVersionId },
        });
        if (!siteVersion) {
            throw new Error('Site version not found');
        }
        const site = await this.siteRepository.findOne({ where: { id: siteId } });
        if (!site) {
            throw new Error('Site not found');
        }
        site.projectFolderId = siteVersion.projectFolderId;
        site.managementName = siteVersion.managementName;
        site.status = siteVersion.status;
        site.url = siteVersion.url;
        site.title = siteVersion.title;
        site.description = siteVersion.description;
        site.isSearch = siteVersion.isSearch;
        site.thumb = siteVersion.thumb;
        site.headCode = siteVersion.headCode;
        site.bodyCode = siteVersion.bodyCode;
        site.createdAt = siteVersion.createdAt;
        site.updatedAt = new Date();
        site.isArchived = siteVersion.isArchived;
        site.userId = siteVersion.userId;
        await this.siteRepository.save(site);
        await this.pageRepository.update({
            projectId: site.projectId,
            siteId: siteId,
        }, {
            isDeleted: true,
        });
        const pageVersions = await this.pageVersionRepository.find({
            where: { siteVersionId: siteVersionId },
        });
        for (const pageVersion of pageVersions) {
            await this.pageRepository.update({ id: pageVersion.pageId }, {
                type: pageVersion.type,
                projectId: pageVersion.projectId,
                siteId: pageVersion.siteId,
                datasource: pageVersion.datasource,
                name: pageVersion.name,
                components: pageVersion.components,
                ts: pageVersion.ts,
                status: pageVersion.status,
                url: pageVersion.url,
                title: pageVersion.title,
                description: pageVersion.description,
                isSearch: pageVersion.isSearch,
                thumb: pageVersion.thumb,
                headCode: pageVersion.headCode,
                bodyCode: pageVersion.bodyCode,
                isPrivate: pageVersion.isPrivate,
                isHome: pageVersion.isHome,
                updatedAt: new Date(),
                userId: pageVersion.userId,
                isDeleted: false,
            });
        }
        return true;
    }
    async getSiteVersions(siteId, page = 1, pageSize = 100, year, month) {
        const query = this.siteVersionRepository
            .createQueryBuilder('siteVersion')
            .where('siteVersion.siteId = :siteId', { siteId });
        if (year && month) {
            const startDate = new Date(year, month - 1, 1);
            const endDate = new Date(year, month, 0, 23, 59, 59);
            query.andWhere('siteVersion.versionCreatedAt >= :startDate', {
                startDate,
            });
            query.andWhere('siteVersion.versionCreatedAt <= :endDate', { endDate });
        }
        else if (year) {
            const startDate = new Date(year, 0, 1);
            const endDate = new Date(year, 11, 31, 23, 59, 59);
            query.andWhere('siteVersion.versionCreatedAt >= :startDate', {
                startDate,
            });
            query.andWhere('siteVersion.versionCreatedAt <= :endDate', { endDate });
        }
        const [data, total] = await query
            .orderBy('siteVersion.versionCreatedAt', 'DESC')
            .skip((page - 1) * pageSize)
            .take(pageSize)
            .getManyAndCount();
        return { data, total, page, pageSize };
    }
    async getSiteVersion(siteVersionId) {
        return this.siteVersionRepository.findOne({ where: { id: siteVersionId } });
    }
    async getPagesBySiteVersionId(siteVersionId) {
        const data = await this.pageVersionRepository.find({
            where: { siteVersionId },
        });
        const pages = data.map(pageVersion => {
            const page = {
                id: pageVersion.pageId,
                type: pageVersion.type,
                parentId: pageVersion.parentId,
                projectId: pageVersion.projectId,
                siteId: pageVersion.siteId,
                datasource: pageVersion.datasource,
                name: pageVersion.name,
                components: pageVersion.components,
                ts: pageVersion.ts,
                status: pageVersion.status,
                url: pageVersion.url,
                title: pageVersion.title,
                description: pageVersion.description,
                isSearch: pageVersion.isSearch,
                thumb: pageVersion.thumb,
                headCode: pageVersion.headCode,
                bodyCode: pageVersion.bodyCode,
                isPrivate: pageVersion.isPrivate,
                isHome: pageVersion.isHome,
                children: pageVersion.children,
                createdAt: pageVersion.createdAt,
                updatedAt: pageVersion.updatedAt,
                userId: pageVersion.userId,
                isDeleted: false,
            };
            return page;
        });
        return pages;
    }
    async getPageBySiteVersionId(siteVersionId, pageId) {
        const pageVersion = await this.pageVersionRepository.findOneBy({
            siteVersionId: siteVersionId,
            pageId: pageId,
        });
        const page = {
            id: pageVersion.pageId,
            type: pageVersion.type,
            parentId: pageVersion.parentId,
            projectId: pageVersion.projectId,
            siteId: pageVersion.siteId,
            datasource: pageVersion.datasource,
            name: pageVersion.name,
            components: pageVersion.components,
            ts: pageVersion.ts,
            status: pageVersion.status,
            url: pageVersion.url,
            title: pageVersion.title,
            description: pageVersion.description,
            isSearch: pageVersion.isSearch,
            thumb: pageVersion.thumb,
            headCode: pageVersion.headCode,
            bodyCode: pageVersion.bodyCode,
            isPrivate: pageVersion.isPrivate,
            isHome: pageVersion.isHome,
            children: pageVersion.children,
            createdAt: pageVersion.createdAt,
            updatedAt: pageVersion.updatedAt,
            userId: pageVersion.userId,
            isDeleted: false,
        };
        return page;
    }
};
exports.VersionHistoryService = VersionHistoryService;
exports.VersionHistoryService = VersionHistoryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(page_entity_1.PageEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(site_entity_1.SiteEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(site_version_entity_1.SiteVersionEntity)),
    __param(3, (0, typeorm_1.InjectRepository)(page_version_entity_1.PageVersionEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], VersionHistoryService);
//# sourceMappingURL=version-history.service.js.map