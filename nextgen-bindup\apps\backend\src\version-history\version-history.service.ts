import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PageEntity } from 'src/page/entities/page.entity';
import { PageVersionEntity } from './entities/page-version.entity';
import { SiteVersionEntity } from './entities/site-version.entity';
import { In, Repository } from 'typeorm';
import { SiteEntity } from 'src/site/entities/site.entity';
import { SiteVersionDto } from './version-history.dto';

@Injectable()
export class VersionHistoryService {
  constructor(
    @InjectRepository(PageEntity)
    private pageRepository: Repository<PageEntity>,
    @InjectRepository(SiteEntity)
    private siteRepository: Repository<SiteEntity>,
    @InjectRepository(SiteVersionEntity)
    private siteVersionRepository: Repository<SiteVersionEntity>,
    @InjectRepository(PageVersionEntity)
    private pageVersionRepository: Repository<PageVersionEntity>,
  ) {}

  async deleteSiteVersion(siteVersionId: number) {
    const siteVersion = await this.siteVersionRepository.findOne({
      where: { id: siteVersionId },
    });
    if (!siteVersion) {
      throw new Error('Site version not found');
    }
    await this.siteVersionRepository.delete(siteVersionId);
    await this.pageVersionRepository.delete({
      siteVersionId: siteVersionId,
    });
  }

  async updateVersionName(siteVersionId: number, versionName: string) {
    const siteVersion = await this.siteVersionRepository.findOne({
      where: { id: siteVersionId },
    });
    if (!siteVersion) {
      throw new Error('Site version not found');
    }
    siteVersion.versionName = versionName;
    await this.siteVersionRepository.save(siteVersion);
  }

  async backupSite(siteId: number, name: string): Promise<SiteVersionEntity> {
    const site = await this.siteRepository.findOne({ where: { id: siteId } });
    if (!site) {
      throw new Error('Site not found');
    }

    const newVersion = new SiteVersionEntity();

    newVersion.siteId = siteId;
    newVersion.projectId = site.projectId;
    newVersion.projectFolderId = site.projectFolderId;
    newVersion.managementName = site.managementName;
    newVersion.status = site.status;
    newVersion.url = site.url;
    newVersion.title = site.title;
    newVersion.description = site.description;
    newVersion.isSearch = site.isSearch;
    newVersion.thumb = site.thumb;
    newVersion.headCode = site.headCode;
    newVersion.bodyCode = site.bodyCode;
    newVersion.createdAt = site.createdAt;
    newVersion.updatedAt = site.updatedAt;
    newVersion.isArchived = site.isArchived;
    newVersion.versionName = name;
    newVersion.userId = site.userId;
    const savedSiteVersion = await this.siteVersionRepository.save(newVersion);
    const pages = await this.pageRepository.find({
      where: {
        projectId: site.projectId,
        siteId: siteId,
        isDeleted: In([false, null]),
      },
    });

    if (!pages.length) {
      return savedSiteVersion;
    }

    const pageVersions = pages.map(page => {
      const pageVersion = new PageVersionEntity();
      pageVersion.siteVersionId = savedSiteVersion.id;
      pageVersion.pageId = page.id;
      pageVersion.type = page.type;
      pageVersion.parentId = page.parentId;
      pageVersion.projectId = page.projectId;
      pageVersion.siteId = page.siteId;
      pageVersion.datasource = page.datasource;
      pageVersion.name = page.name;
      pageVersion.components = page.components;
      pageVersion.ts = page.ts;
      pageVersion.status = page.status;
      pageVersion.url = page.url;
      pageVersion.title = page.title;
      pageVersion.description = page.description;
      pageVersion.isSearch = page.isSearch;
      pageVersion.thumb = page.thumb;
      pageVersion.headCode = page.headCode;
      pageVersion.bodyCode = page.bodyCode;
      pageVersion.isPrivate = page.isPrivate;
      pageVersion.isHome = page.isHome;
      pageVersion.children = page.children;
      pageVersion.createdAt = page.createdAt;
      pageVersion.updatedAt = page.updatedAt;
      pageVersion.userId = page.userId;
      return pageVersion;
    });

    await this.pageVersionRepository.save(pageVersions);

    return savedSiteVersion;
  }

  async restore(siteId: number, siteVersionId: number): Promise<boolean> {
    const siteVersion = await this.siteVersionRepository.findOne({
      where: { id: siteVersionId },
    });

    if (!siteVersion) {
      throw new Error('Site version not found');
    }

    const site = await this.siteRepository.findOne({ where: { id: siteId } });
    if (!site) {
      throw new Error('Site not found');
    }

    // Restore by site
    // Update site with version data
    site.projectFolderId = siteVersion.projectFolderId;
    site.managementName = siteVersion.managementName;
    site.status = siteVersion.status;
    site.url = siteVersion.url;
    site.title = siteVersion.title;
    site.description = siteVersion.description;
    site.isSearch = siteVersion.isSearch;
    site.thumb = siteVersion.thumb;
    site.headCode = siteVersion.headCode;
    site.bodyCode = siteVersion.bodyCode;
    site.createdAt = siteVersion.createdAt;
    site.updatedAt = new Date();
    site.isArchived = siteVersion.isArchived;
    site.userId = siteVersion.userId;
    await this.siteRepository.save(site);

    // soft delete all existing pages
    await this.pageRepository.update(
      {
        projectId: site.projectId,
        siteId: siteId,
      },
      {
        isDeleted: true,
      },
    );

    const pageVersions = await this.pageVersionRepository.find({
      where: { siteVersionId: siteVersionId },
    });

    for (const pageVersion of pageVersions) {
      await this.pageRepository.update(
        { id: pageVersion.pageId },
        {
          type: pageVersion.type,
          projectId: pageVersion.projectId,
          siteId: pageVersion.siteId,
          datasource: pageVersion.datasource,
          name: pageVersion.name,
          components: pageVersion.components,
          ts: pageVersion.ts,
          status: pageVersion.status,
          url: pageVersion.url,
          title: pageVersion.title,
          description: pageVersion.description,
          isSearch: pageVersion.isSearch,
          thumb: pageVersion.thumb,
          headCode: pageVersion.headCode,
          bodyCode: pageVersion.bodyCode,
          isPrivate: pageVersion.isPrivate,
          isHome: pageVersion.isHome,
          updatedAt: new Date(),
          userId: pageVersion.userId,
          isDeleted: false,
        },
      );
    }

    return true;
  }

  async getSiteVersions(
    siteId: number,
    page: number = 1,
    pageSize: number = 100,
    year?: number,
    month?: number,
  ): Promise<SiteVersionDto> {
    const query = this.siteVersionRepository
      .createQueryBuilder('siteVersion')
      .where('siteVersion.siteId = :siteId', { siteId });

    if (year && month) {
      // Filter by year and month
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0, 23, 59, 59); // Last moment of the month

      query.andWhere('siteVersion.versionCreatedAt >= :startDate', {
        startDate,
      });
      query.andWhere('siteVersion.versionCreatedAt <= :endDate', { endDate });
    } else if (year) {
      // Filter by year only
      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year, 11, 31, 23, 59, 59); // Last moment of the year

      query.andWhere('siteVersion.versionCreatedAt >= :startDate', {
        startDate,
      });
      query.andWhere('siteVersion.versionCreatedAt <= :endDate', { endDate });
    }

    const [data, total] = await query
      .orderBy('siteVersion.versionCreatedAt', 'DESC')
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    return { data, total, page, pageSize };
  }

  async getSiteVersion(siteVersionId: number): Promise<SiteVersionEntity> {
    return this.siteVersionRepository.findOne({ where: { id: siteVersionId } });
  }

  async getPagesBySiteVersionId(siteVersionId: number): Promise<PageEntity[]> {
    const data = await this.pageVersionRepository.find({
      where: { siteVersionId },
    });
    // map to page entity
    const pages = data.map(pageVersion => {
      const page: PageEntity = {
        id: pageVersion.pageId,
        type: pageVersion.type,
        parentId: pageVersion.parentId,
        projectId: pageVersion.projectId,
        siteId: pageVersion.siteId,
        datasource: pageVersion.datasource,
        name: pageVersion.name,
        components: pageVersion.components,
        ts: pageVersion.ts,
        status: pageVersion.status,
        url: pageVersion.url,
        title: pageVersion.title,
        description: pageVersion.description,
        isSearch: pageVersion.isSearch,
        thumb: pageVersion.thumb,
        headCode: pageVersion.headCode,
        bodyCode: pageVersion.bodyCode,
        isPrivate: pageVersion.isPrivate,
        isHome: pageVersion.isHome,
        children: pageVersion.children,
        createdAt: pageVersion.createdAt,
        updatedAt: pageVersion.updatedAt,
        userId: pageVersion.userId,
        isDeleted: false,
      };
      return page;
    });
    return pages;
  }

  async getPageBySiteVersionId(
    siteVersionId: number,
    pageId: number,
  ): Promise<PageEntity> {
    const pageVersion = await this.pageVersionRepository.findOneBy({
      siteVersionId: siteVersionId,
      pageId: pageId,
    });
    const page: PageEntity = {
      id: pageVersion.pageId,
      type: pageVersion.type,
      parentId: pageVersion.parentId,
      projectId: pageVersion.projectId,
      siteId: pageVersion.siteId,
      datasource: pageVersion.datasource,
      name: pageVersion.name,
      components: pageVersion.components,
      ts: pageVersion.ts,
      status: pageVersion.status,
      url: pageVersion.url,
      title: pageVersion.title,
      description: pageVersion.description,
      isSearch: pageVersion.isSearch,
      thumb: pageVersion.thumb,
      headCode: pageVersion.headCode,
      bodyCode: pageVersion.bodyCode,
      isPrivate: pageVersion.isPrivate,
      isHome: pageVersion.isHome,
      children: pageVersion.children,
      createdAt: pageVersion.createdAt,
      updatedAt: pageVersion.updatedAt,
      userId: pageVersion.userId,
      isDeleted: false,
    };
    return page;
  }
}
