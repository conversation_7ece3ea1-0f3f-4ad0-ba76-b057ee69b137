import { useEffect, type FC, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import SearchIcon from '@mui/icons-material/Search';
import { CircularProgress, debounce } from '@mui/material';
import Box from '@mui/material/Box';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { PostDto } from '../../dto/post.dto';
import Thumbs from './Thumbs';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const parser = new (window as any).RSSParser();
const domParser = new DOMParser();

const Learning: FC = () => {
  const { t } = useTranslation();
  const [items, setItems] = useState<PostDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState<string>('');

  const [filteredItems, setFilteredItems] = useState<PostDto[]>([]);

  useEffect(() => {
    debounceFilter(items, searchTerm);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm, items]);

  const filterItems = (items: PostDto[], searchTerm: string) => {
    if (!searchTerm) {
      setFilteredItems(items);
      return;
    }

    const text: string = searchTerm.toLowerCase();
    const filteredItems = items.filter(item =>
      item.title.toLowerCase().includes(text),
    );
    setFilteredItems(filteredItems);
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceFilter = useCallback(
    debounce((items, searchTerm) => filterItems(items, searchTerm), 500),
    [],
  );

  useEffect(() => {
    const fetchRss = async () => {
      setLoading(true);
      try {
        const [campFeed, galleryFeed] = await Promise.all([
          parser.parseURL('https://bindup.jp/camp/feed'),
          parser.parseURL('https://gallery.bindup.jp/feed'),
        ]);
        const items = [...campFeed.items, ...galleryFeed.items];
        setItems(items);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchRss();
  }, []);

  const getFirstImage = (htmlString: string) => {
    const doc = domParser.parseFromString(htmlString, 'text/html');
    const firstImage = doc.querySelector('img');
    return firstImage ? firstImage.src : '';
  };

  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="h4">{t('learning.title')}</Typography>
        <Box sx={{ display: 'inherit', gap: 2 }}>
          <TextField
            size="small"
            placeholder={t('common.search')}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              },
            }}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </Box>
      </Box>
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      )}
      {!loading && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
          {filteredItems.map(item => {
            const image = getFirstImage(item['content:encoded'] as string);
            return (
              <Thumbs
                key={item.guid}
                content={item.title}
                image={image}
                onClick={() => {
                  window.open(item.link, '_blank');
                }}
              />
            );
          })}
        </Box>
      )}
    </>
  );
};

export default Learning;
