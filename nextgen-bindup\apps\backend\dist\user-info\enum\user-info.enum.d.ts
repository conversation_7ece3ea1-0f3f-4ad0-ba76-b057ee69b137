export declare enum UserType {
    INDIVIDUAL = 1,
    CORPORATION = 2
}
export declare enum Sex {
    MALE = 1,
    FEMALE = 2,
    OTHER = 3
}
export declare enum CountryCode {
    JAPAN = 1
}
export interface Prefecture {
    code: string;
    value: number;
}
export declare const PREFECTURE_LIST: Record<number, Prefecture[]>;
export declare enum Occupation {
    NONE = 0,
    COMPANY_EMPLOYEE = 1,
    CIVIL_SERVANT = 2,
    PROFESSOR_LECTURER = 3,
    STUDENT = 4,
    FULLTIME_HOUSEWIFE = 5,
    SELF_EMPLOYED = 9,
    UNEMPLOYED = 10,
    OTHER = 11,
    DESIGNER = 12
}
export declare enum Industry {
    NONE = 0,
    DESIGN_INDUSTRY = 1,
    CREATIVE_INDUSTRY = 2,
    MANUFACTURING = 3,
    RETAIL = 4,
    CONSTRUCTION = 5,
    INFORMATION_AND_COMMUNICATIONS = 6,
    FOOD_AND_BEVERAGE_INDUSTRY = 7,
    LEISURE_AND_TOURISM = 8,
    FINANCE_AND_REAL_ESTATE = 9,
    PROFESSIONAL = 10,
    EDUCATION = 11,
    MEDICAL_AND_WELFARE = 12,
    BEAUTY = 13,
    AGRICULTURE_FORESTRY_AND_FISHING = 14,
    CIVIL_SERVANT = 15,
    OTHER = 16
}
export declare enum JobType {
    NONE = 0,
    EXECUTIVE = 1,
    COMPANY_OFFICER = 2,
    SELF_EMPLOYED = 3,
    WEB_MANAGER_APPROVAL_PERSON = 4,
    WEB_MANAGER_NONDECISION_MAKER = 5,
    SALES = 6,
    PUBLIC_RELATIONS_AND_PROMOTION = 7,
    ADMINISTRATION = 8,
    MARKETING = 9,
    TEACHING_PROFESSION = 10,
    PROFESSIONAL_WEB_DESIGNER = 11,
    PROFESSIONAL_DESIGNER = 12,
    PROFESSIONAL_GRAPHIC_DESIGNER = 13,
    PROFESSIONAL_ILLUSTRATOR = 14,
    PROFESSIONAL_PHOTOGRAPHER = 15,
    PROFESSIONAL_OCCUPATION_MUSICIAN = 16,
    PROFESSIONAL_ENGINEER = 17,
    PROFESSIONAL_OTHER = 18,
    HOUSEWIFE = 19,
    STUDENT = 20,
    PARTTIME_WORKER = 21,
    UNEMPLOYED = 22,
    OTHER = 23
}
