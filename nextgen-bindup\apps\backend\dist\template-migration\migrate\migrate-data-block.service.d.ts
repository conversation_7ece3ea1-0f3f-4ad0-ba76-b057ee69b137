import { Site3_Block } from '../dto/site3_block.dto';
import { Site4_BlockData } from '../dto/site4_blockdata.dto';
import { Component } from '@nextgen-bindup/common/dto/component';
export declare class MigrateDataBlockService {
    components: Record<string, Component>;
    parentSide: string;
    block: Site3_Block;
    blockData: Site4_BlockData;
    ts: number;
    constructor(inp: {
        components: Record<string, Component>;
    });
    migrateBlockData(inp: {
        parentSide: string;
        block: Site3_Block;
        blockData: Site4_BlockData;
    }): Component;
    private createBackgroundDiv;
    private createSizeDiv;
    private createMarginDiv;
    private createLayout;
    private createLayoutPlain;
    private createLayoutAsymm;
    private createLayoutTable;
    private createLayoutAlbum;
    private createLayoutTab;
    private createLayoutAccordion;
}
