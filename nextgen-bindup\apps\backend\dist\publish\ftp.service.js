"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FtpService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FtpService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const fs = require("fs/promises");
const path = require("path");
const Ftp = require('jsftp');
let FtpService = FtpService_1 = class FtpService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(FtpService_1.name);
    }
    createFtpConnection() {
        const FTP_CONFIG = {
            host: this.configService.get('FTP_HOST'),
            user: this.configService.get('FTP_USER'),
            pass: this.configService.get('FTP_PASSWORD'),
        };
        return new Ftp(FTP_CONFIG);
    }
    async uploadFTP(folderPath, remotePath) {
        console.log(`folderPath:${folderPath}`);
        const ftp = this.createFtpConnection();
        try {
            await this.clearRemoteDirectoryFtp(ftp, remotePath);
            console.log('* clearRemoteDirectoryFtp');
            await this.uploadFolderFtp(ftp, folderPath, remotePath);
        }
        catch (error) {
            this.logger.error(`Failed to upload folder to FTP: ${error.message}`);
            throw error;
        }
        finally {
            ftp.raw('quit');
        }
    }
    async uploadFolderFtp(ftp, folderPath, remotePath) {
        await this.ensureRemoteDirectoryFtp(ftp, remotePath);
        const items = await fs.readdir(folderPath, { withFileTypes: true });
        for (const item of items) {
            const localItemPath = path.join(folderPath, item.name);
            const remoteItemPath = `${remotePath}/${item.name}`;
            if (item.isDirectory()) {
                await this.uploadFolderFtp(ftp, localItemPath, remoteItemPath);
            }
            else {
                await this.uploadFileFtp(ftp, localItemPath, remoteItemPath);
            }
        }
    }
    async ensureRemoteDirectoryFtp(ftp, remoteDir) {
        return new Promise((resolve, reject) => {
            ftp.raw('MKD', remoteDir, err => {
                if (err && err?.code !== 550) {
                    return reject(err);
                }
                resolve();
            });
        });
    }
    async clearRemoteDirectoryFtp(ftp, remoteDir) {
        return new Promise((resolve, reject) => {
            ftp.ls(remoteDir, async (err, list) => {
                console.log('Clearing remote directory', list);
                if (err)
                    return reject(err);
                try {
                    for (const item of list) {
                        const fullPath = `${remoteDir}/${item.name}`;
                        if (item.type === 1) {
                            await this.clearRemoteDirectoryFtp(ftp, fullPath);
                            await this.removeDirectoryFtp(ftp, fullPath);
                        }
                        else {
                            await this.removeFileFtp(ftp, fullPath);
                        }
                    }
                    resolve();
                }
                catch (error) {
                    reject(error);
                }
            });
        });
    }
    async removeFileFtp(ftp, filePath) {
        return new Promise((resolve, reject) => {
            ftp.raw('DELE', filePath, err => {
                if (err && err?.code !== 550)
                    return reject(err);
                resolve();
            });
        });
    }
    async removeDirectoryFtp(ftp, dirPath) {
        return new Promise((resolve, reject) => {
            ftp.raw('RMD', dirPath, err => {
                if (err && err?.code !== 550)
                    return reject(err);
                resolve();
            });
        });
    }
    async uploadFileFtp(ftp, localPath, remotePath) {
        try {
            const buffer = await fs.readFile(localPath);
            return new Promise((resolve, reject) => {
                ftp.put(buffer, remotePath, err => {
                    if (err) {
                        this.logger.error(`Failed to upload file ${localPath}: ${err.message}`);
                        return reject(err);
                    }
                    this.logger.log(`File ${localPath} uploaded successfully.`);
                    resolve();
                });
            });
        }
        catch (error) {
            this.logger.error(`Error reading file ${localPath}: ${error.message}`);
            throw error;
        }
    }
};
exports.FtpService = FtpService;
exports.FtpService = FtpService = FtpService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], FtpService);
//# sourceMappingURL=ftp.service.js.map