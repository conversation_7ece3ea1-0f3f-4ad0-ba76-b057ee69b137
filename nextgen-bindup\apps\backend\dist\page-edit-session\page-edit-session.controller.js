"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageEditSessionController = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const page_edit_session_service_1 = require("./page-edit-session.service");
let PageEditSessionController = class PageEditSessionController {
    constructor(pageEditSessionService) {
        this.pageEditSessionService = pageEditSessionService;
    }
    handleCron() {
        this.pageEditSessionService.cronJobDeletePageEditSession();
    }
};
exports.PageEditSessionController = PageEditSessionController;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_MINUTE),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PageEditSessionController.prototype, "handleCron", null);
exports.PageEditSessionController = PageEditSessionController = __decorate([
    (0, common_1.Controller)('page-edit-session'),
    __metadata("design:paramtypes", [page_edit_session_service_1.PageEditSessionService])
], PageEditSessionController);
//# sourceMappingURL=page-edit-session.controller.js.map