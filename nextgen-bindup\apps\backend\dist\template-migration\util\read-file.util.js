"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadFileUtil = void 0;
const fs = require("fs");
const unescapeJs = require('unescape-js');
const decode = require('unescape');
const decodePreserveNbsp = (content) => {
    const placeholder = '___NBSP_PLACEHOLDER___';
    const processedContent = content.replace(/&nbsp;/g, placeholder);
    let decoded = decode(processedContent);
    decoded = decoded.replace(new RegExp(placeholder, 'g'), '&nbsp;');
    return decoded;
};
class ReadFileUtil {
    static readSharedCss(dbdata) {
        const sharedCssList = JSON.parse(fs.readFileSync(`${dbdata}/site_shared_css.json`, {
            encoding: 'utf8',
        }));
        for (const sharedCss of sharedCssList) {
            sharedCss.dataJson = JSON.parse(sharedCss.data);
            delete sharedCss.data;
        }
        return sharedCssList;
    }
    static readSites(dbdata) {
        const sites = JSON.parse(fs.readFileSync(`${dbdata}/site0_site.json`, {
            encoding: 'utf8',
        }));
        for (const site of sites) {
            site.webfontinfoJson = JSON.parse(site.webfontinfo);
            delete site.webfontinfo;
            site.propsJson = JSON.parse(site.props);
            delete site.props;
            site.openGraphJson = JSON.parse(site.openGraph);
            delete site.openGraph;
        }
        return sites;
    }
    static readCorners(dbdata) {
        const corners = JSON.parse(fs.readFileSync(`${dbdata}/site1_corner.json`, {
            encoding: 'utf8',
        }));
        for (const corner of corners) {
            corner.infoJson = JSON.parse(corner.info);
            delete corner.info;
            corner.robotsJson = JSON.parse(corner.robots);
            delete corner.robots;
        }
        return corners;
    }
    static readPages(dbdata) {
        const pages = JSON.parse(fs.readFileSync(`${dbdata}/site2_page.json`, {
            encoding: 'utf8',
        }));
        for (const page of pages) {
            page.name = unescapeJs(page.name);
            page.bgSetsJson = JSON.parse(page.bgSets);
            delete page.bgSets;
            page.headSetsJson = JSON.parse(page.headSets);
            page.headSetsJson.scpt = unescapeJs(page.headSetsJson.scpt || '');
            delete page.headSets;
            page.layoutSetsJson = JSON.parse(page.layoutSets);
            delete page.layoutSets;
            page.robotsJSON = JSON.parse(page.robots);
            delete page.robots;
            page.mobileSetsJson = JSON.parse(page.mobileSets);
            delete page.mobileSets;
            page.cssSetsJson = JSON.parse(page.cssSets);
            delete page.cssSets;
            page.openGraphJson = JSON.parse(page.openGraph);
            delete page.openGraph;
            page.areaFloatJson = JSON.parse(page.areaFloat);
            delete page.areaFloat;
        }
        return pages;
    }
    static readBlocks(dbdata) {
        const blocks = JSON.parse(fs.readFileSync(`${dbdata}/site3_block.json`, {
            encoding: 'utf8',
        }));
        blocks.sort((a, b) => {
            const v1 = a.pageId * 100000 + a.areaId * 1000 + a.seq;
            const v2 = b.pageId * 100000 + b.areaId * 1000 + b.seq;
            return v1 > v2 ? 1 : v1 < v2 ? -1 : 0;
        });
        return blocks;
    }
    static readBlockDatas(dbdata) {
        const blockDatas = JSON.parse(fs.readFileSync(`${dbdata}/site4_blockdata.json`, {
            encoding: 'utf8',
        }));
        for (const blockData of blockDatas) {
            blockData.blockdataInfoJson = JSON.parse(blockData.blockdataInfo);
            delete blockData.blockdataInfo;
            blockData.content = decodePreserveNbsp(blockData.content);
            blockData.smartdataJson = JSON.parse(blockData.smartdata);
            delete blockData.smartdata;
            blockData.bgSetsJson = JSON.parse(blockData.bgSets);
            delete blockData.bgSets;
        }
        return blockDatas;
    }
    static readSrcList1(dbdata) {
        const srcList = JSON.parse(fs.readFileSync(`${dbdata}/site6_srclist1.json`, {
            encoding: 'utf8',
        }));
        for (const src of srcList) {
            delete src.signXml;
            delete src.imageDataThumbnail;
        }
        return srcList;
    }
    static readResources(dbdata, srcList1) {
        const resources = JSON.parse(fs.readFileSync(`${dbdata}/site5_resource.json`, {
            encoding: 'utf8',
        }));
        for (const resource of resources) {
            delete resource.blockeditIcon;
            resource.partsPropertyJson = JSON.parse(resource.partsProperty);
            delete resource.partsProperty;
            resource.srcList = srcList1
                .filter(src => src.resourceId === resource.resourceId &&
                src.blockdataId === resource.blockdataId)
                .sort((a, b) => (a.seq > b.seq ? 1 : a.seq < b.seq ? -1 : 0));
        }
        return resources;
    }
    static readTemplateSites(dbdata) {
        const templateSites = JSON.parse(fs.readFileSync(`${dbdata}/template_site.json`, {
            encoding: 'utf8',
        }));
        for (const templateSite of templateSites) {
            templateSite.pagelistJson = JSON.parse(templateSite.pagelist);
            delete templateSite.pagelist;
            templateSite.previewJson = JSON.parse(templateSite.preview);
            delete templateSite.preview;
            templateSite.downloadJson = JSON.parse(templateSite.download);
            delete templateSite.download;
        }
        return templateSites;
    }
}
exports.ReadFileUtil = ReadFileUtil;
//# sourceMappingURL=read-file.util.js.map