import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class NewMigraton1747970237553 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}payment_methods`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isGenerated: true,
            generationStrategy: 'increment',
            isPrimary: true,
          },
          {
            name: 'siteId',
            type: 'int',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'bankTransfer',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'postalTransfer',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'stripePaymentGateway',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'cashOnDelivery',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'deletedAt',
            type: 'timestamptz',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.TABLE_NAME);
  }
}
