import { AuthService } from './auth.service';
import { CreateTempTokenReq, JwtPayloadDto, VerifyTempTokenReq } from './dto/auth.dto';
import { LoginReq } from 'src/auth/dto/login.dto';
import { SignUpReq } from 'src/auth/dto/sign-up.dto';
import { ForgotPwdReq } from 'src/auth/dto/forgot-pwd.dto';
import { ResetPwdReq } from 'src/auth/dto/reset-pwd.dto';
import { ChangePwdReq } from './dto/change-pwd.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    signUp(reqDto: SignUpReq): Promise<import("src/auth/dto/sign-up.dto").SignUpRes>;
    login(reqDto: LoginReq): Promise<import("src/auth/dto/login.dto").LoginRes>;
    forgotPwd(reqDto: ForgotPwdReq): Promise<boolean>;
    resetPwd(reqDto: ResetPwdReq): Promise<boolean>;
    createTempToken(user: JwtPayloadDto, reqDto: CreateTempTokenReq): Promise<import("./dto/auth.dto").CreateTempTokenRes>;
    verifyRemoteToken(reqDto: VerifyTempTokenReq): Promise<import("./dto/auth.dto").VerifyTempTokenRes>;
    verifyDesignEditorToken(reqDto: VerifyTempTokenReq): Promise<import("./dto/auth.dto").VerifyTempTokenRes>;
    changePassword(user: JwtPayloadDto, data: ChangePwdReq): Promise<boolean>;
}
