"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrateDataBlockService = void 0;
const site4_blockdata_dto_1 = require("../dto/site4_blockdata.dto");
const common_util_1 = require("../../utils/common.util");
const migrate_util_1 = require("./migrate.util");
const prop_border_default_value_1 = require("../../page/utils/prop-border-default-value");
const prop_background_default_value_1 = require("../../page/utils/prop-background-default-value");
const prop_spacing_default_value_1 = require("../../page/utils/prop-spacing-default-value");
const component_type_1 = require("../../page/types/component.type");
const prop_size_default_value_1 = require("../../page/utils/prop-size-default-value");
class MigrateDataBlockService {
    constructor(inp) {
        this.components = inp.components;
    }
    migrateBlockData(inp) {
        this.parentSide = inp.parentSide;
        this.block = inp.block;
        this.blockData = inp.blockData;
        this.ts = (0, common_util_1.NEW_TS)();
        const backgroundBlock = this.createBackgroundDiv();
        const sizeBlock = this.createSizeDiv(backgroundBlock.id);
        const marginBlock = this.createMarginDiv(sizeBlock.id);
        return marginBlock;
    }
    createBackgroundDiv() {
        const id = `block${this.block.areaId}_${this.block.blockId}`;
        const properties = migrate_util_1.MigrateUtil.blockProps();
        const borderProps = (0, prop_border_default_value_1.PROP_BORDER_DEFAULT_VALUE)(this.ts);
        const backgroundProps = (0, prop_background_default_value_1.PROP_BACKGROUND_LIST_DEFAULT_VALUE)(this.ts);
        const marginBottom = { value: '0', unit: 'px' };
        const padding = (0, prop_spacing_default_value_1.PROP_SPACING_DEFAULT_VALUE)(this.ts).padding;
        const blockdataInfo = this.blockData.blockdataInfoJson;
        const bgSets = this.blockData.bgSetsJson;
        switch (blockdataInfo.blockdata_frameType) {
            case site4_blockdata_dto_1.BlockData_Info_FrameType.NONE:
                if (bgSets?.hasc && bgSets?.cval) {
                    backgroundProps.list.push({
                        id: '1',
                        type: 'solid',
                        from: 'design',
                        backgroundColor: bgSets.cval.startsWith('rgb')
                            ? bgSets.cval
                            : '#' + bgSets.cval,
                        presetId: null,
                        alpha: 1,
                        visibility: true,
                        ts: this.ts,
                    });
                }
                if (bgSets?.hasi && bgSets?.imgf) {
                    backgroundProps.list.push({
                        id: '2',
                        type: 'image',
                        url: `https://edit3.bindcloud.jp/bindcld/site_data/338884/_src/90214562/${bgSets.imgf}`,
                        position: {
                            x: { value: '0', unit: 'px' },
                            y: { value: '0', unit: 'px' },
                            offsetX: { value: '0', unit: 'px' },
                            offsetY: { value: '0', unit: 'px' },
                        },
                        size: '',
                        sizeLength: {
                            width: { value: '', unit: 'auto' },
                            height: { value: '', unit: 'auto' },
                        },
                        repeat: [
                            site4_blockdata_dto_1.BgSet_Ilay.LEFT_JUSTIFIED,
                            site4_blockdata_dto_1.BgSet_Ilay.VERTICAL_REPEAT,
                            site4_blockdata_dto_1.BgSet_Ilay.RIGHT_JUSTIFIED,
                            site4_blockdata_dto_1.BgSet_Ilay.TOP_ALIGNMENT,
                            site4_blockdata_dto_1.BgSet_Ilay.HORIZONTAL_REPEAT,
                            site4_blockdata_dto_1.BgSet_Ilay.BOTTOM_ALIGNMENT,
                        ].includes(bgSets.ilay)
                            ? 'repeat'
                            : '',
                        attachmentFixed: false,
                        clipText: false,
                        visibility: true,
                        ts: this.ts,
                    });
                }
                break;
            case site4_blockdata_dto_1.BlockData_Info_FrameType.BORDER_ONLY: {
                borderProps.isDetail = true;
                const borderValue = {
                    color: '#E4E4E4',
                    width: { value: '1', unit: 'px' },
                    borderStyle: 'solid',
                };
                borderProps.top = borderValue;
                borderProps.right = borderValue;
                borderProps.bottom = borderValue;
                borderProps.left = borderValue;
                padding.top = { value: '10', unit: 'px' };
                padding.right = { value: '10', unit: 'px' };
                padding.bottom = { value: '10', unit: 'px' };
                padding.left = { value: '10', unit: 'px' };
                padding.isDetail = true;
                break;
            }
            case site4_blockdata_dto_1.BlockData_Info_FrameType.BORDER_RADIUS_ONLY: {
                borderProps.isDetail = true;
                const borderValue = {
                    color: '#E4E4E4',
                    width: { value: '1', unit: 'px' },
                    borderStyle: 'solid',
                };
                borderProps.top = borderValue;
                borderProps.right = borderValue;
                borderProps.bottom = borderValue;
                borderProps.left = borderValue;
                const radiusValue = {
                    width: { value: '6', unit: 'px' },
                    height: { value: '6', unit: 'px' },
                    isDetail: true,
                };
                borderProps.radiusTopLeft = radiusValue;
                borderProps.radiusTopRight = radiusValue;
                borderProps.radiusBottomLeft = radiusValue;
                borderProps.radiusBottomRight = radiusValue;
                break;
            }
            case site4_blockdata_dto_1.BlockData_Info_FrameType.BORDER_GRADIENT: {
                borderProps.isDetail = true;
                borderProps.bottom = {
                    color: '#E4E4E4',
                    width: { value: '2', unit: 'px' },
                    borderStyle: 'solid',
                };
                const radiusValue = {
                    width: { value: '6', unit: 'px' },
                    height: { value: '6', unit: 'px' },
                    isDetail: true,
                };
                borderProps.radiusTopLeft = radiusValue;
                borderProps.radiusTopRight = radiusValue;
                borderProps.radiusBottomLeft = radiusValue;
                borderProps.radiusBottomRight = radiusValue;
                backgroundProps.list.push({
                    id: '3',
                    type: 'gradient',
                    from: `design`,
                    background: 'linear-gradient(180deg, #fdfbfb 0%, #ebedee 100%)',
                    presetId: null,
                    size: '',
                    sizeLength: {
                        width: { value: '', unit: 'auto' },
                        height: { value: '', unit: 'auto' },
                    },
                    repeat: '',
                    attachmentFixed: false,
                    clipText: false,
                    visibility: true,
                    ts: this.ts,
                });
                break;
            }
        }
        if (blockdataInfo.blockdata_frameType !== site4_blockdata_dto_1.BlockData_Info_FrameType.NONE) {
            marginBottom.value = '10';
        }
        properties.marginPadding = (0, prop_spacing_default_value_1.PROP_SPACING_DEFAULT_VALUE)(this.ts, {
            margin: {
                top: { value: '0', unit: 'px' },
                left: { value: '0', unit: 'px' },
                right: { value: '0', unit: 'px' },
                bottom: marginBottom,
                isDetail: true,
                ts: this.ts,
            },
            padding: padding,
        });
        properties.backgrounds = backgroundProps;
        properties.border = borderProps;
        const blockBg = {
            id: `${id}`,
            type: component_type_1.ComponentType.Block,
            name: `${id}`,
            parentId: this.parentSide,
            properties: properties,
            children: [],
            breakpoint: {
                tablet: { ts: this.ts },
                phone: { ts: this.ts },
            },
            ts: this.ts,
        };
        this.components[blockBg.id] = blockBg;
        this.components[this.parentSide].children.push(blockBg.id);
        return blockBg;
    }
    createSizeDiv(parentId) {
        const id = `block${this.block.areaId}_${this.block.blockId}_Size`;
        const blockdataInfo = this.blockData.blockdataInfoJson;
        const properties = migrate_util_1.MigrateUtil.blockProps();
        properties.size = (0, prop_size_default_value_1.PROP_SIZE_DEFAULT_VALUE)(this.ts);
        if (this.parentSide === '__left-side__') {
            properties.size.width = {
                value: '',
                unit: 'auto',
            };
        }
        else {
            properties.size.width = {
                value: `${blockdataInfo?.bwVal ? blockdataInfo.bwVal : ''}`,
                unit: `${!blockdataInfo?.bwVal ? 'auto' : blockdataInfo?.bwType === site4_blockdata_dto_1.BlockData_Info_BwType.PIXEL ? 'px' : '%'}`,
            };
        }
        const blockSize = {
            id: `${id}`,
            type: component_type_1.ComponentType.Block,
            name: `${id}`,
            parentId: parentId,
            properties: properties,
            children: [],
            breakpoint: {
                tablet: { ts: this.ts },
                phone: { ts: this.ts },
            },
            ts: this.ts,
        };
        this.components[blockSize.id] = blockSize;
        this.components[blockSize.parentId].children.push(blockSize.id);
        return blockSize;
    }
    createMarginDiv(parentId) {
        const id = `block${this.block.areaId}_${this.block.blockId}_Margin`;
        const blockdataInfo = this.blockData.blockdataInfoJson;
        const properties = migrate_util_1.MigrateUtil.blockProps();
        properties.layout = this.createLayout();
        const marginInf = blockdataInfo?.blockdata_marginInf
            ? blockdataInfo?.blockdata_marginInf.split(',')
            : ['0'];
        const marginPadding = (0, prop_spacing_default_value_1.PROP_SPACING_DEFAULT_VALUE)(this.ts);
        if (marginInf[0] === '1') {
            marginPadding.padding = {
                left: { value: marginInf[2], unit: 'px' },
                top: { value: marginInf[3], unit: 'px' },
                right: { value: marginInf[4], unit: 'px' },
                bottom: { value: marginInf[5], unit: 'px' },
                isDetail: true,
                ts: this.ts,
            };
        }
        properties.marginPadding = marginPadding;
        const blockMargin = {
            id: `${id}`,
            type: component_type_1.ComponentType.Block,
            name: `${id}`,
            parentId: parentId,
            properties: properties,
            children: [],
            breakpoint: {
                tablet: { ts: this.ts },
                phone: { ts: this.ts },
            },
            ts: this.ts,
        };
        this.components[blockMargin.id] = blockMargin;
        this.components[blockMargin.parentId].children.push(blockMargin.id);
        return blockMargin;
    }
    createLayout() {
        let layout;
        const layoutID = this.blockData.blockdataInfoJson.blockdata_layoutID;
        switch (layoutID) {
            case site4_blockdata_dto_1.BlockData_Layout.PLAIN:
                layout = this.createLayoutPlain();
                break;
            case site4_blockdata_dto_1.BlockData_Layout.ASYMM:
                layout = this.createLayoutAsymm();
                break;
            case site4_blockdata_dto_1.BlockData_Layout.TABLE:
                layout = this.createLayoutTable();
                break;
            case site4_blockdata_dto_1.BlockData_Layout.ALBUM:
                layout = this.createLayoutAlbum();
                break;
            case site4_blockdata_dto_1.BlockData_Layout.TAB:
                layout = this.createLayoutTab();
                break;
            case site4_blockdata_dto_1.BlockData_Layout.ACCORDION:
                layout = this.createLayoutAccordion();
                break;
        }
        return layout;
    }
    createLayoutPlain() {
        let layout;
        const layoutOptID = this.blockData.blockdataInfoJson.blockdata_layoutOptID;
        if (layoutOptID === site4_blockdata_dto_1.BlockData_LayoutOpt.STEP_1) {
            layout = {
                type: 'flex',
                flex: {
                    flexDirection: 'row',
                    verSpacing: { value: '0', unit: 'px' },
                    hozSpacing: { value: '0', unit: 'px' },
                    justifyContent: '',
                    alignContent: '',
                    alignItems: '',
                    flexGrow: '1',
                    flexShrink: '0',
                    flexWrap: 'nowrap',
                },
                grid: null,
                carousel: null,
                ts: this.ts,
            };
        }
        else {
            const column = migrate_util_1.MigrateUtil.getColumns(layoutOptID);
            layout = {
                type: 'grid',
                flex: migrate_util_1.DEFAULT_FLEX_PROP,
                grid: {
                    cols: `${column}`,
                    columnGap: {
                        value: '0',
                        unit: 'px',
                    },
                    rowGap: {
                        value: '0',
                        unit: 'px',
                    },
                    rows: '',
                    sizePerCol: {
                        list: Array.from({ length: column }, () => ({
                            id: '1',
                            size: { unit: 'fr', value: '1' },
                        })),
                    },
                },
                carousel: null,
                ts: this.ts,
            };
        }
        return layout;
    }
    createLayoutAsymm() {
        const layout = {
            type: 'flex',
            flex: {
                flexDirection: 'row',
                verSpacing: { value: '0', unit: 'px' },
                hozSpacing: { value: '0', unit: 'px' },
                justifyContent: '',
                alignContent: '',
                alignItems: '',
                flexGrow: '0',
                flexShrink: '0',
                flexWrap: 'nowrap',
            },
            grid: null,
            carousel: null,
            ts: this.ts,
        };
        return layout;
    }
    createLayoutTable() {
        const layoutOptID = this.blockData.blockdataInfoJson.blockdata_layoutOptID;
        const column = migrate_util_1.MigrateUtil.getColumns(layoutOptID);
        const layout = {
            type: 'grid',
            flex: migrate_util_1.DEFAULT_FLEX_PROP,
            grid: {
                cols: `${column}`,
                columnGap: {
                    value: '0',
                    unit: 'px',
                },
                rowGap: {
                    value: '10',
                    unit: 'px',
                },
                rows: '',
                sizePerCol: {
                    list: Array.from({ length: column }, () => ({
                        id: '1',
                        size: { unit: 'fr', value: '1' },
                    })),
                },
            },
            carousel: null,
            ts: this.ts,
        };
        return layout;
    }
    createLayoutAlbum() {
        return null;
    }
    createLayoutTab() {
        return null;
    }
    createLayoutAccordion() {
        return null;
    }
}
exports.MigrateDataBlockService = MigrateDataBlockService;
//# sourceMappingURL=migrate-data-block.service.js.map