"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePageTable1730961329209 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePageTable1730961329209 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}pages`;
    }
    async up(queryRunner) {
        const childrenColumn = new typeorm_1.TableColumn({
            name: 'children',
            type: 'jsonb',
            isNullable: true,
            isArray: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, childrenColumn);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'children');
    }
}
exports.UpdatePageTable1730961329209 = UpdatePageTable1730961329209;
//# sourceMappingURL=1730961329209-update-page-table.js.map