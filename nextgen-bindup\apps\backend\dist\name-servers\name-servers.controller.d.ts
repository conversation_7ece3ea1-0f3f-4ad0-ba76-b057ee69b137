import { NameServersService } from './name-servers.service';
import { NameServerEntity } from './entities/name-server.entity';
export declare class NameServersController {
    private readonly nameServersService;
    constructor(nameServersService: NameServersService);
    create(data: NameServerEntity): Promise<NameServerEntity>;
    update(id: string, data: Partial<NameServerEntity>): Promise<NameServerEntity>;
    delete(id: string): Promise<boolean>;
    findBySiteId(siteId: string): Promise<NameServerEntity[]>;
    findByProjectId(projectId: string): Promise<NameServerEntity[]>;
}
