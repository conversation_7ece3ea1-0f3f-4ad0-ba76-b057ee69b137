"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubDomainEntity = void 0;
const typeorm_1 = require("typeorm");
let SubDomainEntity = class SubDomainEntity {
};
exports.SubDomainEntity = SubDomainEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], SubDomainEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'projectId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], SubDomainEntity.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'subdomain',
        type: 'varchar',
        length: 255,
        nullable: false,
    }),
    __metadata("design:type", String)
], SubDomainEntity.prototype, "subdomain", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'directory',
        type: 'varchar',
        length: 255,
        nullable: true,
    }),
    __metadata("design:type", String)
], SubDomainEntity.prototype, "directory", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'https',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], SubDomainEntity.prototype, "https", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'ssl',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], SubDomainEntity.prototype, "ssl", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'siteId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], SubDomainEntity.prototype, "siteId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], SubDomainEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], SubDomainEntity.prototype, "updatedAt", void 0);
exports.SubDomainEntity = SubDomainEntity = __decorate([
    (0, typeorm_1.Entity)('subdomains', { schema: process.env.DATABASE_SCHEMA })
], SubDomainEntity);
//# sourceMappingURL=sub-domain.entity.js.map