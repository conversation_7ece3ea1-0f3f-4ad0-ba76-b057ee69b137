"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSubdomainsTable1741679546459 = void 0;
const typeorm_1 = require("typeorm");
class UpdateSubdomainsTable1741679546459 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}subdomains`;
    }
    async up(queryRunner) {
        const column = new typeorm_1.TableColumn({
            name: 'siteId',
            type: 'integer',
            isNullable: false,
        });
        await queryRunner.addColumn(this.TABLE_NAME, column);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
    }
}
exports.UpdateSubdomainsTable1741679546459 = UpdateSubdomainsTable1741679546459;
//# sourceMappingURL=1741679546459-update-subdomains-table.js.map