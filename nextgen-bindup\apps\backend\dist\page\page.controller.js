"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageController = void 0;
const common_1 = require("@nestjs/common");
const page_service_1 = require("./page.service");
const page_entity_1 = require("./entities/page.entity");
const publish_service_1 = require("../publish/publish.service");
const zip_service_1 = require("./zip.service");
const auth_guard_1 = require("../auth/auth.guard");
const user_decorator_1 = require("../auth/user.decorator");
let PageController = class PageController {
    constructor(pageService, publishService, zipService) {
        this.pageService = pageService;
        this.publishService = publishService;
        this.zipService = zipService;
    }
    async zipAndDownload(projectId, siteId, res) {
        try {
            const forderPath = await this.publishService.buildStaticSite(+projectId, +siteId, {
                pageIds: [],
            });
            const zipBuffer = await this.zipService.zipFolder(forderPath);
            res.setHeader('Content-Type', 'application/zip');
            res.setHeader('Content-Disposition', `attachment; filename=${siteId}-archive.zip`);
            res.status(common_1.HttpStatus.OK).send(zipBuffer);
        }
        catch (error) {
            console.error(error);
            res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).send('Error zipping folder');
        }
    }
    async createPage(user, projectId, pageData) {
        pageData.userId = user.userId;
        return this.pageService.createPage(+projectId, pageData);
    }
    async updatePage(pageId, pageData) {
        return this.pageService.updatePage(+pageId, pageData);
    }
    async deletePage(pageId) {
        return this.pageService.deletePage(+pageId);
    }
    async getPagesBySiteId(siteId) {
        return this.pageService.getPagesBySiteId(+siteId);
    }
    async getPageByd(pageId) {
        return this.pageService.getById(+pageId);
    }
    async createBlog(user, projectId, data) {
        data.userId = user.userId;
        return this.pageService.createBlog(+projectId, data);
    }
};
exports.PageController = PageController;
__decorate([
    (0, common_1.Get)('download/:projectId/:siteId'),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Param)('siteId')),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], PageController.prototype, "zipAndDownload", null);
__decorate([
    (0, common_1.Post)('create/:projectId'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, user_decorator_1.ExtractUser)()),
    __param(1, (0, common_1.Param)('projectId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, page_entity_1.PageEntity]),
    __metadata("design:returntype", Promise)
], PageController.prototype, "createPage", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PageController.prototype, "updatePage", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PageController.prototype, "deletePage", null);
__decorate([
    (0, common_1.Get)('by-site/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PageController.prototype, "getPagesBySiteId", null);
__decorate([
    (0, common_1.Get)('one/:pageId'),
    __param(0, (0, common_1.Param)('pageId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PageController.prototype, "getPageByd", null);
__decorate([
    (0, common_1.Post)('blog/:projectId'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, user_decorator_1.ExtractUser)()),
    __param(1, (0, common_1.Param)('projectId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], PageController.prototype, "createBlog", null);
exports.PageController = PageController = __decorate([
    (0, common_1.Controller)('pages'),
    __metadata("design:paramtypes", [page_service_1.PageService,
        publish_service_1.PublishService,
        zip_service_1.ZipService])
], PageController);
//# sourceMappingURL=page.controller.js.map