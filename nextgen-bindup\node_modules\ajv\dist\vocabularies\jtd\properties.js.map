{"version": 3, "file": "properties.js", "sourceRoot": "", "sources": ["../../../lib/vocabularies/jtd/properties.ts"], "names": [], "mappings": ";;;AAOA,kCAA0E;AAC1E,6CAAoE;AACpE,mDAA6D;AAC7D,yCAAwC;AACxC,yCAA8C;AAC9C,mCAAwE;AAExE,IAAK,SAGJ;AAHD,WAAK,SAAS;IACZ,sCAAyB,CAAA;IACzB,gCAAmB,CAAA;AACrB,CAAC,EAHI,SAAS,KAAT,SAAS,QAGb;AAWY,QAAA,KAAK,GAA2B;IAC3C,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;QACf,MAAM,EAAC,MAAM,EAAC,GAAG,GAAG,CAAA;QACpB,OAAO,MAAM,CAAC,SAAS;YACrB,CAAC,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC,UAAU;gBACzC,CAAC,CAAC,qCAAqC;gBACvC,CAAC,CAAC,uBAAuB,MAAM,CAAC,eAAe,GAAG;YACpD,CAAC,CAAC,IAAA,wBAAgB,EAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;IACrC,CAAC;IACD,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE;QACd,MAAM,EAAC,MAAM,EAAC,GAAG,GAAG,CAAA;QACpB,OAAO,MAAM,CAAC,SAAS;YACrB,CAAC,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC,UAAU;gBACzC,CAAC,CAAC,IAAA,WAAC,EAAA,WAAW,MAAM,CAAC,SAAS,yBAAyB,MAAM,CAAC,kBAAkB,GAAG;gBACnF,CAAC,CAAC,IAAA,WAAC,EAAA,WAAW,MAAM,CAAC,SAAS,sBAAsB,MAAM,CAAC,eAAe,GAAG;YAC/E,CAAC,CAAC,IAAA,uBAAe,EAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;IACpC,CAAC;CACF,CAAA;AAED,MAAM,GAAG,GAA0B;IACjC,OAAO,EAAE,YAAY;IACrB,UAAU,EAAE,QAAQ;IACpB,KAAK,EAAL,aAAK;IACL,IAAI,EAAE,kBAAkB;CACzB,CAAA;AAED,0CAA0C;AAC1C,sDAAsD;AACtD,iFAAiF;AACjF,IAAI;AAEJ,SAAgB,kBAAkB,CAAC,GAAe;IAChD,IAAA,wBAAa,EAAC,GAAG,CAAC,CAAA;IAClB,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;IACzC,MAAM,EAAC,oBAAoB,EAAE,QAAQ,EAAC,GAAG,YAAY,CAAA;IACrD,IAAI,EAAE,CAAC,gBAAgB,IAAI,QAAQ;QAAE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;IAClG,IAAI,gBAAgB,EAAE,EAAE,CAAC;QACvB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAA;IAC/E,CAAC;IACD,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAA;IAC7D,MAAM,CAAC,WAAW,EAAE,aAAa,CAAC,GAAG,gBAAgB,CAAC,oBAAoB,CAAC,CAAA;IAC3E,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,IAAI,oBAAoB,EAAE,CAAC;QAClF,OAAM;IACR,CAAC;IAED,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GACjB,EAAE,CAAC,gBAAgB,KAAK,SAAS;QAC/B,CAAC,CAAC,IAAA,8BAAmB,EAAC,GAAG,EAAE,IAAI,CAAC;QAChC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,CAAA;IACrC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAChB,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;QACjC,aAAa,CAAC,UAAU,EAAE,YAAY,EAAE,IAAI,CAAC,CAAA;QAC7C,aAAa,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAAA;QAClD,IAAI,CAAC,oBAAoB;YAAE,kBAAkB,EAAE,CAAA;IACjD,CAAC,CAAC,CACH,CAAA;IACD,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAEf,SAAS,gBAAgB;QACvB,MAAM,KAAK,GAAG,YAAY,CAAC,UAA6C,CAAA;QACxE,MAAM,QAAQ,GAAG,YAAY,CAAC,kBAAqD,CAAA;QACnF,IAAI,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC;YAAE,OAAO,KAAK,CAAA;QACtC,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC;YACtB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAAE,OAAO,IAAI,CAAA;QACpE,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,SAAS,gBAAgB,CAAC,OAAe;QACvC,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,CAAA;QACpC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAA,0BAAmB,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QACvD,IAAI,EAAE,CAAC,gBAAgB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACxE,MAAM,IAAI,KAAK,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAA;QAC9D,CAAC;QACD,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAA,wBAAiB,EAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;IACpB,CAAC;IAED,SAAS,aAAa,CAAC,KAAe,EAAE,OAAe,EAAE,QAAkB;QACzE,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,GAAG,CAAC,EAAE,CACJ,IAAA,qBAAc,EAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,EACtD,GAAG,EAAE,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,EAChD,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAC5B,CAAA;YACD,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,CAAA;QAChB,CAAC;QAED,SAAS,eAAe,CAAC,IAAY;YACnC,IAAI,QAAQ,EAAE,CAAC;gBACb,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;gBACzB,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,EAAC,SAAS,EAAE,SAAS,CAAC,OAAO,EAAE,eAAe,EAAE,IAAI,EAAC,EAAE,EAAC,UAAU,EAAE,IAAI,EAAC,CAAC,CAAA;YAC7F,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAED,SAAS,mBAAmB,CAAC,IAAY,EAAE,OAAe,EAAE,MAAY;QACtE,GAAG,CAAC,SAAS,CACX;YACE,OAAO;YACP,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,IAAI;SACf,EACD,MAAM,CACP,CAAA;IACH,CAAC;IAED,SAAS,kBAAkB;QACzB,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,GAAS,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAA;YAC9E,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,EAAE,WAAW,EAAE,oBAAoB,CAAC,CAAA;YACvE,MAAM,KAAK,GACT,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAA,aAAG,EAAC,OAAO,EAAE,UAAU,CAAC,CAAA;YAC1F,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBACjB,IAAI,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC7B,GAAG,CAAC,IAAI,CAAC,IAAA,WAAC,EAAA,UAAU,IAAI,IAAI,GAAG,GAAG,CAAC,CAAA;gBACrC,CAAC;qBAAM,CAAC;oBACN,GAAG,CAAC,KAAK,CACP,KAAK,EACL,EAAC,SAAS,EAAE,SAAS,CAAC,UAAU,EAAE,kBAAkB,EAAE,GAAG,EAAC,EAC1D,EAAC,YAAY,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,EAAC,CACxC,CAAA;oBACD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS;wBAAE,GAAG,CAAC,KAAK,EAAE,CAAA;gBACrC,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,SAAS,YAAY,CACnB,GAAS,EACT,KAAe,EACf,OAAe,EACf,gBAAyB;QAEzB,IAAI,UAA0B,CAAA;QAC9B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,gDAAgD;YAChD,MAAM,WAAW,GAAG,IAAA,qBAAc,EAAC,EAAE,EAAE,YAAY,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAA;YACtE,UAAU,GAAG,IAAA,aAAG,EAAC,IAAA,oBAAa,EAAC,GAAG,EAAE,WAAmB,EAAE,GAAG,CAAC,CAAC,CAAA;YAC9D,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;gBACnC,UAAU,GAAG,IAAA,aAAG,EAAC,UAAU,EAAE,IAAA,WAAC,EAAA,GAAG,GAAG,QAAQ,gBAAgB,EAAE,CAAC,CAAA;YACjE,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC1D,MAAM,EAAE,GAAG,gBAAgB,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;YACpF,UAAU,GAAG,IAAA,aAAG,EAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA;QACxD,CAAC;aAAM,CAAC;YACN,UAAU,GAAG,IAAI,CAAA;QACnB,CAAC;QACD,OAAO,UAAU,CAAA;IACnB,CAAC;AACH,CAAC;AA1HD,gDA0HC;AAED,kBAAe,GAAG,CAAA"}