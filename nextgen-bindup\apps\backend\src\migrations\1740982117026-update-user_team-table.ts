import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateUserTeamTable1740982117026 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}user_team`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const column: TableColumn = new TableColumn({
      name: 'isAdmin',
      type: 'boolean',
      default: false,
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, column);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'isAdmin');
  }
}
