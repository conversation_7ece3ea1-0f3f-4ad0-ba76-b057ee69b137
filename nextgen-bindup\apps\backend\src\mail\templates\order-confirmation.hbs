<!DOCTYPE html>
<html lang="ja">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ orderCompletionSetting.emailSubject }} #{{ order.id }}</title>
    <style>
        body {
            font-family: "Hiragino Kaku Gothic Pro", "ヒラギノ角ゴ Pro W3", <PERSON><PERSON>, "メイリオ", "MS PGothic", "ＭＳ Ｐゴシック", sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .header {
            padding: 10px;
            text-align: center;
        }

        .shop-logo {
            max-width: 200px;
            max-height: 80px;
            margin-bottom: 15px;
        }

        .section-title {
            color: #555;
            margin-top: 20px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }

        ul {
            list-style: none;
            padding: 0;
        }

        li {
            margin-bottom: 8px;
        }

        .total {
            font-weight: bold;
            font-size: 1.1em;
        }

        a {
            color: #007bff;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        .payment-info {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        .payment-info pre {
            white-space: pre-wrap;
            font-family: inherit;
            margin: 0;
        }

        .email-header,
        .email-footer {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            line-height: 1.8;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            {{#if shopInfo.logoImages.[0]}}
            <img src="{{ shopInfo.logoImages.[0] }}" alt="{{ shopInfo.shopName }}" class="shop-logo">
            {{/if}}
        </div>

        {{#if orderCompletionSetting.emailHeader}}
        <div class="email-header">
            {{{ orderCompletionSetting.emailHeader }}}
        </div>
        {{/if}}

        <p>{{ order.lastName }} {{ order.firstName }} 様</p>
        <p>この度は当店をご利用いただき、誠にありがとうございます。</p>
        <p>ご注文番号 <strong>#{{ order.id }}</strong> の内容をご確認ください。</p>

        <h3 class="section-title">ご注文情報</h3>
        <p>注文番号: <strong>#{{ order.id }}</strong></p>
        <p>注文日時: <strong>{{ order.createdAt }}</strong></p>

        {{#if paymentMethod}}
        <h3 class="section-title">お支払い情報</h3>
        <div class="payment-info">
            <p><strong>お支払い方法:</strong> {{ order.paymentMethodType }}</p>

            {{#if paymentMethod.bankTransfer.isEnabled}}
            {{#if order.isBankTransfer}}
            <pre>{{ paymentMethod.bankTransfer.bankAccount }}</pre>
            <pre>{{ paymentMethod.bankTransfer.description }}</pre>
            {{/if}}
            {{/if}}

            {{#if paymentMethod.postalTransfer.isEnabled}}
            {{#if order.isPostalTransfer}}
            <pre>{{ paymentMethod.postalTransfer.bankAccount }}</pre>
            <pre>{{ paymentMethod.postalTransfer.description }}</pre>
            {{/if}}
            {{/if}}
        </div>
        {{/if}}

        <h3 class="section-title">ご連絡先</h3>
        <p>お名前: {{ order.lastName }} {{ order.firstName }}</p>
        <p>メールアドレス: {{ order.email }}</p>
        <p>電話番号: {{ order.phoneNumber }}</p>
        <p>郵便番号: {{ order.postalCode }}</p>
        <p>都道府県: {{ order.prefecture }}</p>
        <p>住所1: {{ order.addressLine1 }}</p>
        <p>住所2: {{ order.addressLine2 }}</p>

        <h3 class="section-title">配送先</h3>
        <p>お名前: {{ order.shippingLastName }} {{ order.shippingFirstName }}</p>
        <p>メールアドレス: {{ order.shippingEmail }}</p>
        <p>電話番号: {{ order.shippingPhoneNumber }}</p>
        <p>郵便番号: {{ order.shippingPostalCode }}</p>
        <p>都道府県: {{ order.shippingPrefecture }}</p>
        <p>住所1: {{ order.shippingAddressLine1 }}</p>
        <p>住所2: {{ order.shippingAddressLine2 }}</p>

        <h3 class="section-title">その他情報</h3>
        <p>備考: {{ order.additionalInformation }} </p>

        <h3 class="section-title">ご注文内容</h3>
        <ul>
            {{#each order.orderItems}}
            <li>
                <strong>{{ this.productName }}</strong>
                {{#if this.attributes}}
                {{#if this.attributes.attribute1}}
                <br>&nbsp;&nbsp;&nbsp;&nbsp;{{ this.attributes.variantName1 }}: {{ this.attributes.attribute1 }}
                {{#if this.attributes.attribute2}}
                / {{ this.attributes.variantName2 }}: {{ this.attributes.attribute2 }}
                {{/if}}
                {{/if}}
                {{/if}}
                <br>&nbsp;&nbsp;&nbsp;&nbsp;(数量: {{ this.quantity }}, 価格: {{ this.displayPrice }}円, 小計: {{ this.subtotal
                }}円)

                {{#if this.product}}
                {{#if this.product.fileDownload}}
                {{#if this.product.fileDownload.url}}
                <br>&nbsp;&nbsp;&nbsp;&nbsp;ダウンロードリンク:
                <a href="{{this.product.fileDownload.url}}">
                    {{ this.product.fileDownload.name }}をダウンロード
                </a>
                {{/if}}
                {{/if}}
                {{/if}}
            </li>
            {{/each}}
        </ul>

        <h3 class="section-title">お支払い金額</h3>
        <p>商品小計: {{ order.subtotal }}円</p>
        <p>送料: {{ order.shippingFee }}円</p>
        <p class="total">合計金額: <strong>{{ order.total }}円</strong></p>

        <hr>

        {{#if orderCompletionSetting.emailFooter}}
        <div class="email-footer">
            {{{ orderCompletionSetting.emailFooter }}}
        </div>
        {{/if}}
    </div>
</body>

</html>