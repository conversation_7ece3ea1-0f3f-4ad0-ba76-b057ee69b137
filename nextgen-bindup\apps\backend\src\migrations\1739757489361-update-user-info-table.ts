import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateuserInfoTable1739757489361 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}user_info`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const column: TableColumn = new TableColumn({
      name: 'recentlySite',
      type: 'jsonb',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, column);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'recentlySite');
  }
}
