import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('subdomains', { schema: process.env.DATABASE_SCHEMA })
export class SubDomainEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'projectId',
    type: 'integer',
    nullable: false,
  })
  projectId: number;

  @Column({
    name: 'subdomain',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  subdomain: string;

  @Column({
    name: 'directory',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  directory: string;

  @Column({
    name: 'https',
    type: 'boolean',
    nullable: true,
  })
  https: boolean;

  @Column({
    name: 'ssl',
    type: 'boolean',
    nullable: true,
  })
  ssl: boolean;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
