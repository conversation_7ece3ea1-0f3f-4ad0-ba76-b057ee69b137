"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetsModule = void 0;
const common_1 = require("@nestjs/common");
const asset_controller_1 = require("./asset.controller");
const asset_service_1 = require("./asset.service");
const asset_entity_1 = require("./entities/asset.entity");
const typeorm_1 = require("@nestjs/typeorm");
const project_module_1 = require("../project/project.module");
let AssetsModule = class AssetsModule {
};
exports.AssetsModule = AssetsModule;
exports.AssetsModule = AssetsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([asset_entity_1.AssetEntity]), project_module_1.ProjectModule],
        controllers: [asset_controller_1.AssetController],
        providers: [asset_service_1.AssetService],
        exports: [asset_service_1.AssetService],
    })
], AssetsModule);
//# sourceMappingURL=asset.module.js.map