"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetComponentController = void 0;
const common_1 = require("@nestjs/common");
const asset_component_service_1 = require("./asset-component.service");
const auth_guard_1 = require("../auth/auth.guard");
const asset_component_entity_1 = require("./entities/asset-component.entity");
let AssetComponentController = class AssetComponentController {
    constructor(assetComponentService) {
        this.assetComponentService = assetComponentService;
    }
    async create(assetEntity) {
        return await this.assetComponentService.create(assetEntity);
    }
    async update(assetComponentId, data) {
        return await this.assetComponentService.update(assetComponentId, data);
    }
    async getById(assetComponentId) {
        return await this.assetComponentService.findById(assetComponentId);
    }
    async getByProjectId(projectId) {
        return await this.assetComponentService.findByProjectId(+projectId);
    }
    async getBySiteId(projectId, siteId) {
        return await this.assetComponentService.findBySiteId(+projectId, +siteId);
    }
    async delete(assetComponentId) {
        return await this.assetComponentService.delete(assetComponentId);
    }
};
exports.AssetComponentController = AssetComponentController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [asset_component_entity_1.AssetComponent]),
    __metadata("design:returntype", Promise)
], AssetComponentController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('update/:assetComponentId'),
    __param(0, (0, common_1.Param)('assetComponentId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AssetComponentController.prototype, "update", null);
__decorate([
    (0, common_1.Get)('one/:assetComponentId'),
    __param(0, (0, common_1.Param)('assetComponentId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetComponentController.prototype, "getById", null);
__decorate([
    (0, common_1.Get)('project/:projectId'),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetComponentController.prototype, "getByProjectId", null);
__decorate([
    (0, common_1.Get)('site/:projectId/:siteId'),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AssetComponentController.prototype, "getBySiteId", null);
__decorate([
    (0, common_1.Delete)(':assetComponentId'),
    __param(0, (0, common_1.Param)('assetComponentId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetComponentController.prototype, "delete", null);
exports.AssetComponentController = AssetComponentController = __decorate([
    (0, common_1.Controller)('asset-component'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [asset_component_service_1.AssetComponentService])
], AssetComponentController);
//# sourceMappingURL=asset-component.controller.js.map