export interface Site0_Site {
    tmpSiteId: number;
    siteId: number;
    name: string;
    dispFlg: number;
    openFlg: number;
    webDesign: number;
    webfontinfo: string;
    webfontinfoJson: Site_WebFontInfo;
    props: string;
    propsJson: Site_Props;
    openGraph?: string;
    openGraphJson: Site_OpenGraph[];
    siteVersion: string;
    version: number;
    lastPublishDate: string;
    delFlg: number;
    insDate: string;
    insUserId: number;
    updDate: string;
    updUserId: number;
}
export interface Site_WebFontInfo {
    BlockDef: string;
    Midashi1: string;
    Midashi2: string;
    Midashi3: string;
    Lead: string;
    BlockQuote: string;
    Box: string;
    Note: string;
    Credit: string;
    PageTitle: string;
    webfont1: string;
    webfont2: string;
    webfont3: string;
    webfont4: string;
    webfont5: string;
    webfont6: string;
    webfont7: string;
    webfont8: string;
    webfont9: string;
    webfont10: string;
}
export interface Site_Props {
    crdt: string;
    updt: string;
    upct: string;
    url: string;
    svnm: string;
    svus: string;
    svps: string;
    svdr: string;
    gatc: string;
    svtp: string;
    svpt: string;
    svpv: string;
    fvf: string;
    fvth: string;
    wcf: string;
    wcth: string;
    wcrf: string;
    selectedEditMethod: string;
}
export interface Site_OpenGraph {
    key: string;
    value: string;
}
