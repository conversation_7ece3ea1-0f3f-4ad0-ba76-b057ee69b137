import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { AssetComponentService } from './asset-component.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { AssetComponent } from './entities/asset-component.entity';

@Controller('asset-component')
@UseGuards(AuthGuard)
export class AssetComponentController {
  constructor(private readonly assetComponentService: AssetComponentService) {}

  @Post('create')
  async create(@Body() assetEntity: AssetComponent) {
    return await this.assetComponentService.create(assetEntity);
  }

  @Put('update/:assetComponentId')
  async update(
    @Param('assetComponentId') assetComponentId: string,
    @Body() data: Partial<AssetComponent>,
  ) {
    return await this.assetComponentService.update(assetComponentId, data);
  }

  @Get('one/:assetComponentId')
  async getById(@Param('assetComponentId') assetComponentId: string) {
    return await this.assetComponentService.findById(assetComponentId);
  }

  @Get('project/:projectId')
  async getByProjectId(@Param('projectId') projectId: string) {
    return await this.assetComponentService.findByProjectId(+projectId);
  }

  @Get('site/:projectId/:siteId')
  async getBySiteId(
    @Param('projectId') projectId: string,
    @Param('siteId') siteId: string,
  ) {
    return await this.assetComponentService.findBySiteId(+projectId, +siteId);
  }

  @Delete(':assetComponentId')
  async delete(@Param('assetComponentId') assetComponentId: string) {
    return await this.assetComponentService.delete(assetComponentId);
  }
}
