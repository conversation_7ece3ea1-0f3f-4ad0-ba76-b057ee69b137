import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { FontsetService } from './fontset.service';
import { FontsetEntity } from './entities/fontset.entity';

@Controller('fontset')
@UseGuards(AuthGuard)
export class FontsetController {
  constructor(private readonly fontsetService: FontsetService) {}

  @Post('create')
  async create(@Body() assetEntity: FontsetEntity) {
    return await this.fontsetService.create(assetEntity);
  }

  @Put('update/:fontsetId')
  async update(
    @Param('fontsetId') fontsetId: string,
    @Body() data: Partial<FontsetEntity>,
  ) {
    return await this.fontsetService.update(+fontsetId, data);
  }

  @Get('one/:fontsetId')
  async getById(@Param('fontsetId') fontsetId: string) {
    return await this.fontsetService.findById(+fontsetId);
  }

  @Get('project/:projectId')
  async getByProjectId(@Param('projectId') projectId: string) {
    return await this.fontsetService.findByProjectId(+projectId);
  }

  @Get('site/:projectId/:siteId')
  async getBySiteId(
    @Param('projectId') projectId: string,
    @Param('siteId') siteId: string,
  ) {
    return await this.fontsetService.findBySiteId(+projectId, +siteId);
  }

  @Delete(':fontsetId')
  async delete(@Param('fontsetId') fontsetId: string) {
    return await this.fontsetService.delete(+fontsetId);
  }
}
