"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SiteService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const site_entity_1 = require("./entities/site.entity");
const typeorm_2 = require("typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
const config_1 = require("@nestjs/config");
const project_service_1 = require("../project/project.service");
const page_entity_1 = require("../page/entities/page.entity");
const cms_collection_service_1 = require("../cms-collection/cms-collection.service");
const cms_collection_entity_1 = require("../cms-collection/entities/cms-collection.entity");
const cms_collection_items_entity_1 = require("../cms-collection-items/entities/cms-collection-items.entity");
const page_type_1 = require("../page/types/page.type");
let SiteService = class SiteService {
    constructor(configService, projectService, cmsCollectionService, dataSource) {
        this.configService = configService;
        this.projectService = projectService;
        this.cmsCollectionService = cmsCollectionService;
        this.dataSource = dataSource;
    }
    async initCollection(queryRunner, site) {
        const now = new Date();
        let category = await queryRunner.manager
            .getRepository(cms_collection_entity_1.CmsCollectionEntity)
            .findOneBy({
            siteId: site.id,
            dataType: 4,
        });
        let animalItem = null;
        let wildItem = null;
        if (!category) {
            const collection = new cms_collection_entity_1.CmsCollectionEntity();
            collection.siteId = site.id;
            collection.name = 'Categories';
            collection.slug = {
                id: -1,
                desc: '',
                type: 'slug',
                extra: { baseOn: 1 },
                label: 'Slug',
                required: true,
            };
            collection.struct = [
                {
                    id: 1,
                    desc: '',
                    type: 'text',
                    extra: {
                        multi: false,
                        default: '',
                        localize: false,
                        placeholder: '',
                    },
                    label: 'Category name',
                    required: false,
                },
            ];
            collection.rootUserId = site.userId;
            collection.userId = site.userId;
            collection.dataType = 4;
            collection.createdAt = now;
            collection.updatedAt = now;
            category = await queryRunner.manager
                .getRepository(cms_collection_entity_1.CmsCollectionEntity)
                .save(collection);
            animalItem = new cms_collection_items_entity_1.CmsCollectionItemEntity();
            animalItem.cmsCollectionId = category.id;
            animalItem.slug = 'animal';
            animalItem.title = 'Animal';
            animalItem.data = { '1': { id: 1747210074251, value: 'Animal' } };
            animalItem.status = 'draft';
            animalItem.siteId = site.id;
            animalItem.rootUserId = site.userId;
            animalItem.userId = site.userId;
            animalItem = await queryRunner.manager
                .getRepository(cms_collection_items_entity_1.CmsCollectionItemEntity)
                .save(animalItem);
            wildItem = new cms_collection_items_entity_1.CmsCollectionItemEntity();
            wildItem.cmsCollectionId = category.id;
            wildItem.slug = 'wild';
            wildItem.title = 'Wild';
            wildItem.data = { '1': { id: 1747210074251, value: 'Wild' } };
            wildItem.status = 'draft';
            wildItem.siteId = site.id;
            wildItem.rootUserId = site.userId;
            wildItem.userId = site.userId;
            wildItem = await queryRunner.manager
                .getRepository(cms_collection_items_entity_1.CmsCollectionItemEntity)
                .save(wildItem);
        }
        const article = await queryRunner.manager
            .getRepository(cms_collection_entity_1.CmsCollectionEntity)
            .findOneBy({
            siteId: site.id,
            dataType: 3,
        });
        if (!article) {
            const collection = new cms_collection_entity_1.CmsCollectionEntity();
            collection.siteId = site.id;
            collection.name = 'Articles';
            collection.slug = {
                id: -1,
                desc: '',
                type: 'slug',
                extra: { baseOn: 1744702444500 },
                label: 'Slug',
                required: true,
            };
            collection.struct = [
                {
                    id: 1744702444500,
                    desc: '',
                    type: 'text',
                    extra: {
                        multi: false,
                        default: '',
                        localize: false,
                        placeholder: '',
                    },
                    label: 'Title',
                    required: false,
                },
                {
                    id: 1745552845835,
                    desc: '',
                    type: 'text',
                    extra: {
                        multi: true,
                        default: '',
                        localize: false,
                        placeholder: '',
                    },
                    label: 'Highlight',
                    required: false,
                },
                {
                    id: 1744702445997,
                    desc: '',
                    type: 'media',
                    extra: {},
                    label: 'Post image',
                    required: false,
                },
                {
                    id: 1744702441737,
                    desc: '',
                    type: 'richtext',
                    extra: { localize: false },
                    label: 'Body',
                    required: false,
                },
                {
                    id: 1744705577720,
                    desc: '',
                    type: 'db-linkage',
                    extra: {
                        default: [],
                        connectType: 'one-to-many',
                        collectionId: category.id,
                    },
                    label: 'Categories',
                    required: false,
                },
                {
                    id: 1744702447979,
                    desc: '',
                    type: 'date',
                    extra: { time: false },
                    label: 'Published date',
                    required: false,
                },
                {
                    id: 1744702452061,
                    desc: '',
                    type: 'toggle',
                    extra: { default: false },
                    label: 'Disabled',
                    required: false,
                },
                {
                    id: 1746670105343,
                    desc: '',
                    type: 'text',
                    extra: {
                        multi: false,
                        default: '',
                        localize: false,
                        placeholder: '',
                    },
                    label: 'Author',
                    required: false,
                },
            ];
            collection.rootUserId = site.userId;
            collection.userId = site.userId;
            collection.dataType = 3;
            collection.createdAt = now;
            collection.updatedAt = now;
            await await queryRunner.manager
                .getRepository(cms_collection_entity_1.CmsCollectionEntity)
                .save(collection);
            const categoryIds = [];
            if (animalItem?.id)
                categoryIds.push(animalItem.id);
            if (wildItem?.id)
                categoryIds.push(wildItem.id);
            const bisonItem = new cms_collection_items_entity_1.CmsCollectionItemEntity();
            bisonItem.cmsCollectionId = collection.id;
            bisonItem.slug = 'plains-bison';
            bisonItem.title = 'Plains Bison';
            bisonItem.data = {
                '1744702441737': {
                    id: 1745549365057,
                    value: '<p>Prior to European colonization plains bison are estimated to have numbered between 30 million and 60 million animals and were the widest-ranging large mammal in North America. Bison were (and remain) central in the lives and traditions of many Native nations and an umbrella species for many plants and animals sharing its habitat. By 1889, only 512 plains bison remained after the ravages of westward expansion, market demand, and a deliberate effort by the US Government to eliminate the bison to subdue the Native people that relied so heavily upon them. In response to their tragic decline, conservationists and Indigenous peoples successfully brought the plains bison back from the brink of extinction.</p><p>Thanks to their efforts, by 1935, the population had risen to approximately 20,000 bison, and many were restored as wildlife to refuges and parks throughout North America. In honor of the role that this majestic species plays as an icon for the lands and people of the United States, the bison was formally designated as the national mammal in 2016.&nbsp;However, much work remains to restore bison populations. In fact, the number of bison held in “conservation herds” is currently no greater than it was in 1935.</p><p>WWF partners with Native communities seeking to restore bison to their lands. Our goal is to support bison restoration efforts that foster community benefits such as increased access to bison, and ecological and economic sustainability. Currently, WWF works closely with partners such as the Fort Peck Assiniboine and Sioux Tribes, Fort Belknap Indian Community, and the Sicangu Lakota Nation in support of the expressed values, needs, and aspirations of these communities. Additionally, WWF works with the US National Park Service and Parks Canada on bison restoration efforts.&nbsp;</p>',
                },
                '1744702444500': { id: 1745549365055, value: 'Plains Bison' },
                '1744702445997': {
                    id: 1745549365056,
                    value: [
                        'https://pub-8a6bc558fcab4fe4a4ec5f03b0a59802.r2.dev/1-1745549384715-bison.jpg',
                    ],
                },
                '1744702447979': { id: 1745549365059, value: '' },
                '1744702449679': { id: 1745549365060, value: '' },
                '1744702452061': { id: 1745549365061, value: false },
                '1744705577720': {
                    id: 1745549365058,
                    value: categoryIds,
                },
                '1745552845835': {
                    id: 1745552845835,
                    value: 'Prior to European colonization plains bison are estimated to have numbered between 30 million and 60 million animals and were the widest-ranging large mammal in North America. Bison were (and remain) central in the lives and traditions of many Native nations and an umbrella species for many plants and animals sharing its habitat. By 1889, only 512 plains bison remained after the ravages of westward expansion, market demand, and a deliberate effort by the US Government to eliminate the bison to subdue the Native people that relied so heavily upon them. In response to their tragic decline, conservationists and Indigenous peoples successfully brought the plains bison back from the brink of extinction.',
                },
                '1746670105343': { id: 1746670105343, value: 'Kaito' },
            };
            bisonItem.status = 'draft';
            bisonItem.siteId = site.id;
            bisonItem.rootUserId = site.userId;
            bisonItem.userId = site.userId;
            await queryRunner.manager
                .getRepository(cms_collection_items_entity_1.CmsCollectionItemEntity)
                .save(bisonItem);
            const seaTutrleItem = new cms_collection_items_entity_1.CmsCollectionItemEntity();
            seaTutrleItem.cmsCollectionId = collection.id;
            seaTutrleItem.slug = 'sea-turtle';
            seaTutrleItem.title = 'Sea Turtle';
            seaTutrleItem.data = {
                '1744702441737': {
                    id: 1745549470652,
                    value: "<p>Seven different species of sea (or marine) turtles grace our ocean waters, from the shallow seagrass beds of the Indian Ocean, to the colorful reefs of the Coral Triangle and the sandy beaches of the Eastern Pacific. While these highly migratory species periodically come ashore to either bask or nest, sea turtles spend the bulk of their lives in the ocean. WWF's work on sea turtles focuses on five of those species: green, hawksbill, loggerhead, leatherback, and olive ridley.</p><p>Over the last 200 years, human activities have tipped the scales against the survival of these ancient mariners. Slaughtered for their eggs, meat, skin, and shells, sea turtles suffer from poaching and over-exploitation. They also face habitat destruction and accidental capture—known as bycatch—in fishing gear. Climate change has an impact on turtle nesting sites; it alters sand temperatures, which then affects the sex of hatchlings. Three species of sea turtle are now classified as endangered, with two of those being critically endangered.</p><p>WWF is committed to stopping the decline of sea turtles and works for the recovery of the species. We work to secure environments in which both turtles and the people that depend upon them can survive.</p>",
                },
                '1744702444500': { id: 1745549470650, value: 'Sea Turtle' },
                '1744702445997': {
                    id: 1745549470651,
                    value: [
                        'https://pub-8a6bc558fcab4fe4a4ec5f03b0a59802.r2.dev/1-1745549546522-turtle.jpg',
                    ],
                },
                '1744702447979': { id: 1745549470654, value: '' },
                '1744702449679': { id: 1745549470655, value: '' },
                '1744702452061': { id: 1745549470656, value: false },
                '1744705577720': { id: 1745549470653, value: categoryIds },
                '1745552845835': {
                    id: 1745552845835,
                    value: "Seven different species of sea (or marine) turtles grace our ocean waters, from the shallow seagrass beds of the Indian Ocean, to the colorful reefs of the Coral Triangle and the sandy beaches of the Eastern Pacific. While these highly migratory species periodically come ashore to either bask or nest, sea turtles spend the bulk of their lives in the ocean. WWF's work on sea turtles focuses on five of those species: green, hawksbill, loggerhead, leatherback, and olive ridley.",
                },
                '1746670105343': { id: 1746670105343, value: 'Yuna' },
            };
            seaTutrleItem.status = 'draft';
            seaTutrleItem.siteId = site.id;
            seaTutrleItem.rootUserId = site.userId;
            seaTutrleItem.userId = site.userId;
            await queryRunner.manager
                .getRepository(cms_collection_items_entity_1.CmsCollectionItemEntity)
                .save(seaTutrleItem);
        }
    }
    async create(siteEntity) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const project = await this.projectService.findById(siteEntity.projectId);
            if (!project)
                throw new app_exception_1.AppException('error.project_not_found');
            let site = new site_entity_1.SiteEntity();
            site.managementName = siteEntity.managementName;
            site.projectFolderId = siteEntity.projectFolderId;
            site.projectId = siteEntity.projectId;
            site.status = siteEntity.status;
            site.url = siteEntity.url;
            site.title = siteEntity.title;
            site.description = siteEntity.description;
            site.isSearch = siteEntity.isSearch;
            site.thumb = siteEntity.thumb;
            site.headCode = siteEntity.headCode;
            site.bodyCode = siteEntity.bodyCode;
            site.userId = project.userId;
            site = await queryRunner.manager.getRepository(site_entity_1.SiteEntity).save(site);
            const rootPage = new page_entity_1.PageEntity();
            rootPage.type = page_type_1.PageType.ROOT;
            rootPage.projectId = site.projectId;
            rootPage.siteId = site.id;
            rootPage.name = 'ROOT';
            rootPage.components = {};
            rootPage.ts = 1;
            rootPage.status = page_type_1.PageStatus.PUBLISHED;
            rootPage.url = '';
            rootPage.title = 'ROOT';
            rootPage.description = '';
            rootPage.isSearch = false;
            rootPage.thumb = '';
            rootPage.headCode = '';
            rootPage.bodyCode = '';
            rootPage.isPrivate = false;
            rootPage.isHome = false;
            rootPage.children = [];
            rootPage.userId = site.userId;
            rootPage.isDeleted = false;
            await queryRunner.manager.getRepository(page_entity_1.PageEntity).save(rootPage);
            await this.initCollection(queryRunner, site);
            await queryRunner.commitTransaction();
            return site;
        }
        catch (e) {
            console.log(e);
            await queryRunner.rollbackTransaction();
            throw e;
        }
        finally {
            await queryRunner.release();
        }
    }
    async update(siteId, siteData) {
        const site = await this.siteRepo.findOneBy({ id: siteId });
        if (!site)
            throw new app_exception_1.AppException('error.site_not_found');
        delete siteData.id;
        await this.siteRepo.update(siteId, siteData);
        return { ...site, ...siteData };
    }
    async getURLSite(_siteId) {
        console.log('getURLSite', _siteId);
        return this.configService.get('SITE_URL');
    }
    async clone(siteId) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const site = await queryRunner.manager
                .getRepository(site_entity_1.SiteEntity)
                .findOneBy({ id: siteId });
            if (!site)
                throw new app_exception_1.AppException('error.site_not_found');
            const pages = await queryRunner.manager.getRepository(page_entity_1.PageEntity).find({
                where: {
                    siteId: siteId,
                },
            });
            site.managementName = `${site.managementName}-clone`;
            delete site.id;
            const newSite = await queryRunner.manager
                .getRepository(site_entity_1.SiteEntity)
                .save(site);
            await this.initCollection(queryRunner, newSite);
            const newPages = pages?.map(page => {
                delete page.id;
                delete page.createdAt;
                delete page.updatedAt;
                page.siteId = newSite.id;
                return page;
            });
            await queryRunner.manager.getRepository(page_entity_1.PageEntity).save(newPages);
            await queryRunner.commitTransaction();
            return newSite;
        }
        catch (e) {
            await queryRunner.rollbackTransaction();
            throw e;
        }
        finally {
            await queryRunner.release();
        }
    }
    async findById(siteId) {
        return await this.siteRepo.findOneBy({ id: siteId });
    }
    async findByProjectId(projectId) {
        return await this.siteRepo.findBy({ projectId });
    }
    async findByProjectFolderId(projectId, projectFolderId) {
        return await this.siteRepo.findBy({ projectId, projectFolderId });
    }
    async findByIds(ids) {
        return await this.siteRepo.findBy({ id: (0, typeorm_2.In)(ids) });
    }
    async delete(siteId) {
        const page = await this.siteRepo.findOneBy({
            id: siteId,
        });
        if (!page)
            throw new app_exception_1.AppException('error.site_not_found');
        await this.siteRepo.delete(page.id);
        return true;
    }
    async updateArchive(siteId, archive) {
        const site = await this.siteRepo.findOneBy({ id: siteId });
        if (!site)
            throw new app_exception_1.AppException('error.site_not_found');
        site.isArchived = archive;
        return await this.siteRepo.save(site);
    }
};
exports.SiteService = SiteService;
__decorate([
    (0, typeorm_1.InjectRepository)(site_entity_1.SiteEntity),
    __metadata("design:type", typeorm_2.Repository)
], SiteService.prototype, "siteRepo", void 0);
exports.SiteService = SiteService = __decorate([
    (0, common_1.Injectable)(),
    __param(3, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [config_1.ConfigService,
        project_service_1.ProjectService,
        cms_collection_service_1.CmsCollectionService,
        typeorm_2.DataSource])
], SiteService);
//# sourceMappingURL=site.service.js.map