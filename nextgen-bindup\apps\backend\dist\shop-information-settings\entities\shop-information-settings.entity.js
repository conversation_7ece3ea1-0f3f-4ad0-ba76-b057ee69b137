"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShopInformationSettingEntity = void 0;
const typeorm_1 = require("typeorm");
let ShopInformationSettingEntity = class ShopInformationSettingEntity {
};
exports.ShopInformationSettingEntity = ShopInformationSettingEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], ShopInformationSettingEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'siteId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], ShopInformationSettingEntity.prototype, "siteId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isMaintenance',
        type: 'boolean',
        nullable: false,
    }),
    __metadata("design:type", Boolean)
], ShopInformationSettingEntity.prototype, "isMaintenance", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'shopName',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], ShopInformationSettingEntity.prototype, "shopName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'logoImages',
        type: 'text',
        nullable: true,
        array: true,
    }),
    __metadata("design:type", Array)
], ShopInformationSettingEntity.prototype, "logoImages", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'colorTheme',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], ShopInformationSettingEntity.prototype, "colorTheme", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'shopUrl',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], ShopInformationSettingEntity.prototype, "shopUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isSetupGuide',
        type: 'boolean',
        nullable: false,
    }),
    __metadata("design:type", Boolean)
], ShopInformationSettingEntity.prototype, "isSetupGuide", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'guideUrl',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], ShopInformationSettingEntity.prototype, "guideUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'email',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], ShopInformationSettingEntity.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isAddPrivacy',
        type: 'boolean',
        nullable: false,
    }),
    __metadata("design:type", Boolean)
], ShopInformationSettingEntity.prototype, "isAddPrivacy", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'privacyUrl',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], ShopInformationSettingEntity.prototype, "privacyUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'taxMode',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], ShopInformationSettingEntity.prototype, "taxMode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'taxRate',
        type: 'bigint',
        nullable: true,
    }),
    __metadata("design:type", Number)
], ShopInformationSettingEntity.prototype, "taxRate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'taxRegulation',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], ShopInformationSettingEntity.prototype, "taxRegulation", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], ShopInformationSettingEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], ShopInformationSettingEntity.prototype, "updatedAt", void 0);
exports.ShopInformationSettingEntity = ShopInformationSettingEntity = __decorate([
    (0, typeorm_1.Entity)('shop_information_settings', { schema: process.env.DATABASE_SCHEMA })
], ShopInformationSettingEntity);
//# sourceMappingURL=shop-information-settings.entity.js.map