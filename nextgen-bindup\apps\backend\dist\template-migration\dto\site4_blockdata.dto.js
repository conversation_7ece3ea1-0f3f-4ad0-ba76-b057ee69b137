"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BgSet_Ilay = exports.BlockData_Info_FrameType = exports.BlockData_Info_BwUseRefPage = exports.BlockData_Info_BwType = exports.BlockData_LayoutOpt = exports.BlockData_Layout = void 0;
var BlockData_Layout;
(function (BlockData_Layout) {
    BlockData_Layout["PLAIN"] = "plain";
    BlockData_Layout["ASYMM"] = "asymm";
    BlockData_Layout["TABLE"] = "index";
    BlockData_Layout["ALBUM"] = "album";
    BlockData_Layout["TAB"] = "tab";
    BlockData_Layout["ACCORDION"] = "accordion";
})(BlockData_Layout || (exports.BlockData_Layout = BlockData_Layout = {}));
var BlockData_LayoutOpt;
(function (BlockData_LayoutOpt) {
    BlockData_LayoutOpt["STEP_1"] = "col-1";
    BlockData_LayoutOpt["STEP_2"] = "col-2";
    BlockData_LayoutOpt["STEP_3"] = "col-3";
    BlockData_LayoutOpt["STEP_4"] = "col-4";
    BlockData_LayoutOpt["STEP_5"] = "col-5";
    BlockData_LayoutOpt["RIGHT_WIDEL"] = "wider-2";
    BlockData_LayoutOpt["LEFT_WIDEL"] = "widel-2";
})(BlockData_LayoutOpt || (exports.BlockData_LayoutOpt = BlockData_LayoutOpt = {}));
var BlockData_Info_BwType;
(function (BlockData_Info_BwType) {
    BlockData_Info_BwType["PIXEL"] = "0";
    BlockData_Info_BwType["PERCENT"] = "1";
})(BlockData_Info_BwType || (exports.BlockData_Info_BwType = BlockData_Info_BwType = {}));
var BlockData_Info_BwUseRefPage;
(function (BlockData_Info_BwUseRefPage) {
    BlockData_Info_BwUseRefPage[BlockData_Info_BwUseRefPage["FALSE"] = 0] = "FALSE";
    BlockData_Info_BwUseRefPage[BlockData_Info_BwUseRefPage["TRUE"] = 1] = "TRUE";
})(BlockData_Info_BwUseRefPage || (exports.BlockData_Info_BwUseRefPage = BlockData_Info_BwUseRefPage = {}));
var BlockData_Info_FrameType;
(function (BlockData_Info_FrameType) {
    BlockData_Info_FrameType["NONE"] = "0";
    BlockData_Info_FrameType["BORDER_ONLY"] = "1";
    BlockData_Info_FrameType["BORDER_RADIUS_ONLY"] = "2";
    BlockData_Info_FrameType["BORDER_GRADIENT"] = "3";
})(BlockData_Info_FrameType || (exports.BlockData_Info_FrameType = BlockData_Info_FrameType = {}));
var BgSet_Ilay;
(function (BgSet_Ilay) {
    BgSet_Ilay[BgSet_Ilay["TILING"] = 0] = "TILING";
    BgSet_Ilay[BgSet_Ilay["UPPER_LEFT"] = 1] = "UPPER_LEFT";
    BgSet_Ilay[BgSet_Ilay["CENTER_LEFT"] = 2] = "CENTER_LEFT";
    BgSet_Ilay[BgSet_Ilay["LOWER_LEFT"] = 3] = "LOWER_LEFT";
    BgSet_Ilay[BgSet_Ilay["TOP_CENTER"] = 4] = "TOP_CENTER";
    BgSet_Ilay[BgSet_Ilay["TRUE_CENTER"] = 5] = "TRUE_CENTER";
    BgSet_Ilay[BgSet_Ilay["BOTTOM_CENTER"] = 6] = "BOTTOM_CENTER";
    BgSet_Ilay[BgSet_Ilay["UPPER_RIGHT"] = 7] = "UPPER_RIGHT";
    BgSet_Ilay[BgSet_Ilay["CENTER_RIGHT"] = 8] = "CENTER_RIGHT";
    BgSet_Ilay[BgSet_Ilay["LOWER_RIGHT"] = 9] = "LOWER_RIGHT";
    BgSet_Ilay[BgSet_Ilay["LEFT_JUSTIFIED"] = 10] = "LEFT_JUSTIFIED";
    BgSet_Ilay[BgSet_Ilay["VERTICAL_REPEAT"] = 11] = "VERTICAL_REPEAT";
    BgSet_Ilay[BgSet_Ilay["RIGHT_JUSTIFIED"] = 12] = "RIGHT_JUSTIFIED";
    BgSet_Ilay[BgSet_Ilay["TOP_ALIGNMENT"] = 13] = "TOP_ALIGNMENT";
    BgSet_Ilay[BgSet_Ilay["HORIZONTAL_REPEAT"] = 14] = "HORIZONTAL_REPEAT";
    BgSet_Ilay[BgSet_Ilay["BOTTOM_ALIGNMENT"] = 15] = "BOTTOM_ALIGNMENT";
    BgSet_Ilay[BgSet_Ilay["STRETCH"] = 16] = "STRETCH";
})(BgSet_Ilay || (exports.BgSet_Ilay = BgSet_Ilay = {}));
//# sourceMappingURL=site4_blockdata.dto.js.map