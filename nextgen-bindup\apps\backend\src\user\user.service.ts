import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { UserEntity } from './entities/user.entity';

@Injectable()
export class UserService {
  @InjectRepository(UserEntity)
  readonly userRepo: Repository<UserEntity>;

  constructor() {}

  async findByUserId(userid: string): Promise<UserEntity> {
    return await this.userRepo.findOneBy({ userid: userid });
  }

  async findByEmail(email: string): Promise<UserEntity> {
    return await this.userRepo.findOneBy({ email: email });
  }
}
