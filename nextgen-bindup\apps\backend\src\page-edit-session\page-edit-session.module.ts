import { Module } from '@nestjs/common';
import { PageEditSessionService } from './page-edit-session.service';
import { PageEditSessionController } from './page-edit-session.controller';
import { PageEditSessionEntity } from './entities/page-edit-session.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([PageEditSessionEntity])],
  providers: [PageEditSessionService],
  controllers: [PageEditSessionController],
})
export class PageEditSessionModule {}
