import { Body, Controller, Get, Param, Put, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { ShippingNoteSettingEntity } from 'src/shipping-note-settings/entities/shipping-note--settings.entity';
import { DeliveryReceiptSettingsService } from './delivery-receipt-settings.service';

@Controller('delivery-receipt-settings')
@UseGuards(AuthGuard)
export class DeliveryReceiptSettingsController {
  constructor(
    private readonly deliveryReceiptSettingService: DeliveryReceiptSettingsService,
  ) {}

  @Put('update/:id')
  async update(
    @Param('id') id: string,
    @Body() data: Partial<ShippingNoteSettingEntity>,
  ) {
    return await this.deliveryReceiptSettingService.update(+id, data);
  }

  @Get('one-by-site/:siteId')
  async getOneBySiteId(@Param('siteId') siteId: string) {
    return await this.deliveryReceiptSettingService.findOneBySiteId(+siteId);
  }
}
