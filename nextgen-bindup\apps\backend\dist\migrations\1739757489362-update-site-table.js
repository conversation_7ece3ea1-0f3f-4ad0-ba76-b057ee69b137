"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSitesTable1739757489362 = void 0;
const typeorm_1 = require("typeorm");
class UpdateSitesTable1739757489362 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}sites`;
    }
    async up(queryRunner) {
        const userIdColumn = new typeorm_1.TableColumn({
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, userIdColumn);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'userId');
    }
}
exports.UpdateSitesTable1739757489362 = UpdateSitesTable1739757489362;
//# sourceMappingURL=1739757489362-update-site-table.js.map