import { FC, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import {
  Box,
  Typography,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  IconButton,
  Dialog,
  TextField,
  Button,
  Stack,
  DialogActions,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
} from '@mui/material';
import { NameServerEntity } from '../../../dto/name-server.type';
import { nameServerService } from '../../../services/name-server-service';
import ConfirmationDialog from '../../common/ConfirmmationDialog';

type Props = {
  siteId: number;
  projectId: number;
};

enum NameServerType {
  NS = 'NS',
  A = 'A',
  AAAA = 'AAAA',
  CNAME = 'CNAME',
  MX = 'MX',
  TXT = 'TXT',
}

const NameServers: FC<Props> = ({ projectId, siteId }) => {
  const { t } = useTranslation();
  const [nameServers, setNameServers] = useState<NameServerEntity[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<NameServerEntity | null>(
    null,
  );
  const [editingNameServer, setEditingNameServer] =
    useState<NameServerEntity | null>(null);
  const [newNameServer, setNewNameServer] = useState<NameServerEntity>({
    id: undefined,
    projectId: projectId,
    name: '',
    type: NameServerType.NS,
    siteId: siteId,
  });
  const [openDialog, setOpenDialog] = useState(false);

  useEffect(() => {
    const fetchNameServers = async () => {
      const servers = await nameServerService.findBySiteId(siteId);
      setNameServers(servers);
    };
    fetchNameServers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setNewNameServer({ ...newNameServer, [name]: value });
    if (isEditing && editingNameServer) {
      setEditingNameServer({ ...editingNameServer, [name]: value });
    }
  };

  const handleSelectChange = (event: SelectChangeEvent) => {
    const { name, value } = event.target;
    setNewNameServer({ ...newNameServer, [name]: value });
    if (isEditing && editingNameServer) {
      setEditingNameServer({ ...editingNameServer, [name]: value });
    }
  };

  const handleCreate = async () => {
    try {
      await nameServerService.create(newNameServer);
      setOpenDialog(false);
      const servers = await nameServerService.findByProjectId(projectId);
      setNameServers(servers);
      setNewNameServer({
        id: undefined,
        projectId: projectId,
        name: '',
        type: NameServerType.NS,
        siteId: siteId,
      });
    } catch (error) {
      console.error('Error creating name server:', error);
    }
  };

  const handleEdit = (server: NameServerEntity) => {
    setEditingNameServer(server);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const handleUpdate = async () => {
    if (!editingNameServer || !editingNameServer.id) return;
    try {
      await nameServerService.update(editingNameServer.id, editingNameServer);
      setOpenDialog(false);
      const servers = await nameServerService.findBySiteId(siteId);
      setNameServers(servers);
      setEditingNameServer(null);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating name server:', error);
    }
  };

  const handleDelete = (nameServer: NameServerEntity) => {
    setRecordToDelete(nameServer);
    setOpenConfirmDialog(true);
  };

  const confirmDelete = async () => {
    if (!recordToDelete || !recordToDelete.id) return;
    try {
      await nameServerService.delete(recordToDelete.id);
      const records = await nameServerService.findBySiteId(siteId);
      setNameServers(records);
      setOpenConfirmDialog(false);
      setRecordToDelete(null);
    } catch (error) {
      console.error('Error deleting DNS record:', error);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography>{t('site.own_domain.name_server.title')}</Typography>
        <Button
          variant="outlined"
          onClick={() => {
            setOpenDialog(true);
            setIsEditing(false);
            setEditingNameServer(null);
          }}
        >
          {t('common.add')}
        </Button>
      </Box>

      <Table>
        <TableHead>
          <TableRow>
            <TableCell>{t('site.own_domain.name_server.type.label')}</TableCell>
            <TableCell>{t('site.own_domain.name_server.value')}</TableCell>
            <TableCell></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {nameServers.map(server => (
            <TableRow
              key={server.id}
              sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
            >
              <TableCell component="th" scope="row">
                {server.type}
              </TableCell>
              <TableCell>{server.name}</TableCell>
              <TableCell align="right">
                <IconButton onClick={() => handleEdit(server)}>
                  <EditIcon />
                </IconButton>
                <IconButton onClick={() => handleDelete(server)}>
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <Dialog
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setEditingNameServer(null);
          setIsEditing(false);
        }}
      >
        <NameServerForm
          isEditing={isEditing}
          initialNameServer={editingNameServer || newNameServer}
          handleInputChange={handleInputChange}
          handleSelectChange={handleSelectChange}
          onCreate={handleCreate}
          onUpdate={handleUpdate}
          onCancel={() => {
            setOpenDialog(false);
            setEditingNameServer(null);
            setIsEditing(false);
          }}
        />
      </Dialog>

      <ConfirmationDialog
        open={openConfirmDialog}
        title={t('delete_confirm.title')}
        message={t('delete_confirm.message', { name: recordToDelete?.name })}
        onConfirm={confirmDelete}
        onCancel={() => {
          setOpenConfirmDialog(false);
          setRecordToDelete(null);
        }}
      />
    </Box>
  );
};

const NameServerForm: FC<{
  isEditing: boolean;
  initialNameServer: NameServerEntity;
  handleInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleSelectChange: (event: SelectChangeEvent) => void;
  onCreate: () => void;
  onUpdate: () => void;
  onCancel: () => void;
}> = ({
  isEditing,
  initialNameServer,
  handleInputChange,
  handleSelectChange,
  onCreate,
  onUpdate,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [nameServer, setNameServer] = useState(initialNameServer);

  useEffect(() => {
    setNameServer(initialNameServer);
  }, [initialNameServer]);

  return (
    <Stack sx={{ width: '400px', padding: 2 }}>
      <Typography variant="subtitle1">
        {isEditing
          ? t('site.public_server.form.edit_name_server')
          : t('site.public_server.form.add_name_server')}
      </Typography>
      <FormControl fullWidth margin="normal">
        <InputLabel id="type-label">
          {t('site.public_server.form.type')}
        </InputLabel>
        <Select
          labelId="type-label"
          id="type"
          name="type"
          value={nameServer.type}
          label={t('site.public_server.form.type')}
          onChange={handleSelectChange}
        >
          {Object.values(NameServerType).map(type => (
            <MenuItem key={type} value={type}>
              {type}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <TextField
        label={t('site.public_server.form.name')}
        name="name"
        value={nameServer.name}
        onChange={handleInputChange}
        fullWidth
        margin="normal"
      />
      <DialogActions>
        <Button onClick={onCancel}>{t('common.cancel')}</Button>
        <Button variant="contained" onClick={isEditing ? onUpdate : onCreate}>
          {isEditing ? t('common.update') : t('common.create')}
        </Button>
      </DialogActions>
    </Stack>
  );
};

export default NameServers;
