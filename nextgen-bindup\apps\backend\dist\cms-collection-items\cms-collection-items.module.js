"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsCollectionItemsModule = void 0;
const common_1 = require("@nestjs/common");
const cms_collection_items_controller_1 = require("./cms-collection-items.controller");
const cms_collection_items_service_1 = require("./cms-collection-items.service");
const typeorm_1 = require("@nestjs/typeorm");
const cms_collection_items_entity_1 = require("./entities/cms-collection-items.entity");
const cms_collection_module_1 = require("../cms-collection/cms-collection.module");
const product_module_1 = require("../product/product.module");
const shop_information_settings_module_1 = require("../shop-information-settings/shop-information-settings.module");
let CmsCollectionItemsModule = class CmsCollectionItemsModule {
};
exports.CmsCollectionItemsModule = CmsCollectionItemsModule;
exports.CmsCollectionItemsModule = CmsCollectionItemsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cms_collection_items_entity_1.CmsCollectionItemEntity]),
            cms_collection_module_1.CmsCollectionModule,
            product_module_1.ProductModule,
            shop_information_settings_module_1.ShopInformationSettingModule,
        ],
        controllers: [cms_collection_items_controller_1.CmsCollectionItemsController],
        providers: [cms_collection_items_service_1.CmsCollectionItemsService],
        exports: [cms_collection_items_service_1.CmsCollectionItemsService],
    })
], CmsCollectionItemsModule);
//# sourceMappingURL=cms-collection-items.module.js.map