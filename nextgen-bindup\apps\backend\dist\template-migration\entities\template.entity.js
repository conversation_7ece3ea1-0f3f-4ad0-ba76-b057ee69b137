"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateEntity = void 0;
const typeorm_1 = require("typeorm");
let TemplateEntity = class TemplateEntity {
};
exports.TemplateEntity = TemplateEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], TemplateEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'tmpSiteId',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], TemplateEntity.prototype, "tmpSiteId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'name',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], TemplateEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'titleDetail',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplateEntity.prototype, "titleDetail", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'seq',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], TemplateEntity.prototype, "seq", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'description',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplateEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'category',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], TemplateEntity.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'smartblock',
        type: 'boolean',
        nullable: false,
    }),
    __metadata("design:type", Boolean)
], TemplateEntity.prototype, "smartblock", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'pages',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Array)
], TemplateEntity.prototype, "pages", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], TemplateEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], TemplateEntity.prototype, "updatedAt", void 0);
exports.TemplateEntity = TemplateEntity = __decorate([
    (0, typeorm_1.Entity)('templates', { schema: process.env.DATABASE_SCHEMA })
], TemplateEntity);
//# sourceMappingURL=template.entity.js.map