"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductEntity = void 0;
const typeorm_1 = require("typeorm");
let ProductEntity = class ProductEntity {
};
exports.ProductEntity = ProductEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'siteId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "siteId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isOrderable',
        type: 'boolean',
        nullable: false,
    }),
    __metadata("design:type", Boolean)
], ProductEntity.prototype, "isOrderable", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'code',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], ProductEntity.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'name',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], ProductEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'title',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], ProductEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'description',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], ProductEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'images',
        type: 'text',
        nullable: true,
        array: true,
    }),
    __metadata("design:type", Array)
], ProductEntity.prototype, "images", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'priceLabel',
        type: 'varchar',
        length: '250',
        nullable: true,
    }),
    __metadata("design:type", String)
], ProductEntity.prototype, "priceLabel", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'saleLabel',
        type: 'varchar',
        length: '250',
        nullable: true,
    }),
    __metadata("design:type", String)
], ProductEntity.prototype, "saleLabel", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'price',
        type: 'bigint',
        transformer: {
            to: (value) => value,
            from: (value) => Number(value),
        },
        nullable: false,
    }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'sale',
        type: 'bigint',
        transformer: {
            to: (value) => value,
            from: (value) => Number(value),
        },
        nullable: false,
    }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "sale", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'purchaseLimitQuantity',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "purchaseLimitQuantity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'individualShippingCharges',
        type: 'bigint',
        transformer: {
            to: (value) => value,
            from: (value) => Number(value),
        },
        nullable: false,
    }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "individualShippingCharges", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'fileDownload',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], ProductEntity.prototype, "fileDownload", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'unlimitedPurchase',
        type: 'boolean',
        nullable: false,
    }),
    __metadata("design:type", Boolean)
], ProductEntity.prototype, "unlimitedPurchase", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'productType',
        type: 'varchar',
        length: 10,
        nullable: false,
    }),
    __metadata("design:type", String)
], ProductEntity.prototype, "productType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'productVariantType',
        type: 'varchar',
        length: 20,
        nullable: false,
    }),
    __metadata("design:type", String)
], ProductEntity.prototype, "productVariantType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'variants',
        type: 'jsonb',
    }),
    __metadata("design:type", Object)
], ProductEntity.prototype, "variants", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], ProductEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], ProductEntity.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isDeleted',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], ProductEntity.prototype, "isDeleted", void 0);
exports.ProductEntity = ProductEntity = __decorate([
    (0, typeorm_1.Entity)(`products`, {
        schema: process.env.DATABASE_SCHEMA,
    })
], ProductEntity);
//# sourceMappingURL=product.entity.js.map