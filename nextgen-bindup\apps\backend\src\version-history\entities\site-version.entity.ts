import { SiteStatus } from 'src/site/types/site.type';
import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('site_versions', { schema: process.env.DATABASE_SCHEMA })
export class SiteVersionEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: true,
  })
  siteId: number;

  @Column({
    name: 'projectId',
    type: 'integer',
    nullable: true,
  })
  projectId: number;

  @Column({
    name: 'projectFolderId',
    type: 'integer',
    nullable: true,
  })
  projectFolderId: number;

  @Column({
    name: 'managementName',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  managementName: string;

  @Column({
    name: 'status',
    type: 'smallint',
    nullable: false,
    default: SiteStatus.DRAFT,
  })
  status: SiteStatus = SiteStatus.DRAFT;

  @Column({
    name: 'url',
    type: 'varchar',
    length: 255,
  })
  url: string;

  @Column({
    name: 'title',
    type: 'varchar',
    length: 255,
  })
  title: string;

  @Column({
    name: 'description',
    type: 'text',
  })
  description: string;

  @Column({
    name: 'isSearch',
    type: 'boolean',
  })
  isSearch: boolean;

  @Column({
    name: 'thumb',
    type: 'varchar',
    length: 255,
  })
  thumb: string;

  @Column({
    name: 'headCode',
    type: 'text',
  })
  headCode: string;

  @Column({
    name: 'bodyCode',
    type: 'text',
  })
  bodyCode: string;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;

  @Column({
    name: 'isArchived',
    type: 'boolean',
  })
  isArchived: boolean;

  @CreateDateColumn({
    name: 'versionCreatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  versionCreatedAt: Date;

  @Column({
    name: 'versionName',
    type: 'varchar',
    length: 255,
  })
  versionName: string;

  @Column({
    name: 'userId',
    type: 'varchar',
    length: '36',
  })
  userId: string;
}
