"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProxyController = void 0;
const common_1 = require("@nestjs/common");
const site_guard_1 = require("./site.guard");
const site_auth_decorater_1 = require("./site-auth.decorater");
const shipping_note_settings_service_1 = require("../shipping-note-settings/shipping-note-settings.service");
const shop_information_settings_service_1 = require("../shop-information-settings/shop-information-settings.service");
const payment_method_service_1 = require("../payment-method/payment-method.service");
const order_service_1 = require("../order/order.service");
const product_stocks_service_1 = require("../product-stocks/product-stocks.service");
const user_payment_service_1 = require("../payment/user-payment.service");
const create_checkout_session_dto_1 = require("../payment/dto/create-checkout-session.dto");
const site_service_1 = require("../site/site.service");
const order_complete_settings_service_1 = require("../order-complete-settings/order-complete-settings.service");
let ProxyController = class ProxyController {
    constructor(shippingNoteSettingService, shopInformationSettingService, paymentMethodService, orderService, productStockService, userPaymentService, siteService, orderCompletionSettingService) {
        this.shippingNoteSettingService = shippingNoteSettingService;
        this.shopInformationSettingService = shopInformationSettingService;
        this.paymentMethodService = paymentMethodService;
        this.orderService = orderService;
        this.productStockService = productStockService;
        this.userPaymentService = userPaymentService;
        this.siteService = siteService;
        this.orderCompletionSettingService = orderCompletionSettingService;
    }
    checkStock(siteId, { cartItems }) {
        return this.productStockService.checkStock(siteId, cartItems);
    }
    getShopInformation(siteId) {
        return this.shopInformationSettingService.findOneBySiteId(siteId);
    }
    getShippingNoteSetting(siteId) {
        return this.shippingNoteSettingService.findOneBySiteId(siteId);
    }
    getPaymentMethods(siteId) {
        return this.paymentMethodService.getPaymentMethodBySiteId(siteId);
    }
    getOrderCompletionSettings(siteId) {
        return this.orderCompletionSettingService.findOneBySiteId(siteId);
    }
    getOrderDetail(orderId) {
        return this.orderService.findOrderById(+orderId);
    }
    async checkout(siteId, req, body) {
        const origin = req.get('origin') || req.get('referer') || '';
        const cleanedOrigin = origin.split('/').slice(0, 3).join('/');
        const createOrder = body.createOrder;
        if (!createOrder) {
            throw new Error('Create order data is required');
        }
        const site = await this.siteService.findById(siteId);
        if (!site) {
            throw new Error('Site not found');
        }
        createOrder.siteId = siteId;
        if (body.paymentMethodType === "CREDIT_CARD") {
            const checkout = await this.userPaymentService.checkoutWithCreaditCard(cleanedOrigin, createOrder);
            return {
                success: true,
                paymentMethodType: "CREDIT_CARD",
                url: checkout.url,
            };
        }
        if (body.paymentMethodType === "BANK_TRANSFER") {
            const checkout = await this.userPaymentService.checkoutWithBankTransfer(body.createOrder);
            return {
                success: true,
                paymentMethodType: "BANK_TRANSFER",
                orderId: checkout.orderId,
            };
        }
        if (body.paymentMethodType === "CASH_ON_DELIVERY") {
            const checkout = await this.userPaymentService.checkoutWithCashOnDelivery(body.createOrder);
            return {
                success: true,
                paymentMethodType: "CASH_ON_DELIVERY",
                orderId: checkout.orderId,
            };
        }
        if (body.paymentMethodType === "POSTAL_TRANSFER") {
            const checkout = await this.userPaymentService.checkoutWithPostalTransfer(body.createOrder);
            return {
                success: true,
                paymentMethodType: "POSTAL_TRANSFER",
                orderId: checkout.orderId,
            };
        }
    }
};
exports.ProxyController = ProxyController;
__decorate([
    (0, common_1.Post)('check-stock'),
    (0, common_1.UseGuards)(site_guard_1.SiteGuard),
    __param(0, (0, site_auth_decorater_1.SiteId)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", void 0)
], ProxyController.prototype, "checkStock", null);
__decorate([
    (0, common_1.Get)('shop-information-setting'),
    (0, common_1.UseGuards)(site_guard_1.SiteGuard),
    __param(0, (0, site_auth_decorater_1.SiteId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], ProxyController.prototype, "getShopInformation", null);
__decorate([
    (0, common_1.Get)('shipping-note-setting'),
    (0, common_1.UseGuards)(site_guard_1.SiteGuard),
    __param(0, (0, site_auth_decorater_1.SiteId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], ProxyController.prototype, "getShippingNoteSetting", null);
__decorate([
    (0, common_1.Get)('payment-method-setting'),
    (0, common_1.UseGuards)(site_guard_1.SiteGuard),
    __param(0, (0, site_auth_decorater_1.SiteId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], ProxyController.prototype, "getPaymentMethods", null);
__decorate([
    (0, common_1.Get)('order-completion-setting'),
    (0, common_1.UseGuards)(site_guard_1.SiteGuard),
    __param(0, (0, site_auth_decorater_1.SiteId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], ProxyController.prototype, "getOrderCompletionSettings", null);
__decorate([
    (0, common_1.Get)('order-detail/:orderId'),
    __param(0, (0, common_1.Param)('orderId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProxyController.prototype, "getOrderDetail", null);
__decorate([
    (0, common_1.Post)('checkout'),
    (0, common_1.UseGuards)(site_guard_1.SiteGuard),
    __param(0, (0, site_auth_decorater_1.SiteId)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, create_checkout_session_dto_1.CreateCheckoutSession]),
    __metadata("design:returntype", Promise)
], ProxyController.prototype, "checkout", null);
exports.ProxyController = ProxyController = __decorate([
    (0, common_1.Controller)('proxy'),
    __metadata("design:paramtypes", [shipping_note_settings_service_1.ShippingNoteSettingService,
        shop_information_settings_service_1.ShopInformationSettingService,
        payment_method_service_1.PaymentMethodService,
        order_service_1.OrderService,
        product_stocks_service_1.ProductStocksService,
        user_payment_service_1.UserPaymentService,
        site_service_1.SiteService,
        order_complete_settings_service_1.OrderCompletionSettingService])
], ProxyController);
//# sourceMappingURL=proxy.controller.js.map