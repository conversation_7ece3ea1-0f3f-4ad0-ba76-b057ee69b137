import { type FC } from 'react';
import { CardActionArea, CardMedia, Divider } from '@mui/material';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import DefaultImage from '../../assets/default.png';

interface ThumbsProps {
  image: string;
  content: string;
  alt?: string;
  onClick?: () => void;
}

const Thumbs: FC<ThumbsProps> = ({ image, alt, content, onClick }) => {
  return (
    <Card sx={{ width: 282 }}>
      <CardActionArea onClick={onClick}>
        <CardMedia
          component="img"
          height="282"
          image={image || DefaultImage}
          alt={alt || content}
        />
        <Divider />
        <CardContent>
          <Typography>{content}</Typography>
        </CardContent>
      </CardActionArea>
    </Card>
  );
};

export default Thumbs;
