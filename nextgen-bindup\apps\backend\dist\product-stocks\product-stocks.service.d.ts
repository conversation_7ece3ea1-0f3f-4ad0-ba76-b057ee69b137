import { ProductStockEntity } from './entities/product-stock.entity';
import { Repository } from 'typeorm';
import { CartItem, CheckStockResponse } from './dto/product-stock.dto';
import { ProductService } from 'src/product/product.service';
export declare class ProductStocksService {
    private readonly productService;
    readonly productStockRepo: Repository<ProductStockEntity>;
    constructor(productService: ProductService);
    getBySite(siteId: number): Promise<ProductStockEntity[]>;
    getByProduct(siteId: number, productId: number): Promise<ProductStockEntity[]>;
    getByProductIds(siteId: number, productIds: number[]): Promise<ProductStockEntity[]>;
    getById(siteId: number, productId: number, stockId: number): Promise<ProductStockEntity>;
    validateProductStockData(productStockEntity: ProductStockEntity): void;
    create(entity: ProductStockEntity): Promise<ProductStockEntity>;
    update(id: number, entity: ProductStockEntity): Promise<ProductStockEntity>;
    deleteByProductId(siteId: number, productId: number): Promise<void>;
    checkStock(siteId: number, cartItems: CartItem[]): Promise<CheckStockResponse>;
}
