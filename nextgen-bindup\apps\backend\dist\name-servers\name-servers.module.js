"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NameServersModule = void 0;
const common_1 = require("@nestjs/common");
const name_servers_controller_1 = require("./name-servers.controller");
const name_servers_service_1 = require("./name-servers.service");
const name_server_entity_1 = require("./entities/name-server.entity");
const typeorm_1 = require("@nestjs/typeorm");
let NameServersModule = class NameServersModule {
};
exports.NameServersModule = NameServersModule;
exports.NameServersModule = NameServersModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([name_server_entity_1.NameServerEntity])],
        controllers: [name_servers_controller_1.NameServersController],
        providers: [name_servers_service_1.NameServersService],
        exports: [name_servers_service_1.NameServersService],
    })
], NameServersModule);
//# sourceMappingURL=name-servers.module.js.map