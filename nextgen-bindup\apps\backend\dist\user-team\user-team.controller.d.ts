import { UserTeamService } from './user-team.service';
export declare class UserTeamController {
    private readonly userTeamService;
    constructor(userTeamService: UserTeamService);
    getAllByRootUserId(rootUserId: string): Promise<import("./entities/user-team.entity").UserTeamEntity[]>;
    getAllByTeamId(rootUserId: string, teamId: string): Promise<import("./entities/user-team.entity").UserTeamEntity[]>;
    getMemberInfoByTeamId(rootUserId: string, teamId: string): Promise<import("./dto/user-team.dto").UserTeamDto[]>;
    getTeamsOfMember(rootUserId: string): Promise<import("./dto/user-team.dto").UserTeamDto[]>;
}
