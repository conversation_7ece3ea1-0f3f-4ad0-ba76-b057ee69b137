{"version": 3, "file": "version-history.service.js", "sourceRoot": "", "sources": ["../../src/version-history/version-history.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,8DAA2D;AAC3D,wEAAmE;AACnE,wEAAmE;AACnE,qCAAyC;AACzC,8DAA2D;AAIpD,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,YAEU,cAAsC,EAEtC,cAAsC,EAEtC,qBAAoD,EAEpD,qBAAoD;QANpD,mBAAc,GAAd,cAAc,CAAwB;QAEtC,mBAAc,GAAd,cAAc,CAAwB;QAEtC,0BAAqB,GAArB,qBAAqB,CAA+B;QAEpD,0BAAqB,GAArB,qBAAqB,CAA+B;IAC3D,CAAC;IAEJ,KAAK,CAAC,iBAAiB,CAAC,aAAqB;QAC3C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;SAC7B,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QACD,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACvD,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;YACtC,aAAa,EAAE,aAAa;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,aAAqB,EAAE,WAAmB;QAChE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;SAC7B,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QACD,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC;QACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,IAAY;QAC3C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACpC,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,uCAAiB,EAAE,CAAC;QAE3C,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;QAC3B,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACtC,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAClD,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAChD,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAChC,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAC1B,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAC9B,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QAC1C,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAC9B,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACtC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACtC,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACxC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC;QAC9B,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAChC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3E,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YAC3C,KAAK,EAAE;gBACL,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,IAAA,YAAE,EAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;aAC7B;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACpC,MAAM,WAAW,GAAG,IAAI,uCAAiB,EAAE,CAAC;YAC5C,WAAW,CAAC,aAAa,GAAG,gBAAgB,CAAC,EAAE,CAAC;YAChD,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;YAC7B,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YAC7B,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACrC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACvC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACjC,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACzC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YAC7B,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACzC,WAAW,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;YACzB,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACjC,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;YAC3B,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YAC/B,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YAC3C,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACrC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YAC/B,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACrC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACrC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACvC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACjC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACrC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACvC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACvC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACjC,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEpD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAc,EAAE,aAAqB;QACjD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;SAC7B,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACpC,CAAC;QAID,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;QACnD,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;QACjD,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAC3C,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QACrC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QACjC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAGrC,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAC9B;YACE,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,MAAM;SACf,EACD;YACE,SAAS,EAAE,IAAI;SAChB,CACF,CAAC;QAEF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YACzD,KAAK,EAAE,EAAE,aAAa,EAAE,aAAa,EAAE;SACxC,CAAC,CAAC;QAEH,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAC9B,EAAE,EAAE,EAAE,WAAW,CAAC,MAAM,EAAE,EAC1B;gBACE,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,GAAG,EAAE,WAAW,CAAC,GAAG;gBACpB,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,SAAS,EAAE,KAAK;aACjB,CACF,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,OAAe,CAAC,EAChB,WAAmB,GAAG,EACtB,IAAa,EACb,KAAc;QAEd,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB;aACrC,kBAAkB,CAAC,aAAa,CAAC;aACjC,KAAK,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAErD,IAAI,IAAI,IAAI,KAAK,EAAE,CAAC;YAElB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAErD,KAAK,CAAC,QAAQ,CAAC,4CAA4C,EAAE;gBAC3D,SAAS;aACV,CAAC,CAAC;YACH,KAAK,CAAC,QAAQ,CAAC,0CAA0C,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAC1E,CAAC;aAAM,IAAI,IAAI,EAAE,CAAC;YAEhB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAEnD,KAAK,CAAC,QAAQ,CAAC,4CAA4C,EAAE;gBAC3D,SAAS;aACV,CAAC,CAAC;YACH,KAAK,CAAC,QAAQ,CAAC,0CAA0C,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,KAAK;aAC9B,OAAO,CAAC,8BAA8B,EAAE,MAAM,CAAC;aAC/C,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;aAC3B,IAAI,CAAC,QAAQ,CAAC;aACd,eAAe,EAAE,CAAC;QAErB,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,aAAqB;QACxC,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,aAAqB;QACjD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YACjD,KAAK,EAAE,EAAE,aAAa,EAAE;SACzB,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;YACnC,MAAM,IAAI,GAAe;gBACvB,EAAE,EAAE,WAAW,CAAC,MAAM;gBACtB,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,GAAG,EAAE,WAAW,CAAC,GAAG;gBACpB,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,SAAS,EAAE,KAAK;aACjB,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,aAAqB,EACrB,MAAc;QAEd,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC;YAC7D,aAAa,EAAE,aAAa;YAC5B,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;QACH,MAAM,IAAI,GAAe;YACvB,EAAE,EAAE,WAAW,CAAC,MAAM;YACtB,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,SAAS,EAAE,KAAK;SACjB,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AA/SY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,wBAAU,CAAC,CAAA;IAE5B,WAAA,IAAA,0BAAgB,EAAC,wBAAU,CAAC,CAAA;IAE5B,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;qCALZ,oBAAU;QAEV,oBAAU;QAEH,oBAAU;QAEV,oBAAU;GAThC,qBAAqB,CA+SjC"}