import { CreateTempTokenReq, CreateTempTokenRes, VerifyTempTokenRes } from './dto/auth.dto';
import { VersionHistoryService } from 'src/version-history/version-history.service';
import { ResetPwdReq } from './dto/reset-pwd.dto';
import { ForgotPwdReq } from './dto/forgot-pwd.dto';
import { LoginReq, LoginRes } from './dto/login.dto';
import { SignUpReq, SignUpRes } from './dto/sign-up.dto';
import { IdentityService } from 'src/identity/identity.service';
import { UserInfoService } from 'src/user-info/user-info.service';
import { ChangePwdReq } from './dto/change-pwd.dto';
export declare class AuthService {
    private readonly versionHistoryService;
    private readonly identityService;
    private readonly userInfoService;
    constructor(versionHistoryService: VersionHistoryService, identityService: IdentityService, userInfoService: UserInfoService);
    createTempToken(userId: string, regDto: CreateTempTokenReq): Promise<CreateTempTokenRes>;
    verifyTempToken(token: string): Promise<VerifyTempTokenRes>;
    verifyDesignEditorToken(token: string): Promise<VerifyTempTokenRes>;
    signUp(input: SignUpReq): Promise<SignUpRes>;
    login(input: LoginReq): Promise<LoginRes>;
    forgotPwd(input: ForgotPwdReq): Promise<boolean>;
    resetPwd(input: ResetPwdReq): Promise<boolean>;
    private createAccessToken;
    changePassword(email: string, data: ChangePwdReq): Promise<boolean>;
}
