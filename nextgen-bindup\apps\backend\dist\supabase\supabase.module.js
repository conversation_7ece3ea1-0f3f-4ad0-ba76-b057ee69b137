"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var SupabaseModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseModule = void 0;
const common_1 = require("@nestjs/common");
const supabase_js_1 = require("@supabase/supabase-js");
const supabase_constant_1 = require("./supabase.constant");
const supabase_storage_service_1 = require("./supabase-storage.service");
let SupabaseModule = SupabaseModule_1 = class SupabaseModule {
    static forRoot(options) {
        const supabaseProvider = {
            provide: supabase_constant_1.SUPABASE_CLIENT,
            useFactory: () => {
                return (0, supabase_js_1.createClient)(options.url, options.key);
            },
        };
        return {
            module: SupabaseModule_1,
            providers: [supabaseProvider, supabase_storage_service_1.SupabaseStorageService],
            exports: [supabaseProvider, supabase_storage_service_1.SupabaseStorageService],
        };
    }
    static forRootAsync(options) {
        const supabaseProvider = {
            provide: supabase_constant_1.SUPABASE_CLIENT,
            useFactory: async (...args) => {
                const { url, key } = await options.useFactory(...args);
                return (0, supabase_js_1.createClient)(url, key);
            },
            inject: options.inject || [],
        };
        return {
            module: SupabaseModule_1,
            providers: [supabaseProvider, supabase_storage_service_1.SupabaseStorageService],
            exports: [supabaseProvider, supabase_storage_service_1.SupabaseStorageService],
        };
    }
    static forFeature(tableName) {
        const tableProvider = {
            provide: supabase_constant_1.SUPABASE_TABLE,
            useValue: tableName,
        };
        return {
            module: SupabaseModule_1,
            providers: [tableProvider],
            exports: [tableProvider],
        };
    }
};
exports.SupabaseModule = SupabaseModule;
exports.SupabaseModule = SupabaseModule = SupabaseModule_1 = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({})
], SupabaseModule);
//# sourceMappingURL=supabase.module.js.map