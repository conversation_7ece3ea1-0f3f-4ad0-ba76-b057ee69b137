export type SiteDto = {
  id?: number;
  projectId?: number;
  projectFolderId?: number;
  userId?: string;
  managementName: string;
  status: SiteStatus;
  url?: string;
  title?: string;
  description?: string;
  isSearch?: boolean;
  thumb?: string;
  headCode?: string;
  bodyCode?: string;
  isArchived?: boolean;
};

export type SiteEntity = SiteDto & {
  createdAt?: Date;
  updatedAt?: Date;
};

export const enum SiteStatus {
  DRAFT = 1,
  PUBLISHED = 2,
}
