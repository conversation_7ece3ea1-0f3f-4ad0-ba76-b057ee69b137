"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePageTable1730794752461 = void 0;
const page_type_1 = require("../page/types/page.type");
const typeorm_1 = require("typeorm");
class CreatePageTable1730794752461 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}pages`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'type',
                    type: 'varchar',
                    length: '10',
                    isNullable: false,
                    default: `'${page_type_1.PageType.PAGE}'`,
                },
                {
                    name: 'parentId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'projectId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'components',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'ts',
                    type: 'bigint',
                    isNullable: true,
                },
                {
                    name: 'status',
                    type: 'smallint',
                    isNullable: false,
                    default: `'${page_type_1.PageStatus.DRAFT}'`,
                },
                {
                    name: 'url',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'title',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'description',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'isSearch',
                    type: 'boolean',
                    isNullable: true,
                },
                {
                    name: 'thumb',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'headCode',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'bodyCode',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'isPrivate',
                    type: 'boolean',
                    isNullable: false,
                    default: 'false',
                },
                {
                    name: 'isHome',
                    type: 'boolean',
                    isNullable: false,
                    default: 'false',
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
        await queryRunner.query(`alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
    async down(queryRunner) {
        await queryRunner.query(`alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreatePageTable1730794752461 = CreatePageTable1730794752461;
//# sourceMappingURL=1730794752461-create-page-table.js.map