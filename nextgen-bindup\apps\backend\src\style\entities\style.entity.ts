import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { StyleType } from '../enum/style-type.enum';

@Entity('styles', { schema: process.env.DATABASE_SCHEMA })
export class StyleEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'type',
    type: 'smallint',
    nullable: false,
  })
  type: StyleType;

  @Column({
    name: 'projectId',
    type: 'integer',
    nullable: true,
  })
  projectId: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
    default: 1,
  })
  siteId: number;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  name: string;

  @Column({
    name: 'data',
    type: 'jsonb',
    nullable: false,
  })
  data: any;

  @Column({
    name: 'ts',
    type: 'bigint',
    nullable: true,
  })
  ts: number;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
