import { Controller } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { EventType } from 'src/common/event-type.enum';
import { OnEvent } from '@nestjs/event-emitter';
import { OrderEntity } from 'src/order/entites/order.entity';
import { PaymentMethodEntity } from 'src/payment-method/entities/payment-method.entity';
import { PaymentMethodType } from 'src/order/enum/payment-method-type.enum';
import { ShopInformationSettingEntity } from 'src/shop-information-settings/entities/shop-information-settings.entity';
import { OrderCompletionSettingEntity } from 'src/order-complete-settings/entities/order-complete-settings.entity';

@Controller('mail')
export class MailController {
  constructor(private mailerService: MailerService) {}

  @OnEvent(EventType.ORDER_CREATED)
  async handleOrderCreatedEvent(payload: {
    order: OrderEntity;
    paymentMethod: PaymentMethodEntity;
    shopInfo: ShopInformationSettingEntity;
    orderCompletionSetting: OrderCompletionSettingEntity;
  }) {
    console.log('Received order created event:', payload);
    try {
      const order = {
        ...payload.order,
        isBankTransfer:
          payload.order.paymentMethodType === PaymentMethodType.BANK_TRANSFER,
        isPostalTransfer:
          payload.order.paymentMethodType === PaymentMethodType.POSTAL_TRANSFER,
      };

      await this.mailerService.sendMail({
        to: order.email,
        subject: payload.orderCompletionSetting.emailSubject,
        template: './order-confirmation',
        context: {
          order,
          paymentMethod: payload.paymentMethod,
          shopInfo: payload.shopInfo,
          orderCompletionSetting: payload.orderCompletionSetting,
        },
      });
      console.log('Email sent successfully');
    } catch (error) {
      console.error('Error sending email:', error);
    }
  }
}
