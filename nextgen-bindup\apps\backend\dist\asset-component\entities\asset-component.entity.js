"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetComponent = void 0;
const typeorm_1 = require("typeorm");
let AssetComponent = class AssetComponent {
};
exports.AssetComponent = AssetComponent;
__decorate([
    (0, typeorm_1.PrimaryColumn)({
        name: 'id',
        type: 'varchar',
        length: 36,
    }),
    __metadata("design:type", String)
], AssetComponent.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'type',
        type: 'smallint',
        nullable: false,
        default: 1,
    }),
    __metadata("design:type", Number)
], AssetComponent.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'projectId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], AssetComponent.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'siteId',
        type: 'integer',
        nullable: false,
        default: 1,
    }),
    __metadata("design:type", Number)
], AssetComponent.prototype, "siteId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'name',
        type: 'varchar',
        length: 255,
        nullable: false,
    }),
    __metadata("design:type", String)
], AssetComponent.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'folder',
        type: 'boolean',
        default: false,
    }),
    __metadata("design:type", Boolean)
], AssetComponent.prototype, "folder", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'parentFolder',
        type: 'varchar',
        nullable: false,
        length: 255,
        default: '',
    }),
    __metadata("design:type", String)
], AssetComponent.prototype, "parentFolder", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'data',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], AssetComponent.prototype, "data", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'ts',
        type: 'bigint',
        nullable: true,
    }),
    __metadata("design:type", Number)
], AssetComponent.prototype, "ts", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], AssetComponent.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], AssetComponent.prototype, "updatedAt", void 0);
exports.AssetComponent = AssetComponent = __decorate([
    (0, typeorm_1.Entity)('asset_component', { schema: process.env.DATABASE_SCHEMA })
], AssetComponent);
//# sourceMappingURL=asset-component.entity.js.map