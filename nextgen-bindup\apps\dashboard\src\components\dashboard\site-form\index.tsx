import { FC, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Stack,
  Typography,
  DialogContent,
  TextField,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  MenuItem,
  CircularProgress,
  Box,
} from '@mui/material';
import { ProjectFolderEntity } from '../../../dto/project-folder.type';
import { SiteEntity } from '../../../dto/site.type';
import { projectFolderService } from '../../../services/project-folder-service';

export const SiteForm: FC<{
  isEditing: boolean;
  initialSite: SiteEntity;
  handleInputChange: (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void;
  handleSelectChange: (event: SelectChangeEvent) => void;
  onCreate: () => void;
  onUpdate: () => void;
  onCancel: () => void;
}> = ({
  isEditing,
  initialSite,
  handleInputChange,
  handleSelectChange,
  onCreate,
  onUpdate,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [site, setSite] = useState(initialSite);

  const [isLoadFolder, setIsLoadFolder] = useState(true);
  const [folders, setFolders] = useState<ProjectFolderEntity[]>([]);

  useEffect(() => {
    console.log('initialSite', initialSite);
    setSite(initialSite);
  }, [initialSite]);

  useEffect(() => {
    fetchFolders();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [site.projectId]);

  const fetchFolders = async () => {
    setIsLoadFolder(true);

    if (site.projectId) {
      const data = await projectFolderService.findByProjectId(site.projectId);
      setFolders(data);
    } else {
      setFolders([]);
    }

    setIsLoadFolder(false);
  };

  return (
    <Stack sx={{ width: '600px' }}>
      <Typography
        sx={{ paddingBlock: 2, paddingInline: 3 }}
        variant="subtitle2"
        color="textPrimary"
      >
        {t('site.form.title')}
      </Typography>
      <DialogContent sx={{ paddingBlock: 1 }}>
        <Stack direction={'column'} spacing={2}>
          <TextField
            id="name"
            name="managementName"
            label={t('site.form.management_name')}
            variant="filled"
            onChange={handleInputChange}
            value={site.managementName}
            size="small"
          />

          <Stack direction={'row'} spacing={2}>
            <FormControl
              variant="filled"
              fullWidth
              size="small"
              disabled={isLoadFolder}
            >
              <InputLabel id="folder-label">{t('site.form.folder')}</InputLabel>
              <Select
                labelId="folder-label"
                id="folder"
                name="projectFolderId"
                value={site.projectFolderId?.toString() || ' '}
                label={t('site.form.folder')}
                onChange={handleSelectChange}
              >
                <MenuItem value=" ">{t('site.form.none')}</MenuItem>
                {folders.map(folder => (
                  <MenuItem key={folder.id} value={folder.id}>
                    {folder.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {isLoadFolder ? (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CircularProgress color="info" />
              </Box>
            ) : null}
          </Stack>
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button variant="text" onClick={onCancel}>
          {t('common.cancel')}
        </Button>
        <Button
          color="inherit"
          variant="outlined"
          onClick={isEditing ? onUpdate : onCreate}
        >
          {isEditing ? t('common.update') : t('common.create')}
        </Button>
      </DialogActions>
    </Stack>
  );
};
