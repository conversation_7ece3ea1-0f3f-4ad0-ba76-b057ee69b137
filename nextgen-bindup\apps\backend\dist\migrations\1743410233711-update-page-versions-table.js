"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePageVersionsTable1743410233711 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePageVersionsTable1743410233711 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}page_versions`;
    }
    async up(queryRunner) {
        const cmsCollectionIdColumn = new typeorm_1.TableColumn({
            name: 'cmsCollectionId',
            type: 'integer',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, cmsCollectionIdColumn);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'cmsCollectionId');
    }
}
exports.UpdatePageVersionsTable1743410233711 = UpdatePageVersionsTable1743410233711;
//# sourceMappingURL=1743410233711-update-page-versions-table.js.map