"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDefaultSetting = void 0;
const delivery_constant_1 = require("../constant/delivery.constant");
const order_completion_constant_1 = require("../constant/order-completion.constant");
const payment_method_constant_1 = require("../constant/payment-method.constant");
const shipping_note_constant_1 = require("../constant/shipping-note.constant");
const getDefaultSetting = (shopName, email) => {
    return {
        shippingNote: {
            note: shipping_note_constant_1.SHIPPING_NOTE,
        },
        paymentMethod: {
            bankTransfer: {
                isEnabled: false,
                bankAccount: payment_method_constant_1.ACCOUNT_BANK,
                description: payment_method_constant_1.BANK_TRANSFER_DESCRIPTION,
            },
            postalTransfer: {
                isEnabled: false,
                bankAccount: payment_method_constant_1.ACCOUNT_BANK,
                description: payment_method_constant_1.POSTAL_TRANSFER_DESCRIPTION,
            },
            stripePaymentGateway: {
                isEnabled: false,
                description: payment_method_constant_1.STRIPE_DESCRIPTION,
                stripeAccountId: '',
            },
            cashOnDelivery: {
                isEnabled: false,
                description: payment_method_constant_1.CASH_ON_DELIVERY_DESCRIPTION,
                fee: [
                    {
                        fromAmount: 0,
                        codFee: 0,
                    },
                ],
            },
        },
        delivery: {
            headerText: (0, delivery_constant_1.DELIVERY_HEADER_TEXT)(shopName, email),
            footerText: (0, delivery_constant_1.DELIVERY_FOOTER_TEXT)(shopName, email),
        },
        orderCompletion: {
            displayText: order_completion_constant_1.ORDER_COMPLETION_DISPLAY_TEXT,
            emailSubject: (0, order_completion_constant_1.ORDER_COMPLETION_EMAIL_SUBJECT)(shopName),
            emailHeader: (0, order_completion_constant_1.ORDER_COMPLETION_EMAIL_HEADER)(shopName),
            emailFooter: (0, order_completion_constant_1.ORDER_COMPLETION_EMAIL_FOOTER)(shopName, email),
        },
    };
};
exports.getDefaultSetting = getDefaultSetting;
//# sourceMappingURL=get-default-shop-setting.js.map