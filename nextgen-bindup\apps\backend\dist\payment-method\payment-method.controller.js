"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentMethodController = void 0;
const common_1 = require("@nestjs/common");
const payment_method_service_1 = require("./payment-method.service");
const auth_guard_1 = require("../auth/auth.guard");
let PaymentMethodController = class PaymentMethodController {
    constructor(paymentMethodService) {
        this.paymentMethodService = paymentMethodService;
    }
    async getPaymentMethod(siteId) {
        return await this.paymentMethodService.getPaymentMethodBySiteId(siteId);
    }
    async updatePaymentMethod(siteId, paymentMethod) {
        return await this.paymentMethodService.updatePaymentMethod(siteId, paymentMethod);
    }
    async enableStripePaymentGateway(siteId) {
        const result = await this.paymentMethodService.enableStripePaymentGateway(+siteId);
        return {
            success: true,
            onboardingUrl: result.url,
        };
    }
    async checkStripeStatus(siteId) {
        const result = await this.paymentMethodService.checkStripeStatus(+siteId);
        return result;
    }
    async removeStripeAccount(siteId) {
        return await this.paymentMethodService.removeStripeAccount(+siteId);
    }
};
exports.PaymentMethodController = PaymentMethodController;
__decorate([
    (0, common_1.Get)(':siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PaymentMethodController.prototype, "getPaymentMethod", null);
__decorate([
    (0, common_1.Post)(':siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], PaymentMethodController.prototype, "updatePaymentMethod", null);
__decorate([
    (0, common_1.Post)('enable-stripe-payment-gateway/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentMethodController.prototype, "enableStripePaymentGateway", null);
__decorate([
    (0, common_1.Get)('check-stripe-status/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentMethodController.prototype, "checkStripeStatus", null);
__decorate([
    (0, common_1.Post)('remove-stripe-account/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentMethodController.prototype, "removeStripeAccount", null);
exports.PaymentMethodController = PaymentMethodController = __decorate([
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.Controller)('payment-method'),
    __metadata("design:paramtypes", [payment_method_service_1.PaymentMethodService])
], PaymentMethodController);
//# sourceMappingURL=payment-method.controller.js.map