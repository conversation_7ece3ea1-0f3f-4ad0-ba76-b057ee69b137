"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderTable1748587188024 = void 0;
const typeorm_1 = require("typeorm");
class UpdateOrderTable1748587188024 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}orders`;
    }
    async up(queryRunner) {
        const columns = await queryRunner.getTable(this.TABLE_NAME);
        const notExistShippingFee = !columns?.columns.some(col => col.name === 'shippingFee');
        if (notExistShippingFee) {
            await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
                name: 'shippingFee',
                type: 'bigint',
                isNullable: true,
            }));
            return;
        }
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'shippingFee');
    }
}
exports.UpdateOrderTable1748587188024 = UpdateOrderTable1748587188024;
//# sourceMappingURL=1748587188024-update-order-table.js.map