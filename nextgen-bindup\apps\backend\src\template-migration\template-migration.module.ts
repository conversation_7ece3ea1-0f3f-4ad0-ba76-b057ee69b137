import { Modu<PERSON> } from '@nestjs/common';
import { TemplateMigrationController } from './template-migration.controller';
import { TemplateMigrationService } from './template-migration.service';
import { PageModule } from 'src/page/page.module';
import { TemplateEntity } from './entities/template.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TemplatePageEntity } from './entities/template-page.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TemplateEntity,
      TemplatePageEntity,
      TemplatePageEntity,
    ]),
    PageModule,
  ],
  controllers: [TemplateMigrationController],
  providers: [TemplateMigrationService],
  exports: [TemplateMigrationService],
})
export class TemplateMigrationModule {}
