"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentModule = void 0;
const common_1 = require("@nestjs/common");
const asset_component_controller_1 = require("./asset-component.controller");
const asset_component_service_1 = require("./asset-component.service");
const asset_component_entity_1 = require("./entities/asset-component.entity");
const typeorm_1 = require("@nestjs/typeorm");
const project_module_1 = require("../project/project.module");
let ContentModule = class ContentModule {
};
exports.ContentModule = ContentModule;
exports.ContentModule = ContentModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([asset_component_entity_1.AssetComponent]), project_module_1.ProjectModule],
        controllers: [asset_component_controller_1.AssetComponentController],
        providers: [asset_component_service_1.AssetComponentService],
        exports: [asset_component_service_1.AssetComponentService],
    })
], ContentModule);
//# sourceMappingURL=asset-component.module.js.map