import { Controller, Get, Param } from '@nestjs/common';
import { TemplateMigrationService } from './template-migration.service';
import { AppException } from 'src/common/exceptions/app.exception';

@Controller('template-migration')
export class TemplateMigrationController {
  constructor(
    private readonly templateMigrationService: TemplateMigrationService,
  ) {}

  @Get('import')
  async importTemplate() {
    this.templateMigrationService.importTemplate();
    await this.templateMigrationService.importTemplate();
    await this.templateMigrationService.importPorfolioTemplate();
    await this.templateMigrationService.importContactTemplate();
  }

  @Get('import-structure')
  async importStructure() {
    const userId = '1a0715d3-4b77-4010-a20b-b01d5c8ce0ec';
    const templateId =
      await this.templateMigrationService.importTemplateToDB(userId);
    return templateId;
  }

  @Get('create-site/:templateId')
  async createSite(@Param('templateId') templateId: string) {
    if (!templateId) throw new AppException('templateId is required');

    const userId = '1a0715d3-4b77-4010-a20b-b01d5c8ce0ec';
    const projectId = Number(process.env.TEST_TEMPLATE_PROJECT_ID);
    const siteId = Number(process.env.TEST_TEMPLATE_SITE_ID);
    await this.templateMigrationService.createSite(
      Number(templateId),
      userId,
      projectId,
      siteId,
    );
  }

  @Get('import-portfolio')
  async importPorfolioTemplate() {
    this.templateMigrationService.importPorfolioTemplate();
  }

  @Get('import-contact')
  async importContactTemplate() {
    this.templateMigrationService.importContactTemplate();
  }

  @Get('import-photo')
  async importContactPhoto() {
    this.templateMigrationService.importPhotoTemplate();
  }

  @Get('import-profile')
  async importProfileTemplate() {
    this.templateMigrationService.importProfileTemplate();
  }

  @Get('import-temp')
  async importTempTemplate() {
    this.templateMigrationService.importTempTemplate();
  }
}
