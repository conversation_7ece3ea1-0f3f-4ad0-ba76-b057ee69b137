"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsArticleTable1743410233708 = void 0;
class UpdateCmsArticleTable1743410233708 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}cms_articles`;
    }
    async up(queryRunner) {
        await queryRunner.renameTable(this.TABLE_NAME, 'cms_collection_items');
    }
    async down(queryRunner) {
        await queryRunner.renameTable('cms_collection_items', this.TABLE_NAME);
    }
}
exports.UpdateCmsArticleTable1743410233708 = UpdateCmsArticleTable1743410233708;
//# sourceMappingURL=1743410233709-update-cms_articles-table.js.map