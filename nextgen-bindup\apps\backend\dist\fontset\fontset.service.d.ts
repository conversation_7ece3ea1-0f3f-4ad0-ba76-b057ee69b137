import { FontsetEntity } from './entities/fontset.entity';
import { Repository } from 'typeorm';
import { ProjectService } from 'src/project/project.service';
export declare class FontsetService {
    private readonly projectService;
    readonly fontsetRepo: Repository<FontsetEntity>;
    constructor(projectService: ProjectService);
    create(fontsetEntity: FontsetEntity): Promise<FontsetEntity>;
    update(id: number, fontsetData: Partial<FontsetEntity>): Promise<FontsetEntity>;
    findById(id: number): Promise<FontsetEntity>;
    findByProjectId(projectId: number): Promise<FontsetEntity[]>;
    findBySiteId(projectId: number, siteId: number): Promise<FontsetEntity[]>;
    delete(id: number): Promise<boolean>;
}
