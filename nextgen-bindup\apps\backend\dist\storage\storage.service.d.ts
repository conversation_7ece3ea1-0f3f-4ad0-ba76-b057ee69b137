import { SignUploadDto } from '../supabase/dto/sign-upload.dto';
import { SupabaseStorageService } from 'src/supabase/supabase-storage.service';
export declare class StorageService {
    private readonly supabaseStorageService;
    constructor(supabaseStorageService: SupabaseStorageService);
    initBucket(): Promise<void>;
    createSignedUploadUrl(filepath: string): Promise<SignUploadDto>;
    getPublicUrl(filepath: string): Promise<string>;
}
