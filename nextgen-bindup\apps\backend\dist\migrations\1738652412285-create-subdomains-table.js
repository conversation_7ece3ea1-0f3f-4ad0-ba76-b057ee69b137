"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSubdomainsTable1738652412285 = void 0;
const typeorm_1 = require("typeorm");
class CreateSubdomainsTable1738652412285 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}subdomains`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'int',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'projectId',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'subdomain',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'directory',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'https',
                    type: 'boolean',
                    isNullable: true,
                },
                {
                    name: 'ssl',
                    type: 'boolean',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
        await queryRunner.query(`alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
    async down(queryRunner) {
        await queryRunner.query(`alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateSubdomainsTable1738652412285 = CreateSubdomainsTable1738652412285;
//# sourceMappingURL=1738652412285-create-subdomains-table.js.map