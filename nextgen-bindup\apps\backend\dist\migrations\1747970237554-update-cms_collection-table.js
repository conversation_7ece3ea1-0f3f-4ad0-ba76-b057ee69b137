"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCmsCollection1747970237554 = void 0;
const typeorm_1 = require("typeorm");
class UpdateCmsCollection1747970237554 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}cms_collection`;
    }
    async up(queryRunner) {
        const dataTypeColumn = new typeorm_1.TableColumn({
            name: 'dataType',
            type: 'integer',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, dataTypeColumn);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'dataType');
    }
}
exports.UpdateCmsCollection1747970237554 = UpdateCmsCollection1747970237554;
//# sourceMappingURL=1747970237554-update-cms_collection-table.js.map