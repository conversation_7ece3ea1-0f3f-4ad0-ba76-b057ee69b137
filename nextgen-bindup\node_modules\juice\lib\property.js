'use strict';

module.exports = exports = Property;

/**
 * Module dependencies.
 */

var utils = require('./utils');

/**
 * CSS property constructor.
 *
 * @param {String} property
 * @param {String} value
 * @param {Selector} selector the property originates from
 * @param {Integer} priority 0 for normal properties, 2 for !important properties.
 * @param {Array} additional array of integers representing more detailed priorities (sorting)
 * @api public
 */

function Property(prop, value, selector, priority, additionalPriority) {
  this.prop = prop;
  this.value = value;
  this.selector = selector;
  this.priority = priority || 0;
  this.additionalPriority = additionalPriority || [];
}

/**
 * Compares with another Property based on Selector#specificity.
 *
 * @api public
 */

Property.prototype.compareFunc = function(property) {
  var a = [];
  a.push.apply(a, this.selector.specificity());
  a.push.apply(a, this.additionalPriority);
  a[0] += this.priority;
  var b = [];
  b.push.apply(b, property.selector.specificity());
  b.push.apply(b, property.additionalPriority);
  b[0] += property.priority;
  return utils.compareFunc(a, b);
};

Property.prototype.compare = function(property) {
  var winner = this.compareFunc(property);
  if (winner === 1) {
    return this;
  }
  return property;
};


/**
 * Returns CSS property
 *
 * @api public
 */

Property.prototype.toString = function() {
  return this.prop + ': ' + this.value.replace(/['"]+/g, '') + ';';
};
