import { Component } from '@nextgen-bindup/common/dto/component';
import { ComponentType } from 'src/page/types/component.type';
import { PropertyDto } from '@nextgen-bindup/common/dto/component-properties/general-prop.dto';
import { PROP_ACTION_DEFAULT_VALUE } from 'src/page/utils/prop-action-default-value';
import { PROP_SPACING_DEFAULT_VALUE } from 'src/page/utils/prop-spacing-default-value';
import { PROP_SIZE_DEFAULT_VALUE } from 'src/page/utils/prop-size-default-value';
import { PROP_BORDER_DEFAULT_VALUE } from 'src/page/utils/prop-border-default-value';
import { PROP_POSITION_DEFAULT_VALUE } from 'src/page/utils/prop-position-default-value';
import { PROP_EFFECT_LIST_DEFAULT_VALUE } from 'src/page/utils/prop-effect-default-value';
import { PROP_FILTER_DEFAULT_VALUE } from 'src/page/utils/prop-filter-default-value';
import { PROP_DATASOURCE_DEFAULT_VALUE } from 'src/page/utils/prop-datasource-default-value';
import { BlockData_LayoutOpt } from '../dto/site4_blockdata.dto';
import { LayoutFlexPropDto } from '@nextgen-bindup/common/dto/setting-properties/layout/layout-flex-prop.dto';
import { PROP_BACKGROUND_LIST_DEFAULT_VALUE } from 'src/page/utils/prop-background-default-value';

export const DEFAULT_TEMPLATE_COMPONENT: Record<string, Component> = {
  __main__: {
    id: '__main__',
    ts: 1,
    name: 'Main',
    type: ComponentType.Main,
    children: [],
    parentId: '__page__',
    breakpoint: {
      phone: { ts: 1 },
      tablet: { ts: 1 },
    },
    properties: {
      ts: 1,
      size: {
        ts: 1,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: '%', value: '100' },
      },
      border: {
        ts: 1,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: 1, applyTo: 'global' },
      actions: { ts: 1, list: [] },
      effects: { ts: 1, list: [] },
      position: { ts: 1, position: 'relative' },
      backgrounds: { ts: 1, list: [] },
      marginPadding: {
        ts: 1,
        margin: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
  },
  __page__: {
    id: '__page__',
    ts: 1,
    name: 'Page',
    type: ComponentType.Page,
    children: ['__main__'],
    breakpoint: {
      phone: { ts: 1 },
      tablet: { ts: 1 },
    },
    properties: {
      ts: 1,
      size: {
        ts: 1,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'dvh', value: '100' },
      },
      border: {
        ts: 1,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: 1, applyTo: 'global' },
      actions: { ts: 1, list: [] },
      effects: { ts: 1, list: [] },
      position: { ts: 1, position: 'relative' },
      backgrounds: { ts: 1, list: [] },
      marginPadding: {
        ts: 1,
        margin: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
  },
};

export const DEFAULT_FLEX_PROP: LayoutFlexPropDto = {
  flexDirection: 'row',
  verSpacing: { value: '0', unit: 'px' },
  hozSpacing: { value: '0', unit: 'px' },
  justifyContent: '',
  alignContent: '',
  alignItems: '',
  flexGrow: '0',
  flexShrink: '0',
  flexWrap: 'nowrap',
};

export class MigrateUtil {
  static blockProps(): PropertyDto {
    const ts: number = 1;
    const properties: PropertyDto = {
      ts: ts,
      actions: PROP_ACTION_DEFAULT_VALUE(ts),
      marginPadding: PROP_SPACING_DEFAULT_VALUE(ts),
      size: PROP_SIZE_DEFAULT_VALUE(ts),
      border: PROP_BORDER_DEFAULT_VALUE(ts),
      position: PROP_POSITION_DEFAULT_VALUE(ts),
      backgrounds: PROP_BACKGROUND_LIST_DEFAULT_VALUE(ts),
      effects: PROP_EFFECT_LIST_DEFAULT_VALUE(ts),
      filter: PROP_FILTER_DEFAULT_VALUE(ts),
      datasource: PROP_DATASOURCE_DEFAULT_VALUE(ts),
    };
    return properties;
  }

  static getColumns = (layoutOptID: BlockData_LayoutOpt) => {
    switch (layoutOptID) {
      case BlockData_LayoutOpt.STEP_1:
        return 1;
      case BlockData_LayoutOpt.STEP_2:
        return 2;
      case BlockData_LayoutOpt.STEP_3:
        return 3;
      case BlockData_LayoutOpt.STEP_4:
        return 4;
      case BlockData_LayoutOpt.STEP_5:
        return 5;
      default:
        return 1;
    }
  };
}
