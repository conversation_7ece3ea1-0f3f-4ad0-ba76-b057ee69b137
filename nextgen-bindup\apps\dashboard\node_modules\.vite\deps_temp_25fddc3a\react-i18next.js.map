{"version": 3, "sources": ["../../../../../node_modules/void-elements/index.js", "../../../../../node_modules/react-i18next/dist/es/Trans.js", "../../../../../node_modules/react-i18next/dist/es/TransWithoutContext.js", "../../../../../node_modules/html-parse-stringify/src/parse-tag.js", "../../../../../node_modules/html-parse-stringify/src/parse.js", "../../../../../node_modules/html-parse-stringify/src/stringify.js", "../../../../../node_modules/html-parse-stringify/src/index.js", "../../../../../node_modules/react-i18next/dist/es/utils.js", "../../../../../node_modules/react-i18next/dist/es/unescape.js", "../../../../../node_modules/react-i18next/dist/es/defaults.js", "../../../../../node_modules/react-i18next/dist/es/i18nInstance.js", "../../../../../node_modules/react-i18next/dist/es/context.js", "../../../../../node_modules/react-i18next/dist/es/initReactI18next.js", "../../../../../node_modules/react-i18next/dist/es/useTranslation.js", "../../../../../node_modules/react-i18next/dist/es/withTranslation.js", "../../../../../node_modules/react-i18next/dist/es/Translation.js", "../../../../../node_modules/react-i18next/dist/es/I18nextProvider.js", "../../../../../node_modules/react-i18next/dist/es/withSSR.js", "../../../../../node_modules/react-i18next/dist/es/useSSR.js", "../../../../../node_modules/react-i18next/dist/es/index.js"], "sourcesContent": ["/**\n * This file automatically generated from `pre-publish.js`.\n * Do not manually edit.\n */\n\nmodule.exports = {\n  \"area\": true,\n  \"base\": true,\n  \"br\": true,\n  \"col\": true,\n  \"embed\": true,\n  \"hr\": true,\n  \"img\": true,\n  \"input\": true,\n  \"link\": true,\n  \"meta\": true,\n  \"param\": true,\n  \"source\": true,\n  \"track\": true,\n  \"wbr\": true\n};\n", "import { useContext } from 'react';\nimport { nodesToString, Trans as TransWithoutContext } from './TransWithoutContext.js';\nimport { getI18n, I18nContext } from './context.js';\nexport { nodesToString };\nexport function Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  const t = tFromProps || i18n?.t.bind(i18n);\n  return TransWithoutContext({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t?.ns || defaultNSFromContext || i18n?.options?.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}", "import { Fragment, isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { isObject, isString, warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props?.children ?? node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props?.children ?? node.children;\n  return node.props?.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nexport const nodesToString = (children, i18nOptions, i18n, i18nKey) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions?.transSupportBasicHtmlNodes ? i18nOptions.transKeepBasicHtmlNodesFor ?? [] : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (isString(child)) {\n      stringNode += `${child}`;\n      return;\n    }\n    if (isValidElement(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n        return;\n      }\n      if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n        return;\n      }\n      if (shouldKeepChild && childPropsCount === 1 && isString(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n        return;\n      }\n      const content = nodesToString(childChildren, i18nOptions, i18n, i18nKey);\n      stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      return;\n    }\n    if (child === null) {\n      warn(i18n, 'TRANS_NULL_VALUE', `Passed in a null value as child`, {\n        i18nKey\n      });\n      return;\n    }\n    if (isObject(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n        return;\n      }\n      warn(i18n, 'TRANS_INVALID_OBJ', `Invalid child - Object should only have keys {{ value, format }} (format is optional).`, {\n        i18nKey,\n        child\n      });\n      return;\n    }\n    warn(i18n, 'TRANS_INVALID_VAR', `Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.`, {\n      i18nKey,\n      child\n    });\n  });\n  return stringNode;\n};\nconst renderNodes = (children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (isString(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if (isObject(child) && !isValidElement(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = HTML.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props?.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return createElement(c.type, {\n          ...props,\n          key: i,\n          ref: c.props.ref ?? c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children?.[0]?.content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && isObject(child) && child.dummy && !isElement;\n        const isKnownComponent = isObject(children) && Object.hasOwnProperty.call(children, node.name);\n        if (isString(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (isObject(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nconst fixComponentProps = (component, index, translation) => {\n  const componentKey = component.key || index;\n  const comp = cloneElement(component, {\n    key: componentKey\n  });\n  if (!comp.props || !comp.props.children || translation.indexOf(`${index}/>`) < 0 && translation.indexOf(`${index} />`) < 0) {\n    return comp;\n  }\n  function Componentized() {\n    return createElement(Fragment, null, comp);\n  }\n  return createElement(Componentized, {\n    key: componentKey\n  });\n};\nconst generateArrayComponents = (components, translation) => components.map((c, index) => fixComponentProps(c, index, translation));\nconst generateObjectComponents = (components, translation) => {\n  const componentMap = {};\n  Object.keys(components).forEach(c => {\n    Object.assign(componentMap, {\n      [c]: fixComponentProps(components[c], c, translation)\n    });\n  });\n  return componentMap;\n};\nconst generateComponents = (components, translation, i18n, i18nKey) => {\n  if (!components) return null;\n  if (Array.isArray(components)) {\n    return generateArrayComponents(components, translation);\n  }\n  if (isObject(components)) {\n    return generateObjectComponents(components, translation);\n  }\n  warnOnce(i18n, 'TRANS_INVALID_COMPONENTS', `<Trans /> \"components\" prop expects an object or array`, {\n    i18nKey\n  });\n  return null;\n};\nexport function Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', `Trans: You need to pass in an i18next instance using i18nextReactModule`, {\n      i18nKey\n    });\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...getDefaults(),\n    ...i18n.options?.react\n  };\n  let namespaces = ns || t.ns || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions, i18n, i18nKey);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options?.interpolation?.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined && !i18n.options?.interpolation?.alwaysFormat || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  const generatedComponents = generateComponents(components, translation, i18n, i18nKey);\n  const content = renderNodes(generatedComponents || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent ?? reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}", "import lookup from 'void-elements'\nconst attrRE = /\\s([^'\"/\\s><]+?)[\\s/>]|([^\\s=]+)=\\s?(\".*?\"|'.*?')/g\n\nexport default function stringify(tag) {\n  const res = {\n    type: 'tag',\n    name: '',\n    voidElement: false,\n    attrs: {},\n    children: [],\n  }\n\n  const tagMatch = tag.match(/<\\/?([^\\s]+?)[/\\s>]/)\n  if (tagMatch) {\n    res.name = tagMatch[1]\n    if (\n      lookup[tagMatch[1]] ||\n      tag.charAt(tag.length - 2) === '/'\n    ) {\n      res.voidElement = true\n    }\n\n    // handle comment tag\n    if (res.name.startsWith('!--')) {\n      const endIndex = tag.indexOf('-->')\n      return {\n        type: 'comment',\n        comment: endIndex !== -1 ? tag.slice(4, endIndex) : '',\n      }\n    }\n  }\n\n  const reg = new RegExp(attrRE)\n  let result = null\n  for (;;) {\n    result = reg.exec(tag)\n\n    if (result === null) {\n      break\n    }\n\n    if (!result[0].trim()) {\n      continue\n    }\n\n    if (result[1]) {\n      const attr = result[1].trim()\n      let arr = [attr, '']\n\n      if (attr.indexOf('=') > -1) {\n        arr = attr.split('=')\n      }\n\n      res.attrs[arr[0]] = arr[1]\n      reg.lastIndex--\n    } else if (result[2]) {\n      res.attrs[result[2]] = result[3].trim().substring(1, result[3].length - 1)\n    }\n  }\n\n  return res\n}\n", "import parseTag from './parse-tag'\n\nconst tagRE = /<[a-zA-Z0-9\\-\\!\\/](?:\"[^\"]*\"|'[^']*'|[^'\">])*>/g\nconst whitespaceRE = /^\\s*$/\n\n// re-used obj for quick lookups of components\nconst empty = Object.create(null)\n\nexport default function parse(html, options) {\n  options || (options = {})\n  options.components || (options.components = empty)\n  const result = []\n  const arr = []\n  let current\n  let level = -1\n  let inComponent = false\n\n  // handle text at top level\n  if (html.indexOf('<') !== 0) {\n    var end = html.indexOf('<')\n    result.push({\n      type: 'text',\n      content: end === -1 ? html : html.substring(0, end),\n    })\n  }\n\n  html.replace(tagRE, function (tag, index) {\n    if (inComponent) {\n      if (tag !== '</' + current.name + '>') {\n        return\n      } else {\n        inComponent = false\n      }\n    }\n    const isOpen = tag.charAt(1) !== '/'\n    const isComment = tag.startsWith('<!--')\n    const start = index + tag.length\n    const nextChar = html.charAt(start)\n    let parent\n\n    if (isComment) {\n      const comment = parseTag(tag)\n\n      // if we're at root, push new base node\n      if (level < 0) {\n        result.push(comment)\n        return result\n      }\n      parent = arr[level]\n      parent.children.push(comment)\n      return result\n    }\n\n    if (isOpen) {\n      level++\n\n      current = parseTag(tag)\n      if (current.type === 'tag' && options.components[current.name]) {\n        current.type = 'component'\n        inComponent = true\n      }\n\n      if (\n        !current.voidElement &&\n        !inComponent &&\n        nextChar &&\n        nextChar !== '<'\n      ) {\n        current.children.push({\n          type: 'text',\n          content: html.slice(start, html.indexOf('<', start)),\n        })\n      }\n\n      // if we're at root, push new base node\n      if (level === 0) {\n        result.push(current)\n      }\n\n      parent = arr[level - 1]\n\n      if (parent) {\n        parent.children.push(current)\n      }\n\n      arr[level] = current\n    }\n\n    if (!isOpen || current.voidElement) {\n      if (\n        level > -1 &&\n        (current.voidElement || current.name === tag.slice(2, -1))\n      ) {\n        level--\n        // move current up a level to match the end tag\n        current = level === -1 ? result : arr[level]\n      }\n      if (!inComponent && nextChar !== '<' && nextChar) {\n        // trailing text node\n        // if we're at the root, push a base text node. otherwise add as\n        // a child to the current node.\n        parent = level === -1 ? result : arr[level].children\n\n        // calculate correct end of the content slice in case there's\n        // no tag after the text node.\n        const end = html.indexOf('<', start)\n        let content = html.slice(start, end === -1 ? undefined : end)\n        // if a node is nothing but whitespace, collapse it as the spec states:\n        // https://www.w3.org/TR/html4/struct/text.html#h-9.1\n        if (whitespaceRE.test(content)) {\n          content = ' '\n        }\n        // don't add whitespace-only text nodes if they would be trailing text nodes\n        // or if they would be leading whitespace-only text nodes:\n        //  * end > -1 indicates this is not a trailing text node\n        //  * leading node is when level is -1 and parent has length 0\n        if ((end > -1 && level + parent.length >= 0) || content !== ' ') {\n          parent.push({\n            type: 'text',\n            content: content,\n          })\n        }\n      }\n    }\n  })\n\n  return result\n}\n", "function attrString(attrs) {\n  const buff = []\n  for (let key in attrs) {\n    buff.push(key + '=\"' + attrs[key] + '\"')\n  }\n  if (!buff.length) {\n    return ''\n  }\n  return ' ' + buff.join(' ')\n}\n\nfunction stringify(buff, doc) {\n  switch (doc.type) {\n    case 'text':\n      return buff + doc.content\n    case 'tag':\n      buff +=\n        '<' +\n        doc.name +\n        (doc.attrs ? attrString(doc.attrs) : '') +\n        (doc.voidElement ? '/>' : '>')\n      if (doc.voidElement) {\n        return buff\n      }\n      return buff + doc.children.reduce(stringify, '') + '</' + doc.name + '>'\n    case 'comment':\n      buff += '<!--' + doc.comment + '-->'\n      return buff\n  }\n}\n\nexport default function (doc) {\n  return doc.reduce(function (token, rootEl) {\n    return token + stringify('', rootEl)\n  }, '')\n}\n", "import parse from './parse'\nimport stringify from './stringify'\n\nexport default {\n  parse,\n  stringify,\n}\n", "export const warn = (i18n, code, msg, rest) => {\n  const args = [msg, {\n    code,\n    ...(rest || {})\n  }];\n  if (i18n?.services?.logger?.forward) {\n    return i18n.services.logger.forward(args, 'warn', 'react-i18next::', true);\n  }\n  if (isString(args[0])) args[0] = `react-i18next:: ${args[0]}`;\n  if (i18n?.services?.logger?.warn) {\n    i18n.services.logger.warn(...args);\n  } else if (console?.warn) {\n    console.warn(...args);\n  }\n};\nconst alreadyWarned = {};\nexport const warnOnce = (i18n, code, msg, rest) => {\n  if (isString(msg) && alreadyWarned[msg]) return;\n  if (isString(msg)) alreadyWarned[msg] = new Date();\n  warn(i18n, code, msg, rest);\n};\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nexport const loadNamespaces = (i18n, ns, cb) => {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n};\nexport const loadLanguages = (i18n, lng, ns, cb) => {\n  if (isString(ns)) ns = [ns];\n  if (i18n.options.preload && i18n.options.preload.indexOf(lng) > -1) return loadNamespaces(i18n, ns, cb);\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n};\nexport const hasLoadedNamespace = (ns, i18n, options = {}) => {\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce(i18n, 'NO_LANGUAGES', 'i18n.languages were undefined or empty', {\n      languages: i18n.languages\n    });\n    return true;\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n?.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n};\nexport const getDisplayName = Component => Component.displayName || Component.name || (isString(Component) && Component.length > 0 ? Component : 'Unknown');\nexport const isString = obj => typeof obj === 'string';\nexport const isObject = obj => typeof obj === 'object' && obj !== null;", "const matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nexport const unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);", "import { unescape } from './unescape.js';\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape\n};\nexport const setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nexport const getDefaults = () => defaultOptions;", "let i18nInstance;\nexport const setI18n = instance => {\n  i18nInstance = instance;\n};\nexport const getI18n = () => i18nInstance;", "import { createContext } from 'react';\nimport { getDefaults, setDefaults } from './defaults.js';\nimport { getI18n, setI18n } from './i18nInstance.js';\nimport { initReactI18next } from './initReactI18next.js';\nexport { getDefaults, setDefaults, getI18n, setI18n, initReactI18next };\nexport const I18nContext = createContext();\nexport class ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nexport const composeInitialProps = ForComponent => async ctx => {\n  const componentsInitialProps = (await ForComponent.getInitialProps?.(ctx)) ?? {};\n  const i18nInitialProps = getInitialProps();\n  return {\n    ...componentsInitialProps,\n    ...i18nInitialProps\n  };\n};\nexport const getInitialProps = () => {\n  const i18n = getI18n();\n  const namespaces = i18n.reportNamespaces?.getUsedNamespaces() ?? [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n};", "import { setDefaults } from './defaults.js';\nimport { setI18n } from './i18nInstance.js';\nexport const initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    setDefaults(instance.options.react);\n    setI18n(instance);\n  }\n};", "import { useState, useEffect, useContext, useRef, useCallback } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context.js';\nimport { warnOnce, loadNamespaces, loadLanguages, hasLoadedNamespace, isString, isObject } from './utils.js';\nconst usePrevious = (value, ignore) => {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nconst alwaysNewT = (i18n, language, namespace, keyPrefix) => i18n.getFixedT(language, namespace, keyPrefix);\nconst useMemoizedT = (i18n, language, namespace, keyPrefix) => useCallback(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\nexport const useTranslation = (ns, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', 'useTranslation: You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if (isString(optsOrDefaultValue)) return optsOrDefaultValue;\n      if (isObject(optsOrDefaultValue) && isString(optsOrDefaultValue.defaultValue)) return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react?.wait) warnOnce(i18n, 'DEPRECATED_OPTION', 'useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...getDefaults(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  i18n.reportNamespaces.addUsedNamespaces?.(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => hasLoadedNamespace(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = useState(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = useRef(true);\n  useEffect(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        loadLanguages(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        loadNamespaces(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    const boundReset = () => {\n      if (isMounted.current) setT(getNewT);\n    };\n    if (bindI18n) i18n?.on(bindI18n, boundReset);\n    if (bindI18nStore) i18n?.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (i18n) bindI18n?.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  useEffect(() => {\n    if (isMounted.current && ready) {\n      setT(getT);\n    }\n  }, [i18n, keyPrefix, ready]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      loadLanguages(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      loadNamespaces(i18n, namespaces, () => resolve());\n    }\n  });\n};", "import { createElement, forwardRef as forwardRefReact } from 'react';\nimport { useTranslation } from './useTranslation.js';\nimport { getDisplayName } from './utils.js';\nexport const withTranslation = (ns, options = {}) => function Extend(WrappedComponent) {\n  function I18nextWithTranslation({\n    forwardedRef,\n    ...rest\n  }) {\n    const [t, i18n, ready] = useTranslation(ns, {\n      ...rest,\n      keyPrefix: options.keyPrefix\n    });\n    const passDownProps = {\n      ...rest,\n      t,\n      i18n,\n      tReady: ready\n    };\n    if (options.withRef && forwardedRef) {\n      passDownProps.ref = forwardedRef;\n    } else if (!options.withRef && forwardedRef) {\n      passDownProps.forwardedRef = forwardedRef;\n    }\n    return createElement(WrappedComponent, passDownProps);\n  }\n  I18nextWithTranslation.displayName = `withI18nextTranslation(${getDisplayName(WrappedComponent)})`;\n  I18nextWithTranslation.WrappedComponent = WrappedComponent;\n  const forwardRef = (props, ref) => createElement(I18nextWithTranslation, Object.assign({}, props, {\n    forwardedRef: ref\n  }));\n  return options.withRef ? forwardRefReact(forwardRef) : I18nextWithTranslation;\n};", "import { useTranslation } from './useTranslation.js';\nexport const Translation = ({\n  ns,\n  children,\n  ...options\n}) => {\n  const [t, i18n, ready] = useTranslation(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n};", "import { createElement, useMemo } from 'react';\nimport { I18nContext } from './context.js';\nexport function I18nextProvider({\n  i18n,\n  defaultNS,\n  children\n}) {\n  const value = useMemo(() => ({\n    i18n,\n    defaultNS\n  }), [i18n, defaultNS]);\n  return createElement(I18nContext.Provider, {\n    value\n  }, children);\n}", "import { createElement } from 'react';\nimport { useSSR } from './useSSR.js';\nimport { composeInitialProps } from './context.js';\nimport { getDisplayName } from './utils.js';\nexport const withSSR = () => function Extend(WrappedComponent) {\n  function I18nextWithSSR({\n    initialI18nStore,\n    initialLanguage,\n    ...rest\n  }) {\n    useSSR(initialI18nStore, initialLanguage);\n    return createElement(WrappedComponent, {\n      ...rest\n    });\n  }\n  I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n  I18nextWithSSR.displayName = `withI18nextSSR(${getDisplayName(WrappedComponent)})`;\n  I18nextWithSSR.WrappedComponent = WrappedComponent;\n  return I18nextWithSSR;\n};", "import { useContext } from 'react';\nimport { getI18n, I18nContext } from './context.js';\nexport const useSSR = (initialI18nStore, initialLanguage, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n.options?.isClone) return;\n  if (initialI18nStore && !i18n.initializedStoreOnce) {\n    i18n.services.resourceStore.data = initialI18nStore;\n    i18n.options.ns = Object.values(initialI18nStore).reduce((mem, lngResources) => {\n      Object.keys(lngResources).forEach(ns => {\n        if (mem.indexOf(ns) < 0) mem.push(ns);\n      });\n      return mem;\n    }, i18n.options.ns);\n    i18n.initializedStoreOnce = true;\n    i18n.isInitialized = true;\n  }\n  if (initialLanguage && !i18n.initializedLanguageOnce) {\n    i18n.changeLanguage(initialLanguage);\n    i18n.initializedLanguageOnce = true;\n  }\n};", "export { Trans } from './Trans.js';\nexport { Trans as TransWithoutContext } from './TransWithoutContext.js';\nexport { useTranslation } from './useTranslation.js';\nexport { withTranslation } from './withTranslation.js';\nexport { Translation } from './Translation.js';\nexport { I18nextProvider } from './I18nextProvider.js';\nexport { withSSR } from './withSSR.js';\nexport { useSSR } from './useSSR.js';\nexport { initReactI18next } from './initReactI18next.js';\nexport { setDefaults, getDefaults } from './defaults.js';\nexport { setI18n, getI18n } from './i18nInstance.js';\nexport { I18nContext, composeInitialProps, getInitialProps } from './context.js';\nexport const date = () => '';\nexport const time = () => '';\nexport const number = () => '';\nexport const select = () => '';\nexport const plural = () => '';\nexport const selectOrdinal = () => '';"], "mappings": ";;;;;;;;;AAAA;AAAA;AAKA,WAAO,UAAU;AAAA,MACf,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA;AAAA;;;ACpBA,IAAAA,gBAA2B;;;ACA3B,mBAAgF;;;;ACChF,IAAMC,IAAS;AAAA,SAESC,EAAUC,IAAAA;AAChC,MAAMC,KAAM,EACVC,MAAM,OACNC,MAAM,IACNC,aAAAA,OACAC,OAAO,CAAA,GACPC,UAAU,CAAA,EAAA,GAGNC,KAAWP,GAAIQ,MAAM,qBAAA;AAC3B,MAAID,OACFN,GAAIE,OAAOI,GAAS,CAAA,IAElBE,qBAAAA,QAAOF,GAAS,CAAA,CAAA,KACe,QAA/BP,GAAIU,OAAOV,GAAIW,SAAS,CAAA,OAExBV,GAAIG,cAAAA,OAIFH,GAAIE,KAAKS,WAAW,KAAA,IAAQ;AAC9B,QAAMC,KAAWb,GAAIc,QAAQ,KAAA;AAC7B,WAAO,EACLZ,MAAM,WACNa,SAAAA,OAASF,KAAkBb,GAAIgB,MAAM,GAAGH,EAAAA,IAAY,GAAA;EAAA;AAO1D,WAFMI,KAAM,IAAIC,OAAOpB,CAAAA,GACnBqB,KAAS,MAII,UAFfA,KAASF,GAAIG,KAAKpB,EAAAA,KAMlB,KAAKmB,GAAO,CAAA,EAAGE,KAAAA,EAIf,KAAIF,GAAO,CAAA,GAAI;AACb,QAAMG,IAAOH,GAAO,CAAA,EAAGE,KAAAA,GACnBE,IAAM,CAACD,GAAM,EAAA;AAEbA,MAAKR,QAAQ,GAAA,IAAA,OACfS,IAAMD,EAAKE,MAAM,GAAA,IAGnBvB,GAAII,MAAMkB,EAAI,CAAA,CAAA,IAAMA,EAAI,CAAA,GACxBN,GAAIQ;EAAAA,MACKN,CAAAA,GAAO,CAAA,MAChBlB,GAAII,MAAMc,GAAO,CAAA,CAAA,IAAMA,GAAO,CAAA,EAAGE,KAAAA,EAAOK,UAAU,GAAGP,GAAO,CAAA,EAAGR,SAAS,CAAA;AAI5E,SAAOV;AAAAA;AC1DT,IAAM0B,IAAQ;AAAd,IACMC,IAAe;AADrB,IAIMC,IAAQC,uBAAOC,OAAO,IAAA;ACK5B,SAAShC,EAAUiC,IAAMC,IAAAA;AACvB,UAAQA,GAAI/B,MAAAA;IACV,KAAK;AACH,aAAO8B,KAAOC,GAAIC;IACpB,KAAK;AAMH,aALAF,MACE,MACAC,GAAI9B,QACH8B,GAAI5B,QAnBb,SAAoBA,IAAAA;AAClB,YAAM2B,KAAO,CAAA;AACb,iBAASG,MAAO9B,GACd2B,CAAAA,GAAKI,KAAKD,KAAM,OAAO9B,GAAM8B,EAAAA,IAAO,GAAA;AAEtC,eAAKH,GAAKrB,SAGH,MAAMqB,GAAKK,KAAK,GAAA,IAFd;MAAA,EAaqBJ,GAAI5B,KAAAA,IAAS,OACpC4B,GAAI7B,cAAc,OAAO,MACxB6B,GAAI7B,cACC4B,KAEFA,KAAOC,GAAI3B,SAASgC,OAAOvC,GAAW,EAAA,IAAM,OAAOkC,GAAI9B,OAAO;IACvE,KAAK;AAEH,aADA6B,KAAQ,SAASC,GAAIlB,UAAU;EAAA;AAAA;AAAA,IAAA,ICvBtB,EACbwB,OFIF,SAA8BC,IAAMC,IAAAA;AAClCA,EAAAA,OAAYA,KAAU,CAAA,IACtBA,GAAQC,eAAeD,GAAQC,aAAab;AAC5C,MAEIc,IAFExB,KAAS,CAAA,GACTI,IAAM,CAAA,GAERqB,IAAAA,IACAC,IAAAA;AAGJ,MAA0B,MAAtBL,GAAK1B,QAAQ,GAAA,GAAY;AAC3B,QAAIgC,IAAMN,GAAK1B,QAAQ,GAAA;AACvBK,IAAAA,GAAOiB,KAAK,EACVlC,MAAM,QACNgC,SAAAA,OAASY,IAAaN,KAAOA,GAAKd,UAAU,GAAGoB,CAAAA,EAAAA,CAAAA;EAAAA;AAwGnD,SApGAN,GAAKO,QAAQpB,GAAO,SAAU3B,IAAKgD,IAAAA;AACjC,QAAIH,GAAa;AACf,UAAI7C,OAAQ,OAAO2C,GAAQxC,OAAO,IAChC;AAEA0C,UAAAA;IAAc;AAGlB,QAIII,IAJEC,IAA2B,QAAlBlD,GAAIU,OAAO,CAAA,GACpByC,IAAYnD,GAAIY,WAAW,MAAA,GAC3BwC,IAAQJ,KAAQhD,GAAIW,QACpB0C,IAAWb,GAAK9B,OAAO0C,CAAAA;AAG7B,QAAID,GAAW;AACb,UAAMpC,IAAUuC,EAAStD,EAAAA;AAGzB,aAAI4C,IAAQ,KACVzB,GAAOiB,KAAKrB,CAAAA,GACLI,QAET8B,KAAS1B,EAAIqB,CAAAA,GACNtC,SAAS8B,KAAKrB,CAAAA,GACdI;IAAAA;AAsCT,QAnCI+B,MACFN,KAGqB,WADrBD,KAAUW,EAAStD,EAAAA,GACPE,QAAkBuC,GAAQC,WAAWC,GAAQxC,IAAAA,MACvDwC,GAAQzC,OAAO,aACf2C,IAAAA,OAICF,GAAQvC,eACRyC,KAAAA,CACDQ,KACa,QAAbA,KAEAV,GAAQrC,SAAS8B,KAAK,EACpBlC,MAAM,QACNgC,SAASM,GAAKxB,MAAMoC,GAAOZ,GAAK1B,QAAQ,KAAKsC,CAAAA,CAAAA,EAAAA,CAAAA,GAKnC,MAAVR,KACFzB,GAAOiB,KAAKO,EAAAA,IAGdM,KAAS1B,EAAIqB,IAAQ,CAAA,MAGnBK,GAAO3C,SAAS8B,KAAKO,EAAAA,GAGvBpB,EAAIqB,CAAAA,IAASD,MAAAA,CAGVO,KAAUP,GAAQvC,iBAEnBwC,IAAAA,OACCD,GAAQvC,eAAeuC,GAAQxC,SAASH,GAAIgB,MAAM,GAAA,EAAI,OAEvD4B,KAEAD,KAAAA,OAAUC,IAAezB,KAASI,EAAIqB,CAAAA,IAAAA,CAEnCC,KAA4B,QAAbQ,KAAoBA,IAAU;AAIhDJ,MAAAA,KAAAA,OAASL,IAAezB,KAASI,EAAIqB,CAAAA,EAAOtC;AAI5C,UAAMwC,IAAMN,GAAK1B,QAAQ,KAAKsC,CAAAA,GAC1BlB,IAAUM,GAAKxB,MAAMoC,GAAAA,OAAON,IAAAA,SAAyBA,CAAAA;AAGrDlB,QAAa2B,KAAKrB,CAAAA,MACpBA,IAAU,OAMPY,IAAAA,MAAYF,IAAQK,GAAOtC,UAAU,KAAkB,QAAZuB,MAC9Ce,GAAOb,KAAK,EACVlC,MAAM,QACNgC,SAASA,EAAAA,CAAAA;IAAAA;EAAAA,CAAAA,GAOZf;AAAAA,GEzHPpB,WAAAA,SD0BuBkC,IAAAA;AACvB,SAAOA,GAAIK,OAAO,SAAUkB,IAAOC,IAAAA;AACjC,WAAOD,KAAQzD,EAAU,IAAI0D,EAAAA;EAAAA,GAC5B,EAAA;AAAA,EAAA;AAAA,IAAA,sCAAA;;;AElCE,IAAM,OAAO,CAAC,MAAM,MAAM,KAAK,SAAS;AAA/C;AACE,QAAM,OAAO,CAAC,KAAK;AAAA,IACjB;AAAA,IACA,GAAI,QAAQ,CAAC;AAAA,EACf,CAAC;AACD,OAAI,wCAAM,aAAN,mBAAgB,WAAhB,mBAAwB,SAAS;AACnC,WAAO,KAAK,SAAS,OAAO,QAAQ,MAAM,QAAQ,mBAAmB,IAAI;AAAA,EAC3E;AACA,MAAI,SAAS,KAAK,CAAC,CAAC,EAAG,MAAK,CAAC,IAAI,mBAAmB,KAAK,CAAC,CAAC;AAC3D,OAAI,wCAAM,aAAN,mBAAgB,WAAhB,mBAAwB,MAAM;AAChC,SAAK,SAAS,OAAO,KAAK,GAAG,IAAI;AAAA,EACnC,WAAW,mCAAS,MAAM;AACxB,YAAQ,KAAK,GAAG,IAAI;AAAA,EACtB;AACF;AACA,IAAM,gBAAgB,CAAC;AAChB,IAAM,WAAW,CAAC,MAAM,MAAM,KAAK,SAAS;AACjD,MAAI,SAAS,GAAG,KAAK,cAAc,GAAG,EAAG;AACzC,MAAI,SAAS,GAAG,EAAG,eAAc,GAAG,IAAI,oBAAI,KAAK;AACjD,OAAK,MAAM,MAAM,KAAK,IAAI;AAC5B;AACA,IAAM,YAAY,CAAC,MAAM,OAAO,MAAM;AACpC,MAAI,KAAK,eAAe;AACtB,OAAG;AAAA,EACL,OAAO;AACL,UAAM,cAAc,MAAM;AACxB,iBAAW,MAAM;AACf,aAAK,IAAI,eAAe,WAAW;AAAA,MACrC,GAAG,CAAC;AACJ,SAAG;AAAA,IACL;AACA,SAAK,GAAG,eAAe,WAAW;AAAA,EACpC;AACF;AACO,IAAM,iBAAiB,CAAC,MAAM,IAAI,OAAO;AAC9C,OAAK,eAAe,IAAI,UAAU,MAAM,EAAE,CAAC;AAC7C;AACO,IAAM,gBAAgB,CAAC,MAAM,KAAK,IAAI,OAAO;AAClD,MAAI,SAAS,EAAE,EAAG,MAAK,CAAC,EAAE;AAC1B,MAAI,KAAK,QAAQ,WAAW,KAAK,QAAQ,QAAQ,QAAQ,GAAG,IAAI,GAAI,QAAO,eAAe,MAAM,IAAI,EAAE;AACtG,KAAG,QAAQ,CAAAC,OAAK;AACd,QAAI,KAAK,QAAQ,GAAG,QAAQA,EAAC,IAAI,EAAG,MAAK,QAAQ,GAAG,KAAKA,EAAC;AAAA,EAC5D,CAAC;AACD,OAAK,cAAc,KAAK,UAAU,MAAM,EAAE,CAAC;AAC7C;AACO,IAAM,qBAAqB,CAAC,IAAI,MAAM,UAAU,CAAC,MAAM;AAC5D,MAAI,CAAC,KAAK,aAAa,CAAC,KAAK,UAAU,QAAQ;AAC7C,aAAS,MAAM,gBAAgB,0CAA0C;AAAA,MACvE,WAAW,KAAK;AAAA,IAClB,CAAC;AACD,WAAO;AAAA,EACT;AACA,SAAO,KAAK,mBAAmB,IAAI;AAAA,IACjC,KAAK,QAAQ;AAAA,IACb,UAAU,CAACC,eAAc,mBAAmB;AAtDhD;AAuDM,YAAI,aAAQ,aAAR,mBAAkB,QAAQ,uBAAsB,MAAMA,cAAa,SAAS,iBAAiB,WAAWA,cAAa,wBAAwB,CAAC,eAAeA,cAAa,sBAAsB,EAAE,EAAG,QAAO;AAAA,IAClN;AAAA,EACF,CAAC;AACH;AACO,IAAM,iBAAiB,eAAa,UAAU,eAAe,UAAU,SAAS,SAAS,SAAS,KAAK,UAAU,SAAS,IAAI,YAAY;AAC1I,IAAM,WAAW,SAAO,OAAO,QAAQ;AACvC,IAAM,WAAW,SAAO,OAAO,QAAQ,YAAY,QAAQ;;;AC7DlE,IAAM,kBAAkB;AACxB,IAAM,eAAe;AAAA,EACnB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AACX;AACA,IAAM,qBAAqB,OAAK,aAAa,CAAC;AACvC,IAAM,WAAW,UAAQ,KAAK,QAAQ,iBAAiB,kBAAkB;;;ACvBhF,IAAI,iBAAiB;AAAA,EACnB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,4BAA4B;AAAA,EAC5B,oBAAoB;AAAA,EACpB,4BAA4B,CAAC,MAAM,UAAU,KAAK,GAAG;AAAA,EACrD,aAAa;AAAA,EACb;AACF;AACO,IAAM,cAAc,CAAC,UAAU,CAAC,MAAM;AAC3C,mBAAiB;AAAA,IACf,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AACO,IAAM,cAAc,MAAM;;;ACjBjC,IAAI;AACG,IAAM,UAAU,cAAY;AACjC,iBAAe;AACjB;AACO,IAAM,UAAU,MAAM;;;ARC7B,IAAM,cAAc,CAAC,MAAM,gBAAgB;AAL3C;AAME,MAAI,CAAC,KAAM,QAAO;AAClB,QAAM,SAAO,UAAK,UAAL,mBAAY,aAAY,KAAK;AAC1C,MAAI,YAAa,QAAO,KAAK,SAAS;AACtC,SAAO,CAAC,CAAC;AACX;AACA,IAAM,cAAc,UAAQ;AAX5B;AAYE,MAAI,CAAC,KAAM,QAAO,CAAC;AACnB,QAAM,aAAW,UAAK,UAAL,mBAAY,aAAY,KAAK;AAC9C,WAAO,UAAK,UAAL,mBAAY,qBAAoB,WAAW,QAAQ,IAAI;AAChE;AACA,IAAM,wBAAwB,cAAY,MAAM,QAAQ,QAAQ,KAAK,SAAS,MAAM,2BAAc;AAClG,IAAM,aAAa,UAAQ,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAC7D,IAAM,aAAa,CAAC,QAAQ,WAAW;AACrC,QAAM,YAAY;AAAA,IAChB,GAAG;AAAA,EACL;AACA,YAAU,QAAQ,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAC1D,SAAO;AACT;AACO,IAAM,gBAAgB,CAAC,UAAU,aAAa,MAAM,YAAY;AACrE,MAAI,CAAC,SAAU,QAAO;AACtB,MAAI,aAAa;AACjB,QAAM,gBAAgB,WAAW,QAAQ;AACzC,QAAM,aAAY,2CAAa,8BAA6B,YAAY,8BAA8B,CAAC,IAAI,CAAC;AAC5G,gBAAc,QAAQ,CAAC,OAAO,eAAe;AAC3C,QAAI,SAAS,KAAK,GAAG;AACnB,oBAAc,GAAG,KAAK;AACtB;AAAA,IACF;AACA,YAAI,6BAAe,KAAK,GAAG;AACzB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,kBAAkB,OAAO,KAAK,KAAK,EAAE;AAC3C,YAAM,kBAAkB,UAAU,QAAQ,IAAI,IAAI;AAClD,YAAM,gBAAgB,MAAM;AAC5B,UAAI,CAAC,iBAAiB,mBAAmB,CAAC,iBAAiB;AACzD,sBAAc,IAAI,IAAI;AACtB;AAAA,MACF;AACA,UAAI,CAAC,kBAAkB,CAAC,mBAAmB,oBAAoB,MAAM,mBAAmB;AACtF,sBAAc,IAAI,UAAU,MAAM,UAAU;AAC5C;AAAA,MACF;AACA,UAAI,mBAAmB,oBAAoB,KAAK,SAAS,aAAa,GAAG;AACvE,sBAAc,IAAI,IAAI,IAAI,aAAa,KAAK,IAAI;AAChD;AAAA,MACF;AACA,YAAM,UAAU,cAAc,eAAe,aAAa,MAAM,OAAO;AACvE,oBAAc,IAAI,UAAU,IAAI,OAAO,KAAK,UAAU;AACtD;AAAA,IACF;AACA,QAAI,UAAU,MAAM;AAClB,WAAK,MAAM,oBAAoB,mCAAmC;AAAA,QAChE;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,QAAI,SAAS,KAAK,GAAG;AACnB,YAAM;AAAA,QACJ;AAAA,QACA,GAAG;AAAA,MACL,IAAI;AACJ,YAAM,OAAO,OAAO,KAAK,KAAK;AAC9B,UAAI,KAAK,WAAW,GAAG;AACrB,cAAM,QAAQ,SAAS,GAAG,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC;AACvD,sBAAc,KAAK,KAAK;AACxB;AAAA,MACF;AACA,WAAK,MAAM,qBAAqB,0FAA0F;AAAA,QACxH;AAAA,QACA;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,SAAK,MAAM,qBAAqB,0GAA0G;AAAA,MACxI;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AACA,IAAM,cAAc,CAAC,UAAU,cAAc,MAAM,aAAa,eAAe,mBAAmB;AAChG,MAAI,iBAAiB,GAAI,QAAO,CAAC;AACjC,QAAM,YAAY,YAAY,8BAA8B,CAAC;AAC7D,QAAM,gCAAgC,gBAAgB,IAAI,OAAO,UAAU,IAAI,UAAQ,IAAI,IAAI,EAAE,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,YAAY;AAC/H,MAAI,CAAC,YAAY,CAAC,iCAAiC,CAAC,eAAgB,QAAO,CAAC,YAAY;AACxF,QAAM,OAAO,CAAC;AACd,QAAM,UAAU,YAAU;AACxB,UAAM,gBAAgB,WAAW,MAAM;AACvC,kBAAc,QAAQ,WAAS;AAC7B,UAAI,SAAS,KAAK,EAAG;AACrB,UAAI,YAAY,KAAK,EAAG,SAAQ,YAAY,KAAK,CAAC;AAAA,eAAW,SAAS,KAAK,KAAK,KAAC,6BAAe,KAAK,EAAG,QAAO,OAAO,MAAM,KAAK;AAAA,IACnI,CAAC;AAAA,EACH;AACA,UAAQ,QAAQ;AAChB,QAAM,MAAM,oCAAK,MAAM,MAAM,YAAY,MAAM;AAC/C,QAAM,OAAO;AAAA,IACX,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,cAAc,CAAC,OAAO,MAAM,kBAAkB;AA5GtD;AA6GI,UAAM,SAAS,YAAY,KAAK;AAChC,UAAM,iBAAiB,OAAO,QAAQ,KAAK,UAAU,aAAa;AAClE,WAAO,sBAAsB,MAAM,KAAK,eAAe,WAAW,OAAK,WAAM,UAAN,mBAAa,qBAAoB,SAAS;AAAA,EACnH;AACA,QAAM,oBAAoB,CAAC,OAAO,OAAO,KAAKC,IAAG,WAAW;AAC1D,QAAI,MAAM,OAAO;AACf,YAAM,WAAW;AACjB,UAAI,SAAK,2BAAa,OAAO;AAAA,QAC3B,KAAKA;AAAA,MACP,GAAG,SAAS,SAAY,KAAK,CAAC;AAAA,IAChC,OAAO;AACL,UAAI,KAAK,GAAG,sBAAS,IAAI,CAAC,KAAK,GAAG,CAAAC,OAAK;AACrC,cAAM,QAAQ;AAAA,UACZ,GAAGA,GAAE;AAAA,QACP;AACA,eAAO,MAAM;AACb,mBAAO,4BAAcA,GAAE,MAAM;AAAA,UAC3B,GAAG;AAAA,UACH,KAAKD;AAAA,UACL,KAAKC,GAAE,MAAM,OAAOA,GAAE;AAAA,QACxB,GAAG,SAAS,OAAO,KAAK;AAAA,MAC1B,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACA,QAAM,SAAS,CAAC,WAAW,SAAS,kBAAkB;AACpD,UAAM,aAAa,WAAW,SAAS;AACvC,UAAM,WAAW,WAAW,OAAO;AACnC,WAAO,SAAS,OAAO,CAAC,KAAK,MAAMD,OAAM;AAxI7C;AAyIM,YAAM,uBAAqB,gBAAK,aAAL,mBAAgB,OAAhB,mBAAoB,YAAW,KAAK,SAAS,aAAa,YAAY,KAAK,SAAS,CAAC,EAAE,SAAS,MAAM,KAAK,QAAQ;AAC9I,UAAI,KAAK,SAAS,OAAO;AACvB,YAAI,MAAM,WAAW,SAAS,KAAK,MAAM,EAAE,CAAC;AAC5C,YAAI,cAAc,WAAW,KAAK,CAAC,IAAK,OAAM,cAAc,CAAC,EAAE,KAAK,IAAI;AACxE,YAAI,CAAC,IAAK,OAAM,CAAC;AACjB,cAAM,QAAQ,OAAO,KAAK,KAAK,KAAK,EAAE,WAAW,IAAI,WAAW;AAAA,UAC9D,OAAO,KAAK;AAAA,QACd,GAAG,GAAG,IAAI;AACV,cAAM,gBAAY,6BAAe,KAAK;AACtC,cAAM,iCAAiC,aAAa,YAAY,MAAM,IAAI,KAAK,CAAC,KAAK;AACrF,cAAM,uBAAuB,iCAAiC,SAAS,KAAK,KAAK,MAAM,SAAS,CAAC;AACjG,cAAM,mBAAmB,SAAS,QAAQ,KAAK,OAAO,eAAe,KAAK,UAAU,KAAK,IAAI;AAC7F,YAAI,SAAS,KAAK,GAAG;AACnB,gBAAM,QAAQ,KAAK,SAAS,aAAa,YAAY,OAAO,MAAM,KAAK,QAAQ;AAC/E,cAAI,KAAK,KAAK;AAAA,QAChB,WAAW,YAAY,KAAK,KAAK,gCAAgC;AAC/D,gBAAM,QAAQ,YAAY,OAAO,MAAM,aAAa;AACpD,4BAAkB,OAAO,OAAO,KAAKA,EAAC;AAAA,QACxC,WAAW,sBAAsB;AAC/B,gBAAM,QAAQ,OAAO,YAAY,KAAK,UAAU,aAAa;AAC7D,4BAAkB,OAAO,OAAO,KAAKA,EAAC;AAAA,QACxC,WAAW,OAAO,MAAM,WAAW,KAAK,IAAI,CAAC,GAAG;AAC9C,cAAI,kBAAkB;AACpB,kBAAM,QAAQ,YAAY,OAAO,MAAM,aAAa;AACpD,8BAAkB,OAAO,OAAO,KAAKA,IAAG,KAAK,WAAW;AAAA,UAC1D,WAAW,YAAY,8BAA8B,UAAU,QAAQ,KAAK,IAAI,IAAI,IAAI;AACtF,gBAAI,KAAK,aAAa;AACpB,kBAAI,SAAK,4BAAc,KAAK,MAAM;AAAA,gBAChC,KAAK,GAAG,KAAK,IAAI,IAAIA,EAAC;AAAA,cACxB,CAAC,CAAC;AAAA,YACJ,OAAO;AACL,oBAAM,QAAQ,OAAO,YAAY,KAAK,UAAU,aAAa;AAC7D,kBAAI,SAAK,4BAAc,KAAK,MAAM;AAAA,gBAChC,KAAK,GAAG,KAAK,IAAI,IAAIA,EAAC;AAAA,cACxB,GAAG,KAAK,CAAC;AAAA,YACX;AAAA,UACF,WAAW,KAAK,aAAa;AAC3B,gBAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AAAA,UAC7B,OAAO;AACL,kBAAM,QAAQ,OAAO,YAAY,KAAK,UAAU,aAAa;AAC7D,gBAAI,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG;AAAA,UAClD;AAAA,QACF,WAAW,SAAS,KAAK,KAAK,CAAC,WAAW;AACxC,gBAAM,UAAU,KAAK,SAAS,CAAC,IAAI,qBAAqB;AACxD,cAAI,QAAS,KAAI,KAAK,OAAO;AAAA,QAC/B,OAAO;AACL,4BAAkB,OAAO,oBAAoB,KAAKA,IAAG,KAAK,SAAS,WAAW,KAAK,CAAC,kBAAkB;AAAA,QACxG;AAAA,MACF,WAAW,KAAK,SAAS,QAAQ;AAC/B,cAAM,gBAAgB,YAAY;AAClC,cAAM,UAAU,iBAAiB,YAAY,SAAS,KAAK,SAAS,aAAa,YAAY,KAAK,SAAS,MAAM,KAAK,QAAQ,CAAC,IAAI,KAAK,SAAS,aAAa,YAAY,KAAK,SAAS,MAAM,KAAK,QAAQ;AAC3M,YAAI,eAAe;AACjB,cAAI,SAAK,4BAAc,eAAe;AAAA,YACpC,KAAK,GAAG,KAAK,IAAI,IAAIA,EAAC;AAAA,UACxB,GAAG,OAAO,CAAC;AAAA,QACb,OAAO;AACL,cAAI,KAAK,OAAO;AAAA,QAClB;AAAA,MACF;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACA,QAAM,SAAS,OAAO,CAAC;AAAA,IACrB,OAAO;AAAA,IACP,UAAU,YAAY,CAAC;AAAA,EACzB,CAAC,GAAG,KAAK,WAAW,YAAY,CAAC,CAAC,CAAC;AACnC,SAAO,YAAY,OAAO,CAAC,CAAC;AAC9B;AACA,IAAM,oBAAoB,CAAC,WAAW,OAAO,gBAAgB;AAC3D,QAAM,eAAe,UAAU,OAAO;AACtC,QAAM,WAAO,2BAAa,WAAW;AAAA,IACnC,KAAK;AAAA,EACP,CAAC;AACD,MAAI,CAAC,KAAK,SAAS,CAAC,KAAK,MAAM,YAAY,YAAY,QAAQ,GAAG,KAAK,IAAI,IAAI,KAAK,YAAY,QAAQ,GAAG,KAAK,KAAK,IAAI,GAAG;AAC1H,WAAO;AAAA,EACT;AACA,WAAS,gBAAgB;AACvB,eAAO,4BAAc,uBAAU,MAAM,IAAI;AAAA,EAC3C;AACA,aAAO,4BAAc,eAAe;AAAA,IAClC,KAAK;AAAA,EACP,CAAC;AACH;AACA,IAAM,0BAA0B,CAAC,YAAY,gBAAgB,WAAW,IAAI,CAACC,IAAG,UAAU,kBAAkBA,IAAG,OAAO,WAAW,CAAC;AAClI,IAAM,2BAA2B,CAAC,YAAY,gBAAgB;AAC5D,QAAM,eAAe,CAAC;AACtB,SAAO,KAAK,UAAU,EAAE,QAAQ,CAAAA,OAAK;AACnC,WAAO,OAAO,cAAc;AAAA,MAC1B,CAACA,EAAC,GAAG,kBAAkB,WAAWA,EAAC,GAAGA,IAAG,WAAW;AAAA,IACtD,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AACA,IAAM,qBAAqB,CAAC,YAAY,aAAa,MAAM,YAAY;AACrE,MAAI,CAAC,WAAY,QAAO;AACxB,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,WAAO,wBAAwB,YAAY,WAAW;AAAA,EACxD;AACA,MAAI,SAAS,UAAU,GAAG;AACxB,WAAO,yBAAyB,YAAY,WAAW;AAAA,EACzD;AACA,WAAS,MAAM,4BAA4B,0DAA0D;AAAA,IACnG;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACO,SAAS,MAAM;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW,CAAC;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,GAAG;AAAA,EACH;AAAA,EACA,GAAG;AACL,GAAG;AAlQH;AAmQE,QAAM,OAAO,iBAAiB,QAAQ;AACtC,MAAI,CAAC,MAAM;AACT,aAAS,MAAM,uBAAuB,2EAA2E;AAAA,MAC/G;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAMC,KAAI,cAAc,KAAK,EAAE,KAAK,IAAI,MAAM,OAAK;AACnD,QAAM,sBAAsB;AAAA,IAC1B,GAAG,YAAY;AAAA,IACf,IAAG,UAAK,YAAL,mBAAc;AAAA,EACnB;AACA,MAAI,aAAa,MAAMA,GAAE,QAAM,UAAK,YAAL,mBAAc;AAC7C,eAAa,SAAS,UAAU,IAAI,CAAC,UAAU,IAAI,cAAc,CAAC,aAAa;AAC/E,QAAM,eAAe,cAAc,UAAU,qBAAqB,MAAM,OAAO;AAC/E,QAAM,eAAe,YAAY,gBAAgB,oBAAoB,uBAAuB;AAC5F,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,MAAM,YAAY,eAAe,aAAa,gBAAgB,YAAY,IAAI,gBAAgB;AACpG,OAAI,gBAAK,YAAL,mBAAc,kBAAd,mBAA6B,kBAAkB;AACjD,aAAS,UAAU,OAAO,KAAK,MAAM,EAAE,SAAS,IAAI;AAAA,MAClD,GAAG;AAAA,MACH,GAAG,KAAK,QAAQ,cAAc;AAAA,IAChC,IAAI;AAAA,MACF,GAAG,KAAK,QAAQ,cAAc;AAAA,IAChC;AAAA,EACF;AACA,QAAM,wBAAwB,UAAU,UAAU,UAAa,GAAC,gBAAK,YAAL,mBAAc,kBAAd,mBAA6B,iBAAgB,CAAC,WAAW,SAAS,gBAAgB;AAAA,IAChJ,eAAe;AAAA,MACb,GAAG,SAAS;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AACA,QAAM,gBAAgB;AAAA,IACpB,GAAG;AAAA,IACH,SAAS,WAAW,SAAS;AAAA,IAC7B;AAAA,IACA,GAAG;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA,IAAI;AAAA,EACN;AACA,QAAM,cAAc,MAAMA,GAAE,KAAK,aAAa,IAAI;AAClD,QAAM,sBAAsB,mBAAmB,YAAY,aAAa,MAAM,OAAO;AACrF,QAAM,UAAU,YAAY,uBAAuB,UAAU,aAAa,MAAM,qBAAqB,eAAe,cAAc;AAClI,QAAM,cAAc,UAAU,oBAAoB;AAClD,SAAO,kBAAc,4BAAc,aAAa,iBAAiB,OAAO,IAAI;AAC9E;;;ASpTA,IAAAC,gBAA8B;;;ACEvB,IAAM,mBAAmB;AAAA,EAC9B,MAAM;AAAA,EACN,KAAK,UAAU;AACb,gBAAY,SAAS,QAAQ,KAAK;AAClC,YAAQ,QAAQ;AAAA,EAClB;AACF;;;ADHO,IAAM,kBAAc,6BAAc;AAClC,IAAM,mBAAN,MAAuB;AAAA,EAC5B,cAAc;AACZ,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA,EACA,kBAAkB,YAAY;AAC5B,eAAW,QAAQ,QAAM;AACvB,UAAI,CAAC,KAAK,eAAe,EAAE,EAAG,MAAK,eAAe,EAAE,IAAI;AAAA,IAC1D,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,WAAO,OAAO,KAAK,KAAK,cAAc;AAAA,EACxC;AACF;AACO,IAAM,sBAAsB,kBAAgB,OAAM,QAAO;AAnBhE;AAoBE,QAAM,yBAA0B,QAAM,kBAAa,oBAAb,sCAA+B,SAAS,CAAC;AAC/E,QAAM,mBAAmB,gBAAgB;AACzC,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AACO,IAAM,kBAAkB,MAAM;AA3BrC;AA4BE,QAAM,OAAO,QAAQ;AACrB,QAAM,eAAa,UAAK,qBAAL,mBAAuB,wBAAuB,CAAC;AAClE,QAAM,MAAM,CAAC;AACb,QAAM,mBAAmB,CAAC;AAC1B,OAAK,UAAU,QAAQ,OAAK;AAC1B,qBAAiB,CAAC,IAAI,CAAC;AACvB,eAAW,QAAQ,QAAM;AACvB,uBAAiB,CAAC,EAAE,EAAE,IAAI,KAAK,kBAAkB,GAAG,EAAE,KAAK,CAAC;AAAA,IAC9D,CAAC;AAAA,EACH,CAAC;AACD,MAAI,mBAAmB;AACvB,MAAI,kBAAkB,KAAK;AAC3B,SAAO;AACT;;;AVrCO,SAASC,OAAM;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW,CAAC;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,GAAG;AAAA,EACH;AAAA,EACA,GAAG;AACL,GAAG;AAnBH;AAoBE,QAAM;AAAA,IACJ,MAAM;AAAA,IACN,WAAW;AAAA,EACb,QAAI,0BAAW,WAAW,KAAK,CAAC;AAChC,QAAM,OAAO,iBAAiB,mBAAmB,QAAQ;AACzD,QAAMC,KAAI,eAAc,6BAAM,EAAE,KAAK;AACrC,SAAO,MAAoB;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAI,OAAMA,MAAA,gBAAAA,GAAG,OAAM,0BAAwB,kCAAM,YAAN,mBAAe;AAAA,IAC1D;AAAA,IACA,GAAG;AAAA,IACH;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH;;;AY1CA,IAAAC,gBAAqE;AAGrE,IAAM,cAAc,CAAC,OAAO,WAAW;AACrC,QAAM,UAAM,sBAAO;AACnB,+BAAU,MAAM;AACd,QAAI,UAAU,SAAS,IAAI,UAAU;AAAA,EACvC,GAAG,CAAC,OAAO,MAAM,CAAC;AAClB,SAAO,IAAI;AACb;AACA,IAAM,aAAa,CAAC,MAAM,UAAU,WAAW,cAAc,KAAK,UAAU,UAAU,WAAW,SAAS;AAC1G,IAAM,eAAe,CAAC,MAAM,UAAU,WAAW,kBAAc,2BAAY,WAAW,MAAM,UAAU,WAAW,SAAS,GAAG,CAAC,MAAM,UAAU,WAAW,SAAS,CAAC;AAC5J,IAAM,iBAAiB,CAAC,IAAI,QAAQ,CAAC,MAAM;AAZlD;AAaE,QAAM;AAAA,IACJ,MAAM;AAAA,EACR,IAAI;AACJ,QAAM;AAAA,IACJ,MAAM;AAAA,IACN,WAAW;AAAA,EACb,QAAI,0BAAW,WAAW,KAAK,CAAC;AAChC,QAAM,OAAO,iBAAiB,mBAAmB,QAAQ;AACzD,MAAI,QAAQ,CAAC,KAAK,iBAAkB,MAAK,mBAAmB,IAAI,iBAAiB;AACjF,MAAI,CAAC,MAAM;AACT,aAAS,MAAM,uBAAuB,wFAAwF;AAC9H,UAAM,YAAY,CAAC,GAAG,uBAAuB;AAC3C,UAAI,SAAS,kBAAkB,EAAG,QAAO;AACzC,UAAI,SAAS,kBAAkB,KAAK,SAAS,mBAAmB,YAAY,EAAG,QAAO,mBAAmB;AACzG,aAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,EAAE,SAAS,CAAC,IAAI;AAAA,IAC9C;AACA,UAAM,cAAc,CAAC,WAAW,CAAC,GAAG,KAAK;AACzC,gBAAY,IAAI;AAChB,gBAAY,OAAO,CAAC;AACpB,gBAAY,QAAQ;AACpB,WAAO;AAAA,EACT;AACA,OAAI,UAAK,QAAQ,UAAb,mBAAoB,KAAM,UAAS,MAAM,qBAAqB,qHAAqH;AACvL,QAAM,cAAc;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,KAAK,QAAQ;AAAA,IAChB,GAAG;AAAA,EACL;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,aAAa,MAAM,0BAAwB,UAAK,YAAL,mBAAc;AAC7D,eAAa,SAAS,UAAU,IAAI,CAAC,UAAU,IAAI,cAAc,CAAC,aAAa;AAC/E,mBAAK,kBAAiB,sBAAtB,4BAA0C;AAC1C,QAAM,SAAS,KAAK,iBAAiB,KAAK,yBAAyB,WAAW,MAAM,CAAAC,OAAK,mBAAmBA,IAAG,MAAM,WAAW,CAAC;AACjI,QAAM,WAAW,aAAa,MAAM,MAAM,OAAO,MAAM,YAAY,WAAW,aAAa,aAAa,WAAW,CAAC,GAAG,SAAS;AAChI,QAAM,OAAO,MAAM;AACnB,QAAM,UAAU,MAAM,WAAW,MAAM,MAAM,OAAO,MAAM,YAAY,WAAW,aAAa,aAAa,WAAW,CAAC,GAAG,SAAS;AACnI,QAAM,CAACC,IAAG,IAAI,QAAI,wBAAS,IAAI;AAC/B,MAAI,WAAW,WAAW,KAAK;AAC/B,MAAI,MAAM,IAAK,YAAW,GAAG,MAAM,GAAG,GAAG,QAAQ;AACjD,QAAM,mBAAmB,YAAY,QAAQ;AAC7C,QAAM,gBAAY,sBAAO,IAAI;AAC7B,+BAAU,MAAM;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,cAAU,UAAU;AACpB,QAAI,CAAC,SAAS,CAAC,aAAa;AAC1B,UAAI,MAAM,KAAK;AACb,sBAAc,MAAM,MAAM,KAAK,YAAY,MAAM;AAC/C,cAAI,UAAU,QAAS,MAAK,OAAO;AAAA,QACrC,CAAC;AAAA,MACH,OAAO;AACL,uBAAe,MAAM,YAAY,MAAM;AACrC,cAAI,UAAU,QAAS,MAAK,OAAO;AAAA,QACrC,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,SAAS,oBAAoB,qBAAqB,YAAY,UAAU,SAAS;AACnF,WAAK,OAAO;AAAA,IACd;AACA,UAAM,aAAa,MAAM;AACvB,UAAI,UAAU,QAAS,MAAK,OAAO;AAAA,IACrC;AACA,QAAI,SAAU,8BAAM,GAAG,UAAU;AACjC,QAAI,cAAe,8BAAM,MAAM,GAAG,eAAe;AACjD,WAAO,MAAM;AACX,gBAAU,UAAU;AACpB,UAAI,KAAM,sCAAU,MAAM,KAAK,QAAQ,CAAAC,OAAK,KAAK,IAAIA,IAAG,UAAU;AAClE,UAAI,iBAAiB,KAAM,eAAc,MAAM,GAAG,EAAE,QAAQ,CAAAA,OAAK,KAAK,MAAM,IAAIA,IAAG,UAAU,CAAC;AAAA,IAChG;AAAA,EACF,GAAG,CAAC,MAAM,QAAQ,CAAC;AACnB,+BAAU,MAAM;AACd,QAAI,UAAU,WAAW,OAAO;AAC9B,WAAK,IAAI;AAAA,IACX;AAAA,EACF,GAAG,CAAC,MAAM,WAAW,KAAK,CAAC;AAC3B,QAAM,MAAM,CAACD,IAAG,MAAM,KAAK;AAC3B,MAAI,IAAIA;AACR,MAAI,OAAO;AACX,MAAI,QAAQ;AACZ,MAAI,MAAO,QAAO;AAClB,MAAI,CAAC,SAAS,CAAC,YAAa,QAAO;AACnC,QAAM,IAAI,QAAQ,aAAW;AAC3B,QAAI,MAAM,KAAK;AACb,oBAAc,MAAM,MAAM,KAAK,YAAY,MAAM,QAAQ,CAAC;AAAA,IAC5D,OAAO;AACL,qBAAe,MAAM,YAAY,MAAM,QAAQ,CAAC;AAAA,IAClD;AAAA,EACF,CAAC;AACH;;;AC1GA,IAAAE,gBAA6D;AAGtD,IAAM,kBAAkB,CAAC,IAAI,UAAU,CAAC,MAAM,SAAS,OAAO,kBAAkB;AACrF,WAAS,uBAAuB;AAAA,IAC9B;AAAA,IACA,GAAG;AAAA,EACL,GAAG;AACD,UAAM,CAACC,IAAG,MAAM,KAAK,IAAI,eAAe,IAAI;AAAA,MAC1C,GAAG;AAAA,MACH,WAAW,QAAQ;AAAA,IACrB,CAAC;AACD,UAAM,gBAAgB;AAAA,MACpB,GAAG;AAAA,MACH,GAAAA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,IACV;AACA,QAAI,QAAQ,WAAW,cAAc;AACnC,oBAAc,MAAM;AAAA,IACtB,WAAW,CAAC,QAAQ,WAAW,cAAc;AAC3C,oBAAc,eAAe;AAAA,IAC/B;AACA,eAAO,6BAAc,kBAAkB,aAAa;AAAA,EACtD;AACA,yBAAuB,cAAc,0BAA0B,eAAe,gBAAgB,CAAC;AAC/F,yBAAuB,mBAAmB;AAC1C,QAAM,aAAa,CAAC,OAAO,YAAQ,6BAAc,wBAAwB,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,IAChG,cAAc;AAAA,EAChB,CAAC,CAAC;AACF,SAAO,QAAQ,cAAU,cAAAC,YAAgB,UAAU,IAAI;AACzD;;;AC9BO,IAAM,cAAc,CAAC;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,GAAG;AACL,MAAM;AACJ,QAAM,CAACC,IAAG,MAAM,KAAK,IAAI,eAAe,IAAI,OAAO;AACnD,SAAO,SAASA,IAAG;AAAA,IACjB;AAAA,IACA,KAAK,KAAK;AAAA,EACZ,GAAG,KAAK;AACV;;;ACXA,IAAAC,gBAAuC;AAEhC,SAAS,gBAAgB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,YAAQ,uBAAQ,OAAO;AAAA,IAC3B;AAAA,IACA;AAAA,EACF,IAAI,CAAC,MAAM,SAAS,CAAC;AACrB,aAAO,6BAAc,YAAY,UAAU;AAAA,IACzC;AAAA,EACF,GAAG,QAAQ;AACb;;;ACdA,IAAAC,gBAA8B;;;ACA9B,IAAAC,gBAA2B;AAEpB,IAAM,SAAS,CAAC,kBAAkB,iBAAiB,QAAQ,CAAC,MAAM;AAFzE;AAGE,QAAM;AAAA,IACJ,MAAM;AAAA,EACR,IAAI;AACJ,QAAM;AAAA,IACJ,MAAM;AAAA,EACR,QAAI,0BAAW,WAAW,KAAK,CAAC;AAChC,QAAM,OAAO,iBAAiB,mBAAmB,QAAQ;AACzD,OAAI,UAAK,YAAL,mBAAc,QAAS;AAC3B,MAAI,oBAAoB,CAAC,KAAK,sBAAsB;AAClD,SAAK,SAAS,cAAc,OAAO;AACnC,SAAK,QAAQ,KAAK,OAAO,OAAO,gBAAgB,EAAE,OAAO,CAAC,KAAK,iBAAiB;AAC9E,aAAO,KAAK,YAAY,EAAE,QAAQ,QAAM;AACtC,YAAI,IAAI,QAAQ,EAAE,IAAI,EAAG,KAAI,KAAK,EAAE;AAAA,MACtC,CAAC;AACD,aAAO;AAAA,IACT,GAAG,KAAK,QAAQ,EAAE;AAClB,SAAK,uBAAuB;AAC5B,SAAK,gBAAgB;AAAA,EACvB;AACA,MAAI,mBAAmB,CAAC,KAAK,yBAAyB;AACpD,SAAK,eAAe,eAAe;AACnC,SAAK,0BAA0B;AAAA,EACjC;AACF;;;ADtBO,IAAM,UAAU,MAAM,SAAS,OAAO,kBAAkB;AAC7D,WAAS,eAAe;AAAA,IACtB;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,GAAG;AACD,WAAO,kBAAkB,eAAe;AACxC,eAAO,6BAAc,kBAAkB;AAAA,MACrC,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACA,iBAAe,kBAAkB,oBAAoB,gBAAgB;AACrE,iBAAe,cAAc,kBAAkB,eAAe,gBAAgB,CAAC;AAC/E,iBAAe,mBAAmB;AAClC,SAAO;AACT;;;AEPO,IAAM,OAAO,MAAM;AACnB,IAAM,OAAO,MAAM;AACnB,IAAM,SAAS,MAAM;AACrB,IAAM,SAAS,MAAM;AACrB,IAAM,SAAS,MAAM;AACrB,IAAM,gBAAgB,MAAM;", "names": ["import_react", "attrRE", "stringify", "tag", "res", "type", "name", "voidElement", "attrs", "children", "tagMatch", "match", "lookup", "char<PERSON>t", "length", "startsWith", "endIndex", "indexOf", "comment", "slice", "reg", "RegExp", "result", "exec", "trim", "attr", "arr", "split", "lastIndex", "substring", "tagRE", "whitespaceRE", "empty", "Object", "create", "buff", "doc", "content", "key", "push", "join", "reduce", "parse", "html", "options", "components", "current", "level", "inComponent", "end", "replace", "index", "parent", "isOpen", "isComment", "start", "nextChar", "parseTag", "test", "token", "rootEl", "n", "i18nInstance", "i", "c", "t", "import_react", "Trans", "t", "import_react", "n", "t", "e", "import_react", "t", "forwardRefReact", "t", "import_react", "import_react", "import_react"]}