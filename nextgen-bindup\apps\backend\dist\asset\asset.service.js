"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetService = void 0;
const common_1 = require("@nestjs/common");
const asset_entity_1 = require("./entities/asset.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const project_service_1 = require("../project/project.service");
const app_exception_1 = require("../common/exceptions/app.exception");
let AssetService = class AssetService {
    constructor(projectService) {
        this.projectService = projectService;
    }
    async create(assetEntity) {
        const project = await this.projectService.findById(assetEntity.projectId);
        if (!project)
            throw new app_exception_1.AppException('error.project_not_found');
        const now = new Date();
        const asset = new asset_entity_1.AssetEntity();
        asset.type = assetEntity.type;
        asset.projectId = assetEntity.projectId;
        asset.name = assetEntity.name;
        asset.url = assetEntity.url;
        asset.createdAt = now;
        asset.updatedAt = now;
        return await this.assetRepo.save(asset);
    }
    async update(id, assetData) {
        const asset = await this.assetRepo.findOneBy({ id: id });
        if (!asset)
            throw new app_exception_1.AppException('asset.error.not_found');
        delete assetData.id;
        assetData.updatedAt = new Date();
        await this.assetRepo.update(id, assetData);
        return { ...asset, ...assetData };
    }
    async findById(id) {
        return await this.assetRepo.findOneBy({ id });
    }
    async findByProjectId(projectId) {
        return await this.assetRepo.findBy({ projectId });
    }
    async delete(id) {
        const asset = await this.assetRepo.findOneBy({ id });
        if (!asset)
            throw new app_exception_1.AppException('asset.error.not_found');
        await this.assetRepo.delete(asset.id);
        return true;
    }
    async findByUrl(url) {
        return await this.assetRepo.findOneBy({ url });
    }
};
exports.AssetService = AssetService;
__decorate([
    (0, typeorm_1.InjectRepository)(asset_entity_1.AssetEntity),
    __metadata("design:type", typeorm_2.Repository)
], AssetService.prototype, "assetRepo", void 0);
exports.AssetService = AssetService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [project_service_1.ProjectService])
], AssetService);
//# sourceMappingURL=asset.service.js.map