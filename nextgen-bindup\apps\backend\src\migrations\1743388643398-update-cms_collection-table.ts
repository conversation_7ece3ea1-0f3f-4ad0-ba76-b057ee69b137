import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCmsCollectionTable1743388643398
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}cms_collection`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'title');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log(!!queryRunner);
  }
}
