"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageEditSessionModule = void 0;
const common_1 = require("@nestjs/common");
const page_edit_session_service_1 = require("./page-edit-session.service");
const page_edit_session_controller_1 = require("./page-edit-session.controller");
const page_edit_session_entity_1 = require("./entities/page-edit-session.entity");
const typeorm_1 = require("@nestjs/typeorm");
let PageEditSessionModule = class PageEditSessionModule {
};
exports.PageEditSessionModule = PageEditSessionModule;
exports.PageEditSessionModule = PageEditSessionModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([page_edit_session_entity_1.PageEditSessionEntity])],
        providers: [page_edit_session_service_1.PageEditSessionService],
        controllers: [page_edit_session_controller_1.PageEditSessionController],
    })
], PageEditSessionModule);
//# sourceMappingURL=page-edit-session.module.js.map