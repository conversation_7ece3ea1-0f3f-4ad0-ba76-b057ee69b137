import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateProductTable1748830899424 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}products`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'priceLabel',
        type: 'varchar',
        length: '250',
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'saleLabel',
        type: 'varchar',
        length: '250',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'priceLabel');
    await queryRunner.dropColumn(this.TABLE_NAME, 'saleLabel');
  }
}
