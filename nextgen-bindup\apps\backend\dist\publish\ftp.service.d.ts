import { ConfigService } from '@nestjs/config';
export declare class FtpService {
    private readonly configService;
    private readonly logger;
    constructor(configService: ConfigService);
    private createFtpConnection;
    uploadFTP(folderPath: string, remotePath: string): Promise<void>;
    private uploadFolderFtp;
    private ensureRemoteDirectoryFtp;
    private clearRemoteDirectoryFtp;
    private removeFileFtp;
    private removeDirectoryFtp;
    private uploadFileFtp;
}
