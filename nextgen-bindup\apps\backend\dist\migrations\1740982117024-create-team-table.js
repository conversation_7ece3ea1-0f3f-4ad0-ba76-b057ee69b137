"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTeam1740982117024 = void 0;
const typeorm_1 = require("typeorm");
class CreateTeam1740982117024 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}teams`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'rootUserId',
                    type: 'varchar',
                    length: '36',
                    isNullable: false,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                },
            ],
        }), true);
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_teams_rootUserId',
            columnNames: ['rootUserId'],
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_teams_rootUserId');
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateTeam1740982117024 = CreateTeam1740982117024;
//# sourceMappingURL=1740982117024-create-team-table.js.map