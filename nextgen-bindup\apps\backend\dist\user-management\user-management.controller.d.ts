import { UserManagementService } from './user-management.service';
import { InviteMemberReq } from './dto/invite-member.dto';
import { JwtPayloadDto } from 'src/auth/dto/auth.dto';
import { UpdateMemberOfTeamReq } from './dto/update-member-of-team.dto';
export declare class UserManagementController {
    private readonly userManagementService;
    constructor(userManagementService: UserManagementService);
    inviteMember(user: JwtPayloadDto, input: InviteMemberReq): Promise<boolean>;
    updateMemberOfTeam(user: JwtPayloadDto, input: UpdateMemberOfTeamReq): Promise<boolean>;
}
