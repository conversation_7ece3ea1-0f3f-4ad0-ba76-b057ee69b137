"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PROP_BORDER_DEFAULT_VALUE = void 0;
const PROP_BORDER_DEFAULT_VALUE = (ts, prop) => ({
    top: {
        color: '#000',
        width: { value: '0', unit: 'px' },
        borderStyle: 'none',
    },
    right: {
        color: '#000',
        width: { value: '0', unit: 'px' },
        borderStyle: 'none',
    },
    bottom: {
        color: '#000',
        width: { value: '0', unit: 'px' },
        borderStyle: 'none',
    },
    left: {
        color: '#000',
        width: { value: '0', unit: 'px' },
        borderStyle: 'none',
    },
    radiusTopLeft: {
        width: { unit: 'px', value: '0' },
        height: { unit: 'px', value: '0' },
        isDetail: false,
    },
    radiusTopRight: {
        width: { unit: 'px', value: '0' },
        height: { unit: 'px', value: '0' },
        isDetail: false,
    },
    radiusBottomLeft: {
        width: { unit: 'px', value: '0' },
        height: { unit: 'px', value: '0' },
        isDetail: false,
    },
    radiusBottomRight: {
        width: { unit: 'px', value: '0' },
        height: { unit: 'px', value: '0' },
        isDetail: false,
    },
    isDetail: false,
    ts: ts,
    ...(prop || undefined),
});
exports.PROP_BORDER_DEFAULT_VALUE = PROP_BORDER_DEFAULT_VALUE;
//# sourceMappingURL=prop-border-default-value.js.map