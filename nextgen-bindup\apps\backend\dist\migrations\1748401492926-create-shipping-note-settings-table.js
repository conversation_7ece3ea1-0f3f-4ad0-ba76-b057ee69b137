"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateShippingNoteTable1748401492926 = void 0;
const typeorm_1 = require("typeorm");
class CreateShippingNoteTable1748401492926 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}shipping_note_settings`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'siteId',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'shippingFee',
                    type: 'bigint',
                    isNullable: true,
                },
                {
                    name: 'shippingFeeDetail',
                    type: 'jsonb',
                    isNullable: false,
                },
                {
                    name: 'isFreeShippingCondition',
                    type: 'boolean',
                    isNullable: false,
                },
                {
                    name: 'freeShippingCondition',
                    type: 'bigint',
                    isNullable: true,
                },
                {
                    name: 'note',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateShippingNoteTable1748401492926 = CreateShippingNoteTable1748401492926;
//# sourceMappingURL=1748401492926-create-shipping-note-settings-table.js.map