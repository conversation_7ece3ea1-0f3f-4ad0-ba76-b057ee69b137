"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSiteTable1739757489360 = void 0;
const typeorm_1 = require("typeorm");
class UpdateSiteTable1739757489360 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}sites`;
    }
    async up(queryRunner) {
        const column = new typeorm_1.TableColumn({
            name: 'isArchived',
            type: 'boolean',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, column);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'isArchived');
    }
}
exports.UpdateSiteTable1739757489360 = UpdateSiteTable1739757489360;
//# sourceMappingURL=1739757489360-update-site-table.js.map