import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { CmsCollectionService } from './cms-collection.service';
import { CmsCollectionEntity } from './entities/cms-collection.entity';

@Controller('cms-collection')
@UseGuards(AuthGuard)
export class CmsCollectionController {
  constructor(private readonly collectionService: CmsCollectionService) {}

  @Post('create')
  async create(@Body() assetEntity: CmsCollectionEntity) {
    return await this.collectionService.create(assetEntity);
  }

  @Put('update/:id')
  async update(
    @Param('id') id: string,
    @Body() data: Partial<CmsCollectionEntity>,
  ) {
    return await this.collectionService.update(+id, data);
  }

  @Get('one/:id')
  async getById(@Param('id') id: string) {
    return await this.collectionService.findById(+id);
  }

  @Get('site/:siteId')
  async getBySiteId(@Param('siteId') siteId: string) {
    return await this.collectionService.findBySiteId(+siteId);
  }

  @Get('with-product/site/:siteId')
  async getCollectionWithProductBySiteId(@Param('siteId') siteId: string) {
    return await this.collectionService.findWithProductBySiteId(+siteId);
  }

  @Delete(':id')
  async delete(@Param('id') id: string) {
    return await this.collectionService.delete(+id);
  }
}
