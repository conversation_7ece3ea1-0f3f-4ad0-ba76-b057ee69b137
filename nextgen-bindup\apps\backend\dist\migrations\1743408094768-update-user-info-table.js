"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUserInfoTable1743408094768 = void 0;
const typeorm_1 = require("typeorm");
class UpdateUserInfoTable1743408094768 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}user_info`;
    }
    async up(queryRunner) {
        const stripeCustomerIdColumn = new typeorm_1.TableColumn({
            name: 'stripeCustomerId',
            type: 'varchar',
            length: '250',
            isNullable: true,
        });
        const activeSubscriptionColumn = new typeorm_1.TableColumn({
            name: 'activeSubscription',
            type: 'boolean',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, stripeCustomerIdColumn);
        await queryRunner.addColumn(this.TABLE_NAME, activeSubscriptionColumn);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'stripeCustomerId');
        await queryRunner.dropColumn(this.TABLE_NAME, 'activeSubscription');
    }
}
exports.UpdateUserInfoTable1743408094768 = UpdateUserInfoTable1743408094768;
//# sourceMappingURL=1743408094768-update-user-info-table.js.map