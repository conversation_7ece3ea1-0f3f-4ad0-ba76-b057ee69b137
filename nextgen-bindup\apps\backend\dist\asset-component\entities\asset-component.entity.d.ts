import { AssetComponentType } from '../enum/asset-component-type.enum';
import { AssetComponentData } from '@nextgen-bindup/common/dto/assetComponent';
export declare class AssetComponent {
    id: string;
    type: AssetComponentType;
    projectId: number;
    siteId: number;
    name: string;
    folder: boolean;
    parentFolder: string;
    data: AssetComponentData;
    ts: number;
    createdAt: Date;
    updatedAt: Date;
}
