"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MailController = void 0;
const common_1 = require("@nestjs/common");
const mailer_1 = require("@nestjs-modules/mailer");
const event_type_enum_1 = require("../common/event-type.enum");
const event_emitter_1 = require("@nestjs/event-emitter");
let MailController = class MailController {
    constructor(mailerService) {
        this.mailerService = mailerService;
    }
    async handleOrderCreatedEvent(payload) {
        console.log('Received order created event:', payload);
        try {
            const order = {
                ...payload.order,
                isBankTransfer: payload.order.paymentMethodType === "BANK_TRANSFER",
                isPostalTransfer: payload.order.paymentMethodType === "POSTAL_TRANSFER",
            };
            await this.mailerService.sendMail({
                to: order.email,
                subject: payload.orderCompletionSetting.emailSubject,
                template: './order-confirmation',
                context: {
                    order,
                    paymentMethod: payload.paymentMethod,
                    shopInfo: payload.shopInfo,
                    orderCompletionSetting: payload.orderCompletionSetting,
                },
            });
            console.log('Email sent successfully');
        }
        catch (error) {
            console.error('Error sending email:', error);
        }
    }
};
exports.MailController = MailController;
__decorate([
    (0, event_emitter_1.OnEvent)(event_type_enum_1.EventType.ORDER_CREATED),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MailController.prototype, "handleOrderCreatedEvent", null);
exports.MailController = MailController = __decorate([
    (0, common_1.Controller)('mail'),
    __metadata("design:paramtypes", [mailer_1.MailerService])
], MailController);
//# sourceMappingURL=mail.controller.js.map