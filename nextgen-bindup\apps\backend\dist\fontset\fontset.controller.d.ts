import { FontsetService } from './fontset.service';
import { FontsetEntity } from './entities/fontset.entity';
export declare class FontsetController {
    private readonly fontsetService;
    constructor(fontsetService: FontsetService);
    create(assetEntity: FontsetEntity): Promise<FontsetEntity>;
    update(fontsetId: string, data: Partial<FontsetEntity>): Promise<FontsetEntity>;
    getById(fontsetId: string): Promise<FontsetEntity>;
    getByProjectId(projectId: string): Promise<FontsetEntity[]>;
    getBySiteId(projectId: string, siteId: string): Promise<FontsetEntity[]>;
    delete(fontsetId: string): Promise<boolean>;
}
