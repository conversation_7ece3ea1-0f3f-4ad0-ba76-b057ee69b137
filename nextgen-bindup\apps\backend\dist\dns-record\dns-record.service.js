"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DnsRecordService = void 0;
const common_1 = require("@nestjs/common");
const dns_record_entity_1 = require("./entities/dns-record.entity");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
let DnsRecordService = class DnsRecordService {
    async create(data) {
        delete data.id;
        const now = new Date();
        data.createdAt = now;
        data.updatedAt = now;
        return await this.dnsRecordRepo.save(data);
    }
    async update(id, data) {
        const entity = await this.dnsRecordRepo.findOneBy({ id });
        if (!entity)
            throw new app_exception_1.AppException('api.error.dns_record_not_found');
        const now = new Date();
        data.updatedAt = now;
        delete data.id;
        await this.dnsRecordRepo.update(id, data);
        return { ...entity, ...data };
    }
    async delete(id) {
        const entity = await this.dnsRecordRepo.findOneBy({ id });
        if (!entity)
            throw new app_exception_1.AppException('api.error.dns_record_not_found');
        await this.dnsRecordRepo.delete(id);
        return true;
    }
    async findBySiteId(siteId) {
        return await this.dnsRecordRepo.findBy({ siteId });
    }
    async findByProjectId(projectId) {
        return await this.dnsRecordRepo.findBy({ projectId });
    }
};
exports.DnsRecordService = DnsRecordService;
__decorate([
    (0, typeorm_2.InjectRepository)(dns_record_entity_1.DnsRecordEntity),
    __metadata("design:type", typeorm_1.Repository)
], DnsRecordService.prototype, "dnsRecordRepo", void 0);
exports.DnsRecordService = DnsRecordService = __decorate([
    (0, common_1.Injectable)()
], DnsRecordService);
//# sourceMappingURL=dns-record.service.js.map