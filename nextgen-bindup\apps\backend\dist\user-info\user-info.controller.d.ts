import { UserInfoService } from './user-info.service';
import { JwtPayloadDto } from 'src/auth/dto/auth.dto';
import { UserInfoEntity } from './entities/user-info.entity';
export declare class UserInfoController {
    private readonly userInfoService;
    constructor(userInfoService: UserInfoService);
    updateRecently(user: JwtPayloadDto, siteId: string): Promise<boolean>;
    me(user: JwtPayloadDto): Promise<UserInfoEntity>;
    update(user: JwtPayloadDto, data: Partial<UserInfoEntity>): Promise<UserInfoEntity>;
}
