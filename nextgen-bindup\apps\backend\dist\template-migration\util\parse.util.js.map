{"version": 3, "file": "parse.util.js", "sourceRoot": "", "sources": ["../../../src/template-migration/util/parse.util.ts"], "names": [], "mappings": ";;;AAAA,2CAA2C;AAS3C,MAAM,iBAAiB,GAAG,SAAS,CAAC;AACpC,MAAM,UAAU,GAAG,QAAQ,CAAC;AAE5B,MAAa,SAAS;IACpB,MAAM,CAAC,YAAY,CACjB,SAA0B,EAC1B,SAA2B;QAE3B,MAAM,MAAM,GAAwB,EAAE,CAAC;QAEvC,MAAM,OAAO,GAAG,SAAS,SAAS,CAAC,OAAO,SAAS,CAAC;QACpD,MAAM,GAAG,GAAG,IAAI,kBAAS,EAAE,CAAC,eAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACjE,MAAM,IAAI,GAAG,GAAG,CAAC,eAAe,CAAC;QAEjC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY;gBAAE,SAAS;YAClD,MAAM,IAAI,GAAG,IAA0B,CAAC;YACxC,MAAM,OAAO,GAAsB,SAAS,CAAC,iBAAiB,CAC5D,SAAS,CAAC,WAAW,EACrB,IAAI,EACJ,SAAS,CACV,CAAC;YAGF,IACE,OAAO,CAAC,UAAU,CAAC,KAAK,KAAK,iBAAiB;gBAC9C,OAAO,CAAC,QAAQ,KAAK,IAAI;gBAEzB,SAAS;YACX,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,iBAAiB,CACtB,WAAmB,EACnB,IAAa,EACb,SAA2B;QAE3B,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,MAAM,UAAU,GAA2B,EAAE,CAAC;QAC9C,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;YACzB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACnC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;YACrC,CAAC;QACH,CAAC;QAED,MAAM,OAAO,GAAsB;YACjC,KAAK,EAAE,KAAK,EAAE;YACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,EAAE;YACR,UAAU,EAAE,UAA0C;YACtD,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAW,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC1D,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC,IAAI,CAC/B,QAAQ,CAAC,EAAE,CACT,QAAQ,CAAC,UAAU,KAAK,UAAU;gBAClC,QAAQ,CAAC,WAAW,KAAK,WAAW,CACvC,CAAC;QACJ,CAAC;QAED,IAAI,UAAU,GAAW,CAAC,CAAC;QAC3B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBACxB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;gBAC9B,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;oBAExB,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;oBACxD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;wBACpB,KAAK,EAAE,UAAU,EAAE;wBACnB,QAAQ,EAAE,OAAO;wBACjB,IAAI,EAAE,aAAa;wBACnB,UAAU,EAAE,EAAkC;wBAC9C,QAAQ,EAAE,EAAE;qBACb,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAE/B,MAAM,YAAY,GAAG,IAAe,CAAC;gBACrC,MAAM,YAAY,GAAG,SAAS,CAAC,iBAAiB,CAC9C,WAAW,EACX,YAAY,EACZ,SAAS,CACV,CAAC;gBAGF,IACE,YAAY,CAAC,UAAU,CAAC,KAAK,KAAK,iBAAiB;oBACnD,YAAY,CAAC,QAAQ,KAAK,IAAI;oBAE9B,SAAS;gBACX,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAoED,MAAM,CAAC,UAAU,CAAC,YAAiC;QACjD,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,MAAM,MAAM,GAAsB,CAAC,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;QAEvE,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;YACjC,IAAI,QAAQ,GAAY,KAAK,CAAC;YAC9B,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,KAAK,UAAU;gBAAE,QAAQ,GAAG,IAAI,CAAC;iBACpD,IACH,KAAK,CAAC,QAAQ,CAAC,MAAM;gBACrB,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,KAAK,UAAU;gBAE/C,QAAQ,GAAG,IAAI,CAAC;YAElB,IAAI,QAAQ,EAAE,CAAC;gBACb,KAAK,EAAE,CAAC;gBACR,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,WAAmB;QACzC,OAAO,WAAW;aACf,KAAK,CAAC,GAAG,CAAC;aACV,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;aAC5B,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACpB,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACrC,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;gBACjB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;YACjC,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACX,CAAC;CACF;AAxMD,8BAwMC"}