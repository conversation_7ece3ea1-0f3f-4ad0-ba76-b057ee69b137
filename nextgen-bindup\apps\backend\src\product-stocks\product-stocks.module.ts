import { forwardRef, Module } from '@nestjs/common';
import { ProductStocksController } from './product-stocks.controller';
import { ProductStocksService } from './product-stocks.service';
import { ProductStockEntity } from './entities/product-stock.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductModule } from 'src/product/product.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProductStockEntity]),
    forwardRef(() => ProductModule),
  ],
  controllers: [ProductStocksController],
  providers: [ProductStocksService],
  exports: [ProductStocksService],
})
export class ProductStocksModule {}
