import { Site6_Srclist1 } from './site6_srclist1.dto';
export declare enum Resource_PartsType {
    CART = "cart",
    DOWNLOAD = "download",
    SHIFT = "shift",
    TABLE = "table",
    SYNC = "sync",
    SIGN = "sign",
    TAG = "tag",
    IMAGE = "image",
    LINK = "link",
    MOVIE = "movie",
    ID = "id",
    OFFICE = "office",
    LIVE = "live"
}
export interface Site5_Resource {
    tmpSiteId: number;
    resourceId: number;
    siteId: number;
    blockdataId: number;
    partsType: Resource_PartsType;
    blockeditIcon: string;
    partsProperty: string;
    partsPropertyJson: Resource_PartsProperty;
    delFlg: number;
    insDate: string;
    updDate: string;
    srcList?: Site6_Srclist1[];
}
export interface Resource_PartsProperty {
    clck: string;
    mdfy: string;
    bodr: string;
    wd: string;
    ht: string;
    algn: string;
    tgnm: string;
    tgwn: string;
    tgwd: string;
    tght: string;
    tgsc: string;
    tgrs: string;
    osize: string;
    alt: string;
    scID: string;
    efct: string;
    ro: string;
    lkon: string;
    cmt: string;
    zmsc: string;
    zmwd: string;
    zmht: string;
    name: string;
    html: string;
    img: string;
    text: string;
    link: string;
    isrc: string;
    iblw: string;
}
