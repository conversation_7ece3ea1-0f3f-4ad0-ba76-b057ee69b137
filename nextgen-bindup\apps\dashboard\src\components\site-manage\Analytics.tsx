import { type FC } from 'react';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Typography from '@mui/material/Typography';
import { LineChart } from '@mui/x-charts/LineChart';

const pData = [2400, 1398, 9800, 3908, 4800, 3800, 4300];
const xLabels = ['0', '20', '40', '60', '80', '90', '200'];

const Analytics: FC = () => (
  <Box sx={{ display: 'flex' }}>
    <LineChart
      height={520}
      series={[{ data: pData, label: 'Series 1' }]}
      xAxis={[{ scaleType: 'point', data: xLabels }]}
    />
    <Box sx={{ display: 'inherit', flexDirection: 'column', gap: 3 }}>
      <Card sx={{ width: 320, height: 240 }}>
        <CardHeader title="Lorem ipsum" />
        <CardContent>
          <Typography variant="h1">0000</Typography>
          <Typography variant="body1">2024-00-00 - 2024-00-00</Typography>
        </CardContent>
      </Card>
      <Card sx={{ width: 320, height: 240 }}>
        <CardHeader title="Lorem ipsum" />
        <CardContent>
          <Typography variant="h1">0000</Typography>
          <Typography variant="body1">2024-00-00 - 2024-00-00</Typography>
        </CardContent>
      </Card>
    </Box>
  </Box>
);

export default Analytics;
