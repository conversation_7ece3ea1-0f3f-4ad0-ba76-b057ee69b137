import { Injectable } from '@nestjs/common';
import { SignUploadDto } from '../supabase/dto/sign-upload.dto';
import { SupabaseStorageService } from 'src/supabase/supabase-storage.service';

@Injectable()
export class StorageService {
  constructor(
    private readonly supabaseStorageService: SupabaseStorageService,
  ) {}

  async initBucket() {
    await this.supabaseStorageService.createBucket();
  }

  async createSignedUploadUrl(filepath: string): Promise<SignUploadDto> {
    return await this.supabaseStorageService.createSignedUploadUrl(filepath);
  }

  async getPublicUrl(filepath: string): Promise<string> {
    return await this.supabaseStorageService.getPublicUrl(filepath);
  }
}
