import { Module } from '@nestjs/common';
import { DnsRecordController } from './dns-record.controller';
import { DnsRecordService } from './dns-record.service';
import { DnsRecordEntity } from './entities/dns-record.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([DnsRecordEntity])],
  controllers: [DnsRecordController],
  providers: [DnsRecordService],
  exports: [DnsRecordService],
})
export class DnsRecordModule {}
