"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUpdateProductTable1746783765334 = void 0;
const typeorm_1 = require("typeorm");
class CreateUpdateProductTable1746783765334 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}products`;
    }
    async up(queryRunner) {
        await queryRunner.dropColumns(this.TABLE_NAME, [
            'stockQuantity',
            'attributes',
            'stockVariants',
        ]);
        const variants = new typeorm_1.TableColumn({
            name: 'variants',
            type: 'jsonb',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, variants);
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_product_siteId',
            columnNames: ['siteId'],
            isUnique: false,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_product_siteId');
    }
}
exports.CreateUpdateProductTable1746783765334 = CreateUpdateProductTable1746783765334;
//# sourceMappingURL=1746783765334-update-product-table.js.map