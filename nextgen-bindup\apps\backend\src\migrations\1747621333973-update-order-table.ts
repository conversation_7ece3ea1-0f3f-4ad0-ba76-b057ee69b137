import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateOrderTable1747621333973 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}orders`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    // add siteId column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'siteId',
        type: 'integer',
        isNullable: false,
      }),
    );
    // add projectId column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'projectId',
        type: 'integer',
        isNullable: false,
      }),
    );
    // add shippingFee column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'shippingFee',
        type: 'bigint',
        isNullable: true,
      }),
    );
    // add taxFee column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'taxFee',
        type: 'bigint',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // drop siteId column
    await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
    // drop projectId column
    await queryRunner.dropColumn(this.TABLE_NAME, 'projectId');
  }
}
