import { Boolean1, <PERSON>olean2 } from './boolean.enum';
export interface Site2_Page {
    tmpSiteId: number;
    pageId: number;
    siteId: number;
    parentCornerId: number;
    seq: number;
    name: string;
    filename: string;
    publicFlg: number;
    labelColor: number;
    layoutId: string;
    title: string;
    bgSets: string;
    bgSetsJson: Page_BGSet[];
    headSets: string;
    headSetsJson: Page_HeadSets;
    layoutSets: string;
    layoutSetsJson: Page_LayoutSets;
    robots: string;
    robotsJSON: Page_Robots;
    mobileSets: string;
    mobileSetsJson: Page_MobileSets;
    cssSets: string;
    cssSetsJson: Page_CssSets;
    openGraph: string;
    openGraphJson: Page_OpenGraph;
    areaFloat: string;
    areaFloatJson: Page_AreaFloat;
    version: number;
    delFlg: number;
    insDate: string;
    updDate: string;
}
export interface Page_BGSet {
    skin: Boolean1;
    hasi: string;
    ilay: string;
    imgth: string;
    imgID: string;
    hasc: Boolean1;
    cval: string;
    nospace: string;
}
export interface Page_HeadSets {
    scpt: string;
    cche: Boolean1;
    enc: string;
    rfon: Boolean1;
    rfto: string;
}
export interface Page_LayoutSets {
    arUse0: string;
    arVal0: string;
    arType0: string;
    arUse1: string;
    arVal1: string;
    arType1: string;
    arUse2: string;
    arVal2: string;
    arType2: string;
    arUse3: string;
    arVal3: string;
    arType3: string;
    arUse4: string;
    arVal4: string;
    arType4: string;
    arUse5: string;
    arVal5: string;
    arType5: string;
    arUse6: string;
    arVal6: string;
    arType6: string;
    arUse7: string;
    arVal7: string;
    arType7: string;
    arUse8: string;
    arVal8: string;
    arType8: string;
    bwUse: string;
    bwVal: string;
    bwType: string;
}
export interface Page_Robots {
    ison: string;
    desc: string;
    kw: string;
    use: Boolean2;
}
export interface Page_MobileSets {
    not_use: string;
}
export interface Page_CssSets {
    'area-header': string;
    'area-billboard': string;
    'area-main': string;
    'area-side-a': string;
    'area-side-b': string;
    'area-footer': string;
}
export interface Page_OpenGraph {
    use_site_setting: boolean;
    items: unknown[];
}
export interface Page_AreaFloat {
    not_use: string;
}
