/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"../../node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ \"../../node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\n([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1yb3V0ZS1sb2FkZXIvaW5kZXguanM/a2luZD1QQUdFUyZwYWdlPSUyRl9lcnJvciZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2Vycm9yJmFic29sdXRlQXBwUGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfYXBwJmFic29sdXRlRG9jdW1lbnRQYXRoPXByaXZhdGUtbmV4dC1wYWdlcyUyRl9kb2N1bWVudCZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBd0Y7QUFDaEM7QUFDRTtBQUMxRDtBQUN5RDtBQUNWO0FBQy9DO0FBQ3NEO0FBQ3REO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxxREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyx1QkFBdUIsd0VBQUssQ0FBQyxxREFBUTtBQUNyQyx1QkFBdUIsd0VBQUssQ0FBQyxxREFBUTtBQUNyQywyQkFBMkIsd0VBQUssQ0FBQyxxREFBUTtBQUN6QyxlQUFlLHdFQUFLLENBQUMscURBQVE7QUFDN0Isd0JBQXdCLHdFQUFLLENBQUMscURBQVE7QUFDN0M7QUFDTyxnQ0FBZ0Msd0VBQUssQ0FBQyxxREFBUTtBQUM5QyxnQ0FBZ0Msd0VBQUssQ0FBQyxxREFBUTtBQUM5QyxpQ0FBaUMsd0VBQUssQ0FBQyxxREFBUTtBQUMvQyxnQ0FBZ0Msd0VBQUssQ0FBQyxxREFBUTtBQUM5QyxvQ0FBb0Msd0VBQUssQ0FBQyxxREFBUTtBQUN6RDtBQUNPLHdCQUF3QixrR0FBZ0I7QUFDL0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGFBQWEsOERBQVc7QUFDeEIsa0JBQWtCLG1FQUFnQjtBQUNsQyxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQsaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbmV4dGdlbi1iaW5kdXAvbGl2ZS1lZGl0b3IvP2I1N2IiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgYXBwIGFuZCBkb2N1bWVudCBtb2R1bGVzLlxuaW1wb3J0ICogYXMgZG9jdW1lbnQgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fZG9jdW1lbnRcIjtcbmltcG9ydCAqIGFzIGFwcCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19hcHBcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJwcml2YXRlLW5leHQtcGFnZXMvX2Vycm9yXCI7XG4vLyBSZS1leHBvcnQgdGhlIGNvbXBvbmVudCAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsICdnZXRTdGF0aWNQcm9wcycpO1xuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsICdnZXRTdGF0aWNQYXRocycpO1xuZXhwb3J0IGNvbnN0IGdldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U2VydmVyU2lkZVByb3BzJyk7XG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbmV4cG9ydCBjb25zdCByZXBvcnRXZWJWaXRhbHMgPSBob2lzdCh1c2VybGFuZCwgJ3JlcG9ydFdlYlZpdGFscycpO1xuLy8gUmUtZXhwb3J0IGxlZ2FjeSBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTdGF0aWNQYXRocycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhcmFtcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUGFyYW1zJyk7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgJ3Vuc3RhYmxlX2dldFNlcnZlclByb3BzJyk7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTZXJ2ZXJTaWRlUHJvcHMnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTLFxuICAgICAgICBwYWdlOiBcIi9fZXJyb3JcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL19lcnJvclwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJydcbiAgICB9LFxuICAgIGNvbXBvbmVudHM6IHtcbiAgICAgICAgLy8gZGVmYXVsdCBleHBvcnQgbWlnaHQgbm90IGV4aXN0IHdoZW4gb3B0aW1pemVkIGZvciBkYXRhIG9ubHlcbiAgICAgICAgQXBwOiBhcHAuZGVmYXVsdCxcbiAgICAgICAgRG9jdW1lbnQ6IGRvY3VtZW50LmRlZmF1bHRcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/pages/GlobalStyles.tsx":
/*!************************************!*\
  !*** ./src/pages/GlobalStyles.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/react/jsx-dev-runtime */ \"@emotion/react/jsx-dev-runtime\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_AppContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/AppContext */ \"./src/providers/AppContext.tsx\");\n/* harmony import */ var _service_font_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/service/font-service */ \"./src/service/font-service.ts\");\n/* harmony import */ var _utils_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/component */ \"./src/utils/component.ts\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @emotion/react */ \"@emotion/react\");\n/* harmony import */ var _nextgen_bindup_common_dto_types_style_type_enum__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @nextgen-bindup/common/dto/types/style-type.enum */ \"../../packages/common/dto/types/style-type.enum.ts\");\n/* harmony import */ var _nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @nextgen-bindup/common/utility */ \"../../packages/common/utility.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__, _service_font_service__WEBPACK_IMPORTED_MODULE_3__, _utils_component__WEBPACK_IMPORTED_MODULE_4__, _emotion_react__WEBPACK_IMPORTED_MODULE_5__]);\n([_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__, _service_font_service__WEBPACK_IMPORTED_MODULE_3__, _utils_component__WEBPACK_IMPORTED_MODULE_4__, _emotion_react__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst DEFAULT_GLOBAL_STYLE = {\n    html: (0,_emotion_react__WEBPACK_IMPORTED_MODULE_5__.css)({\n        position: 'relative',\n        overflowX: 'hidden'\n    }),\n    body: (0,_emotion_react__WEBPACK_IMPORTED_MODULE_5__.css)({\n        padding: '1px',\n        height: '100dvh'\n    }),\n    '#__next': (0,_emotion_react__WEBPACK_IMPORTED_MODULE_5__.css)({\n        height: '100%'\n    })\n};\nconst GlobalStyles = ()=>{\n    const { styles } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_providers_AppContext__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n    const [globalStyles, setGlobalStyles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ...DEFAULT_GLOBAL_STYLE\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const colorStyles = {};\n        const typoClass = {};\n        if (!styles) return;\n        for (const style of styles.list){\n            switch(style.type){\n                case _nextgen_bindup_common_dto_types_style_type_enum__WEBPACK_IMPORTED_MODULE_6__.StyleType.COLOR:\n                    const varName = `--myStyle-${style.id}`;\n                    const colorData = style.data;\n                    colorStyles[varName] = colorData.light || undefined;\n                    break;\n                case _nextgen_bindup_common_dto_types_style_type_enum__WEBPACK_IMPORTED_MODULE_6__.StyleType.TYPOGRAPHY:\n                    const typoStyles = {};\n                    const typoData = style.data;\n                    // -- fontFamily --------------------------------------\n                    if (typoData.fontSetId) {\n                        typoStyles.fontFamily = _service_font_service__WEBPACK_IMPORTED_MODULE_3__.fontService.getFontFamilyByFontSetId(typoData.fontSetId);\n                    }\n                    // -- fontStyle --------------------------------------\n                    typoStyles.fontWeight = typoData.weight;\n                    const typoFontStyle = typoData.sizeByDevice?.pc;\n                    if (typoFontStyle) {\n                        // fontSize\n                        if (typoFontStyle.fontSize) typoStyles.fontSize = (0,_utils_component__WEBPACK_IMPORTED_MODULE_4__.unitObjectStyle)(typoFontStyle.fontSize);\n                        // lineHeight\n                        if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_7__.isNull)(typoFontStyle.lineHeight)) typoStyles.lineHeight = (0,_utils_component__WEBPACK_IMPORTED_MODULE_4__.unitObjectStyle)(typoFontStyle.lineHeight);\n                        // letterSpacing\n                        if (typoFontStyle.spacing) typoStyles.letterSpacing = (0,_utils_component__WEBPACK_IMPORTED_MODULE_4__.unitObjectStyle)(typoFontStyle.spacing);\n                        // minSize\n                        if (typoFontStyle.minSize) typoStyles.minWidth = (0,_utils_component__WEBPACK_IMPORTED_MODULE_4__.unitObjectStyle)(typoFontStyle.minSize);\n                        // maxSize\n                        if (typoFontStyle.maxSize) typoStyles.maxWidth = (0,_utils_component__WEBPACK_IMPORTED_MODULE_4__.unitObjectStyle)(typoFontStyle.maxSize);\n                    }\n                    // -- format --------------------------------------\n                    typoStyles.fontWeight = typoData.weight;\n                    typoStyles.textDecoration = typoData.textDecoration;\n                    // -- textTransform --------------------------------------\n                    if (typoData.textTransform) typoStyles.textTransform = typoData.textTransform;\n                    // -- textShadow --------------------------------------\n                    // if (typoData.textShadow) typoStyles.textShadow = typoData.textShadow;\n                    typoClass[`.myStyle-${style.id}`] = (0,_emotion_react__WEBPACK_IMPORTED_MODULE_5__.css)({\n                        ...typoStyles\n                    });\n                    break;\n                default:\n                    break;\n            }\n        }\n        const newGlobalStyles = {\n            ...DEFAULT_GLOBAL_STYLE,\n            ':root': (0,_emotion_react__WEBPACK_IMPORTED_MODULE_5__.css)({\n                ...colorStyles\n            }),\n            ...typoClass\n        };\n        setGlobalStyles(newGlobalStyles);\n    }, [\n        styles\n    ]);\n    return /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emotion_react__WEBPACK_IMPORTED_MODULE_5__.Global, {\n        styles: globalStyles\n    }, void 0, false, {\n        fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\GlobalStyles.tsx\",\n        lineNumber: 118,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GlobalStyles);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/GlobalStyles.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/react/jsx-dev-runtime */ \"@emotion/react/jsx-dev-runtime\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! gsap */ \"gsap\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(gsap__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap_dist_ScrollTrigger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap/dist/ScrollTrigger */ \"gsap/dist/ScrollTrigger\");\n/* harmony import */ var gsap_dist_ScrollTrigger__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(gsap_dist_ScrollTrigger__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _providers_AppProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/AppProvider */ \"./src/providers/AppProvider.tsx\");\n/* harmony import */ var _assets_global_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../assets/global.css */ \"./src/assets/global.css\");\n/* harmony import */ var _assets_global_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_assets_global_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _GlobalStyles__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./GlobalStyles */ \"./src/pages/GlobalStyles.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__, _providers_AppProvider__WEBPACK_IMPORTED_MODULE_3__, _GlobalStyles__WEBPACK_IMPORTED_MODULE_5__]);\n([_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__, _providers_AppProvider__WEBPACK_IMPORTED_MODULE_3__, _GlobalStyles__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// pages/_app.tsx\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_1___default().registerPlugin(gsap_dist_ScrollTrigger__WEBPACK_IMPORTED_MODULE_2__.ScrollTrigger);\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_AppProvider__WEBPACK_IMPORTED_MODULE_3__.AppProvider, {\n        children: [\n            /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlobalStyles__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGlCQUFpQjs7QUFDTztBQUNnQztBQUVGO0FBQ3hCO0FBQ1k7QUFFMUNBLDBEQUFtQixDQUFDQyxrRUFBYUE7QUFFakMsU0FBU0ksTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBWTtJQUMvQyxxQkFDRSx1RUFBQ0wsK0RBQVdBOzswQkFDVix1RUFBQ0MscURBQVlBOzs7OzswQkFDYix1RUFBQ0c7Z0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7O0FBRzlCO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbmV4dGdlbi1iaW5kdXAvbGl2ZS1lZGl0b3IvLi9zcmMvcGFnZXMvX2FwcC50c3g/ZjlkNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWdlcy9fYXBwLnRzeFxyXG5pbXBvcnQgZ3NhcCBmcm9tICdnc2FwJztcclxuaW1wb3J0IHsgU2Nyb2xsVHJpZ2dlciB9IGZyb20gJ2dzYXAvZGlzdC9TY3JvbGxUcmlnZ2VyJztcclxuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcclxuaW1wb3J0IHsgQXBwUHJvdmlkZXIgfSBmcm9tICdAL3Byb3ZpZGVycy9BcHBQcm92aWRlcic7XHJcbmltcG9ydCAnLi4vYXNzZXRzL2dsb2JhbC5jc3MnO1xyXG5pbXBvcnQgR2xvYmFsU3R5bGVzIGZyb20gJy4vR2xvYmFsU3R5bGVzJztcclxuXHJcbmdzYXAucmVnaXN0ZXJQbHVnaW4oU2Nyb2xsVHJpZ2dlcik7XHJcblxyXG5mdW5jdGlvbiBNeUFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxBcHBQcm92aWRlcj5cclxuICAgICAgPEdsb2JhbFN0eWxlcyAvPlxyXG4gICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XHJcbiAgICA8L0FwcFByb3ZpZGVyPlxyXG4gICk7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IE15QXBwO1xyXG4iXSwibmFtZXMiOlsiZ3NhcCIsIlNjcm9sbFRyaWdnZXIiLCJBcHBQcm92aWRlciIsIkdsb2JhbFN0eWxlcyIsInJlZ2lzdGVyUGx1Z2luIiwiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyDocument)\n/* harmony export */ });\n/* harmony import */ var _emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/react/jsx-dev-runtime */ \"@emotion/react/jsx-dev-runtime\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"../../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @emotion/cache */ \"@emotion/cache\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @emotion/react */ \"@emotion/react\");\n/* harmony import */ var _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @emotion/server/create-instance */ \"@emotion/server/create-instance\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__, _emotion_cache__WEBPACK_IMPORTED_MODULE_3__, _emotion_react__WEBPACK_IMPORTED_MODULE_4__, _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_5__]);\n([_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__, _emotion_cache__WEBPACK_IMPORTED_MODULE_3__, _emotion_react__WEBPACK_IMPORTED_MODULE_4__, _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst createEmotionCache = ()=>{\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n        key: 'css'\n    });\n};\nclass MyDocument extends (next_document__WEBPACK_IMPORTED_MODULE_1___default()) {\n    render() {\n        return /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n            children: [\n                /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                    children: /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://unpkg.com/modern-css-reset/dist/reset.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                            fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                            fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this);\n    }\n}\nMyDocument.getInitialProps = async (ctx)=>{\n    const originalRenderPage = ctx.renderPage;\n    const cache = createEmotionCache();\n    const { extractCriticalToChunks } = (0,_emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(cache);\n    ctx.renderPage = ()=>originalRenderPage({\n            enhanceApp: (App)=>(props)=>/*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emotion_react__WEBPACK_IMPORTED_MODULE_4__.CacheProvider, {\n                        value: cache,\n                        children: /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(App, {\n                            ...props\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined)\n        });\n    const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_1___default().getInitialProps(ctx);\n    const emotionStyles = extractCriticalToChunks(initialProps.html);\n    const emotionStyleTags = emotionStyles.styles.map((style)=>/*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n            \"data-emotion\": `${style.key} ${style.ids.join(' ')}`,\n            dangerouslySetInnerHTML: {\n                __html: style.css\n            }\n        }, style.key, false, {\n            fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\pages\\\\_document.tsx\",\n            lineNumber: 54,\n            columnNumber: 5\n        }, undefined));\n    return {\n        ...initialProps,\n        styles: [\n            ...react__WEBPACK_IMPORTED_MODULE_2___default().Children.toArray(initialProps.styles),\n            ...emotionStyleTags\n        ]\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/providers/AppContext.tsx":
/*!**************************************!*\
  !*** ./src/providers/AppContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/constants */ \"./src/utils/constants.ts\");\n\n\nconst AppContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createContext({\n    // DRAP - DROP\n    drapElement: null,\n    setDrapElement: ()=>{},\n    pageData: null,\n    setPageData: ()=>{},\n    version: null,\n    // The data of styles\n    styles: {\n        ts: 0,\n        list: []\n    },\n    setStyles: ()=>{},\n    // The data of content\n    assetComponents: {\n        ts: 0,\n        list: []\n    },\n    setAssetComponents: ()=>{},\n    selectedId: '',\n    selectComponent: ()=>{},\n    getVariantsRect: ()=>{},\n    hoverId: '',\n    hoverComponent: ()=>{},\n    isEdgeEditing: false,\n    setIsEdgeEditing: ()=>{},\n    isTextEditing: false,\n    setIsTextEditing: ()=>{},\n    // post the message to the parent window\n    postMessage: ()=>{},\n    // the message received from the parent window\n    receiveMessage: {\n        type: _utils_constants__WEBPACK_IMPORTED_MODULE_1__.RECEIVE_MSG_TYPE.NONE,\n        ts: 0\n    },\n    setReceiveMessage: ()=>{},\n    // mousePoint\n    mousePoint: {\n        x: 0,\n        y: 0,\n        key: '0_0'\n    },\n    setMousePoint: ()=>{},\n    // drappingId\n    drappingId: '',\n    setDrappingId: ()=>{},\n    // draggerMark\n    draggerMark: {\n        id: '',\n        width: 0,\n        height: 0,\n        background: false,\n        lineTop: false,\n        lineBottom: false,\n        lineLeft: false,\n        lineRight: false\n    },\n    setDraggerMark: ()=>{},\n    // edge container status\n    edgeContainerStatus: {\n        state: 'none',\n        relativeId: '',\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    },\n    setEdgeContainerStatus: ()=>{},\n    // viewport\n    viewport: {\n        vw: 0,\n        vh: 0,\n        dvw: 0,\n        dvh: 0,\n        dvmin: 0,\n        dvmax: 0\n    },\n    setViewport: ()=>{},\n    // mode\n    mode: _utils_constants__WEBPACK_IMPORTED_MODULE_1__.MODE.DESIGN,\n    setMode: ()=>{},\n    // collections\n    collections: [],\n    setCollections: ()=>{},\n    // collectionItems\n    collectionItems: {},\n    setCollectionItems: ()=>{},\n    // AssetComponent editor\n    componentEditor: null,\n    setComponentEditor: ()=>{}\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/providers/AppContext.tsx\n");

/***/ }),

/***/ "./src/providers/AppProvider.tsx":
/*!***************************************!*\
  !*** ./src/providers/AppProvider.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppProvider: () => (/* binding */ AppProvider)\n/* harmony export */ });\n/* harmony import */ var _emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/react/jsx-dev-runtime */ \"@emotion/react/jsx-dev-runtime\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _service_asset_component_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/service/asset-component-service */ \"./src/service/asset-component-service.ts\");\n/* harmony import */ var _service_font_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/service/font-service */ \"./src/service/font-service.ts\");\n/* harmony import */ var _service_styles_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/service/styles-service */ \"./src/service/styles-service.ts\");\n/* harmony import */ var _utils_alias_page_util__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/alias-page.util */ \"./src/utils/alias-page.util.ts\");\n/* harmony import */ var _utils_asset_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/asset-component */ \"./src/utils/asset-component.ts\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/constants */ \"./src/utils/constants.ts\");\n/* harmony import */ var _utils_wl_global__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/wl-global */ \"./src/utils/wl-global.ts\");\n/* harmony import */ var _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @nextgen-bindup/common/dto/types/component.type */ \"../../packages/common/dto/types/component.type.ts\");\n/* harmony import */ var _nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @nextgen-bindup/common/utility */ \"../../packages/common/utility.ts\");\n/* harmony import */ var _AppContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AppContext */ \"./src/providers/AppContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__, _service_font_service__WEBPACK_IMPORTED_MODULE_3__, _service_styles_service__WEBPACK_IMPORTED_MODULE_4__]);\n([_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__, _service_font_service__WEBPACK_IMPORTED_MODULE_3__, _service_styles_service__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\nconst AppProvider = (props)=>{\n    const [drapElement, setDrapElement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    //---------------------------------------\n    const [pageData, setPageData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [version, setVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [styles, setStyles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ts: 0,\n        list: []\n    });\n    const [assetComponents, setAssetComponents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ts: 0,\n        list: []\n    });\n    const [selectedId, setSelectedId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hoverId, setHoverId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isEdgeEditing, setIsEdgeEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTextEditing, setIsTextEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [receiveMessage, setReceiveMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.NONE,\n        ts: 0\n    });\n    const [mousePoint, setMousePoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0,\n        key: '0_0'\n    });\n    const [drappingId, setDrappingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [draggerMark, setDraggerMark] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: '',\n        width: 0,\n        height: 0,\n        background: false,\n        lineTop: false,\n        lineBottom: false,\n        lineLeft: false,\n        lineRight: false\n    });\n    const [edgeContainerStatus, setEdgeContainerStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        state: 'none',\n        relativeId: '',\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    });\n    const [viewport, setViewport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vw: 0,\n        vh: 0,\n        dvw: 0,\n        dvh: 0,\n        dvmin: 0,\n        dvmax: 0\n    });\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_utils_constants__WEBPACK_IMPORTED_MODULE_7__.MODE.DESIGN);\n    const [collections, setCollections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [collectionItems, setCollectionItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [componentEditor, setComponentEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    //---------------------------------------\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const urlParams = new URLSearchParams(window.location.search);\n        const device = urlParams?.get('device');\n        _utils_wl_global__WEBPACK_IMPORTED_MODULE_8__.GLOBAL.DEVICE = device || '';\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const projectId = Number(pageData?.projectId) || 0;\n        const siteId = Number(pageData?.siteId) || 0;\n        if (!projectId || !siteId) return;\n        _service_styles_service__WEBPACK_IMPORTED_MODULE_4__.stylesService.subscribe(async ()=>{\n            const styleList = await _service_styles_service__WEBPACK_IMPORTED_MODULE_4__.stylesService.findBySiteId(projectId, siteId);\n            setStyles({\n                ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)(),\n                list: styleList\n            });\n        });\n        _service_asset_component_service__WEBPACK_IMPORTED_MODULE_2__.assetComponentService.subscribe(async ()=>{\n            const components = await _service_asset_component_service__WEBPACK_IMPORTED_MODULE_2__.assetComponentService.findBySiteId(projectId, siteId);\n            setAssetComponents({\n                ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)(),\n                list: components\n            });\n        });\n        _service_font_service__WEBPACK_IMPORTED_MODULE_3__.fontService.findBySiteId(projectId, siteId);\n        _service_font_service__WEBPACK_IMPORTED_MODULE_3__.fontService.subscribe(projectId, siteId, ()=>{\n            if (pageData) {\n                setPageData({\n                    ...pageData,\n                    ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)()\n                });\n            }\n        });\n    }, [\n        pageData?.projectId,\n        pageData?.siteId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const id = receiveMessage.id || '';\n        switch(receiveMessage.type){\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.PARENT_DRAPPING:\n                if (receiveMessage.data) {\n                    setDrapElement(receiveMessage.data);\n                } else {\n                    setDrapElement(null);\n                    setHoverId('');\n                }\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.INIT_PAGE:\n                {\n                    sessionStorage.setItem('token', receiveMessage.extra ? receiveMessage.extra['token'] : '');\n                    _utils_wl_global__WEBPACK_IMPORTED_MODULE_8__.GLOBAL.ALIAS_PAGE = structuredClone(_utils_alias_page_util__WEBPACK_IMPORTED_MODULE_5__.INIT_ALIAS_PAGE);\n                    const newPageData = receiveMessage.data;\n                    setPageData(newPageData);\n                    setSelectedId('');\n                    setHoverId('');\n                    selectComponent('', true);\n                    setMode(receiveMessage.mode || _utils_constants__WEBPACK_IMPORTED_MODULE_7__.MODE.DESIGN);\n                    break;\n                }\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.CHANGE_MODE_PREVIEW:\n                setVersion(receiveMessage.version);\n                setPageData(receiveMessage.data);\n                if (receiveMessage.version) {\n                    selectComponent('', true);\n                } else {\n                    selectComponent('__page__', true);\n                }\n                setSelectedId('');\n                setHoverId('');\n                setDrapElement(null);\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.UPDATE_PAGE:\n                setPageData(receiveMessage.data);\n                setTimeout(()=>{\n                    selectComponent(receiveMessage.id || '', true);\n                }, 0);\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.ADD_COMPONENT:\n                setPageData(receiveMessage.data);\n                setTimeout(()=>{\n                    selectComponent(id, true);\n                }, 0);\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.DRAP_DROP_COMPONENT:\n                setPageData(receiveMessage.data);\n                setTimeout(()=>{\n                    // レンダリングが終わってから\n                    selectComponent(receiveMessage.id || '', true);\n                }, 0);\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.SELECT_COMPONENT:\n                selectComponent(receiveMessage.id || '', true);\n                setHoverId('');\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.REFLECT_SELECT_COMPONENT:\n                selectComponent(receiveMessage.id || '', true, {\n                    postType: _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.REFLECT_SELECT_COMPONENT\n                });\n                setHoverId('');\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.REFLECT_HOVER_COMPONENT:\n                hoverComponent(receiveMessage.id || '', {\n                    dragging: false,\n                    postType: _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.REFLECT_HOVER_COMPONENT\n                });\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.UPDATE_SETTING:\n                if (componentEditor) {\n                    const [variantId, state, componentId] = id.split('=');\n                    if (!variantId || !state || !componentId) {\n                        break;\n                    }\n                    const updatedAssetComponent = window.structuredClone(componentEditor);\n                    const variant = updatedAssetComponent.data.components.variantsData[variantId];\n                    if (!variant) {\n                        break;\n                    }\n                    if (state == 'default') {\n                        variant.layers[componentId] = receiveMessage.data;\n                    } else if (state == 'hover' && variant.hover) {\n                        variant.hover.layers[componentId] = receiveMessage.data;\n                    } else if (state == 'pressed' && variant.pressed) {\n                        variant.pressed.layers[componentId] = receiveMessage.data;\n                    }\n                    setComponentEditor(updatedAssetComponent);\n                    break;\n                }\n                const newPageData = window.structuredClone(pageData);\n                if (newPageData?.components[id]) {\n                    newPageData.components[id] = window.structuredClone(receiveMessage.data);\n                    setPageData(newPageData);\n                }\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.ADD_HEADER_FOOTER:\n                setPageData(receiveMessage.data);\n                setTimeout(()=>{\n                    selectComponent(id, true);\n                }, 0);\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.COPY_COMPONENT:\n                setPageData(receiveMessage.data);\n                setTimeout(()=>{\n                    selectComponent(id, true);\n                }, 0);\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.DELETE_COMPONENT:\n                setPageData(receiveMessage.data);\n                const updatedPageData = receiveMessage.data;\n                if (updatedPageData.components[selectedId]) {\n                    setTimeout(()=>{\n                        selectComponent(selectedId, true);\n                    }, 0);\n                } else {\n                    setSelectedId('');\n                }\n                if (!updatedPageData.components[hoverId]) {\n                    hoverComponent('');\n                }\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.DELETE_COMPONENT_FOR_COMPONENT_EDITOR:\n                setAssetComponents(receiveMessage.data);\n                setSelectedId('');\n                setHoverId('');\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.UPDATE_GRID_POSITION:\n                setPageData(receiveMessage.data);\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.UPDATE_SIDE_SETTING:\n                setPageData(receiveMessage.data);\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.UPDATE_STYLES:\n                setStyles(receiveMessage.data);\n                break;\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.DELETE_PAGE:\n                if (receiveMessage.id == pageData?.id) {\n                    setPageData(null);\n                    setTimeout(()=>{\n                        selectComponent('', true);\n                    }, 0);\n                }\n            case _utils_constants__WEBPACK_IMPORTED_MODULE_7__.RECEIVE_MSG_TYPE.UPDATE_ASSET_COMPONENT:\n                setAssetComponents(receiveMessage.data);\n                if (componentEditor) {\n                    setTimeout(()=>{\n                        selectComponent(receiveMessage.id || selectedId, true);\n                    }, 100);\n                }\n                break;\n        }\n    }, [\n        receiveMessage.ts\n    ]);\n    const pageDataRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(pageData);\n    pageDataRef.current = pageData;\n    const selectComponent = (id, isFromDesignEditor, options)=>{\n        const isComponentEditor = location.pathname.includes('component_editor');\n        if (isComponentEditor) {\n            selectComponentForComponentEditor(id, isFromDesignEditor, options);\n            return;\n        }\n        if (!id && !isFromDesignEditor) {\n            setSelectedId('');\n            postMessage({\n                type: _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.SELECT_COMPONENT,\n                id: '',\n                ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)(),\n                data: {\n                    isFromDesignEditor: isFromDesignEditor\n                }\n            });\n            return;\n        }\n        let targetId = id;\n        const splitedIds = id.split('=');\n        if (splitedIds.length > 1) {\n            targetId = splitedIds[0];\n        }\n        setSelectedId(targetId);\n        const target = document.getElementById(targetId);\n        if (!target) {\n            return;\n        }\n        const data = pageDataRef.current?.components[targetId];\n        const targetRect = target.getBoundingClientRect();\n        const targetStyle = getComputedStyle(target);\n        const fontSize = Number(getComputedStyle(target).fontSize.replace('px', ''));\n        const parentsRect = [];\n        const getParent = (parentId)=>{\n            if (!pageData) {\n                return;\n            }\n            const parent = pageData.components[parentId || ''];\n            if (!parentId || !parent) {\n                return;\n            }\n            const parentEl = document.getElementById(parentId);\n            if (!parentEl) {\n                if (parent.parentId) getParent(parent.parentId);\n                return;\n            }\n            const parentRect = parentEl.getBoundingClientRect();\n            parentsRect.push({\n                width: parentRect.width,\n                height: parentRect.height,\n                top: parentRect.top,\n                left: parentRect.left\n            });\n            if (parent.parentId) getParent(parent.parentId);\n        };\n        getParent(data?.parentId || '');\n        const marginTop = Number(targetStyle.marginTop.replace('px', ''));\n        const marginLeft = Number(targetStyle.marginLeft.replace('px', ''));\n        const borderTop = Number(targetStyle.borderTopWidth.replace('px', ''));\n        const borderBottom = Number(targetStyle.borderBottomWidth.replace('px', ''));\n        const borderLeft = Number(targetStyle.borderLeftWidth.replace('px', ''));\n        const borderRight = Number(targetStyle.borderRightWidth.replace('px', ''));\n        const settingWidthObj = data?.properties.size?.width || {\n            unit: '%',\n            value: '100'\n        };\n        postMessage({\n            type: options?.postType || _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.SELECT_COMPONENT,\n            ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)(),\n            id: targetId,\n            data: {\n                type: data?.type,\n                target: {\n                    width: Math.round(targetRect.width),\n                    settingWidthObj,\n                    height: Math.round(targetRect.height),\n                    top: targetRect.top - marginTop,\n                    left: targetRect.left - marginLeft,\n                    marginTop: marginTop,\n                    marginLeft: marginLeft,\n                    marginRight: Number(targetStyle.marginRight.replace('px', '')),\n                    marginBottom: Number(targetStyle.marginBottom.replace('px', '')),\n                    paddingTop: Number(targetStyle.paddingTop.replace('px', '')),\n                    paddingLeft: Number(targetStyle.paddingLeft.replace('px', '')),\n                    paddingRight: Number(targetStyle.paddingRight.replace('px', '')),\n                    paddingBottom: Number(targetStyle.paddingBottom.replace('px', '')),\n                    borderTopWidth: borderTop,\n                    borderLeftWidth: borderLeft,\n                    borderRightWidth: borderRight,\n                    borderBottomWidth: borderBottom\n                },\n                parents: parentsRect,\n                viewport: {\n                    dvw: viewport.dvw,\n                    dvh: viewport.dvh\n                },\n                fontSize: fontSize,\n                isTextEditing: options?.isTextEditing || false,\n                dragging: options?.dragging || false,\n                isFromDesignEditor: isFromDesignEditor\n            }\n        });\n    };\n    const selectComponentForComponentEditor = (id, isFromDesignEditor, options)=>{\n        if (!id && !isFromDesignEditor) {\n            setSelectedId('');\n            postMessage({\n                type: _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.SELECT_COMPONENT,\n                id: '',\n                ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)(),\n                data: {\n                    isFromDesignEditor: isFromDesignEditor\n                }\n            });\n            return;\n        }\n        if (!id) {\n            setSelectedId('');\n            return;\n        }\n        const [variantId, state, selectedId] = id.split('=');\n        // fail safe\n        if (!variantId || !state || !selectedId) {\n            return;\n        }\n        setSelectedId(id);\n        // get rects of the target element\n        const target = document.getElementById(id);\n        if (!target) {\n            return;\n        }\n        const targetRect = target.getBoundingClientRect();\n        const targetStyle = getComputedStyle(target);\n        const fontSize = Number(getComputedStyle(target).fontSize.replace('px', ''));\n        // get rects of the target parent element\n        const assetComponent = componentEditor;\n        if (!assetComponent) {\n            return;\n        }\n        const variant = assetComponent.data.components.variantsData[variantId];\n        let targetComponent = variant.layers[selectedId];\n        if (variant.hover && state == 'hover') {\n            targetComponent = variant.hover.layers[selectedId];\n        } else if (variant.pressed && state == 'pressed') {\n            targetComponent = variant.pressed.layers[selectedId];\n        }\n        const parentsRect = [];\n        const getParent = (parentId)=>{\n            if (!variant) {\n                return;\n            }\n            let parent = variant.layers[parentId || ''];\n            if (variant.hover && state == 'hover') {\n                parent = variant.hover.layers[parentId || ''];\n            } else if (variant.pressed && state == 'pressed') {\n                parent = variant.pressed.layers[parentId || ''];\n            }\n            if (!parentId || !parent) {\n                return;\n            }\n            const parentEl = document.getElementById(`${variantId}=${state}=${parentId}`);\n            if (!parentEl) {\n                if (parent.parentId) getParent(parent.parentId);\n                return;\n            }\n            const parentRect = parentEl.getBoundingClientRect();\n            parentsRect.push({\n                width: parentRect.width,\n                height: parentRect.height,\n                top: parentRect.top,\n                left: parentRect.left\n            });\n            if (parent.parentId) getParent(parent.parentId);\n        };\n        getParent(targetComponent?.parentId || '');\n        const marginTop = Number(targetStyle.marginTop.replace('px', ''));\n        const marginLeft = Number(targetStyle.marginLeft.replace('px', ''));\n        const borderTop = Number(targetStyle.borderTopWidth.replace('px', ''));\n        const borderBottom = Number(targetStyle.borderBottomWidth.replace('px', ''));\n        const borderLeft = Number(targetStyle.borderLeftWidth.replace('px', ''));\n        const borderRight = Number(targetStyle.borderRightWidth.replace('px', ''));\n        const settingWidthObj = targetComponent?.properties.size?.width || {\n            unit: '%',\n            value: '100'\n        };\n        postMessage({\n            type: options?.postType || _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.SELECT_COMPONENT,\n            ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)(),\n            id: selectedId,\n            data: {\n                type: targetComponent?.type,\n                assetComponent: {\n                    variantId: variantId,\n                    state: state\n                },\n                target: {\n                    width: Math.round(targetRect.width),\n                    settingWidthObj,\n                    height: Math.round(targetRect.height),\n                    top: targetRect.top - marginTop,\n                    left: targetRect.left - marginLeft,\n                    marginTop: marginTop,\n                    marginLeft: marginLeft,\n                    marginRight: Number(targetStyle.marginRight.replace('px', '')),\n                    marginBottom: Number(targetStyle.marginBottom.replace('px', '')),\n                    paddingTop: Number(targetStyle.paddingTop.replace('px', '')),\n                    paddingLeft: Number(targetStyle.paddingLeft.replace('px', '')),\n                    paddingRight: Number(targetStyle.paddingRight.replace('px', '')),\n                    paddingBottom: Number(targetStyle.paddingBottom.replace('px', '')),\n                    borderTopWidth: borderTop,\n                    borderLeftWidth: borderLeft,\n                    borderRightWidth: borderRight,\n                    borderBottomWidth: borderBottom\n                },\n                parents: parentsRect,\n                viewport: {\n                    dvw: viewport.dvw,\n                    dvh: viewport.dvh\n                },\n                fontSize: fontSize,\n                isTextEditing: options?.isTextEditing || false,\n                dragging: options?.dragging || false,\n                isFromDesignEditor: isFromDesignEditor\n            }\n        });\n    };\n    const hoverComponent = (hoveredId, options)=>{\n        if (_utils_wl_global__WEBPACK_IMPORTED_MODULE_8__.GLOBAL.HOVER_TIMEOUT) {\n            clearTimeout(_utils_wl_global__WEBPACK_IMPORTED_MODULE_8__.GLOBAL.HOVER_TIMEOUT);\n            _utils_wl_global__WEBPACK_IMPORTED_MODULE_8__.GLOBAL.HOVER_TIMEOUT = null;\n        }\n        _utils_wl_global__WEBPACK_IMPORTED_MODULE_8__.GLOBAL.HOVER_TIMEOUT = setTimeout(()=>{\n            execHoverComponent(hoveredId, options);\n        }, 50);\n    };\n    const execHoverComponent = (hoveredId, options)=>{\n        if (componentEditor) {\n            hoverComponentForComponentEditor(hoveredId, options);\n            return;\n        }\n        if (!hoveredId) {\n            setHoverId('');\n            postMessage({\n                type: options?.postType || _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.HOVER_COMPONENT,\n                id: '',\n                ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)()\n            });\n            return;\n        }\n        let targetId = hoveredId;\n        const splitedIds = hoveredId.split('=');\n        if (splitedIds.length > 1) {\n            targetId = splitedIds[0];\n        }\n        setHoverId(targetId);\n        const target = document.getElementById(targetId);\n        if (!target) {\n            return;\n        }\n        const data = pageData?.components[targetId];\n        const targetRect = target.getBoundingClientRect();\n        const parentsRect = [];\n        const getParent = (parentId)=>{\n            if (!pageData || parentId == '__page__') {\n                return;\n            }\n            const parent = pageData.components[parentId || ''];\n            if (!parentId || !parent) {\n                return;\n            }\n            const parentEl = document.getElementById(parentId);\n            if (!parentEl) {\n                if (parent.parentId) getParent(parent.parentId);\n                return;\n            }\n            const parentRect = parentEl.getBoundingClientRect();\n            parentsRect.push({\n                id: parentId,\n                width: parentRect.width,\n                height: parentRect.height,\n                top: parentRect.top,\n                left: parentRect.left\n            });\n            if (parent.parentId) getParent(parent.parentId);\n        };\n        getParent(data?.parentId || '');\n        const settingWidthObj = data?.properties.size?.width || {\n            unit: '%',\n            value: '100'\n        };\n        postMessage({\n            type: options?.postType || _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.HOVER_COMPONENT,\n            ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)(),\n            id: targetId,\n            data: {\n                type: data?.type,\n                target: {\n                    width: targetRect.width,\n                    settingWidthObj,\n                    height: targetRect.height,\n                    top: targetRect.top,\n                    left: targetRect.left\n                },\n                parents: parentsRect,\n                dragging: options?.dragging || false\n            }\n        });\n    };\n    const hoverComponentForComponentEditor = (hoveredId, options)=>{\n        if (!hoveredId) {\n            setHoverId('');\n            postMessage({\n                type: options?.postType || _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.HOVER_COMPONENT,\n                id: '',\n                ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)()\n            });\n            return;\n        }\n        const [variantId, state, ownId] = hoveredId.split('=');\n        // fail safe\n        if (!variantId || !state || !ownId) {\n            return;\n        }\n        const target = document.getElementById(hoveredId);\n        if (!target) {\n            return;\n        }\n        const assetComponent = componentEditor;\n        if (!assetComponent) {\n            return;\n        }\n        const variant = assetComponent.data.components.variantsData[variantId];\n        let targetComponent = variant.layers[selectedId];\n        if (variant.hover && state == 'hover') {\n            targetComponent = variant.hover.layers[selectedId];\n        } else if (variant.pressed && state == 'pressed') {\n            targetComponent = variant.pressed.layers[selectedId];\n        }\n        const targetRect = target.getBoundingClientRect();\n        const parentsRect = [];\n        const getParent = (parentId)=>{\n            if (!variant) {\n                return;\n            }\n            let parent = variant.layers[parentId || ''];\n            if (variant.hover && state == 'hover') {\n                parent = variant.hover.layers[parentId || ''];\n            } else if (variant.pressed && state == 'pressed') {\n                parent = variant.pressed.layers[parentId || ''];\n            }\n            if (!parentId || !parent) {\n                return;\n            }\n            const parentEl = document.getElementById(`${variantId}=${state}=${parentId}`);\n            if (!parentEl) {\n                if (parent.parentId) getParent(parent.parentId);\n                return;\n            }\n            const parentRect = parentEl.getBoundingClientRect();\n            parentsRect.push({\n                id: parentId,\n                width: parentRect.width,\n                height: parentRect.height,\n                top: parentRect.top,\n                left: parentRect.left\n            });\n            if (parent.parentId) getParent(parent.parentId);\n        };\n        getParent(targetComponent?.parentId || '');\n        const settingWidthObj = targetComponent?.properties.size?.width || {\n            unit: '%',\n            value: '100'\n        };\n        postMessage({\n            type: options?.postType || _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.HOVER_COMPONENT,\n            ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)(),\n            id: ownId,\n            data: {\n                type: targetComponent?.type,\n                assetComponent: {\n                    variantId: variantId,\n                    state: state\n                },\n                target: {\n                    width: targetRect.width,\n                    settingWidthObj,\n                    height: targetRect.height,\n                    top: targetRect.top,\n                    left: targetRect.left\n                },\n                parents: parentsRect,\n                dragging: options?.dragging || false\n            }\n        });\n    };\n    const postMessage = (message)=>{\n        // console.log('LIVE - SENT:', message);\n        const data = {\n            ...message,\n            from: _utils_wl_global__WEBPACK_IMPORTED_MODULE_8__.GLOBAL.DEVICE\n        };\n        window.parent.postMessage(data, '*');\n    };\n    const getVariantsRect = ()=>{\n        if (!componentEditor) {\n            return;\n        }\n        const variantRects = componentEditor.data.components.variants.map((variantId)=>{\n            const target = document.getElementById(variantId);\n            const targetHover = document.getElementById(variantId + '-hover');\n            const targetPressed = document.getElementById(variantId + '-pressed');\n            if (!target) {\n                return;\n            }\n            const targetRect = target.getBoundingClientRect();\n            const hoverRect = targetHover?.getBoundingClientRect();\n            const pressedRect = targetPressed?.getBoundingClientRect();\n            return {\n                id: variantId,\n                target: {\n                    width: targetRect.width,\n                    height: targetRect.height,\n                    top: targetRect.top,\n                    left: targetRect.left\n                },\n                targetHover: hoverRect ? {\n                    width: hoverRect.width,\n                    height: hoverRect.height,\n                    top: hoverRect.top,\n                    left: hoverRect.left\n                } : undefined,\n                targetPressed: pressedRect ? {\n                    width: pressedRect.width,\n                    height: pressedRect.height,\n                    top: pressedRect.top,\n                    left: pressedRect.left\n                } : undefined\n            };\n        });\n        // send a message to the design-editor\n        const message = {\n            type: _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.GET_VARIANTS_RECT,\n            ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)(),\n            data: variantRects\n        };\n        postMessage(message);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!drapElement || !pageData || drapElement.type == 'cancel' || version) {\n            setDraggerMark({\n                id: '',\n                width: 0,\n                height: 0\n            });\n            return;\n        }\n        if (drapElement.type == 'drop') {\n            let targetId = draggerMark.id;\n            let parentId = '';\n            let position = '';\n            if (draggerMark.background) {\n                if (componentEditor) {\n                    const componentId = draggerMark.id.split('=')[2];\n                    parentId = componentId || '';\n                    targetId = componentId || '';\n                } else {\n                    parentId = draggerMark.id;\n                    targetId = draggerMark.id;\n                }\n                position = 'in';\n            } else {\n                let component = pageData.components[draggerMark.id];\n                if (componentEditor) {\n                    const [variantId, state, componentId] = draggerMark.id.split('=');\n                    const variant = componentEditor.data.components.variantsData[variantId];\n                    if (variant) {\n                        component = (0,_utils_asset_component__WEBPACK_IMPORTED_MODULE_6__.getTargetVariantComponent)(variant, state, componentId);\n                    }\n                    targetId = componentId || '';\n                } else {\n                    targetId = draggerMark.id;\n                }\n                if (!component) return;\n                parentId = component.parentId || '';\n                position = draggerMark.lineTop || draggerMark.lineLeft ? 'prev' : 'next';\n            }\n            const [variantId, state] = draggerMark.id.split('=');\n            postMessage({\n                type: _utils_constants__WEBPACK_IMPORTED_MODULE_7__.POST_MSG_TYPE.DROP_NEW_COMPONENT,\n                ts: (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_10__.NEW_TS)(),\n                id: draggerMark.id,\n                data: {\n                    assetComponentId: drapElement.element.elId,\n                    type: drapElement.element.elType,\n                    target: {\n                        id: targetId,\n                        parentId: parentId,\n                        position: position\n                    },\n                    assetComponent: componentEditor ? {\n                        variantId: variantId,\n                        state: state\n                    } : undefined\n                }\n            });\n            setDraggerMark({\n                id: '',\n                width: 0,\n                height: 0\n            });\n            return;\n        }\n        const clientX = drapElement.clientX || 0;\n        const clientY = drapElement.clientY || 0;\n        const halfWidth = (drapElement.element.elWidth || 70) / 2;\n        const halfHeight = (drapElement.element.elHeight || 40) / 2;\n        const elements = document.elementsFromPoint(clientX, clientY);\n        const target = elements.find((element)=>element.id);\n        let targetData = pageData.components[target?.id || ''];\n        if (componentEditor) {\n            const [variantId, state, componentId] = target?.id.split('=') || [];\n            const variant = componentEditor.data.components.variantsData[variantId];\n            if (variant) {\n                targetData = (0,_utils_asset_component__WEBPACK_IMPORTED_MODULE_6__.getTargetVariantComponent)(variant, state, componentId);\n            }\n        }\n        if (!target || !targetData || target.id == '__page__') {\n            setDraggerMark({\n                id: '',\n                width: 0,\n                height: 0\n            });\n            return;\n        }\n        let parent = pageData.components[targetData.parentId || ''];\n        if (componentEditor) {\n            const [variantId, state, componentId] = target.id.split('=');\n            const variant = componentEditor.data.components.variantsData[variantId];\n            if (variant) {\n                parent = (0,_utils_asset_component__WEBPACK_IMPORTED_MODULE_6__.getTargetVariantComponent)(variant, state, componentId);\n            }\n        }\n        let isHorizontalLayout = false;\n        if (parent?.properties.layout) {\n            const layout = parent.properties.layout;\n            if (layout.type == 'grid' || layout.flex.flexDirection.indexOf('row') >= 0) {\n                isHorizontalLayout = true;\n            }\n        }\n        const inArea = [\n            '__main__',\n            '__header__',\n            '__footer__',\n            '__left-side__',\n            '__right-side__'\n        ].includes(target.id);\n        const targetRect = target.getBoundingClientRect();\n        if (!inArea) {\n            // 対象がエリアじゃなければ、マウス位置次第で対象の要素の前後に配置\n            // ちょっと上にはみ出るなら対象の要素の前に目印\n            if (isHorizontalLayout) {\n                if (clientX - halfWidth < targetRect.left) {\n                    setDraggerMark({\n                        id: target.id,\n                        width: targetRect.width,\n                        height: targetRect.height,\n                        lineLeft: true\n                    });\n                    return;\n                } else if (targetRect.right < clientX + halfWidth) {\n                    setDraggerMark({\n                        id: target.id,\n                        width: targetRect.width,\n                        height: targetRect.height,\n                        lineRight: true\n                    });\n                    return;\n                }\n            } else {\n                if (clientY - halfHeight < targetRect.top) {\n                    setDraggerMark({\n                        id: target.id,\n                        width: targetRect.width,\n                        height: targetRect.height,\n                        lineTop: true\n                    });\n                    return;\n                } else if (targetRect.bottom < clientY + halfHeight) {\n                    setDraggerMark({\n                        id: target.id,\n                        width: targetRect.width,\n                        height: targetRect.height,\n                        lineBottom: true\n                    });\n                    return;\n                }\n            }\n        }\n        // マウス位置が完全に中にあるので、細かく状況を見る\n        if (![\n            _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__.ComponentType.Main,\n            _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__.ComponentType.Header,\n            _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__.ComponentType.Footer,\n            _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__.ComponentType.LeftSide,\n            _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__.ComponentType.RightSide,\n            _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__.ComponentType.Section,\n            _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__.ComponentType.Block,\n            _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__.ComponentType.Columns\n        ].includes(targetData.type)) {\n            // 子要素を持てない要素なので、前後に目印\n            if (isHorizontalLayout) {\n                if (clientX - halfWidth < targetRect.left) {\n                    setDraggerMark({\n                        id: target.id,\n                        width: targetRect.width,\n                        height: targetRect.height,\n                        lineLeft: true\n                    });\n                } else if (targetRect.right < clientX + halfWidth) {\n                    setDraggerMark({\n                        id: target.id,\n                        width: targetRect.width,\n                        height: targetRect.height,\n                        lineRight: true\n                    });\n                }\n            } else {\n                if (clientY - targetRect.top < clientY - targetRect.bottom) {\n                    // 上寄りなので前に目印\n                    setDraggerMark({\n                        id: target.id,\n                        width: targetRect.width,\n                        height: targetRect.height,\n                        lineTop: true\n                    });\n                } else {\n                    setDraggerMark({\n                        id: target.id,\n                        width: targetRect.width,\n                        height: targetRect.height,\n                        lineBottom: true\n                    });\n                }\n            }\n            return;\n        }\n        if (targetData.children.length <= 0) {\n            // 子要素を持たないので、背景色の目印\n            setDraggerMark({\n                id: target.id,\n                width: targetRect.width,\n                height: targetRect.height,\n                background: true\n            });\n            return;\n        }\n        // 目線が親に変わっているので、レイアウト情報を改めて取得\n        if (targetData.properties.layout) {\n            const layout = targetData.properties.layout;\n            isHorizontalLayout = layout.type == 'grid' || layout.flex.flexDirection.indexOf('row') >= 0;\n        }\n        // 子要素を持っているので、子要素との位置関係を調べる\n        let nearestChild = {\n            id: '',\n            x: 0,\n            y: 0,\n            width: 0,\n            height: 0\n        };\n        targetData.children.forEach((child)=>{\n            let id = child;\n            if (componentEditor) {\n                const [variantId, state] = target.id.split('=');\n                id = `${variantId}=${state}=${child}`;\n            }\n            const childElement = document.getElementById(id);\n            if (!childElement) {\n                return;\n            }\n            const childRects = childElement.getBoundingClientRect();\n            const x = childRects.left + childRects.width / 2;\n            const y = childRects.top + childRects.height / 2;\n            if (!nearestChild.id) {\n                nearestChild = {\n                    id: id,\n                    x: x,\n                    y: y,\n                    width: childRects.width,\n                    height: childRects.height\n                };\n                return;\n            }\n            if (isHorizontalLayout) {\n                if (Math.abs(x - clientX) < Math.abs(nearestChild.x - clientX)) {\n                    nearestChild = {\n                        id: id,\n                        x: x,\n                        y: y,\n                        width: childRects.width,\n                        height: childRects.height\n                    };\n                }\n            } else {\n                if (Math.abs(y - clientY) < Math.abs(nearestChild.y - clientY)) {\n                    nearestChild = {\n                        id: id,\n                        x: x,\n                        y: y,\n                        width: childRects.width,\n                        height: childRects.height\n                    };\n                }\n            }\n        });\n        if (isHorizontalLayout) {\n            if (clientX < nearestChild.x) {\n                setDraggerMark({\n                    id: nearestChild.id,\n                    width: nearestChild.width,\n                    height: nearestChild.height,\n                    lineLeft: true\n                });\n            } else {\n                setDraggerMark({\n                    id: nearestChild.id,\n                    width: nearestChild.width,\n                    height: nearestChild.height,\n                    lineRight: true\n                });\n            }\n        } else {\n            if (clientY < nearestChild.y) {\n                setDraggerMark({\n                    id: nearestChild.id,\n                    width: nearestChild.width,\n                    height: nearestChild.height,\n                    lineTop: true\n                });\n            } else {\n                setDraggerMark({\n                    id: nearestChild.id,\n                    width: nearestChild.width,\n                    height: nearestChild.height,\n                    lineBottom: true\n                });\n            }\n        }\n    }, [\n        drapElement\n    ]);\n    //---------------------------------------\n    return /*#__PURE__*/ (0,_emotion_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AppContext__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Provider, {\n        value: {\n            drapElement: drapElement,\n            setDrapElement: setDrapElement,\n            //---------------------------------------\n            pageData: pageData,\n            setPageData: setPageData,\n            version: version,\n            styles: styles,\n            setStyles: setStyles,\n            assetComponents: assetComponents,\n            setAssetComponents: setAssetComponents,\n            selectedId: selectedId,\n            selectComponent: selectComponent,\n            getVariantsRect: getVariantsRect,\n            hoverId: hoverId,\n            hoverComponent: hoverComponent,\n            isEdgeEditing: isEdgeEditing,\n            setIsEdgeEditing: setIsEdgeEditing,\n            isTextEditing: isTextEditing,\n            setIsTextEditing: setIsTextEditing,\n            postMessage: postMessage,\n            receiveMessage: receiveMessage,\n            setReceiveMessage: setReceiveMessage,\n            mousePoint: mousePoint,\n            setMousePoint: setMousePoint,\n            drappingId: drappingId,\n            setDrappingId: setDrappingId,\n            draggerMark: draggerMark,\n            setDraggerMark: setDraggerMark,\n            edgeContainerStatus,\n            setEdgeContainerStatus,\n            viewport,\n            setViewport,\n            mode,\n            setMode,\n            collections,\n            setCollections,\n            collectionItems,\n            setCollectionItems,\n            componentEditor: componentEditor,\n            setComponentEditor: setComponentEditor\n        },\n        children: props.children\n    }, void 0, false, {\n        fileName: \"C:\\\\Work\\\\Automation\\\\nextgen-bindup\\\\nextgen-bindup\\\\apps\\\\live-editor\\\\src\\\\providers\\\\AppProvider.tsx\",\n        lineNumber: 1186,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/providers/AppProvider.tsx\n");

/***/ }),

/***/ "./src/service/api-service.ts":
/*!************************************!*\
  !*** ./src/service/api-service.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: `${process.env.NEXT_PUBLIC_API_URL}`,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\napiClient.interceptors.request.use((config)=>{\n    const token = sessionStorage.getItem('token');\n    config.headers.Authorization = `Bearer ${token}`;\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response && error.response.data) {\n        return Promise.reject(error.response.data);\n    } else if (error.response && error.response.status === 401) {\n        console.error('Unauthorized, redirect to login...');\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/service/api-service.ts\n");

/***/ }),

/***/ "./src/service/api/font-service.ts":
/*!*****************************************!*\
  !*** ./src/service/api/font-service.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fontApiService: () => (/* binding */ fontApiService)\n/* harmony export */ });\n/* harmony import */ var _api_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api-service */ \"./src/service/api-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_api_service__WEBPACK_IMPORTED_MODULE_0__]);\n_api_service__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nclass FontApiService {\n    async findBySiteId(projectId, siteId) {\n        const response = await _api_service__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/fontset/site/${projectId}/${siteId}`);\n        return response.data;\n    }\n}\nconst fontApiService = new FontApiService();\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc2VydmljZS9hcGkvZm9udC1zZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQ3VDO0FBR3ZDLE1BQU1DO0lBQ0osTUFBTUMsYUFDSkMsU0FBaUIsRUFDakJDLE1BQWMsRUFDWTtRQUMxQixNQUFNQyxXQUFXLE1BQU1MLHdEQUFhLENBQ2xDLENBQUMsY0FBYyxFQUFFRyxVQUFVLENBQUMsRUFBRUMsUUFBUTtRQUV4QyxPQUFPQyxTQUFTRSxJQUFJO0lBQ3RCO0FBQ0Y7QUFFTyxNQUFNQyxpQkFBaUIsSUFBSVAsaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG5leHRnZW4tYmluZHVwL2xpdmUtZWRpdG9yLy4vc3JjL3NlcnZpY2UvYXBpL2ZvbnQtc2VydmljZS50cz9lZDg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZvbnRTZXRFbnRpdHkgfSBmcm9tICdAbmV4dGdlbi1iaW5kdXAvY29tbW9uL2R0by9mb250JztcclxuaW1wb3J0IGFwaUNsaWVudCBmcm9tICcuLi9hcGktc2VydmljZSc7XHJcbmltcG9ydCB7IElGb250U2VydmljZSB9IGZyb20gJy4uL2ZvbnQtc2VydmljZSc7XHJcblxyXG5jbGFzcyBGb250QXBpU2VydmljZSBpbXBsZW1lbnRzIElGb250U2VydmljZSB7XHJcbiAgYXN5bmMgZmluZEJ5U2l0ZUlkKFxyXG4gICAgcHJvamVjdElkOiBudW1iZXIsXHJcbiAgICBzaXRlSWQ6IG51bWJlcixcclxuICApOiBQcm9taXNlPEZvbnRTZXRFbnRpdHlbXT4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0PEZvbnRTZXRFbnRpdHlbXT4oXHJcbiAgICAgIGAvZm9udHNldC9zaXRlLyR7cHJvamVjdElkfS8ke3NpdGVJZH1gLFxyXG4gICAgKTtcclxuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IGZvbnRBcGlTZXJ2aWNlID0gbmV3IEZvbnRBcGlTZXJ2aWNlKCk7XHJcbiJdLCJuYW1lcyI6WyJhcGlDbGllbnQiLCJGb250QXBpU2VydmljZSIsImZpbmRCeVNpdGVJZCIsInByb2plY3RJZCIsInNpdGVJZCIsInJlc3BvbnNlIiwiZ2V0IiwiZGF0YSIsImZvbnRBcGlTZXJ2aWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/service/api/font-service.ts\n");

/***/ }),

/***/ "./src/service/api/styles-service.ts":
/*!*******************************************!*\
  !*** ./src/service/api/styles-service.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stylesApiService: () => (/* binding */ stylesApiService)\n/* harmony export */ });\n/* harmony import */ var _api_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api-service */ \"./src/service/api-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_api_service__WEBPACK_IMPORTED_MODULE_0__]);\n_api_service__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nclass StylesApiService {\n    async findBySiteId(projectId, siteId) {\n        const response = await _api_service__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/styles/site/${projectId}/${siteId}`);\n        return response.data;\n    }\n}\nconst stylesApiService = new StylesApiService();\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc2VydmljZS9hcGkvc3R5bGVzLXNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDdUM7QUFHdkMsTUFBTUM7SUFDSixNQUFNQyxhQUNKQyxTQUFpQixFQUNqQkMsTUFBYyxFQUNVO1FBQ3hCLE1BQU1DLFdBQVcsTUFBTUwsd0RBQWEsQ0FDbEMsQ0FBQyxhQUFhLEVBQUVHLFVBQVUsQ0FBQyxFQUFFQyxRQUFRO1FBRXZDLE9BQU9DLFNBQVNFLElBQUk7SUFDdEI7QUFDRjtBQUVPLE1BQU1DLG1CQUFtQixJQUFJUCxtQkFBbUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbmV4dGdlbi1iaW5kdXAvbGl2ZS1lZGl0b3IvLi9zcmMvc2VydmljZS9hcGkvc3R5bGVzLXNlcnZpY2UudHM/MTc3YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTdHlsZUVudGl0eSB9IGZyb20gJ0BuZXh0Z2VuLWJpbmR1cC9jb21tb24vZHRvL3N0eWxlcy9zdHlsZS5kdG8nO1xyXG5pbXBvcnQgYXBpQ2xpZW50IGZyb20gJy4uL2FwaS1zZXJ2aWNlJztcclxuaW1wb3J0IHsgSVN0eWxlc1NlcnZpY2UgfSBmcm9tICcuLi9zdHlsZXMtc2VydmljZSc7XHJcblxyXG5jbGFzcyBTdHlsZXNBcGlTZXJ2aWNlIGltcGxlbWVudHMgSVN0eWxlc1NlcnZpY2Uge1xyXG4gIGFzeW5jIGZpbmRCeVNpdGVJZChcclxuICAgIHByb2plY3RJZDogbnVtYmVyLFxyXG4gICAgc2l0ZUlkOiBudW1iZXIsXHJcbiAgKTogUHJvbWlzZTxTdHlsZUVudGl0eVtdPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQ8U3R5bGVFbnRpdHlbXT4oXHJcbiAgICAgIGAvc3R5bGVzL3NpdGUvJHtwcm9qZWN0SWR9LyR7c2l0ZUlkfWAsXHJcbiAgICApO1xyXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgY29uc3Qgc3R5bGVzQXBpU2VydmljZSA9IG5ldyBTdHlsZXNBcGlTZXJ2aWNlKCk7XHJcbiJdLCJuYW1lcyI6WyJhcGlDbGllbnQiLCJTdHlsZXNBcGlTZXJ2aWNlIiwiZmluZEJ5U2l0ZUlkIiwicHJvamVjdElkIiwic2l0ZUlkIiwicmVzcG9uc2UiLCJnZXQiLCJkYXRhIiwic3R5bGVzQXBpU2VydmljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/service/api/styles-service.ts\n");

/***/ }),

/***/ "./src/service/asset-component-service.ts":
/*!************************************************!*\
  !*** ./src/service/asset-component-service.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assetComponentService: () => (/* binding */ assetComponentService)\n/* harmony export */ });\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/supabase/client */ \"./src/utils/supabase/client.ts\");\n/* harmony import */ var _utils_supabase_supabase_repository__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/supabase/supabase-repository */ \"./src/utils/supabase/supabase-repository.ts\");\n\n\nclass AssetComponentService extends _utils_supabase_supabase_repository__WEBPACK_IMPORTED_MODULE_1__.SupabaseService {\n    constructor(){\n        super('asset_component');\n    }\n    async createAssetComponent(data) {\n        return await this.create(data);\n    }\n    async subscribe(callback) {\n        this.supabase.channel('asset_component_changes').on('postgres_changes', {\n            table: 'contents',\n            schema: 'public',\n            event: '*'\n        }, callback).subscribe();\n    }\n    async unsubscribe(channel) {\n        const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.getSupabaseClient)();\n        await supabase.removeChannel(channel);\n    }\n    async findBySiteId(projectId, siteId) {\n        const { data, error } = await this.supabase.from(this.tableName).select('*').eq('projectId', projectId).eq('siteId', siteId);\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    async getContentById(id) {\n        return await this.findById(id);\n    }\n    async updateContent(id, data) {\n        return await this.update(id, data);\n    }\n    async deleteContent(id) {\n        return await this.delete(id);\n    }\n}\nconst assetComponentService = new AssetComponentService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/service/asset-component-service.ts\n");

/***/ }),

/***/ "./src/service/font-service.ts":
/*!*************************************!*\
  !*** ./src/service/font-service.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fontService: () => (/* binding */ fontService)\n/* harmony export */ });\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/supabase/client */ \"./src/utils/supabase/client.ts\");\n/* harmony import */ var _api_font_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api/font-service */ \"./src/service/api/font-service.ts\");\n/* harmony import */ var _supabase_font_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase/font-service */ \"./src/service/supabase/font-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_api_font_service__WEBPACK_IMPORTED_MODULE_1__]);\n_api_font_service__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nclass FontService {\n    getService() {\n        if (process.env.NEXT_PUBLIC_USING_API === '1') {\n            return _api_font_service__WEBPACK_IMPORTED_MODULE_1__.fontApiService;\n        } else {\n            return _supabase_font_service__WEBPACK_IMPORTED_MODULE_2__.fontSbService;\n        }\n    }\n    async subscribe(projectId, siteId, callback) {\n        const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.getSupabaseClient)();\n        supabase.channel('font_sets_changes').on('postgres_changes', {\n            table: 'contents',\n            schema: 'public',\n            event: '*'\n        }, // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        (_)=>{\n            // get all font and update cache\n            this.findBySiteId(projectId, siteId).then(()=>{\n                callback();\n            });\n        }).subscribe();\n    }\n    async findBySiteId(projectId, siteId) {\n        const fontSets = await this.getService().findBySiteId(projectId, siteId);\n        fontSets.forEach((fontSet)=>{\n            this.fontSetsCache.set(fontSet.id.toString(), fontSet);\n        });\n        setTimeout(()=>{\n            this.updateGoogleLinkToDocument(fontSets);\n        }, 1000);\n        return fontSets;\n    }\n    getFontFamilyByFontSetId(fontSetId) {\n        const fontSet = this.fontSetsCache.get(`${fontSetId}`);\n        if (!fontSet) {\n            return '';\n        }\n        return fontSet?.fonts.map((font)=>font.name).join(', ') || '';\n    }\n    updateGoogleLinkToDocument(fontSets) {\n        fontSets.forEach((fontSet)=>{\n            fontSet.fonts?.forEach((font)=>{\n                const formattedFont = font.name.replace(/\\s+/g, '+');\n                const href = `https://fonts.googleapis.com/css?family=${formattedFont}`;\n                if (!document) {\n                    return;\n                }\n                const existingLink = document.querySelector(`link[data-google-font=\"${formattedFont}\"]`);\n                if (!existingLink) {\n                    const linkTag = document.createElement('link');\n                    linkTag.rel = 'stylesheet';\n                    linkTag.href = href;\n                    linkTag.setAttribute('data-google-font', formattedFont);\n                    document.head.appendChild(linkTag);\n                }\n            });\n        });\n    }\n    constructor(){\n        this.fontSetsCache = new Map();\n    }\n}\nconst fontService = new FontService();\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/service/font-service.ts\n");

/***/ }),

/***/ "./src/service/styles-service.ts":
/*!***************************************!*\
  !*** ./src/service/styles-service.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stylesService: () => (/* binding */ stylesService)\n/* harmony export */ });\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/supabase/client */ \"./src/utils/supabase/client.ts\");\n/* harmony import */ var _api_styles_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api/styles-service */ \"./src/service/api/styles-service.ts\");\n/* harmony import */ var _supabase_styles_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase/styles-service */ \"./src/service/supabase/styles-service.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_api_styles_service__WEBPACK_IMPORTED_MODULE_1__]);\n_api_styles_service__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nclass StylesService {\n    getService() {\n        if (process.env.NEXT_PUBLIC_USING_API === '1') {\n            return _api_styles_service__WEBPACK_IMPORTED_MODULE_1__.stylesApiService;\n        } else {\n            return _supabase_styles_service__WEBPACK_IMPORTED_MODULE_2__.stylesSbService;\n        }\n    }\n    async subscribe(callback) {\n        const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.getSupabaseClient)();\n        supabase.channel('styles_changes').on('postgres_changes', {\n            table: 'styles',\n            schema: 'public',\n            event: '*'\n        }, (payload)=>{\n            callback(payload);\n        }).subscribe();\n    }\n    async unsubscribe(channel) {\n        const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.getSupabaseClient)();\n        await supabase.removeChannel(channel);\n    }\n    async findBySiteId(projectId, siteId) {\n        return await this.getService().findBySiteId(projectId, siteId);\n    }\n}\nconst stylesService = new StylesService();\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/service/styles-service.ts\n");

/***/ }),

/***/ "./src/service/supabase/font-service.ts":
/*!**********************************************!*\
  !*** ./src/service/supabase/font-service.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fontSbService: () => (/* binding */ fontSbService)\n/* harmony export */ });\n/* harmony import */ var _utils_supabase_supabase_repository__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/supabase/supabase-repository */ \"./src/utils/supabase/supabase-repository.ts\");\n\nclass FontSbService extends _utils_supabase_supabase_repository__WEBPACK_IMPORTED_MODULE_0__.SupabaseService {\n    constructor(){\n        super('font_sets');\n    }\n    async findBySiteId(projectId, siteId) {\n        const { data, error } = await this.supabase.from(this.tableName).select('*').eq('projectId', projectId).eq('siteId', siteId);\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n}\nconst fontSbService = new FontSbService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc2VydmljZS9zdXBhYmFzZS9mb250LXNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUU7QUFJdkUsTUFBTUMsc0JBQ0lELGdGQUFlQTtJQUd2QkUsYUFBYztRQUNaLEtBQUssQ0FBQztJQUNSO0lBRUEsTUFBTUMsYUFDSkMsU0FBaUIsRUFDakJDLE1BQWMsRUFDWTtRQUMxQixNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTSxJQUFJLENBQUNDLFFBQVEsQ0FDeENDLElBQUksQ0FBQyxJQUFJLENBQUNDLFNBQVMsRUFDbkJDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsYUFBYVIsV0FDaEJRLEVBQUUsQ0FBQyxVQUFVUDtRQUVoQixJQUFJRSxPQUFPO1lBQ1QsTUFBTSxJQUFJTSxNQUFNTixNQUFNTyxPQUFPO1FBQy9CO1FBQ0EsT0FBT1I7SUFDVDtBQUNGO0FBRU8sTUFBTVMsZ0JBQWdCLElBQUlkLGdCQUFnQiIsInNvdXJjZXMiOlsid2VicGFjazovL0BuZXh0Z2VuLWJpbmR1cC9saXZlLWVkaXRvci8uL3NyYy9zZXJ2aWNlL3N1cGFiYXNlL2ZvbnQtc2VydmljZS50cz83NGMxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFN1cGFiYXNlU2VydmljZSB9IGZyb20gJ0AvdXRpbHMvc3VwYWJhc2Uvc3VwYWJhc2UtcmVwb3NpdG9yeSc7XHJcbmltcG9ydCB7IEZvbnRTZXRFbnRpdHkgfSBmcm9tICdAbmV4dGdlbi1iaW5kdXAvY29tbW9uL2R0by9mb250JztcclxuaW1wb3J0IHsgSUZvbnRTZXJ2aWNlIH0gZnJvbSAnLi4vZm9udC1zZXJ2aWNlJztcclxuXHJcbmNsYXNzIEZvbnRTYlNlcnZpY2VcclxuICBleHRlbmRzIFN1cGFiYXNlU2VydmljZTxGb250U2V0RW50aXR5PlxyXG4gIGltcGxlbWVudHMgSUZvbnRTZXJ2aWNlXHJcbntcclxuICBjb25zdHJ1Y3RvcigpIHtcclxuICAgIHN1cGVyKCdmb250X3NldHMnKTtcclxuICB9XHJcblxyXG4gIGFzeW5jIGZpbmRCeVNpdGVJZChcclxuICAgIHByb2plY3RJZDogbnVtYmVyLFxyXG4gICAgc2l0ZUlkOiBudW1iZXIsXHJcbiAgKTogUHJvbWlzZTxGb250U2V0RW50aXR5W10+IHtcclxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHRoaXMuc3VwYWJhc2VcclxuICAgICAgLmZyb20odGhpcy50YWJsZU5hbWUpXHJcbiAgICAgIC5zZWxlY3QoJyonKVxyXG4gICAgICAuZXEoJ3Byb2plY3RJZCcsIHByb2plY3RJZClcclxuICAgICAgLmVxKCdzaXRlSWQnLCBzaXRlSWQpO1xyXG5cclxuICAgIGlmIChlcnJvcikge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IubWVzc2FnZSk7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gZGF0YSBhcyBGb250U2V0RW50aXR5W107XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgY29uc3QgZm9udFNiU2VydmljZSA9IG5ldyBGb250U2JTZXJ2aWNlKCk7XHJcbiJdLCJuYW1lcyI6WyJTdXBhYmFzZVNlcnZpY2UiLCJGb250U2JTZXJ2aWNlIiwiY29uc3RydWN0b3IiLCJmaW5kQnlTaXRlSWQiLCJwcm9qZWN0SWQiLCJzaXRlSWQiLCJkYXRhIiwiZXJyb3IiLCJzdXBhYmFzZSIsImZyb20iLCJ0YWJsZU5hbWUiLCJzZWxlY3QiLCJlcSIsIkVycm9yIiwibWVzc2FnZSIsImZvbnRTYlNlcnZpY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/service/supabase/font-service.ts\n");

/***/ }),

/***/ "./src/service/supabase/styles-service.ts":
/*!************************************************!*\
  !*** ./src/service/supabase/styles-service.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stylesSbService: () => (/* binding */ stylesSbService)\n/* harmony export */ });\n/* harmony import */ var _utils_supabase_supabase_repository__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/supabase/supabase-repository */ \"./src/utils/supabase/supabase-repository.ts\");\n\nclass StylesSbService extends _utils_supabase_supabase_repository__WEBPACK_IMPORTED_MODULE_0__.SupabaseService {\n    constructor(){\n        super('styles');\n    }\n    async findBySiteId(projectId, siteId) {\n        const { data, error } = await this.supabase.from(this.tableName).select('*').eq('projectId', projectId).eq('siteId', siteId);\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n}\nconst stylesSbService = new StylesSbService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/service/supabase/styles-service.ts\n");

/***/ }),

/***/ "./src/utils/alias-page.util.ts":
/*!**************************************!*\
  !*** ./src/utils/alias-page.util.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INIT_ALIAS_PAGE: () => (/* binding */ INIT_ALIAS_PAGE),\n/* harmony export */   createAliasComponent: () => (/* binding */ createAliasComponent)\n/* harmony export */ });\n/* harmony import */ var _nextgen_bindup_common_default_value_property_prop_action_default_value__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nextgen-bindup/common/default-value/property/prop-action-default-value */ \"../../packages/common/default-value/property/prop-action-default-value.ts\");\n/* harmony import */ var _nextgen_bindup_common_default_value_property_prop_background_default_value__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nextgen-bindup/common/default-value/property/prop-background-default-value */ \"../../packages/common/default-value/property/prop-background-default-value.ts\");\n/* harmony import */ var _nextgen_bindup_common_default_value_property_prop_border_default_value__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nextgen-bindup/common/default-value/property/prop-border-default-value */ \"../../packages/common/default-value/property/prop-border-default-value.ts\");\n/* harmony import */ var _nextgen_bindup_common_default_value_property_prop_effect_default_value__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @nextgen-bindup/common/default-value/property/prop-effect-default-value */ \"../../packages/common/default-value/property/prop-effect-default-value.ts\");\n/* harmony import */ var _nextgen_bindup_common_default_value_property_prop_filter_default_value__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @nextgen-bindup/common/default-value/property/prop-filter-default-value */ \"../../packages/common/default-value/property/prop-filter-default-value.ts\");\n/* harmony import */ var _nextgen_bindup_common_default_value_property_prop_position_default_value__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @nextgen-bindup/common/default-value/property/prop-position-default-value */ \"../../packages/common/default-value/property/prop-position-default-value.ts\");\n/* harmony import */ var _nextgen_bindup_common_default_value_property_prop_size_default_value__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @nextgen-bindup/common/default-value/property/prop-size-default-value */ \"../../packages/common/default-value/property/prop-size-default-value.ts\");\n/* harmony import */ var _nextgen_bindup_common_default_value_property_prop_spacing_default_value__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @nextgen-bindup/common/default-value/property/prop-spacing-default-value */ \"../../packages/common/default-value/property/prop-spacing-default-value.ts\");\n/* harmony import */ var _nextgen_bindup_common_dto_page__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @nextgen-bindup/common/dto/page */ \"../../packages/common/dto/page.ts\");\n/* harmony import */ var _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @nextgen-bindup/common/dto/types/component.type */ \"../../packages/common/dto/types/component.type.ts\");\n\n\n\n\n\n\n\n\n\n\nconst ts = 1;\nconst INIT_ALIAS_PAGE = {\n    id: 'alias',\n    type: _nextgen_bindup_common_dto_page__WEBPACK_IMPORTED_MODULE_8__.PageType.PAGE,\n    name: 'alias',\n    siteId: 'alias',\n    projectId: 'alias',\n    children: [],\n    components: {\n        __page__: {\n            id: '__page__',\n            type: _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__.ComponentType.Page,\n            name: 'Page',\n            parentId: undefined,\n            properties: {\n                ts: 1,\n                actions: (0,_nextgen_bindup_common_default_value_property_prop_action_default_value__WEBPACK_IMPORTED_MODULE_0__.PROP_ACTION_DEFAULT_VALUE)(ts),\n                marginPadding: (0,_nextgen_bindup_common_default_value_property_prop_spacing_default_value__WEBPACK_IMPORTED_MODULE_7__.PROP_SPACING_DEFAULT_VALUE)(ts),\n                size: (0,_nextgen_bindup_common_default_value_property_prop_size_default_value__WEBPACK_IMPORTED_MODULE_6__.PROP_PAGE_SIZE_DEFAULT_VALUE)(ts),\n                border: (0,_nextgen_bindup_common_default_value_property_prop_border_default_value__WEBPACK_IMPORTED_MODULE_2__.PROP_BORDER_DEFAULT_VALUE)(ts),\n                position: (0,_nextgen_bindup_common_default_value_property_prop_position_default_value__WEBPACK_IMPORTED_MODULE_5__.PROP_POSITION_DEFAULT_VALUE)(ts),\n                backgrounds: (0,_nextgen_bindup_common_default_value_property_prop_background_default_value__WEBPACK_IMPORTED_MODULE_1__.PROP_BACKGROUND_LIST_DEFAULT_VALUE)(ts),\n                effects: (0,_nextgen_bindup_common_default_value_property_prop_effect_default_value__WEBPACK_IMPORTED_MODULE_3__.PROP_EFFECT_LIST_DEFAULT_VALUE)(ts),\n                filter: (0,_nextgen_bindup_common_default_value_property_prop_filter_default_value__WEBPACK_IMPORTED_MODULE_4__.PROP_FILTER_DEFAULT_VALUE)(ts)\n            },\n            children: [\n                '__main__'\n            ],\n            breakpoint: {\n                tablet: {\n                    ts: 1\n                },\n                phone: {\n                    ts: 1\n                }\n            },\n            ts: 1\n        },\n        __main__: {\n            id: '__main__',\n            type: _nextgen_bindup_common_dto_types_component_type__WEBPACK_IMPORTED_MODULE_9__.ComponentType.Main,\n            name: 'Main',\n            parentId: '__page__',\n            properties: {\n                ts: 1,\n                actions: (0,_nextgen_bindup_common_default_value_property_prop_action_default_value__WEBPACK_IMPORTED_MODULE_0__.PROP_ACTION_DEFAULT_VALUE)(ts),\n                marginPadding: (0,_nextgen_bindup_common_default_value_property_prop_spacing_default_value__WEBPACK_IMPORTED_MODULE_7__.PROP_SPACING_DEFAULT_VALUE)(ts),\n                size: (0,_nextgen_bindup_common_default_value_property_prop_size_default_value__WEBPACK_IMPORTED_MODULE_6__.PROP_PAGE_SIZE_DEFAULT_VALUE)(ts, {\n                    height: {\n                        value: '',\n                        unit: 'auto'\n                    },\n                    minHeight: {\n                        value: '100',\n                        unit: '%'\n                    }\n                }),\n                border: (0,_nextgen_bindup_common_default_value_property_prop_border_default_value__WEBPACK_IMPORTED_MODULE_2__.PROP_BORDER_DEFAULT_VALUE)(ts),\n                position: (0,_nextgen_bindup_common_default_value_property_prop_position_default_value__WEBPACK_IMPORTED_MODULE_5__.PROP_POSITION_DEFAULT_VALUE)(ts),\n                backgrounds: (0,_nextgen_bindup_common_default_value_property_prop_background_default_value__WEBPACK_IMPORTED_MODULE_1__.PROP_BACKGROUND_LIST_DEFAULT_VALUE)(ts),\n                effects: (0,_nextgen_bindup_common_default_value_property_prop_effect_default_value__WEBPACK_IMPORTED_MODULE_3__.PROP_EFFECT_LIST_DEFAULT_VALUE)(ts),\n                filter: (0,_nextgen_bindup_common_default_value_property_prop_filter_default_value__WEBPACK_IMPORTED_MODULE_4__.PROP_FILTER_DEFAULT_VALUE)(ts)\n            },\n            children: [],\n            breakpoint: {\n                tablet: {\n                    ts: 1\n                },\n                phone: {\n                    ts: 1\n                }\n            },\n            ts: ts\n        }\n    },\n    ts: 1\n};\nconst cloneComponent = (aliasPage, pageData, parentComponent, componentId, index)=>{\n    const aliasStage = parentComponent.aliasStage || 0;\n    const newId = `s${aliasStage}-a${index}_${componentId}`;\n    const component = aliasStage > 1 ? aliasPage.components[componentId] : pageData.components[componentId];\n    const newComponent = structuredClone(component);\n    newComponent.id = newId;\n    newComponent.parentId = parentComponent.id;\n    newComponent.collectionIndex = index;\n    newComponent.aliasStage = aliasStage;\n    newComponent.children = [];\n    aliasPage.components[newId] = newComponent;\n    parentComponent.children.push(newId);\n    for (const childId of component.children){\n        cloneComponent(aliasPage, pageData, newComponent, childId, index);\n    }\n};\nconst createAliasComponent = (aliasPage, pageData, component, numberComponent)=>{\n    if (!pageData || !component) return aliasPage;\n    const aliasStage = (component.aliasStage || 0) + 1;\n    const newId = `s${aliasStage}_${component.id}`;\n    const parentComponent = structuredClone(component);\n    parentComponent.id = newId;\n    parentComponent.aliasStage = aliasStage;\n    parentComponent.children = [];\n    aliasPage.components[newId] = parentComponent;\n    for(let index = 1; index <= numberComponent; index++){\n        for (const childId of component.children){\n            cloneComponent(aliasPage, pageData, parentComponent, childId, index);\n        }\n    }\n    return aliasPage;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/alias-page.util.ts\n");

/***/ }),

/***/ "./src/utils/asset-component.ts":
/*!**************************************!*\
  !*** ./src/utils/asset-component.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findAssetComponent: () => (/* binding */ findAssetComponent),\n/* harmony export */   getAssetComponent: () => (/* binding */ getAssetComponent),\n/* harmony export */   getTargetVariantComponent: () => (/* binding */ getTargetVariantComponent)\n/* harmony export */ });\nconst findAssetComponent = (component, context)=>{\n    const assetComponent = context.list.find((e)=>e.id === component.assetComponent?.id);\n    if (!assetComponent) return null;\n    return assetComponent.data[component.id] || null;\n};\nconst getAssetComponent = (component, context)=>{\n    const assetComponent = context.list.find((e)=>e.id === component.assetComponent?.id);\n    if (!assetComponent) return null;\n    const retComponent = window.structuredClone(component);\n    retComponent.children = assetComponent.data.components.variantsData[component.assetComponent.variantId].children;\n    return retComponent;\n};\nconst getTargetVariantComponent = (variant, state, componentId)=>{\n    if (state == 'hover' && variant.hover) {\n        return variant.hover.layers[componentId];\n    } else if (state == 'pressed' && variant.pressed) {\n        return variant.pressed.layers[componentId];\n    }\n    return variant.layers[componentId];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/asset-component.ts\n");

/***/ }),

/***/ "./src/utils/component.ts":
/*!********************************!*\
  !*** ./src/utils/component.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   layoutGridStyleByDevice: () => (/* binding */ layoutGridStyleByDevice),\n/* harmony export */   mediaStyleByDevice: () => (/* binding */ mediaStyleByDevice),\n/* harmony export */   mergeRefs: () => (/* binding */ mergeRefs),\n/* harmony export */   renderStyle: () => (/* binding */ renderStyle),\n/* harmony export */   sizeStyleByDevice: () => (/* binding */ sizeStyleByDevice),\n/* harmony export */   unitObjectStyle: () => (/* binding */ unitObjectStyle)\n/* harmony export */ });\n/* harmony import */ var _service_font_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/service/font-service */ \"./src/service/font-service.ts\");\n/* harmony import */ var _nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nextgen-bindup/common/utility */ \"../../packages/common/utility.ts\");\n/* harmony import */ var _wl_global__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./wl-global */ \"./src/utils/wl-global.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_service_font_service__WEBPACK_IMPORTED_MODULE_0__]);\n_service_font_service__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst renderStyle = (styleContext, component, parentComponent)=>{\n    let styles = {};\n    if (!component) return styles;\n    // pc\n    styles = renderOneStyle(styleContext, component.properties, parentComponent);\n    if (component.breakpoint?.tablet) {\n        styles['@media (max-width: 768px)'] = renderOneStyle(styleContext, component.breakpoint.tablet, parentComponent);\n    }\n    if (component.breakpoint?.phone) {\n        styles['@media (max-width: 640px)'] = renderOneStyle(styleContext, component.breakpoint.phone, parentComponent);\n    }\n    return styles;\n};\nconst renderOneStyle = (styleContext, data, parentComponent)=>{\n    let styles = {};\n    if (!data) return styles;\n    styles = marginPaddingStyle(styles, data.marginPadding);\n    styles = sizeStyle(styles, data.size);\n    styles = borderStyle(styles, data.border);\n    styles = positionStyle(styles, data.position);\n    styles = backgroundStyle(styleContext, styles, data.backgrounds);\n    styles = effectStyle(styleContext, styles, data.effects);\n    styles = filterStyle(styles, data.filter);\n    styles = textStyle(styles, data.text);\n    styles = paragraphStyle(styles, data.paragraph);\n    styles = linkStyle(styles);\n    styles = mediaStyle(styles, data.media);\n    styles = transformStyle(styles);\n    styles = layerStyle(styles);\n    styles = buttonStyle(styles, data.button);\n    styles = checkboxInputStyle(styles, data.checkbox);\n    styles = textInputStyle(styles, data.textInput);\n    styles = textAreaInputStyle(styles, data.textAreaInput);\n    styles = selectInputStyle(styles, data.selectInput);\n    styles = datetimePickerStyle(styles, data.dateTimePicker);\n    if (data.layout?.type == 'grid') {\n        // TODO: 静的HTMLの出力方法によってはここは必要\n        styles = layoutGridStyle(styles, data.layout.grid);\n    } else if (data.layout?.type == 'flex') {\n        styles = layoutFlexStyle(styles, data.layout.flex);\n    }\n    if (parentComponent?.properties.layout?.type === 'grid') {\n        styles = layoutGridItemStyle(styles, data.gridItem);\n    } else if (parentComponent?.properties.layout?.type === 'flex' && parentComponent.properties.layout.flex?.flexDirection) {\n        styles = layoutFlexItemStyle(styles, parentComponent, data.flexItem);\n    }\n    return styles;\n};\nconst marginPaddingStyle = (styles, data)=>{\n    if (!data) return styles;\n    // margin\n    if (data.margin) {\n        // TODO: DesignEditor マージンの確認\n        if (data.margin.isDetail) {\n            styles.marginTop = unitObjectStyle(data.margin.top);\n            styles.marginRight = unitObjectStyle(data.margin.right);\n            styles.marginBottom = unitObjectStyle(data.margin.bottom);\n            styles.marginLeft = unitObjectStyle(data.margin.left);\n        } else {\n            const marginTopBottom = unitObjectStyle(data.margin.top);\n            const marginLeftRight = unitObjectStyle(data.margin.left);\n            styles.marginTop = marginTopBottom;\n            styles.marginBottom = marginTopBottom;\n            styles.marginLeft = marginLeftRight;\n            styles.marginRight = marginLeftRight;\n        }\n    }\n    // padding\n    if (data.padding) {\n        // DesignEditor パディングの確認\n        if (data.padding.isDetail) {\n            styles.paddingTop = unitObjectStyle(data.padding.top);\n            styles.paddingRight = unitObjectStyle(data.padding.right);\n            styles.paddingBottom = unitObjectStyle(data.padding.bottom);\n            styles.paddingLeft = unitObjectStyle(data.padding.left);\n        } else {\n            const paddingTopBottom = unitObjectStyle(data.padding.top);\n            const paddingLeftRight = unitObjectStyle(data.padding.left);\n            styles.paddingTop = paddingTopBottom;\n            styles.paddingBottom = paddingTopBottom;\n            styles.paddingLeft = paddingLeftRight;\n            styles.paddingRight = paddingLeftRight;\n        }\n    }\n    return styles;\n};\nconst sizeStyle = (styles, data)=>{\n    if (!data) return styles;\n    // width\n    styles.width = unitObjectStyle(data.width);\n    styles.minWidth = unitObjectStyle(data.minWidth);\n    styles.maxWidth = unitObjectStyle(data.maxWidth);\n    // height\n    styles.height = unitObjectStyle(data.height) || 'auto';\n    styles.minHeight = unitObjectStyle(data.minHeight);\n    styles.maxHeight = unitObjectStyle(data.maxHeight);\n    // overflow\n    styles.overflow = data.overflow === 'hide' ? 'hidden' : data.overflow === 'always' ? 'auto' : undefined;\n    if (data.overflow === 'hscroll') {\n        styles.overflowX = 'auto';\n    } else if (data.overflow === 'vscroll') {\n        styles.overflowY = 'auto';\n    }\n    return styles;\n};\nconst sizeStyleByDevice = (styles, component)=>{\n    if (!component) return styles;\n    let size = component.properties.size || null;\n    if (_wl_global__WEBPACK_IMPORTED_MODULE_2__.GLOBAL.DEVICE === 'tablet' && component.breakpoint?.tablet?.size) {\n        size = {\n            ...size,\n            ...component.breakpoint?.tablet?.size\n        };\n    } else if (_wl_global__WEBPACK_IMPORTED_MODULE_2__.GLOBAL.DEVICE === 'sp' && component.breakpoint?.phone?.size) {\n        size = {\n            ...size,\n            ...component.breakpoint?.phone?.size\n        };\n    }\n    if (size) styles = sizeStyle(styles, size);\n    return styles;\n};\nconst borderStyle = (styles, data)=>{\n    if (!data) return styles;\n    // border\n    if (data.isDetail) {\n        if (data.top?.width.value) {\n            styles.borderTopColor = data.top.color || undefined;\n            styles.borderTopWidth = unitObjectStyle(data.top.width);\n            styles.borderTopStyle = data.top.borderStyle;\n        }\n        if (data.right?.width.value) {\n            styles.borderRightColor = data.right.color || undefined;\n            styles.borderRightWidth = unitObjectStyle(data.right.width);\n            styles.borderRightStyle = data.right.borderStyle;\n        }\n        if (data.bottom?.width.value) {\n            styles.borderBottomColor = data.bottom.color || undefined;\n            styles.borderBottomWidth = unitObjectStyle(data.bottom.width);\n            styles.borderBottomStyle = data.bottom.borderStyle;\n        }\n        if (data.left?.width.value) {\n            styles.borderLeftColor = data.left.color || undefined;\n            styles.borderLeftWidth = unitObjectStyle(data.left.width);\n            styles.borderLeftStyle = data.left.borderStyle;\n        }\n    } else if (data.top?.width.value) {\n        styles.borderColor = data.top.color || undefined;\n        styles.borderWidth = unitObjectStyle(data.top.width);\n        styles.borderStyle = data.top.borderStyle;\n    }\n    // radius\n    if (data.isDetail) {\n        const { radiusTopLeft, radiusBottomLeft, radiusTopRight, radiusBottomRight } = data;\n        if (radiusTopLeft.isDetail) {\n            styles.borderTopLeftRadius = `${unitObjectStyle(radiusTopLeft.width) || '0px'} ${unitObjectStyle(radiusTopLeft.height) || '0px'}`;\n        } else {\n            styles.borderTopLeftRadius = unitObjectStyle(radiusTopLeft.width) || '0px';\n        }\n        if (radiusBottomLeft.isDetail) {\n            styles.borderBottomLeftRadius = `${unitObjectStyle(radiusBottomLeft.width) || '0px'} ${unitObjectStyle(radiusBottomLeft.height) || '0px'}`;\n        } else {\n            styles.borderBottomLeftRadius = unitObjectStyle(radiusBottomLeft.width) || '0px';\n        }\n        if (radiusTopRight.isDetail) {\n            styles.borderTopRightRadius = `${unitObjectStyle(radiusTopRight.width) || '0px'} ${unitObjectStyle(radiusTopRight.height) || '0px'}`;\n        } else {\n            styles.borderTopRightRadius = unitObjectStyle(radiusTopRight.width) || '0px';\n        }\n        if (radiusBottomRight.isDetail) {\n            styles.borderBottomRightRadius = `${unitObjectStyle(radiusBottomRight.width) || '0px'} ${unitObjectStyle(radiusBottomRight.height) || '0px'}`;\n        } else {\n            styles.borderBottomRightRadius = unitObjectStyle(radiusBottomRight.width) || '0px';\n        }\n    } else {\n        if (data.radiusTopLeft.isDetail) {\n            styles.borderRadius = `${unitObjectStyle(data.radiusTopLeft.width) || '0px'} ${unitObjectStyle(data.radiusTopLeft.height)}`;\n        } else {\n            styles.borderRadius = unitObjectStyle(data.radiusTopLeft.width) || '0px';\n        }\n    }\n    return styles;\n};\nconst positionStyle = (styles, data)=>{\n    if (!data) return styles;\n    // position\n    styles.position = data.position || 'relative';\n    if (data.position === 'fixed') {\n        styles.top = unitObjectStyle(data.top) || '0px';\n        styles.right = unitObjectStyle(data.right) || '0px';\n        styles.bottom = unitObjectStyle(data.bottom) || '0px';\n        styles.left = unitObjectStyle(data.left) || '0px';\n    }\n    // zIndex\n    if (data.zIndex) styles.zIndex = data.zIndex;\n    return styles;\n};\nconst backgroundStyle = (styleContext, styles, data)=>{\n    if (!data || !data.list) return styles;\n    const backgrounds = [];\n    const backgroundClips = [];\n    let backgroundColor = '';\n    for (const setting of data.list){\n        switch(setting.type){\n            case 'solid':\n                const solidSetting = setting;\n                if (!solidSetting.visibility) {\n                    break;\n                }\n                if (solidSetting.from === 'design' && solidSetting.backgroundColor) {\n                    backgroundColor = backgroundColor || (0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.HexToRgbA)(solidSetting.backgroundColor, solidSetting.alpha);\n                } else if (solidSetting.from === 'preset' && solidSetting.presetId) {\n                    backgroundColor = `var(--myStyle-${solidSetting.presetId})`;\n                }\n                break;\n            case 'gradient':\n                const gradientSetting = setting;\n                if (!gradientSetting.visibility) {\n                    break;\n                }\n                const repeat = gradientSetting.repeat;\n                let size = gradientSetting.size;\n                if (size == 'length') {\n                    const { width, height } = gradientSetting.sizeLength;\n                    size = `${unitObjectStyle(width) || '0px'} ${unitObjectStyle(height) || '0px'}`;\n                }\n                const fixed = gradientSetting.attachmentFixed ? 'fixed' : '';\n                if (gradientSetting.from === 'design' && gradientSetting.background) {\n                    let strBackground = `${gradientSetting.background} ${fixed} top left`;\n                    if (size) strBackground += ` / ${size}`;\n                    if (repeat) strBackground += ` ${repeat}`;\n                    backgrounds.push(strBackground);\n                    backgroundClips.push(gradientSetting.clipText ? 'text' : 'border-box');\n                } else if (gradientSetting.from === 'preset' && gradientSetting.presetId) {\n                    const colorStyle = styleContext.list.find((e)=>e.id === gradientSetting.presetId);\n                    const color = (colorStyle?.data).light;\n                    if (color) {\n                        if (color.indexOf('gradient') >= 0) {\n                            backgrounds.push(`var(--myStyle-${gradientSetting.presetId}) ${fixed} top left / ${size} ${repeat}`);\n                            backgroundClips.push(gradientSetting.clipText ? 'text' : 'border-box');\n                        } else if (!backgroundColor) {\n                            backgroundColor = `var(--myStyle-${gradientSetting.presetId})`;\n                        }\n                    }\n                }\n                break;\n            case 'image':\n                const imgSetting = setting;\n                if (!imgSetting.visibility) {\n                    break;\n                }\n                if (imgSetting.url) {\n                    const repeat = imgSetting.repeat;\n                    let size = imgSetting.size;\n                    if (size === 'length') {\n                        const { width, height } = imgSetting.sizeLength;\n                        size = `${unitObjectStyle(width) || '0px'} ${unitObjectStyle(height) || '0px'}`;\n                    }\n                    let position = '';\n                    if (imgSetting.position) {\n                        const { x, y, offsetX, offsetY } = imgSetting.position;\n                        let xPosition = '';\n                        if (x.unit == 'left' || x.unit == 'right') {\n                            xPosition = `${x.unit} ${unitObjectStyle(offsetX) || '0px'}`;\n                        } else {\n                            xPosition = `left ${unitObjectStyle(x) || '0px'}`;\n                        }\n                        let yPosition = '';\n                        if (y.unit == 'top' || y.unit == 'bottom') {\n                            yPosition = `${y.unit} ${unitObjectStyle(offsetY) || '0px'}`;\n                        } else {\n                            yPosition = `top ${unitObjectStyle(y) || '0px'}`;\n                        }\n                        position = `${xPosition} ${yPosition}`;\n                    }\n                    const fixed = imgSetting.attachmentFixed ? 'fixed' : '';\n                    backgroundClips.push(imgSetting.clipText ? 'text' : 'border-box');\n                    let url = `url('${imgSetting.url}') ${fixed} ${position}`;\n                    if (size) url += ` / ${size}`;\n                    backgrounds.push(`${url} ${repeat}`);\n                }\n                break;\n            case 'video':\n                break;\n            case 'slide':\n                break;\n        }\n    }\n    if (backgroundColor) {\n        backgrounds.push(backgroundColor);\n    }\n    styles.background = backgrounds.join(', ');\n    styles.backgroundClip = backgroundClips.join(', ');\n    return styles;\n};\nconst effectStyle = (styleContext, styles, data)=>{\n    if (!data || !data.list) return styles;\n    for (const setting of data.list){\n        switch(setting.type){\n            case 'shadow':\n                console.log('shadow', setting);\n                const shadowSetting = setting;\n                let color = '#000000';\n                if (typeof shadowSetting.colorStyleId === 'string' && shadowSetting.colorStyleId.startsWith('#')) {\n                    // If it is a color code string, use it as it is.\n                    color = shadowSetting.colorStyleId;\n                } else if (typeof shadowSetting.colorStyleId === 'number') {\n                    // If it is a numeric value, it will be used as a color style ID as in the past.\n                    const colorStyle = styleContext.list.find((style)=>style.id === shadowSetting.colorStyleId);\n                    color = colorStyle?.data?.light || '#000000';\n                }\n                // Build the shadow CSS value\n                const x = shadowSetting.x?.value || '0';\n                const y = shadowSetting.y?.value || '0';\n                const blur = shadowSetting.blur?.value || '0';\n                const expand = shadowSetting.expand?.value || '0';\n                const inset = shadowSetting.displayInside ? 'inset ' : '';\n                const shadowValue = `${inset}${x}px ${y}px ${blur}px ${expand}px ${color}`;\n                // Apply box-shadow or filter: drop-shadow\n                if (shadowSetting.affect === 'box') {\n                    styles.boxShadow = shadowValue;\n                } else {\n                    // For element, use filter: drop-shadow\n                    styles.filter = `drop-shadow(${x}px ${y}px ${blur}px ${color})`;\n                }\n                break;\n            case 'cursor':\n                const cursorSetting = setting;\n                if (cursorSetting.kind) styles.cursor = cursorSetting.kind;\n                break;\n            case 'blend':\n                const blendSetting = setting;\n                if (blendSetting.mode) styles.mixBlendMode = blendSetting.mode;\n                break;\n        }\n    }\n    return styles;\n};\nconst filterStyle = (styles, data)=>{\n    if (!data) return styles;\n    const filters = [];\n    if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.blur)) filters.push(`blur(${data.blur}px)`);\n    if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.brightness)) filters.push(`brightness(${data.brightness}%)`);\n    if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.contrast)) filters.push(`contrast(${data.contrast}%)`);\n    //if (!isNull(data.dropShadow) filters.push(`hue-rotate(${data.dropShadow}deg)`);\n    if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.grayscale)) filters.push(`grayscale(${data.grayscale}%)`);\n    if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.hue)) filters.push(`hue-rotate(${data.hue}deg)`);\n    if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.invert)) filters.push(`invert(${data.invert}%)`);\n    if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.opacity)) filters.push(`opacity(${data.opacity}%)`);\n    if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.saturate)) filters.push(`saturate(${data.saturate}%)`);\n    if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isNull)(data.sepia)) filters.push(`sepia(${data.sepia}%)`);\n    if (filters.length) {\n        if (data.applyTo === 'global') {\n            styles.filter = filters.join(' ');\n        } else {\n            styles.backdropFilter = filters.join(' ');\n        }\n    }\n    return styles;\n};\nconst textStyle = (styles, data)=>{\n    if (!data) return styles;\n    // colorStyleId\n    if (data.colorStyleId) {\n        styles.color = `var(--myStyle-${data.colorStyleId})`;\n    }\n    if (data.type === 'specification') {\n        const value = data.value;\n        // fontName\n        if (value.fontSetId) {\n            styles.fontFamily = _service_font_service__WEBPACK_IMPORTED_MODULE_0__.fontService.getFontFamilyByFontSetId(value.fontSetId);\n        }\n        // fontWeight\n        if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isEmpty)(value.fontWeight)) styles.fontWeight = value.fontWeight;\n        // fontSize\n        if (value.fontSize) styles.fontSize = unitObjectStyle(value.fontSize);\n        // lineHeight\n        if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isNull)(value.lineHeight)) styles.lineHeight = value.lineHeight;\n        // letterSpacing\n        if (value.spacing) styles.letterSpacing = unitObjectStyle(value.spacing);\n        // format\n        for (const format of data.format){\n            switch(format){\n                case 'bold':\n                    styles.fontWeight = 'bold';\n                    break;\n                case 'underline':\n                    styles.textDecoration = 'underline';\n                    break;\n                case 'overline':\n                    styles.textDecoration = 'overline';\n                    break;\n                case 'format-clear':\n                    styles.textDecoration = 'none';\n                    break;\n                case 'italic':\n                    styles.fontStyle = 'italic';\n                    break;\n                case 'strike-through':\n                    styles.textDecoration = 'line-through';\n                    break;\n                case 'bullet-list':\n                    styles.listStyleType = 'disc';\n                    break;\n                case 'link':\n                    styles.textDecoration = 'underline';\n                    styles.color = 'blue';\n                    break;\n            }\n        }\n        // textTransform\n        if (data.textTransform) styles.textTransform = data.textTransform;\n        // textShadow\n        if (!(0,_nextgen_bindup_common_utility__WEBPACK_IMPORTED_MODULE_1__.isEmpty)(data.textShadow)) styles.textShadow = data.textShadow;\n        // minSize\n        if (data.minSize) styles.minWidth = unitObjectStyle(data.minSize);\n        // maxSize\n        if (data.maxSize) styles.maxWidth = unitObjectStyle(data.maxSize);\n    }\n    return styles;\n};\nconst paragraphStyle = (styles, data)=>{\n    if (!data) return styles;\n    // alignment\n    styles.textAlign = data.alignment || undefined;\n    // indentation\n    styles.textIndent = unitObjectStyle(data.indentation);\n    // ellipsis\n    styles.textOverflow = data.ellipsis ? 'ellipsis' : undefined;\n    // verticalAlign\n    styles.verticalAlign = data.verticalAlign || undefined;\n    return styles;\n};\nconst linkStyle = (styles)=>{\n    return styles;\n};\nconst mediaStyle = (styles, data)=>{\n    if (!data) {\n        return styles;\n    }\n    if (data.type == 'image') {\n        const imageData = data;\n        // object-position\n        const { x, y, offsetX, offsetY } = imageData.position;\n        let xPosition = '';\n        if (x.unit == 'left' || x.unit == 'right') {\n            xPosition = `${x.unit} ${unitObjectStyle(offsetX) || '0px'}`;\n        } else {\n            xPosition = `left ${unitObjectStyle(x) || '0px'}`;\n        }\n        let yPosition = '';\n        if (y.unit == 'top' || y.unit == 'bottom') {\n            yPosition = `${y.unit} ${unitObjectStyle(offsetY) || '0px'}`;\n        } else {\n            yPosition = `top ${unitObjectStyle(y) || '0px'}`;\n        }\n        const position = `${xPosition} ${yPosition}`;\n        styles.objectPosition = position;\n        styles.objectFit = imageData.objectFit;\n        // aspect-ratio\n        if (imageData.aspectRatio == 'auto') {\n            styles.aspectRatio = 'auto';\n        } else {\n            const { width, height } = imageData.freeRatio;\n            styles.aspectRatio = `${width} / ${height}`;\n        }\n        styles.float = imageData.float;\n    }\n    return styles;\n};\nconst mediaStyleByDevice = (styles, component)=>{\n    if (!component) return styles;\n    let media = component.properties.media || null;\n    if (_wl_global__WEBPACK_IMPORTED_MODULE_2__.GLOBAL.DEVICE === 'tablet' && component.breakpoint?.tablet?.media) {\n        media = {\n            ...media,\n            ...component.breakpoint?.tablet?.media\n        };\n    } else if (_wl_global__WEBPACK_IMPORTED_MODULE_2__.GLOBAL.DEVICE === 'sp' && component.breakpoint?.phone?.media) {\n        media = {\n            ...media,\n            ...component.breakpoint?.phone?.media\n        };\n    }\n    if (media) {\n        styles = mediaStyle(styles, media);\n    }\n    return styles;\n};\nconst transformStyle = (styles)=>{\n    return styles;\n};\nconst layerStyle = (styles)=>{\n    return styles;\n};\nconst buttonStyle = (styles, data)=>{\n    if (!data) return styles;\n    // shape\n    styles.borderRadius = data.shape === 'rounded' ? '7px' : data.shape === 'sharp' ? '0px' : data.shape === 'circle' ? '50%' : undefined;\n    // size\n    styles.padding = data.size === 'small' ? '2px 5px' : data.size === 'large' ? '10px 20px' : '5px 10px';\n    // color\n    styles.backgroundColor = data.color === 'primary' ? '#0d6efd' : data.color === 'secondary' ? '#6c757d' : data.color === 'success' ? '#198754' : data.color === 'danger' ? '#dc3545' : data.color === 'warning' ? '#ffc107' : data.color === 'info' ? '#0dcaf0' : data.color === 'light' ? '#f8f9fa' : data.color === 'dark' ? '#212529' : undefined;\n    return styles;\n};\nconst checkboxInputStyle = (styles, data)=>{\n    if (!data) return styles;\n    return styles;\n};\nconst textInputStyle = (styles, data)=>{\n    if (!data) return styles;\n    // color\n    if (data.color === 'dark') {\n        styles.backgroundColor = '#232320';\n        styles.color = '#fff';\n    }\n    return styles;\n};\nconst textAreaInputStyle = (styles, data)=>{\n    if (!data) return styles;\n    // resize\n    if (data.resize) styles.resize = data.resize;\n    return styles;\n};\nconst selectInputStyle = (styles, data)=>{\n    if (!data) return styles;\n    return styles;\n};\nconst datetimePickerStyle = (styles, data)=>{\n    if (!data) return styles;\n    if (!data.isShowBorder) {\n        styles.border = 'none';\n        styles.outline = 'none';\n    }\n    return styles;\n};\nconst layoutGridStyle = (styles, data)=>{\n    if (!data) return styles;\n    styles.display = 'grid';\n    const isColumnsAuto = !data.cols ? true : false;\n    const isRowsAuto = !data.rows ? true : false;\n    // 行サイズ\n    if (data.sizePerCol) {\n        const { list, repeat, repeatCount, autoFill, autoFit } = data.sizePerCol;\n        const columnSize = [];\n        list.forEach((column)=>{\n            const { unit, value } = column.size;\n            if (unit == 'auto' || unit == 'min-content' || unit == 'max-content') {\n                columnSize.push(unit);\n                return;\n            }\n            columnSize.push(`${value}${unit}`);\n        });\n        let repeatParam = String(repeatCount || 1);\n        if (autoFill) {\n            repeatParam = 'auto-fill';\n        } else if (autoFit) {\n            repeatParam = 'auto-fit';\n        }\n        let templateColumns = columnSize.join(' ');\n        if (repeat) {\n            templateColumns = `repeat(${repeatParam}, ${templateColumns})`;\n        }\n        if (isColumnsAuto) {\n            styles.gridAutoColumns = columnSize.join(' ');\n        } else {\n            styles.gridTemplateColumns = templateColumns;\n        }\n    }\n    // 行サイズ\n    if (data.sizePerRow) {\n        const { list, repeat, repeatCount, autoFill, autoFit } = data.sizePerRow;\n        const rowSize = [];\n        list.forEach((row)=>{\n            const { unit, value } = row.size;\n            if (unit == 'auto' || unit == 'min-content' || unit == 'max-content') {\n                rowSize.push(unit);\n                return;\n            }\n            rowSize.push(`${value}${unit}`);\n        });\n        let repeatParam = String(repeatCount || 1);\n        if (autoFill) {\n            repeatParam = 'auto-fill';\n        } else if (autoFit) {\n            repeatParam = 'auto-fit';\n        }\n        let templateRows = rowSize.join(' ');\n        if (repeat) {\n            templateRows = `repeat(${repeatParam}, ${templateRows})`;\n        }\n        if (isRowsAuto) {\n            styles.gridAutoRows = rowSize.join(' ');\n        } else {\n            styles.gridTemplateRows = templateRows;\n        }\n    }\n    // ギャップ\n    const gap = `${unitObjectStyle(data.rowGap) || '0px'} ${unitObjectStyle(data.columnGap) || '0px'}`;\n    styles.gap = gap;\n    // grid-auto-flow\n    let gridAutoFlow = '';\n    if (data.gridAutoFlow) {\n        gridAutoFlow = data.gridAutoFlow;\n    }\n    if (data.fillEmptyCell) {\n        gridAutoFlow = gridAutoFlow ? `${gridAutoFlow} dense` : 'dense';\n    }\n    styles.gridAutoFlow = gridAutoFlow;\n    styles.justifyContent = data.justifyContent;\n    styles.alignContent = data.alignContent;\n    styles.justifyItems = data.justifyItems;\n    styles.alignItems = data.alignItems;\n    // data.\n    return styles;\n};\nconst layoutGridStyleByDevice = (styles, component)=>{\n    if (!component) return styles;\n    let layout = component.properties.layout || null;\n    if (_wl_global__WEBPACK_IMPORTED_MODULE_2__.GLOBAL.DEVICE === 'tablet' && component.breakpoint?.tablet?.layout) {\n        layout = {\n            ...layout,\n            ...component.breakpoint?.tablet?.layout\n        };\n    } else if (_wl_global__WEBPACK_IMPORTED_MODULE_2__.GLOBAL.DEVICE === 'sp' && component.breakpoint?.phone?.layout) {\n        layout = {\n            ...layout,\n            ...component.breakpoint?.phone?.layout\n        };\n    }\n    if (layout?.type === 'grid') {\n        styles = layoutGridStyle(styles, layout.grid);\n    }\n    // data.\n    return styles;\n};\nconst layoutGridItemStyle = (styles, data)=>{\n    if (!data) return styles;\n    styles.gridColumnStart = data.startColumn || undefined;\n    styles.gridColumnEnd = data.endColumn || undefined;\n    styles.gridRowStart = data.startRow || undefined;\n    styles.gridRowEnd = data.endRow || undefined;\n    styles.justifySelf = data.justifySelf;\n    styles.alignSelf = data.alignSelf;\n    return styles;\n};\nconst layoutFlexStyle = (styles, data)=>{\n    if (!data || !data.flexDirection) return styles;\n    styles.display = 'flex';\n    styles.flexDirection = data.flexDirection;\n    if (data.justifyContent) {\n        styles.justifyContent = data.justifyContent;\n    }\n    if (data.alignContent) {\n        styles.alignContent = data.alignContent;\n    }\n    if (data.alignItems) {\n        styles.alignItems = data.alignItems;\n    }\n    if (data.flexWrap) {\n        styles.flexWrap = data.flexWrap;\n    }\n    if (data.hozSpacing || data.verSpacing) {\n        const rowGap = unitObjectStyle(data.hozSpacing);\n        const colGap = unitObjectStyle(data.verSpacing);\n        styles.gap = `${rowGap || 0} ${colGap || 0}`;\n    }\n    // data.\n    return styles;\n};\nconst layoutFlexItemStyle = (styles, parentComponent, data)=>{\n    const flexSettings = parentComponent.properties.layout?.flex;\n    if (!flexSettings) return styles;\n    if (flexSettings.flexGrow) {\n        styles.flexGrow = flexSettings.flexGrow;\n    }\n    if (flexSettings.flexShrink) {\n        styles.flexShrink = flexSettings.flexShrink;\n    }\n    if (data?.alignSelf) {\n        styles.alignSelf = data.alignSelf;\n    }\n    if (data?.flexGrow) {\n        styles.flexGrow = data.flexGrow;\n        styles.flexBasis = 0;\n    }\n    if (data?.flexShrink) {\n        styles.flexShrink = data.flexShrink;\n    }\n    if (data?.order) {\n        styles.order = data.order;\n    }\n    return styles;\n};\nconst unitObjectStyle = (data)=>{\n    if (!data) return undefined;\n    if (data.unit == 'auto') {\n        return 'auto';\n    }\n    if (data.value) {\n        return `${data.value}${data.unit}`;\n    }\n    return undefined;\n};\nconst mergeRefs = (...refs)=>{\n    return (element)=>{\n        refs.forEach((ref)=>{\n            if (typeof ref === 'function') {\n                ref(element); // 関数型のrefを処理\n            } else if (ref && typeof ref === 'object') {\n                ref.current = element; // オブジェクト型のrefを処理\n            }\n        });\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/component.ts\n");

/***/ }),

/***/ "./src/utils/constants.ts":
/*!********************************!*\
  !*** ./src/utils/constants.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_RECEIVE_MESSAGE: () => (/* binding */ DEFAULT_RECEIVE_MESSAGE),\n/* harmony export */   MODE: () => (/* binding */ MODE),\n/* harmony export */   POST_MSG_TYPE: () => (/* binding */ POST_MSG_TYPE),\n/* harmony export */   RECEIVE_MSG_TYPE: () => (/* binding */ RECEIVE_MSG_TYPE)\n/* harmony export */ });\nconst MODE = {\n    DESIGN: 'site_design_edit',\n    CONTENT: 'site_content_edit',\n    PREVIEW: 'site_preview'\n};\nconst POST_MSG_TYPE = {\n    NONE: '',\n    IM_READY: 'im-ready',\n    ADD_COMPONENT: 'add-component',\n    SELECT_COMPONENT: 'select-component',\n    GET_VARIANTS_RECT: 'get-variants-rect',\n    REFLECT_SELECT_COMPONENT: 'reflect_select-component',\n    HOVER_COMPONENT: 'hover-component',\n    REFLECT_HOVER_COMPONENT: 'reflect_hover-component',\n    DRAP_DROP_COMPONENT: 'drap-drop-component',\n    DROP_NEW_COMPONENT: 'drop-new-component',\n    UPDATE_TEXT: 'update-text',\n    UPDATE_USER_EDIT: 'update-user-edit',\n    ADD_HEADER_FOOTER: 'add-header-footer',\n    DELETE_COMPONENT: 'delete-component',\n    UPDATE_GRID_POSITION: 'update-grid-position',\n    SET_IFRAME_HEIGHT: 'set-iframe-height',\n    ON_SCALE: 'on-scale',\n    SELECT_IMAGE: 'select-image'\n};\nconst RECEIVE_MSG_TYPE = {\n    NONE: '',\n    INIT_PAGE: 'init-page',\n    CHANGE_MODE_PREVIEW: 'change-mode-preview',\n    UPDATE_PAGE: 'update-page',\n    ADD_COMPONENT: 'add-component',\n    UPDATE_SETTING: 'update-setting',\n    DRAP_DROP_COMPONENT: 'drap-drop-component',\n    SELECT_COMPONENT: 'select-component',\n    REFLECT_SELECT_COMPONENT: 'reflect_select-component',\n    REFLECT_HOVER_COMPONENT: 'reflect_hover-component',\n    UPDATE_STYLES: 'update-styles',\n    ADD_HEADER_FOOTER: 'add-header-footer',\n    UPDATE_SIDE_SETTING: 'update-side-setting',\n    COPY_COMPONENT: 'copy-component',\n    DELETE_COMPONENT: 'delete-component',\n    DELETE_COMPONENT_FOR_COMPONENT_EDITOR: 'delete-component-for-component-editor',\n    UPDATE_GRID_POSITION: 'update-grid-position',\n    PARENT_DRAPPING: 'parent-draping',\n    DELETE_PAGE: 'delete-page',\n    UPDATE_ASSET_COMPONENT: 'update-asset-component'\n};\nconst DEFAULT_RECEIVE_MESSAGE = {\n    type: RECEIVE_MSG_TYPE.NONE,\n    ts: 0\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/constants.ts\n");

/***/ }),

/***/ "./src/utils/supabase/client.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/client.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nlet supabaseClient = null;\nconst getSupabaseClient = ()=>{\n    if (!supabaseClient) {\n        supabaseClient = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(`${\"https://qxgdiywidkdhfwnqhaxv.supabase.co\"}`, `${\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2RpeXdpZGtkaGZ3bnFoYXh2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjkwNDUwMzEsImV4cCI6MjA0NDYyMTAzMX0.akAFcPxyaN4dQEP0ybm2b-UmcWUpv4JkONYDMb2GKRw\"}`);\n    }\n    return supabaseClient;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvc3VwYWJhc2UvY2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUVyRCxJQUFJQyxpQkFBeUQ7QUFFdEQsTUFBTUMsb0JBQW9CO0lBQy9CLElBQUksQ0FBQ0QsZ0JBQWdCO1FBQ25CQSxpQkFBaUJELG1FQUFZQSxDQUMzQixHQUFHRywwQ0FBb0MsRUFBRSxFQUN6QyxHQUFHQSxrTkFBeUMsRUFBRTtJQUVsRDtJQUNBLE9BQU9GO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BuZXh0Z2VuLWJpbmR1cC9saXZlLWVkaXRvci8uL3NyYy91dGlscy9zdXBhYmFzZS9jbGllbnQudHM/NzFhMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnO1xyXG5cclxubGV0IHN1cGFiYXNlQ2xpZW50OiBSZXR1cm5UeXBlPHR5cGVvZiBjcmVhdGVDbGllbnQ+IHwgbnVsbCA9IG51bGw7XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0U3VwYWJhc2VDbGllbnQgPSAoKSA9PiB7XHJcbiAgaWYgKCFzdXBhYmFzZUNsaWVudCkge1xyXG4gICAgc3VwYWJhc2VDbGllbnQgPSBjcmVhdGVDbGllbnQoXHJcbiAgICAgIGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTH1gLFxyXG4gICAgICBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWX1gLFxyXG4gICAgKTtcclxuICB9XHJcbiAgcmV0dXJuIHN1cGFiYXNlQ2xpZW50O1xyXG59O1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VDbGllbnQiLCJnZXRTdXBhYmFzZUNsaWVudCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/utils/supabase/client.ts\n");

/***/ }),

/***/ "./src/utils/supabase/supabase-repository.ts":
/*!***************************************************!*\
  !*** ./src/utils/supabase/supabase-repository.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseService: () => (/* binding */ SupabaseService)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"./src/utils/supabase/client.ts\");\n\nclass SupabaseService {\n    constructor(tableName){\n        this.tableName = tableName;\n        this.supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.getSupabaseClient)();\n    }\n    async create(data) {\n        const { data: createdData, error } = await this.supabase.from(this.tableName).insert(data).single();\n        if (error) {\n            console.log('error', error);\n            throw new Error(error.message);\n        }\n        return createdData;\n    }\n    async findAll() {\n        const { data, error } = await this.supabase.from(this.tableName).select('*');\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    async findById(id) {\n        const { data, error } = await this.supabase.from(this.tableName).select('*').eq('id', id).single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    async update(id, data) {\n        const { data: updatedData, error } = await this.supabase.from(this.tableName).update(data).eq('id', id).single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return updatedData;\n    }\n    async delete(id) {\n        const { error } = await this.supabase.from(this.tableName).delete().eq('id', id);\n        if (error) {\n            return false;\n        }\n        return true;\n    }\n    async createMany(data) {\n        const { data: createdData, error } = await this.supabase.from(this.tableName).insert(data);\n        if (error) {\n            throw new Error(error.message);\n        }\n        return createdData;\n    }\n    async updateMany(ids, data) {\n        const { data: updatedData, error } = await this.supabase.from(this.tableName).update(data).in('id', ids);\n        if (error) {\n            throw new Error(error.message);\n        }\n        return updatedData;\n    }\n    async deleteMany(ids) {\n        const { data, error } = await this.supabase.from(this.tableName).delete().in('id', ids);\n        if (error || !data) {\n            return false;\n        }\n        return data.length > 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/supabase/supabase-repository.ts\n");

/***/ }),

/***/ "./src/utils/wl-global.ts":
/*!********************************!*\
  !*** ./src/utils/wl-global.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GLOBAL: () => (/* binding */ GLOBAL)\n/* harmony export */ });\n/* harmony import */ var _alias_page_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./alias-page.util */ \"./src/utils/alias-page.util.ts\");\n\nconst GLOBAL = {\n    // pc | tablet | sp\n    DEVICE: '',\n    // Store alias components\n    // Declare globally instead of putting them into Context to avoid inaccurate data updates due to asynchronous execution\n    ALIAS_PAGE: structuredClone(_alias_page_util__WEBPACK_IMPORTED_MODULE_0__.INIT_ALIAS_PAGE),\n    HOVER_TIMEOUT: null\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvd2wtZ2xvYmFsLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQ29EO0FBVTdDLE1BQU1DLFNBQXNCO0lBQ2pDLG1CQUFtQjtJQUNuQkMsUUFBUTtJQUVSLHlCQUF5QjtJQUN6Qix1SEFBdUg7SUFDdkhDLFlBQVlDLGdCQUFnQkosNkRBQWVBO0lBRTNDSyxlQUFlO0FBQ2pCLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbmV4dGdlbi1iaW5kdXAvbGl2ZS1lZGl0b3IvLi9zcmMvdXRpbHMvd2wtZ2xvYmFsLnRzPzgyYTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZSB9IGZyb20gJ0BuZXh0Z2VuLWJpbmR1cC9jb21tb24vZHRvL3BhZ2UnO1xyXG5pbXBvcnQgeyBJTklUX0FMSUFTX1BBR0UgfSBmcm9tICcuL2FsaWFzLXBhZ2UudXRpbCc7XHJcblxyXG5leHBvcnQgdHlwZSBERVZJQ0VfVFlQRSA9ICcnIHwgJ3BjJyB8ICd0YWJsZXQnIHwgJ3NwJztcclxuXHJcbmludGVyZmFjZSBHbG9iYWxTdGF0ZSB7XHJcbiAgREVWSUNFOiBERVZJQ0VfVFlQRTtcclxuICBBTElBU19QQUdFOiBQYWdlO1xyXG4gIEhPVkVSX1RJTUVPVVQ6IE5vZGVKUy5UaW1lb3V0IHwgbnVsbDtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IEdMT0JBTDogR2xvYmFsU3RhdGUgPSB7XHJcbiAgLy8gcGMgfCB0YWJsZXQgfCBzcFxyXG4gIERFVklDRTogJycsXHJcblxyXG4gIC8vIFN0b3JlIGFsaWFzIGNvbXBvbmVudHNcclxuICAvLyBEZWNsYXJlIGdsb2JhbGx5IGluc3RlYWQgb2YgcHV0dGluZyB0aGVtIGludG8gQ29udGV4dCB0byBhdm9pZCBpbmFjY3VyYXRlIGRhdGEgdXBkYXRlcyBkdWUgdG8gYXN5bmNocm9ub3VzIGV4ZWN1dGlvblxyXG4gIEFMSUFTX1BBR0U6IHN0cnVjdHVyZWRDbG9uZShJTklUX0FMSUFTX1BBR0UpLFxyXG5cclxuICBIT1ZFUl9USU1FT1VUOiBudWxsLFxyXG59O1xyXG4iXSwibmFtZXMiOlsiSU5JVF9BTElBU19QQUdFIiwiR0xPQkFMIiwiREVWSUNFIiwiQUxJQVNfUEFHRSIsInN0cnVjdHVyZWRDbG9uZSIsIkhPVkVSX1RJTUVPVVQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/utils/wl-global.ts\n");

/***/ }),

/***/ "../../packages/common/default-value/property/prop-action-default-value.ts":
/*!*********************************************************************************!*\
  !*** ../../packages/common/default-value/property/prop-action-default-value.ts ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROP_ACTION_DEFAULT_VALUE: () => (/* binding */ PROP_ACTION_DEFAULT_VALUE)\n/* harmony export */ });\nconst PROP_ACTION_DEFAULT_VALUE = (ts, prop)=>({\n        ts: ts,\n        list: prop?.list || []\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29tbW9uL2RlZmF1bHQtdmFsdWUvcHJvcGVydHkvcHJvcC1hY3Rpb24tZGVmYXVsdC12YWx1ZS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBRU8sTUFBTUEsNEJBQTRCLENBQ3ZDQyxJQUNBQyxPQUN1QjtRQUN2QkQsSUFBSUE7UUFDSkUsTUFBTUQsTUFBTUMsUUFBUSxFQUFFO0lBQ3hCLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbmV4dGdlbi1iaW5kdXAvbGl2ZS1lZGl0b3IvLi4vLi4vcGFja2FnZXMvY29tbW9uL2RlZmF1bHQtdmFsdWUvcHJvcGVydHkvcHJvcC1hY3Rpb24tZGVmYXVsdC12YWx1ZS50cz80MGYxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFjdGlvbkxpc3RQcm9wRHRvIH0gZnJvbSAnLi4vLi4vZHRvL3NldHRpbmctcHJvcGVydGllcy9hY3Rpb24tcHJvcC5kdG8nO1xyXG5cclxuZXhwb3J0IGNvbnN0IFBST1BfQUNUSU9OX0RFRkFVTFRfVkFMVUUgPSAoXHJcbiAgdHM6IG51bWJlcixcclxuICBwcm9wPzogUGFydGlhbDxBY3Rpb25MaXN0UHJvcER0bz4sXHJcbik6IEFjdGlvbkxpc3RQcm9wRHRvID0+ICh7XHJcbiAgdHM6IHRzLFxyXG4gIGxpc3Q6IHByb3A/Lmxpc3QgfHwgW10sXHJcbn0pO1xyXG4iXSwibmFtZXMiOlsiUFJPUF9BQ1RJT05fREVGQVVMVF9WQUxVRSIsInRzIiwicHJvcCIsImxpc3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/common/default-value/property/prop-action-default-value.ts\n");

/***/ }),

/***/ "../../packages/common/default-value/property/prop-background-default-value.ts":
/*!*************************************************************************************!*\
  !*** ../../packages/common/default-value/property/prop-background-default-value.ts ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROP_BACKGROUND_LIST_DEFAULT_VALUE: () => (/* binding */ PROP_BACKGROUND_LIST_DEFAULT_VALUE)\n/* harmony export */ });\nconst PROP_BACKGROUND_LIST_DEFAULT_VALUE = (ts, prop)=>({\n        list: [],\n        ts: ts,\n        ...prop || undefined\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29tbW9uL2RlZmF1bHQtdmFsdWUvcHJvcGVydHkvcHJvcC1iYWNrZ3JvdW5kLWRlZmF1bHQtdmFsdWUudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUVPLE1BQU1BLHFDQUFxQyxDQUNoREMsSUFDQUMsT0FDMkI7UUFDM0JDLE1BQU0sRUFBRTtRQUNSRixJQUFJQTtRQUNKLEdBQUlDLFFBQVFFLFNBQVM7SUFDdkIsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL0BuZXh0Z2VuLWJpbmR1cC9saXZlLWVkaXRvci8uLi8uLi9wYWNrYWdlcy9jb21tb24vZGVmYXVsdC12YWx1ZS9wcm9wZXJ0eS9wcm9wLWJhY2tncm91bmQtZGVmYXVsdC12YWx1ZS50cz8yZThhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJhY2tncm91bmRMaXN0UHJvcER0byB9IGZyb20gJy4uLy4uL2R0by9zZXR0aW5nLXByb3BlcnRpZXMvYmFja2dyb3VuZC1wcm9wLmR0byc7XHJcblxyXG5leHBvcnQgY29uc3QgUFJPUF9CQUNLR1JPVU5EX0xJU1RfREVGQVVMVF9WQUxVRSA9IChcclxuICB0czogbnVtYmVyLFxyXG4gIHByb3A/OiBQYXJ0aWFsPEJhY2tncm91bmRMaXN0UHJvcER0bz4sXHJcbik6IEJhY2tncm91bmRMaXN0UHJvcER0byA9PiAoe1xyXG4gIGxpc3Q6IFtdLFxyXG4gIHRzOiB0cyxcclxuICAuLi4ocHJvcCB8fCB1bmRlZmluZWQpLFxyXG59KTtcclxuIl0sIm5hbWVzIjpbIlBST1BfQkFDS0dST1VORF9MSVNUX0RFRkFVTFRfVkFMVUUiLCJ0cyIsInByb3AiLCJsaXN0IiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/common/default-value/property/prop-background-default-value.ts\n");

/***/ }),

/***/ "../../packages/common/default-value/property/prop-border-default-value.ts":
/*!*********************************************************************************!*\
  !*** ../../packages/common/default-value/property/prop-border-default-value.ts ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROP_BORDER_DEFAULT_VALUE: () => (/* binding */ PROP_BORDER_DEFAULT_VALUE)\n/* harmony export */ });\nconst PROP_BORDER_DEFAULT_VALUE = (ts, prop)=>({\n        top: {\n            color: '#000',\n            width: {\n                value: '0',\n                unit: 'px'\n            },\n            borderStyle: 'none'\n        },\n        right: {\n            color: '#000',\n            width: {\n                value: '0',\n                unit: 'px'\n            },\n            borderStyle: 'none'\n        },\n        bottom: {\n            color: '#000',\n            width: {\n                value: '0',\n                unit: 'px'\n            },\n            borderStyle: 'none'\n        },\n        left: {\n            color: '#000',\n            width: {\n                value: '0',\n                unit: 'px'\n            },\n            borderStyle: 'none'\n        },\n        radiusTopLeft: {\n            width: {\n                unit: 'px',\n                value: '0'\n            },\n            height: {\n                unit: 'px',\n                value: '0'\n            },\n            isDetail: false\n        },\n        radiusTopRight: {\n            width: {\n                unit: 'px',\n                value: '0'\n            },\n            height: {\n                unit: 'px',\n                value: '0'\n            },\n            isDetail: false\n        },\n        radiusBottomLeft: {\n            width: {\n                unit: 'px',\n                value: '0'\n            },\n            height: {\n                unit: 'px',\n                value: '0'\n            },\n            isDetail: false\n        },\n        radiusBottomRight: {\n            width: {\n                unit: 'px',\n                value: '0'\n            },\n            height: {\n                unit: 'px',\n                value: '0'\n            },\n            isDetail: false\n        },\n        isDetail: false,\n        ts: ts,\n        ...prop || undefined\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/common/default-value/property/prop-border-default-value.ts\n");

/***/ }),

/***/ "../../packages/common/default-value/property/prop-effect-default-value.ts":
/*!*********************************************************************************!*\
  !*** ../../packages/common/default-value/property/prop-effect-default-value.ts ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROP_EFFECT_LIST_DEFAULT_VALUE: () => (/* binding */ PROP_EFFECT_LIST_DEFAULT_VALUE)\n/* harmony export */ });\nconst PROP_EFFECT_LIST_DEFAULT_VALUE = (ts, prop)=>({\n        list: [],\n        ts: ts,\n        ...prop || undefined\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29tbW9uL2RlZmF1bHQtdmFsdWUvcHJvcGVydHkvcHJvcC1lZmZlY3QtZGVmYXVsdC12YWx1ZS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBRU8sTUFBTUEsaUNBQWlDLENBQzVDQyxJQUNBQyxPQUN1QjtRQUN2QkMsTUFBTSxFQUFFO1FBQ1JGLElBQUlBO1FBQ0osR0FBSUMsUUFBUUUsU0FBUztJQUN2QixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG5leHRnZW4tYmluZHVwL2xpdmUtZWRpdG9yLy4uLy4uL3BhY2thZ2VzL2NvbW1vbi9kZWZhdWx0LXZhbHVlL3Byb3BlcnR5L3Byb3AtZWZmZWN0LWRlZmF1bHQtdmFsdWUudHM/Y2IwZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFZmZlY3RMaXN0UHJvcER0byB9IGZyb20gJy4uLy4uL2R0by9zZXR0aW5nLXByb3BlcnRpZXMvZWZmZWN0LXByb3AuZHRvJztcclxuXHJcbmV4cG9ydCBjb25zdCBQUk9QX0VGRkVDVF9MSVNUX0RFRkFVTFRfVkFMVUUgPSAoXHJcbiAgdHM6IG51bWJlcixcclxuICBwcm9wPzogUGFydGlhbDxFZmZlY3RMaXN0UHJvcER0bz4sXHJcbik6IEVmZmVjdExpc3RQcm9wRHRvID0+ICh7XHJcbiAgbGlzdDogW10sXHJcbiAgdHM6IHRzLFxyXG4gIC4uLihwcm9wIHx8IHVuZGVmaW5lZCksXHJcbn0pO1xyXG4iXSwibmFtZXMiOlsiUFJPUF9FRkZFQ1RfTElTVF9ERUZBVUxUX1ZBTFVFIiwidHMiLCJwcm9wIiwibGlzdCIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/common/default-value/property/prop-effect-default-value.ts\n");

/***/ }),

/***/ "../../packages/common/default-value/property/prop-filter-default-value.ts":
/*!*********************************************************************************!*\
  !*** ../../packages/common/default-value/property/prop-filter-default-value.ts ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROP_FILTER_DEFAULT_VALUE: () => (/* binding */ PROP_FILTER_DEFAULT_VALUE)\n/* harmony export */ });\nconst PROP_FILTER_DEFAULT_VALUE = (ts, prop)=>({\n        applyTo: 'global',\n        ts: ts,\n        ...prop || undefined\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29tbW9uL2RlZmF1bHQtdmFsdWUvcHJvcGVydHkvcHJvcC1maWx0ZXItZGVmYXVsdC12YWx1ZS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBRU8sTUFBTUEsNEJBQTRCLENBQ3ZDQyxJQUNBQyxPQUNtQjtRQUNuQkMsU0FBUztRQUNURixJQUFJQTtRQUNKLEdBQUlDLFFBQVFFLFNBQVM7SUFDdkIsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL0BuZXh0Z2VuLWJpbmR1cC9saXZlLWVkaXRvci8uLi8uLi9wYWNrYWdlcy9jb21tb24vZGVmYXVsdC12YWx1ZS9wcm9wZXJ0eS9wcm9wLWZpbHRlci1kZWZhdWx0LXZhbHVlLnRzPzM0MjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRmlsdGVyUHJvcER0byB9IGZyb20gJy4uLy4uL2R0by9zZXR0aW5nLXByb3BlcnRpZXMvZmlsdGVyLXByb3AuZHRvJztcclxuXHJcbmV4cG9ydCBjb25zdCBQUk9QX0ZJTFRFUl9ERUZBVUxUX1ZBTFVFID0gKFxyXG4gIHRzOiBudW1iZXIsXHJcbiAgcHJvcD86IFBhcnRpYWw8RmlsdGVyUHJvcER0bz4sXHJcbik6IEZpbHRlclByb3BEdG8gPT4gKHtcclxuICBhcHBseVRvOiAnZ2xvYmFsJyxcclxuICB0czogdHMsXHJcbiAgLi4uKHByb3AgfHwgdW5kZWZpbmVkKSxcclxufSk7XHJcbiJdLCJuYW1lcyI6WyJQUk9QX0ZJTFRFUl9ERUZBVUxUX1ZBTFVFIiwidHMiLCJwcm9wIiwiYXBwbHlUbyIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/common/default-value/property/prop-filter-default-value.ts\n");

/***/ }),

/***/ "../../packages/common/default-value/property/prop-position-default-value.ts":
/*!***********************************************************************************!*\
  !*** ../../packages/common/default-value/property/prop-position-default-value.ts ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROP_POSITION_DEFAULT_VALUE: () => (/* binding */ PROP_POSITION_DEFAULT_VALUE)\n/* harmony export */ });\nconst PROP_POSITION_DEFAULT_VALUE = (ts, prop)=>({\n        position: 'relative',\n        ts: ts,\n        ...prop || undefined\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29tbW9uL2RlZmF1bHQtdmFsdWUvcHJvcGVydHkvcHJvcC1wb3NpdGlvbi1kZWZhdWx0LXZhbHVlLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFFTyxNQUFNQSw4QkFBOEIsQ0FDekNDLElBQ0FDLE9BQ3FCO1FBQ3JCQyxVQUFVO1FBQ1ZGLElBQUlBO1FBQ0osR0FBSUMsUUFBUUUsU0FBUztJQUN2QixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG5leHRnZW4tYmluZHVwL2xpdmUtZWRpdG9yLy4uLy4uL3BhY2thZ2VzL2NvbW1vbi9kZWZhdWx0LXZhbHVlL3Byb3BlcnR5L3Byb3AtcG9zaXRpb24tZGVmYXVsdC12YWx1ZS50cz82OTk5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBvc2l0aW9uUHJvcER0byB9IGZyb20gJy4uLy4uL2R0by9zZXR0aW5nLXByb3BlcnRpZXMvcG9zaXRpb24tcHJvcC5kdG8nO1xyXG5cclxuZXhwb3J0IGNvbnN0IFBST1BfUE9TSVRJT05fREVGQVVMVF9WQUxVRSA9IChcclxuICB0czogbnVtYmVyLFxyXG4gIHByb3A/OiBQYXJ0aWFsPFBvc2l0aW9uUHJvcER0bz4sXHJcbik6IFBvc2l0aW9uUHJvcER0byA9PiAoe1xyXG4gIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxyXG4gIHRzOiB0cyxcclxuICAuLi4ocHJvcCB8fCB1bmRlZmluZWQpLFxyXG59KTtcclxuIl0sIm5hbWVzIjpbIlBST1BfUE9TSVRJT05fREVGQVVMVF9WQUxVRSIsInRzIiwicHJvcCIsInBvc2l0aW9uIiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/common/default-value/property/prop-position-default-value.ts\n");

/***/ }),

/***/ "../../packages/common/default-value/property/prop-size-default-value.ts":
/*!*******************************************************************************!*\
  !*** ../../packages/common/default-value/property/prop-size-default-value.ts ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROP_PAGE_SIZE_DEFAULT_VALUE: () => (/* binding */ PROP_PAGE_SIZE_DEFAULT_VALUE),\n/* harmony export */   PROP_SIZE_DEFAULT_VALUE: () => (/* binding */ PROP_SIZE_DEFAULT_VALUE)\n/* harmony export */ });\nconst PROP_SIZE_DEFAULT_VALUE = (ts, prop)=>({\n        ts: ts,\n        width: {\n            value: '',\n            unit: 'auto'\n        },\n        height: {\n            value: '',\n            unit: 'auto'\n        },\n        minWidth: {\n            value: '',\n            unit: 'auto'\n        },\n        maxWidth: {\n            value: '',\n            unit: 'auto'\n        },\n        minHeight: {\n            value: '',\n            unit: 'auto'\n        },\n        maxHeight: {\n            value: '',\n            unit: 'auto'\n        },\n        overflow: 'unset',\n        ...prop || undefined\n    });\nconst PROP_PAGE_SIZE_DEFAULT_VALUE = (ts, prop)=>({\n        width: {\n            value: '100',\n            unit: '%'\n        },\n        height: {\n            value: '',\n            unit: 'auto'\n        },\n        minWidth: {\n            value: '',\n            unit: 'auto'\n        },\n        maxWidth: {\n            value: '',\n            unit: 'auto'\n        },\n        minHeight: {\n            value: '100',\n            unit: 'dvh'\n        },\n        maxHeight: {\n            value: '',\n            unit: 'auto'\n        },\n        overflow: 'unset',\n        ts: ts,\n        ...prop || undefined\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/common/default-value/property/prop-size-default-value.ts\n");

/***/ }),

/***/ "../../packages/common/default-value/property/prop-spacing-default-value.ts":
/*!**********************************************************************************!*\
  !*** ../../packages/common/default-value/property/prop-spacing-default-value.ts ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROP_SPACING_DEFAULT_VALUE: () => (/* binding */ PROP_SPACING_DEFAULT_VALUE)\n/* harmony export */ });\nconst PROP_SPACING_DEFAULT_VALUE = (ts, prop)=>({\n        margin: {\n            top: {\n                value: '0',\n                unit: 'px'\n            },\n            left: {\n                value: '0',\n                unit: 'px'\n            },\n            right: {\n                value: '0',\n                unit: 'px'\n            },\n            bottom: {\n                value: '0',\n                unit: 'px'\n            },\n            isDetail: false,\n            ts: 0\n        },\n        padding: {\n            top: {\n                value: '0',\n                unit: 'px'\n            },\n            left: {\n                value: '0',\n                unit: 'px'\n            },\n            right: {\n                value: '0',\n                unit: 'px'\n            },\n            bottom: {\n                value: '0',\n                unit: 'px'\n            },\n            isDetail: false,\n            ts: 0\n        },\n        ts: ts,\n        ...prop || undefined\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/common/default-value/property/prop-spacing-default-value.ts\n");

/***/ }),

/***/ "../../packages/common/dto/page.ts":
/*!*****************************************!*\
  !*** ../../packages/common/dto/page.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageStatus: () => (/* binding */ PageStatus),\n/* harmony export */   PageType: () => (/* binding */ PageType)\n/* harmony export */ });\nconst PageType = {\n    DIRECTORY: 'dir',\n    PAGE: 'page',\n    BLOG: 'blog',\n    BLOG_LIST: 'blog-list',\n    BLOG_DETAIL: 'blog-detail',\n    ROOT: 'root'\n};\nconst PageStatus = {\n    DRAFT: 1,\n    PUBLISHED: 2\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/common/dto/page.ts\n");

/***/ }),

/***/ "../../packages/common/dto/types/component.type.ts":
/*!*********************************************************!*\
  !*** ../../packages/common/dto/types/component.type.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComponentType: () => (/* binding */ ComponentType)\n/* harmony export */ });\nvar ComponentType;\n(function(ComponentType) {\n    ComponentType[\"Page\"] = \"page\";\n    ComponentType[\"Header\"] = \"header\";\n    ComponentType[\"Footer\"] = \"footer\";\n    ComponentType[\"Main\"] = \"main\";\n    ComponentType[\"LeftSide\"] = \"left-side\";\n    ComponentType[\"RightSide\"] = \"right-side\";\n    //---------------------\n    ComponentType[\"Block\"] = \"block\";\n    ComponentType[\"Columns\"] = \"columns\";\n    ComponentType[\"Text\"] = \"text\";\n    ComponentType[\"Heading\"] = \"heading\";\n    ComponentType[\"Link\"] = \"link\";\n    ComponentType[\"Button\"] = \"btn\";\n    ComponentType[\"Media\"] = \"media\";\n    ComponentType[\"Image\"] = \"image\";\n    ComponentType[\"Video\"] = \"video\";\n    ComponentType[\"TextInput\"] = \"txt-in\";\n    ComponentType[\"NumberInput\"] = \"num-in\";\n    ComponentType[\"PasswordInput\"] = \"pwd-in\";\n    ComponentType[\"TextAreaInput\"] = \"txt-area-in\";\n    ComponentType[\"CheckboxInput\"] = \"chk-in\";\n    ComponentType[\"Section\"] = \"section\";\n    ComponentType[\"ButtonGroup\"] = \"btn-group\";\n    ComponentType[\"Html\"] = \"html\";\n    ComponentType[\"HtmlVideo\"] = \"html-video\";\n    ComponentType[\"YoutubeVideo\"] = \"youtube-video\";\n    ComponentType[\"SelectInput\"] = \"select-in\";\n    ComponentType[\"DatetimePickerInput\"] = \"dt-picker-in\";\n    ComponentType[\"RadioGroupInput\"] = \"radio-group-in\";\n    ComponentType[\"Icon\"] = \"icon\";\n    ComponentType[\"AssetComponent\"] = \"asset-component\";\n    ComponentType[\"SlideShow\"] = \"slide-show\";\n    //---------------------\n    ComponentType[\"None\"] = \"\";\n    ComponentType[\"Component\"] = \"component\";\n    ComponentType[\"Grid\"] = \"grid\";\n    ComponentType[\"Form\"] = \"form\";\n    ComponentType[\"Table\"] = \"table\";\n    ComponentType[\"DataDetail\"] = \"data-detail\";\n    ComponentType[\"Card\"] = \"card\";\n    ComponentType[\"Chart\"] = \"chart\";\n    ComponentType[\"Calendar\"] = \"calendar\";\n    ComponentType[\"Collapse\"] = \"collapse\";\n    ComponentType[\"Accordion\"] = \"accordion\";\n    ComponentType[\"Alert\"] = \"alert\";\n    ComponentType[\"Marquee\"] = \"marquee\";\n    ComponentType[\"Slider\"] = \"slider\";\n    ComponentType[\"UploadInput\"] = \"upload-in\";\n    ComponentType[\"DialogInput\"] = \"dialog\";\n    ComponentType[\"ComponentAlias\"] = \"component-alias\";\n    ComponentType[\"ProductVariant\"] = \"product-variant\";\n    ComponentType[\"ProductImages\"] = \"product-images\";\n    ComponentType[\"ProductPrice\"] = \"product-price\";\n})(ComponentType || (ComponentType = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/common/dto/types/component.type.ts\n");

/***/ }),

/***/ "../../packages/common/dto/types/style-type.enum.ts":
/*!**********************************************************!*\
  !*** ../../packages/common/dto/types/style-type.enum.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyleType: () => (/* binding */ StyleType)\n/* harmony export */ });\nvar StyleType;\n(function(StyleType) {\n    StyleType[StyleType[\"COLOR\"] = 1] = \"COLOR\";\n    StyleType[StyleType[\"TYPOGRAPHY\"] = 2] = \"TYPOGRAPHY\";\n    StyleType[StyleType[\"APPEARANCE\"] = 3] = \"APPEARANCE\";\n    StyleType[StyleType[\"SIZE\"] = 4] = \"SIZE\";\n})(StyleType || (StyleType = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29tbW9uL2R0by90eXBlcy9zdHlsZS10eXBlLmVudW0udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7VUFBa0JBOzs7OztHQUFBQSxjQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BuZXh0Z2VuLWJpbmR1cC9saXZlLWVkaXRvci8uLi8uLi9wYWNrYWdlcy9jb21tb24vZHRvL3R5cGVzL3N0eWxlLXR5cGUuZW51bS50cz9kMDc1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBlbnVtIFN0eWxlVHlwZSB7XHJcbiAgQ09MT1IgPSAxLFxyXG4gIFRZUE9HUkFQSFkgPSAyLFxyXG4gIEFQUEVBUkFOQ0UgPSAzLFxyXG4gIFNJWkUgPSA0LFxyXG59XHJcbiJdLCJuYW1lcyI6WyJTdHlsZVR5cGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/common/dto/types/style-type.enum.ts\n");

/***/ }),

/***/ "../../packages/common/utility.ts":
/*!****************************************!*\
  !*** ../../packages/common/utility.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvertToSlug: () => (/* binding */ ConvertToSlug),\n/* harmony export */   HexToRgbA: () => (/* binding */ HexToRgbA),\n/* harmony export */   NEW_TS: () => (/* binding */ NEW_TS),\n/* harmony export */   RgbToHex: () => (/* binding */ RgbToHex),\n/* harmony export */   getErrMsg: () => (/* binding */ getErrMsg),\n/* harmony export */   getTypedKeys: () => (/* binding */ getTypedKeys),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   isNull: () => (/* binding */ isNull),\n/* harmony export */   sanitizedUrl: () => (/* binding */ sanitizedUrl)\n/* harmony export */ });\nconst isNull = (value)=>{\n    return value === null || value === undefined;\n};\nconst isEmpty = (value)=>{\n    return value === null || value === undefined || value === '';\n};\n/**\r\n * Object.keys() that returns an array of union type\r\n */ const getTypedKeys = (obj)=>Object.keys(obj);\nconst HexToRgbA = (hex, alpha)=>{\n    if (alpha === undefined || alpha === null) alpha = 1;\n    let c;\n    if (/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {\n        c = hex.substring(1).split('');\n        if (c.length == 3) {\n            c = [\n                c[0],\n                c[0],\n                c[1],\n                c[1],\n                c[2],\n                c[2]\n            ];\n        }\n        c = '0x' + c.join('');\n        return `rgba(${c >> 16 & 255},${c >> 8 & 255},${c & 255},${alpha})`;\n    }\n    throw new Error(`Bad Hex: ${hex}`);\n};\nconst RgbToHex = (rgb)=>{\n    const match = rgb.match(/\\d+/g);\n    if (!match) return '#000000';\n    return '#' + match.map(ComponentToHex).join('');\n};\nconst ComponentToHex = (val)=>{\n    const a = Number(val).toString(16);\n    return a.length === 1 ? '0' + a : a;\n};\nconst NEW_TS = ()=>new Date().getTime();\nconst ConvertToSlug = (text)=>{\n    return text.toLowerCase().replace(/ /g, '-').replace(/[^\\w-]+/g, '');\n};\nconst sanitizedUrl = (url)=>{\n    return url.toLocaleLowerCase().trim().replace(/ +/g, '_').replace(/_+/g, '-');\n};\nconst getErrMsg = (e)=>{\n    const err = e;\n    if ('data' in err) return e.data;\n    return err.message;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/common/utility.ts\n");

/***/ }),

/***/ "./src/assets/global.css":
/*!*******************************!*\
  !*** ./src/assets/global.css ***!
  \*******************************/
/***/ (() => {



/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "gsap":
/*!***********************!*\
  !*** external "gsap" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("gsap");

/***/ }),

/***/ "gsap/dist/ScrollTrigger":
/*!******************************************!*\
  !*** external "gsap/dist/ScrollTrigger" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("gsap/dist/ScrollTrigger");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "@emotion/cache":
/*!*********************************!*\
  !*** external "@emotion/cache" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("@emotion/cache");;

/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("@emotion/react");;

/***/ }),

/***/ "@emotion/react/jsx-dev-runtime":
/*!*************************************************!*\
  !*** external "@emotion/react/jsx-dev-runtime" ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@emotion/react/jsx-dev-runtime");;

/***/ }),

/***/ "@emotion/server/create-instance":
/*!**************************************************!*\
  !*** external "@emotion/server/create-instance" ***!
  \**************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@emotion/server/create-instance");;

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();