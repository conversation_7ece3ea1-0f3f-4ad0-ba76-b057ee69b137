import { Component } from '@nextgen-bindup/common/dto/component';
import { Site4_BlockData } from '../dto/site4_blockdata.dto';
import { Site5_Resource } from '../dto/site5_resource.dto';
import { Block_Area, Site3_Block } from '../dto/site3_block.dto';
import { Site2_Page } from '../dto/site2_page.dto';
export declare class TemplateUtil {
    page: Site2_Page;
    blocks: Site3_Block[];
    components: Record<string, Component>;
    blockDatas: Site4_BlockData[];
    resources: Site5_Resource[];
    ID_INDEX: number;
    constructor(inp: {
        page: Site2_Page;
        blocks: Site3_Block[];
        blockDatas: Site4_BlockData[];
        resources: Site5_Resource[];
    });
    migrationLayout(): void;
    createBillboard(): void;
    createSideA(): void;
    migrateBlock(inp: {
        areaId: Block_Area;
        seq: number;
    }, log?: boolean): Record<string, Component>;
    private createContentBlock;
    private createDataBlocks;
    private creatIndentBubble;
    private createIndentBubble_Li;
    private createMenuB;
    private createMenuB_Li;
    private createNaviNumbersComponent;
    private getLayoutWidth;
}
