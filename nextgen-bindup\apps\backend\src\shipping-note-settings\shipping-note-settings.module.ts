import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ShippingNoteSettingEntity } from './entities/shipping-note--settings.entity';
import { ShippingNoteSettingController } from './shipping-note-settings.controller';
import { ShippingNoteSettingService } from './shipping-note-settings.service';

@Module({
  imports: [TypeOrmModule.forFeature([ShippingNoteSettingEntity])],
  controllers: [ShippingNoteSettingController],
  providers: [ShippingNoteSettingService],
  exports: [ShippingNoteSettingService],
})
export class ShippingNoteSettingModule {}
