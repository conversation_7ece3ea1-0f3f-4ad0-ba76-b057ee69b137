import { Module } from '@nestjs/common';
import { SubDomainController } from './sub-domain.controller';
import { SubDomainService } from './sub-domain.service';
import { SubDomainEntity } from './entities/sub-domain.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([SubDomainEntity])],
  controllers: [SubDomainController],
  providers: [SubDomainService],
  exports: [SubDomainService],
})
export class SubDomainModule {}
