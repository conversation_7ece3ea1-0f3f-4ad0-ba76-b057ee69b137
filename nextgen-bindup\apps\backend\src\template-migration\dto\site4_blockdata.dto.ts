import { Boolean1 } from './boolean.enum';

export interface Site4_BlockData {
  tmpSiteId: number;
  blockdataId: number;
  siteId: number;
  anchorName: string;

  blockdataInfo: string;
  blockdataInfoJson: BlockData_Info;

  content: string; // html - encoded

  smartdata: string;
  smartdataJson: BlockData_SmartData;

  smartthumb: string;

  bgSets: string;
  bgSetsJson: BlockData_BGSets;

  cssSet: string;
  version: number;
  delFlg: number;
  insDate: string;
  insUserId: number;
  updDate: string;
  updUserId: number;
}

export enum BlockData_Layout {
  PLAIN = 'plain',
  ASYMM = 'asymm',
  TABLE = 'index',
  ALBUM = 'album',
  TAB = 'tab',
  ACCORDION = 'accordion',
}

export enum BlockData_LayoutOpt {
  STEP_1 = 'col-1',
  STEP_2 = 'col-2',
  STEP_3 = 'col-3',
  STEP_4 = 'col-4',
  STEP_5 = 'col-5',
  RIGHT_WIDEL = 'wider-2',
  LEFT_WIDEL = 'widel-2',
}

export enum BlockData_Info_BwType {
  PIXEL = '0',
  PERCENT = '1',
}

export enum BlockData_Info_BwUseRefPage {
  FALSE = 0,
  TRUE = 1,
}

export enum BlockData_Info_FrameType {
  NONE = '0',
  BORDER_ONLY = '1',
  BORDER_RADIUS_ONLY = '2',
  BORDER_GRADIENT = '3',
}

export interface BlockData_Info {
  blockdata_layoutID: BlockData_Layout;
  blockdata_layoutOptID: BlockData_LayoutOpt;
  blockdata_frameType: BlockData_Info_FrameType;
  blockdata_lineHeight: string;
  blockdata_margin: string;
  blockdata_skinNo: string;
  blockdata_isOffOnMobile: Boolean1;
  blockdata_marginInf: string;
  blockdata_lineHeightNum: string;
  bwUseRefPage: BlockData_Info_BwUseRefPage;
  bwVal: string;
  bwType: BlockData_Info_BwType;
}

export interface BlockData_SmartData {
  not_use: string;
}

export enum BgSet_Ilay {
  TILING = 0,
  UPPER_LEFT = 1,
  CENTER_LEFT = 2,
  LOWER_LEFT = 3,
  TOP_CENTER = 4,
  TRUE_CENTER = 5,
  BOTTOM_CENTER = 6,
  UPPER_RIGHT = 7,
  CENTER_RIGHT = 8,
  LOWER_RIGHT = 9,
  LEFT_JUSTIFIED = 10,
  VERTICAL_REPEAT = 11,
  RIGHT_JUSTIFIED = 12,
  TOP_ALIGNMENT = 13,
  HORIZONTAL_REPEAT = 14,
  BOTTOM_ALIGNMENT = 15,
  STRETCH = 16,
}
export interface BlockData_BGSets {
  // has color
  hasc: Boolean1;
  cval: string; // 'rgba(227, 0, 0, 0.773)'

  // has image
  hasi: Boolean1;
  imgf: string; //'01_arrow_pentagon-outlined.png';
  imgth: string;
  ilay: BgSet_Ilay;

  // has image smarphone
  hasisp: Boolean1;
  imgfsp: string;
  imgthsp: string;
  ilaysp: BgSet_Ilay;

  // class name
  className: string;
}
