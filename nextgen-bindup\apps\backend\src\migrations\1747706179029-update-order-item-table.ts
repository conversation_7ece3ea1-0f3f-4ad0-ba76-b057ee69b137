import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateOrderItemTable1747706179029 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}order_items`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    // add attributes column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'attributes',
        type: 'jsonb',
        isNullable: true,
      }),
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async down(queryRunner: QueryRunner): Promise<void> {}
}
