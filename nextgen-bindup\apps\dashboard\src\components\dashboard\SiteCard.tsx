import { type FC } from 'react';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { Link } from '@tanstack/react-router';
import { SiteEntity } from '../../dto/site.type';

type Props = {
  data: SiteEntity;
};

const SiteCard: FC<Props> = ({ data }) => {
  const url = `/sites/${data.id}`;

  return (
    <Card sx={{ width: 360, height: 370 }}>
      <Link to={url}>
        <CardContent
          sx={{
            height: 320,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: '#0000008F',
            color: '#fff',
            overflow: 'hidden',
          }}
        >
          <img src={data.thumb} />
        </CardContent>
      </Link>
      <CardActions sx={{ justifyContent: 'space-between' }}>
        <Typography>{data.managementName}</Typography>
        <IconButton>
          <MoreHorizIcon />
        </IconButton>
      </CardActions>
    </Card>
  );
};

export default SiteCard;
