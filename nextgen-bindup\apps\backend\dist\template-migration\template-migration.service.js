"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateMigrationService = void 0;
const common_1 = require("@nestjs/common");
const page_service_1 = require("../page/page.service");
const read_file_util_1 = require("./util/read-file.util");
const site3_block_dto_1 = require("./dto/site3_block.dto");
const page_entity_1 = require("../page/entities/page.entity");
const template_util_1 = require("./util/template.util");
const path = require("path");
const fs = require("fs/promises");
const template_entity_1 = require("./entities/template.entity");
const template_site_entity_1 = require("./entities/template-site.entity");
const template_page_entity_1 = require("./entities/template-page.entity");
const site_type_1 = require("../site/types/site.type");
const common_util_1 = require("../utils/common.util");
const page_type_1 = require("../page/types/page.type");
const component_type_1 = require("../page/types/component.type");
const prop_action_default_value_1 = require("../page/utils/prop-action-default-value");
const prop_background_default_value_1 = require("../page/utils/prop-background-default-value");
const prop_border_default_value_1 = require("../page/utils/prop-border-default-value");
const prop_effect_default_value_1 = require("../page/utils/prop-effect-default-value");
const prop_filter_default_value_1 = require("../page/utils/prop-filter-default-value");
const prop_position_default_value_1 = require("../page/utils/prop-position-default-value");
const prop_size_default_value_1 = require("../page/utils/prop-size-default-value");
const prop_spacing_default_value_1 = require("../page/utils/prop-spacing-default-value");
const typeorm_1 = require("typeorm");
const site_entity_1 = require("../site/entities/site.entity");
const typeorm_2 = require("@nestjs/typeorm");
class ProcessedPageData extends template_page_entity_1.TemplatePageEntity {
}
let TemplateMigrationService = class TemplateMigrationService {
    constructor(dataSource, pageService) {
        this.dataSource = dataSource;
        this.pageService = pageService;
    }
    async importTemplate() {
        const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
        const pages = read_file_util_1.ReadFileUtil.readPages(dbdata);
        const blocks = read_file_util_1.ReadFileUtil.readBlocks(dbdata);
        const blockDatas = read_file_util_1.ReadFileUtil.readBlockDatas(dbdata);
        const srcList1 = read_file_util_1.ReadFileUtil.readSrcList1(dbdata);
        const resources = read_file_util_1.ReadFileUtil.readResources(dbdata, srcList1);
        const pageId = parseInt(process.env.TEST_TEMPLATE_PAGE_ID);
        const pageEntity = await this.pageService.getById(pageId);
        const page = pages.find(page => page.seq === 1);
        const templateUtil = new template_util_1.TemplateUtil({
            page: page,
            blocks: blocks,
            blockDatas: blockDatas,
            resources: resources,
        });
        templateUtil.createBillboard();
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.HEADER,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.HEADER,
            seq: 2,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.BILLBOARD,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 2,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 3,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 4,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 5,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 6,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.FOOTER,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.FOOTER,
            seq: 2,
        });
        await this.pageService.updateComponent(pageEntity.id, pageEntity.components);
        console.log('\x1b[32mTOP\x1b[33m - %s\x1b[0m', new Date());
    }
    async importPorfolioTemplate() {
        const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
        const pages = read_file_util_1.ReadFileUtil.readPages(dbdata);
        const blocks = read_file_util_1.ReadFileUtil.readBlocks(dbdata);
        const blockDatas = read_file_util_1.ReadFileUtil.readBlockDatas(dbdata);
        const srcList1 = read_file_util_1.ReadFileUtil.readSrcList1(dbdata);
        const resources = read_file_util_1.ReadFileUtil.readResources(dbdata, srcList1);
        const pageId = 400;
        const pageEntity = await this.pageService.getById(pageId);
        const page = pages.find(page => page.seq === 2);
        const templateUtil = new template_util_1.TemplateUtil({
            page: page,
            blocks: blocks,
            blockDatas: blockDatas,
            resources: resources,
        });
        templateUtil.createBillboard();
        templateUtil.createSideA();
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.HEADER,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.HEADER,
            seq: 2,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.BILLBOARD,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.SIDE_A,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 2,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 3,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 4,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.FOOTER,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.FOOTER,
            seq: 2,
        });
        await this.pageService.updateComponent(pageEntity.id, pageEntity.components);
        console.log('\x1b[32mPorfolio\x1b[33m - %s\x1b[0m', new Date());
    }
    async importContactTemplate() {
        const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
        const pages = read_file_util_1.ReadFileUtil.readPages(dbdata);
        const blocks = read_file_util_1.ReadFileUtil.readBlocks(dbdata);
        const blockDatas = read_file_util_1.ReadFileUtil.readBlockDatas(dbdata);
        const srcList1 = read_file_util_1.ReadFileUtil.readSrcList1(dbdata);
        const resources = read_file_util_1.ReadFileUtil.readResources(dbdata, srcList1);
        const pageId = 416;
        const pageEntity = await this.pageService.getById(pageId);
        const page = pages.find(page => page.seq === 5);
        const templateUtil = new template_util_1.TemplateUtil({
            page: page,
            blocks: blocks,
            blockDatas: blockDatas,
            resources: resources,
        });
        templateUtil.createBillboard();
        templateUtil.createSideA();
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.HEADER,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.HEADER,
            seq: 2,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.BILLBOARD,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.SIDE_A,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 2,
        }, true);
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.FOOTER,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.FOOTER,
            seq: 2,
        });
        await this.pageService.updateComponent(pageEntity.id, pageEntity.components);
        console.log('\x1b[32mContact\x1b[33m - %s\x1b[0m', new Date());
    }
    async importPhotoTemplate() {
        const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
        const pages = read_file_util_1.ReadFileUtil.readPages(dbdata);
        const blocks = read_file_util_1.ReadFileUtil.readBlocks(dbdata);
        const blockDatas = read_file_util_1.ReadFileUtil.readBlockDatas(dbdata);
        const srcList1 = read_file_util_1.ReadFileUtil.readSrcList1(dbdata);
        const resources = read_file_util_1.ReadFileUtil.readResources(dbdata, srcList1);
        const pageId = 308;
        const pageEntity = await this.pageService.getById(pageId);
        const page = pages.find(page => page.seq === 3);
        const templateUtil = new template_util_1.TemplateUtil({
            page: page,
            blocks: blocks,
            blockDatas: blockDatas,
            resources: resources,
        });
        templateUtil.createSideA();
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.HEADER,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.HEADER,
            seq: 2,
        });
        templateUtil.createBillboard();
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.BILLBOARD,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.SIDE_A,
            seq: 1,
        }, true);
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 2,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 3,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.FOOTER,
            seq: 1,
        });
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.FOOTER,
            seq: 2,
        });
        await this.pageService.updateComponent(pageEntity.id, pageEntity.components);
        console.log('\x1b[32mDONE\x1b[33m - %s\x1b[0m', new Date());
    }
    async importProfileTemplate() {
        const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
        const pages = read_file_util_1.ReadFileUtil.readPages(dbdata);
        const blocks = read_file_util_1.ReadFileUtil.readBlocks(dbdata);
        const blockDatas = read_file_util_1.ReadFileUtil.readBlockDatas(dbdata);
        const srcList1 = read_file_util_1.ReadFileUtil.readSrcList1(dbdata);
        const resources = read_file_util_1.ReadFileUtil.readResources(dbdata, srcList1);
        const pageId = 418;
        const pageEntity = await this.pageService.getById(pageId);
        const page = pages.find(page => page.seq === 4);
        const templateUtil = new template_util_1.TemplateUtil({
            page: page,
            blocks: blocks,
            blockDatas: blockDatas,
            resources: resources,
        });
        templateUtil.createSideA();
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 1,
        });
        await this.pageService.updateComponent(pageEntity.id, pageEntity.components);
        console.log('\x1b[32mDONE\x1b[33m - %s\x1b[0m', new Date());
    }
    async importTempTemplate() {
        const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
        const pages = read_file_util_1.ReadFileUtil.readPages(dbdata);
        const blocks = read_file_util_1.ReadFileUtil.readBlocks(dbdata);
        const blockDatas = read_file_util_1.ReadFileUtil.readBlockDatas(dbdata);
        const srcList1 = read_file_util_1.ReadFileUtil.readSrcList1(dbdata);
        const resources = read_file_util_1.ReadFileUtil.readResources(dbdata, srcList1);
        const pageId = 418;
        const pageEntity = await this.pageService.getById(pageId);
        const page = pages.find(page => page.seq === 6);
        const templateUtil = new template_util_1.TemplateUtil({
            page: page,
            blocks: blocks,
            blockDatas: blockDatas,
            resources: resources,
        });
        templateUtil.createSideA();
        pageEntity.components = templateUtil.migrateBlock({
            areaId: site3_block_dto_1.Block_Area.MAIN,
            seq: 1,
        });
        await this.pageService.updateComponent(pageEntity.id, pageEntity.components);
        console.log('\x1b[32mDONE\x1b[33m - %s\x1b[0m', new Date());
    }
    async importTemplateToDB(userId) {
        const dbdata = `${process.env.TEST_TEMPLATE_PATH}/_dbdata`;
        const templatePath = path.resolve(dbdata, 'template_site.json');
        const siteFilePath = path.resolve(dbdata, 'site0_site.json');
        const cornerFilePath = path.resolve(dbdata, 'site1_corner.json');
        const pageFilePath = path.resolve(dbdata, 'site2_page.json');
        try {
            await fs.access(templatePath);
            await fs.access(siteFilePath);
            await fs.access(cornerFilePath);
            await fs.access(pageFilePath);
        }
        catch (error) {
            throw new Error(`File not found: ${error.message}`);
        }
        const processedData = await this.processTemplateData(userId, dbdata);
        const templateId = await this.insertProcessedData(processedData);
        return templateId;
    }
    async createSite(templateId, userId, projectId, siteId, projectFolderId) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const template = await queryRunner.manager
                .getRepository(template_entity_1.TemplateEntity)
                .findOneBy({ id: templateId });
            if (!template) {
                throw new Error(`Template with id ${templateId} not found`);
            }
            const templateSite = await queryRunner.manager
                .getRepository(template_site_entity_1.TemplateSiteEntity)
                .findOneBy({ templateId: templateId });
            if (!templateSite) {
                throw new Error(`Template site for template ${templateId} not found`);
            }
            const templatePages = await queryRunner.manager
                .getRepository(template_page_entity_1.TemplatePageEntity)
                .findBy({ templateSiteId: templateSite.id });
            if (!templatePages || templatePages.length === 0) {
                throw new Error(`No template pages found for template site ${templateSite.id}`);
            }
            const createdSite = await queryRunner.manager
                .getRepository(site_entity_1.SiteEntity)
                .findOneBy({ id: siteId });
            createdSite.projectFolderId = projectFolderId;
            createdSite.managementName = templateSite.managementName;
            createdSite.status = templateSite.status;
            createdSite.url = this.generateSlug(templateSite.managementName);
            createdSite.title = templateSite.title;
            createdSite.description = templateSite.description;
            createdSite.isSearch = templateSite.isSearch;
            createdSite.thumb = templateSite.thumb;
            createdSite.headCode = templateSite.headCode;
            createdSite.bodyCode = templateSite.bodyCode;
            createdSite.isArchived = false;
            createdSite.userId = userId;
            await queryRunner.manager.getRepository(site_entity_1.SiteEntity).save(createdSite);
            await queryRunner.manager.getRepository(page_entity_1.PageEntity).delete({
                projectId: projectId,
                siteId: siteId,
            });
            const templateRootPage = templatePages.find(p => p.type === page_type_1.PageType.ROOT);
            if (!templateRootPage) {
                throw new Error('Template root page not found');
            }
            const rootPage = {
                ...templateRootPage,
                siteId: createdSite.id,
                projectId: projectId,
            };
            const createdRootPage = await queryRunner.manager
                .getRepository(page_entity_1.PageEntity)
                .save(rootPage);
            const pageIdMapping = new Map();
            pageIdMapping.set(templateRootPage.id, createdRootPage.id);
            const templatesToCreate = templatePages.filter(p => p.type !== page_type_1.PageType.ROOT);
            const sortedTemplates = this.sortTemplatePagesByHierarchy(templatesToCreate);
            const templatePageIds = await this.createAllPagesFromTemplate(queryRunner, sortedTemplates, createdSite.id, projectId, userId, pageIdMapping);
            await this.updateRealPageChildren(queryRunner, templatePages, pageIdMapping);
            await queryRunner.commitTransaction();
            return templatePageIds;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async updateRealPageChildren(queryRunner, templatePages, pageIdMapping) {
        for (const templatePage of templatePages) {
            if (templatePage.children && templatePage.children.length > 0) {
                const realPageId = pageIdMapping.get(templatePage.id);
                if (realPageId) {
                    const realChildrenIds = [];
                    for (const templateChildId of templatePage.children) {
                        const realChildId = pageIdMapping.get(templateChildId);
                        if (realChildId) {
                            realChildrenIds.push(realChildId);
                        }
                        else {
                            console.warn(`Warning: Child page ${templateChildId} not found in mapping for parent ${templatePage.id}`);
                        }
                    }
                    if (realChildrenIds.length > 0) {
                        await queryRunner.manager
                            .getRepository(page_entity_1.PageEntity)
                            .update(realPageId, {
                            children: realChildrenIds,
                        });
                    }
                }
            }
        }
    }
    async createAllPagesFromTemplate(queryRunner, templatePages, siteId, projectId, userId, pageIdMapping) {
        const createdPages = [];
        for (const templatePage of templatePages) {
            let realParentId = null;
            if (templatePage.parentId) {
                realParentId = pageIdMapping.get(templatePage.parentId) || null;
            }
            const pageData = new page_entity_1.PageEntity();
            pageData.type = templatePage.type;
            pageData.parentId = realParentId;
            pageData.projectId = projectId;
            pageData.siteId = siteId;
            pageData.name = templatePage.name;
            pageData.components =
                templatePage.components ||
                    this.createDefaultPageComponents(templatePage.ts);
            pageData.ts = templatePage.ts;
            pageData.status = templatePage.status;
            pageData.url = templatePage.url;
            pageData.title = templatePage.title;
            pageData.description = templatePage.description;
            pageData.isSearch = templatePage.isSearch;
            pageData.thumb = templatePage.thumb;
            pageData.headCode = templatePage.headCode;
            pageData.bodyCode = templatePage.bodyCode;
            pageData.isPrivate = templatePage.isPrivate;
            pageData.isHome = templatePage.isHome;
            pageData.children = [];
            pageData.userId = userId;
            pageData.isDeleted = false;
            const createdPage = await queryRunner.manager
                .getRepository(page_entity_1.PageEntity)
                .save(pageData);
            pageIdMapping.set(templatePage.id, createdPage.id);
            createdPages.push({
                id: createdPage.id,
                templatePageId: templatePage.templatePageId,
                parentId: realParentId,
            });
        }
        return createdPages;
    }
    sortTemplatePagesByHierarchy(pages) {
        const result = [];
        const processed = new Set();
        const addPage = (page) => {
            if (processed.has(page.id))
                return;
            if (page.parentId) {
                const parent = pages.find(p => p.id === page.parentId);
                if (parent && !processed.has(parent.id)) {
                    addPage(parent);
                }
            }
            result.push(page);
            processed.add(page.id);
        };
        for (const page of pages) {
            addPage(page);
        }
        return result;
    }
    async processTemplateData(userId, dbData) {
        const oldTemplates = read_file_util_1.ReadFileUtil.readTemplateSites(dbData);
        const oldSites = read_file_util_1.ReadFileUtil.readSites(dbData);
        const oldCorners = read_file_util_1.ReadFileUtil.readCorners(dbData);
        const oldPages = read_file_util_1.ReadFileUtil.readPages(dbData);
        if (!oldTemplates || oldTemplates.length === 0) {
            throw new Error('Invalid templates data');
        }
        if (!oldSites || oldSites.length === 0) {
            throw new Error('Invalid sites data');
        }
        if (!oldCorners || oldCorners.length === 0) {
            throw new Error('Invalid corners data');
        }
        if (!oldPages || oldPages.length === 0) {
            throw new Error('Invalid pages data');
        }
        const oldTemplate = oldTemplates[0];
        const template = {
            tmpSiteId: oldTemplate.id,
            name: oldTemplate.title,
            titleDetail: oldTemplate.titleDetail,
            seq: oldTemplate.seq,
            description: oldTemplate.description,
            category: oldTemplate.category,
            smartblock: oldTemplate.smartblock === 'true',
            pages: oldTemplate.pagelistJson,
            id: undefined,
            createdAt: undefined,
            updatedAt: undefined,
        };
        const site = oldSites[0];
        const templateSite = {
            managementName: site.name || `Imported Site ${Date.now()}`,
            status: site.dispFlg === 1 ? site_type_1.SiteStatus.PUBLISHED : site_type_1.SiteStatus.DRAFT,
            url: this.generateSlug(site.name || ''),
            title: site.name || 'Imported Site',
            description: `Imported from template ${site.siteVersion || 'v1'}`,
            isSearch: true,
            thumb: '',
            headCode: '',
            bodyCode: '',
            userId: userId,
            templateId: 0,
            isArchived: false,
            id: undefined,
            createdAt: undefined,
            updatedAt: undefined,
        };
        const rootCorner = oldCorners.find(corner => !corner.parentCornerId);
        if (!rootCorner) {
            throw new Error('No root corner found (corner without parent)');
        }
        let ts = (0, common_util_1.NEW_TS)();
        const rootTemplatePage = {
            type: page_type_1.PageType.ROOT,
            parentId: null,
            templateId: 0,
            templateSiteId: 0,
            templatePageId: 0,
            name: rootCorner.name || 'ROOT',
            components: {},
            ts: ts++,
            status: page_type_1.PageStatus.PUBLISHED,
            url: this.generateSlug(rootCorner.name || 'root'),
            title: rootCorner.name || 'ROOT',
            description: '',
            isSearch: true,
            thumb: '',
            headCode: '',
            bodyCode: '',
            isPrivate: false,
            isHome: false,
            children: [],
            userId: userId,
            isDeleted: false,
            oldId: rootCorner.cornerId,
            oldParentId: 0,
            id: undefined,
            createdAt: undefined,
            updatedAt: undefined,
        };
        const folderCorners = oldCorners.filter(corner => corner.parentCornerId);
        const folderTemplatePages = folderCorners.map(corner => ({
            type: page_type_1.PageType.DIRECTORY,
            parentId: null,
            templateId: 0,
            templateSiteId: 0,
            templatePageId: 0,
            name: corner.name || `Folder ${corner.cornerId}`,
            components: {},
            ts: ts++,
            status: page_type_1.PageStatus.PUBLISHED,
            url: this.generateSlug(corner.name || `folder-${corner.cornerId}`),
            title: corner.name || `Folder ${corner.cornerId}`,
            description: '',
            isSearch: true,
            thumb: '',
            headCode: '',
            bodyCode: '',
            isPrivate: false,
            isHome: false,
            children: [],
            userId: userId,
            isDeleted: false,
            oldId: corner.cornerId,
            oldParentId: corner.parentCornerId,
            id: undefined,
            createdAt: undefined,
            updatedAt: undefined,
        }));
        const sortedPages = oldPages.sort((a, b) => a.seq - b.seq);
        const templatePages = sortedPages.map(page => ({
            type: page_type_1.PageType.PAGE,
            parentId: null,
            templateId: 0,
            templateSiteId: 0,
            templatePageId: page.pageId,
            name: page.name,
            components: this.createDefaultPageComponents(ts++),
            ts: ts++,
            status: page.publicFlg === 1 ? page_type_1.PageStatus.PUBLISHED : page_type_1.PageStatus.DRAFT,
            url: this.generateSlug(page.name),
            title: page.title || page.name,
            description: '',
            isSearch: page.publicFlg === 1,
            thumb: '',
            headCode: page.headSetsJson.scpt || '',
            bodyCode: '',
            isPrivate: page.publicFlg !== 1,
            isHome: false,
            children: [],
            userId: userId,
            isDeleted: false,
            oldId: page.pageId,
            oldParentId: page.parentCornerId,
            id: undefined,
            createdAt: undefined,
            updatedAt: undefined,
        }));
        return {
            template,
            templateSite,
            rootTemplatePage,
            folderTemplatePages,
            templatePages,
        };
    }
    generateSlug(text) {
        return text
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
    }
    createDefaultPageComponents(ts) {
        return {
            __page__: {
                id: '__page__',
                type: component_type_1.ComponentType.Page,
                name: 'Page',
                parentId: undefined,
                properties: {
                    ts: ts,
                    actions: (0, prop_action_default_value_1.PROP_ACTION_DEFAULT_VALUE)(ts),
                    marginPadding: (0, prop_spacing_default_value_1.PROP_SPACING_DEFAULT_VALUE)(ts),
                    size: (0, prop_size_default_value_1.PROP_PAGE_SIZE_DEFAULT_VALUE)(ts),
                    border: (0, prop_border_default_value_1.PROP_BORDER_DEFAULT_VALUE)(ts),
                    position: (0, prop_position_default_value_1.PROP_POSITION_DEFAULT_VALUE)(ts),
                    backgrounds: (0, prop_background_default_value_1.PROP_BACKGROUND_LIST_DEFAULT_VALUE)(ts),
                    effects: (0, prop_effect_default_value_1.PROP_EFFECT_LIST_DEFAULT_VALUE)(ts),
                    filter: (0, prop_filter_default_value_1.PROP_FILTER_DEFAULT_VALUE)(ts),
                },
                children: ['__main__'],
                breakpoint: {
                    tablet: { ts: ts },
                    phone: { ts: ts },
                },
                ts: ts,
            },
            __main__: {
                id: '__main__',
                type: component_type_1.ComponentType.Main,
                name: 'Main',
                parentId: '__page__',
                properties: {
                    ts: ts,
                    actions: (0, prop_action_default_value_1.PROP_ACTION_DEFAULT_VALUE)(ts),
                    marginPadding: (0, prop_spacing_default_value_1.PROP_SPACING_DEFAULT_VALUE)(ts),
                    size: (0, prop_size_default_value_1.PROP_PAGE_SIZE_DEFAULT_VALUE)(ts, {
                        height: { value: '', unit: 'auto' },
                        minHeight: { value: '100', unit: '%' },
                    }),
                    border: (0, prop_border_default_value_1.PROP_BORDER_DEFAULT_VALUE)(ts),
                    position: (0, prop_position_default_value_1.PROP_POSITION_DEFAULT_VALUE)(ts),
                    backgrounds: (0, prop_background_default_value_1.PROP_BACKGROUND_LIST_DEFAULT_VALUE)(ts),
                    effects: (0, prop_effect_default_value_1.PROP_EFFECT_LIST_DEFAULT_VALUE)(ts),
                    filter: (0, prop_filter_default_value_1.PROP_FILTER_DEFAULT_VALUE)(ts),
                },
                children: [],
                breakpoint: {
                    tablet: { ts: ts },
                    phone: { ts: ts },
                },
                ts: ts,
            },
        };
    }
    async insertProcessedData(processedData) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const oldTemplate = await queryRunner.manager
                .getRepository(template_entity_1.TemplateEntity)
                .findOneBy({ tmpSiteId: processedData.template.tmpSiteId });
            if (oldTemplate) {
                processedData.template.id = oldTemplate.id;
                await queryRunner.manager.getRepository(template_page_entity_1.TemplatePageEntity).delete({
                    templateId: oldTemplate.id,
                });
                await queryRunner.manager.getRepository(template_site_entity_1.TemplateSiteEntity).delete({
                    templateId: oldTemplate.id,
                });
            }
            const template = await queryRunner.manager
                .getRepository(template_entity_1.TemplateEntity)
                .save(processedData.template);
            const templateSiteData = {
                ...processedData.templateSite,
                templateId: template.id,
            };
            const templateSite = await queryRunner.manager
                .getRepository(template_site_entity_1.TemplateSiteEntity)
                .save(templateSiteData);
            const rootTemplatePageData = {
                ...processedData.rootTemplatePage,
                templateId: template.id,
                templateSiteId: templateSite.id,
            };
            const rootPage = await queryRunner.manager
                .getRepository(template_page_entity_1.TemplatePageEntity)
                .save(rootTemplatePageData);
            const createdFolders = await this.createFolderTemplatePages(queryRunner, processedData.folderTemplatePages, rootPage.id, template.id, templateSite.id);
            const topLevelFolderIds = createdFolders
                .filter(f => f.oldParentId === processedData.rootTemplatePage.oldId)
                .map(f => f.id);
            await queryRunner.manager
                .getRepository(template_page_entity_1.TemplatePageEntity)
                .update(rootPage.id, {
                children: topLevelFolderIds,
            });
            const createdTemplatePages = await this.createTemplatePages(queryRunner, processedData.templatePages, createdFolders, templateSite.id, rootPage.id);
            await this.updateFolderTemplatePageChildren(queryRunner, createdFolders, createdTemplatePages);
            const rootPageIds = createdTemplatePages
                .filter(p => p.parentId === null)
                .map(p => p.id);
            const currentRootChildren = topLevelFolderIds;
            const updatedRootChildren = [...currentRootChildren, ...rootPageIds];
            await queryRunner.manager
                .getRepository(template_page_entity_1.TemplatePageEntity)
                .update(rootPage.id, {
                children: updatedRootChildren,
            });
            await queryRunner.commitTransaction();
            return template.id;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async createFolderTemplatePages(queryRunner, folders, rootPageId, templateId, templateSiteId) {
        const sortedFolders = this.sortFoldersByHierarchy(folders);
        const createdFolders = [];
        for (const folder of sortedFolders) {
            let parentId = rootPageId;
            if (folder.oldParentId) {
                const parentFolder = createdFolders.find(f => f.oldId === folder.oldParentId);
                if (parentFolder) {
                    parentId = parentFolder.id;
                }
            }
            const folderData = {
                ...folder,
                parentId,
                templateId,
                templateSiteId,
            };
            const createdFolder = await queryRunner.manager
                .getRepository(template_page_entity_1.TemplatePageEntity)
                .save(folderData);
            createdFolders.push({
                id: createdFolder.id,
                name: createdFolder.name,
                oldId: folder.oldId,
                oldParentId: folder.oldParentId,
            });
        }
        return createdFolders;
    }
    async createTemplatePages(queryRunner, templatePages, createdFTPages, templateSiteId, rootPageId) {
        const createdTemplatePages = [];
        for (const page of templatePages) {
            const parentFolder = createdFTPages.find(f => f.oldId === page.oldParentId);
            let parentId;
            if (parentFolder) {
                parentId = parentFolder.id;
            }
            else {
                parentId = rootPageId;
            }
            const pageData = {
                ...page,
                parentId,
                templateSiteId,
            };
            const createdTemplatePage = await queryRunner.manager
                .getRepository(template_page_entity_1.TemplatePageEntity)
                .save(pageData);
            createdTemplatePages.push({
                id: createdTemplatePage.id,
                parentId: parentFolder ? parentFolder.id : null,
            });
        }
        return createdTemplatePages;
    }
    async updateFolderTemplatePageChildren(queryRunner, createdFTPages, createdTemplatePages) {
        for (const folder of createdFTPages) {
            const folderPages = createdTemplatePages.filter(page => page.parentId === folder.id);
            const childrenIds = folderPages.map(page => page.id);
            await queryRunner.manager
                .getRepository(template_page_entity_1.TemplatePageEntity)
                .update(folder.id, {
                children: childrenIds,
            });
        }
    }
    sortFoldersByHierarchy(folders) {
        const result = [];
        const processed = new Set();
        const addFolder = (folder) => {
            if (processed.has(folder.oldId))
                return;
            if (folder.oldParentId) {
                const parent = folders.find(f => f.oldId === folder.oldParentId);
                if (parent && !processed.has(parent.oldId)) {
                    addFolder(parent);
                }
            }
            result.push(folder);
            processed.add(folder.oldId);
        };
        for (const folder of folders) {
            addFolder(folder);
        }
        return result;
    }
};
exports.TemplateMigrationService = TemplateMigrationService;
exports.TemplateMigrationService = TemplateMigrationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_1.DataSource,
        page_service_1.PageService])
], TemplateMigrationService);
//# sourceMappingURL=template-migration.service.js.map