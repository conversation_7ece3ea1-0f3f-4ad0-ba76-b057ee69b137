import * as fs from 'fs';
import * as path from 'path';

export const ensureFolderExists = (folderPath: string) => {
  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath, { recursive: true });
  }
};

export const ensureDirectories = (basePath: string, subPath: string) => {
  const fullPath = path.join(basePath, subPath);
  fs.mkdirSync(fullPath, { recursive: true });
};

export const copyFile = (source: string, destination: string) => {
  const destDir = path.dirname(destination);
  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true });
  }
  try {
    fs.copyFileSync(source, destination);
  } catch (e) {
    console.error(`Error copying file: ${e}`);
  }
};

export const deleteFolderRecursive = folderPath => {
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach(file => {
      const curPath = path.join(folderPath, file);
      if (fs.lstatSync(curPath).isDirectory()) {
        deleteFolderRecursive(curPath);
      } else {
        fs.unlinkSync(curPath);
      }
    });
    fs.rmdirSync(folderPath);
  }
};
