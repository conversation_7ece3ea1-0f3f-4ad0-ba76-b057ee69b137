import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateSiteTable1739757489360 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}sites`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const column: TableColumn = new TableColumn({
      name: 'isArchived',
      type: 'boolean',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, column);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'isArchived');
  }
}
