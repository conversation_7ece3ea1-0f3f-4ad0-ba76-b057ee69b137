import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateSiteVersionTable1740974200208 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}site_versions`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const userIdColumn: TableColumn = new TableColumn({
      name: 'userId',
      type: 'varchar',
      length: '36',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, userIdColumn);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'userId');
  }
}
