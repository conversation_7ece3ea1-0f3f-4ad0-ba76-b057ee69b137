"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZipService = void 0;
const common_1 = require("@nestjs/common");
const AdmZip = require("adm-zip");
const fs = require("fs/promises");
const path = require("path");
let ZipService = class ZipService {
    async zipFolder(folderPath) {
        const zip = new AdmZip();
        const addFilesToZip = async (folder) => {
            const files = await fs.readdir(folder);
            await Promise.all(files.map(async (file) => {
                const filePath = path.join(folder, file);
                const stats = await fs.stat(filePath);
                const relativePath = path.relative(folderPath, filePath);
                if (stats.isDirectory()) {
                    await addFilesToZip(filePath);
                }
                else {
                    const fileContent = await fs.readFile(filePath);
                    zip.addFile(relativePath, fileContent);
                }
            }));
        };
        await addFilesToZip(folderPath);
        return zip.toBuffer();
    }
};
exports.ZipService = ZipService;
exports.ZipService = ZipService = __decorate([
    (0, common_1.Injectable)()
], ZipService);
//# sourceMappingURL=zip.service.js.map