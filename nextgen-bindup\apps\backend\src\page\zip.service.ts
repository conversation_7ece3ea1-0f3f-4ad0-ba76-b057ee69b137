import { Injectable } from '@nestjs/common';
import * as AdmZip from 'adm-zip';
import * as fs from 'fs/promises';
import * as path from 'path';

@Injectable()
export class ZipService {
  async zipFolder(folderPath: string): Promise<Buffer> {
    const zip = new AdmZip();

    const addFilesToZip = async (folder: string) => {
      const files = await fs.readdir(folder);

      await Promise.all(
        files.map(async file => {
          const filePath = path.join(folder, file);
          const stats = await fs.stat(filePath);
          const relativePath = path.relative(folderPath, filePath);

          if (stats.isDirectory()) {
            await addFilesToZip(filePath);
          } else {
            const fileContent = await fs.readFile(filePath);
            zip.addFile(relativePath, fileContent);
          }
        }),
      );
    };

    await addFilesToZip(folderPath);
    return zip.toBuffer();
  }
}
