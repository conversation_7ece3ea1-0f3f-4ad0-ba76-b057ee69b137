"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateProductTable1748830899424 = void 0;
const typeorm_1 = require("typeorm");
class UpdateProductTable1748830899424 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}products`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'priceLabel',
            type: 'varchar',
            length: '250',
            isNullable: true,
        }));
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'saleLabel',
            type: 'varchar',
            length: '250',
            isNullable: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'priceLabel');
        await queryRunner.dropColumn(this.TABLE_NAME, 'saleLabel');
    }
}
exports.UpdateProductTable1748830899424 = UpdateProductTable1748830899424;
//# sourceMappingURL=1748830899424-update-product-table.js.map