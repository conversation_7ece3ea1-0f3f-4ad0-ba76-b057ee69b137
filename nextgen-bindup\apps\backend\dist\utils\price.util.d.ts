import { ProductEntity } from 'src/product/entities/product.entity';
import { OrderItemEntity } from 'src/order/entites/order-item.entity';
import { PaymentMethodType } from 'src/order/enum/payment-method-type.enum';
import { ShippingNoteSettingEntity } from 'src/shipping-note-settings/entities/shipping-note--settings.entity';
import { ShopInformationSettingEntity } from 'src/shop-information-settings/entities/shop-information-settings.entity';
import { TaxMode, TaxRegulation } from 'src/shop-information-settings/enums';
export interface Fee {
    subtotal: number;
    shippingFee: number;
    total: number;
    platformFee: number;
    paymentGatewayFee: number;
    shopNetPayout: number;
}
export declare const calculateFees: (orderItems: OrderItemEntity[], shippingNoteSetting: ShippingNoteSettingEntity, platformFeeRate: number, paymentGatewayFeeRate: number, paymentMethodType: PaymentMethodType, shippingPrefecture: string) => {
    subtotal: number;
    shippingFee: number;
    total: number;
    platformFee: number;
    paymentGatewayFee: number;
    shopNetPayout: number;
};
export declare const getShippingFee: (showShippingAddress: boolean, shippingPrefecture: string, orderItems: OrderItemEntity[], shippingNoteSetting: ShippingNoteSettingEntity) => number;
export declare const calculatePriceWithTax: (price: number, taxMode?: TaxMode, taxRate?: number, taxRegulation?: TaxRegulation) => number;
export declare const formatPriceWithTax: (price: number) => string;
export declare const formatDisplayPrice: (price: number, salePrice: number | null) => string;
export declare const getDisplayPrice: (product: ProductEntity, shopInformation?: ShopInformationSettingEntity) => number;
