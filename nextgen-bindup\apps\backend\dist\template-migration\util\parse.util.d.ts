import { BlockData_Content, BlockData_Group } from '../dto/blockdata-content.dto';
import { Site5_Resource } from '../dto/site5_resource.dto';
import { Site4_BlockData } from '../dto/site4_blockdata.dto';
export declare class ParseUtil {
    static parseContent(blockData: Site4_BlockData, resources: Site5_Resource[]): BlockData_Content[];
    static parseChildContent(blockdataId: number, elem: Element, resources: Site5_Resource[]): BlockData_Content;
    static groupChild(dataContents: BlockData_Content[]): BlockData_Group[];
    static parseStyleString(styleString: string): {};
}
