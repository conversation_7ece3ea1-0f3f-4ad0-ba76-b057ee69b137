import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateSubdomainsTable1741679546459 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}subdomains`;
  public async up(queryRunner: QueryRunner): Promise<void> {
    const column: TableColumn = new TableColumn({
      name: 'siteId',
      type: 'integer',
      isNullable: false,
    });
    await queryRunner.addColumn(this.TABLE_NAME, column);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
  }
}
