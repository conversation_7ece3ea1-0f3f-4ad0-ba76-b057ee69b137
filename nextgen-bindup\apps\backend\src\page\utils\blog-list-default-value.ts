import { Component } from '@nextgen-bindup/common/dto/component';
import { ComponentType } from '../types/component.type';
import { BgSolidColorPropDto } from '@nextgen-bindup/common/dto/setting-properties/background-prop.dto';
import { MediaImagePropDto } from '@nextgen-bindup/common/dto/setting-properties/media-prop.dto';

export const BLOG_LIST_DEFAULT_COMPONENT_VALUE = (
  ts: number,
  collectionId: number,
): Record<string, Component> => ({
  __main__: {
    id: '__main__',
    ts: ts,
    name: 'Main',
    type: ComponentType.Main,
    children: ['image_1745552335970', 'block_1745552368009'],
    parentId: '__page__',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '100' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      layout: {
        ts: ts,
        flex: {
          flexGrow: '0',
          flexWrap: 'wrap',
          alignItems: 'start',
          flexShrink: '0',
          hozSpacing: { unit: 'px', value: '0' },
          verSpacing: { unit: 'px', value: '0' },
          alignContent: 'start',
          flexDirection: 'row',
          justifyContent: 'start',
        },
        grid: null,
        type: 'flex',
        carousel: null,
      },
      actions: { ts: ts, list: [] },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: true,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: true,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  __page__: {
    id: '__page__',
    ts: ts,
    name: 'Page',
    type: ComponentType.Page,
    children: ['__main__'],
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'dvh', value: '100' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  link_1746674097069: {
    id: 'link_1746674097069',
    ts: ts,
    name: 'Link',
    type: ComponentType.Link,
    children: ['block_1746682100196'],
    parentId: 'block_1745552413616',
    properties: {
      ts: ts,
      link: {
        ts: ts,
        type: 'site',
        value: '',
        openType: 'new-tab',
      },
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '150' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      text: {
        ts: ts,
        text: '',
        type: 'specification',
        value: {},
        format: [],
        colorStyleId: null,
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 999 },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  link_1746674313882: {
    id: 'link_1746674313882',
    ts: ts,
    name: 'Link',
    type: ComponentType.Link,
    children: ['heading_1745552812239'],
    parentId: 'block_1745552413616',
    properties: {
      ts: ts,
      link: {
        ts: ts,
        type: 'site',
        value: '',
        openType: 'new-tab',
      },
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      text: {
        ts: ts,
        text: 'Cua-rơ Nga lần đầu thắng chặng ở giải xe đạp xuyên Việt',
        type: 'specification',
        value: {},
        format: [],
        colorStyleId: null,
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 999 },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  link_1746674625039: {
    id: 'link_1746674625039',
    ts: ts,
    name: 'Link',
    type: ComponentType.Link,
    children: ['text_1745553603996'],
    parentId: 'block_1745552413616',
    properties: {
      ts: ts,
      link: {
        ts: ts,
        type: 'site',
        value: '',
        openType: 'new-tab',
      },
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      text: {
        ts: ts,
        text: 'Ngoại binh Ivan Blokhin của An Giang dễ dàng thắng chặng 19 tại giải xe đạp toàn quốc HTV 2025, trưa 24/4.\nSau một ngày nghỉ, các tay đua bước vào chinh phục chặng 19 từ Phan Thiết đi Bà Rịa – Vũng Tàu dài 176,5 km. Các đội mạnh đang nắm giữ các danh hiệu quan trọng nên họ kèm nhau kỹ, không để các tay đua chủ lực tách tốp.',
        type: 'specification',
        value: {},
        format: [],
        colorStyleId: null,
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 999 },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  text_1745553603996: {
    id: 'text_1745553603996',
    ts: ts,
    name: 'Text',
    type: ComponentType.Text,
    children: [],
    parentId: 'link_1746674625039',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '200' },
        minHeight: { unit: 'auto', value: '' },
      },
      text: {
        ts: ts,
        tag: 'p',
        text: '<p>Text</p>',
        type: 'specification',
        value: {},
        format: [],
        colorStyleId: 2,
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 1745552845835 },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      paragraph: {
        ts: 1747292156351,
        ellipsis: true,
        alignment: 'left',
        indentation: { unit: 'px', value: '' },
        verticalAlign: 'top',
      },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  text_1747130126250: {
    id: 'text_1747130126250',
    ts: ts,
    name: 'Text',
    type: ComponentType.Text,
    children: [],
    parentId: 'block_1747130106906',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      text: {
        ts: ts,
        tag: 'p',
        text: '<p>Text</p>',
        type: 'specification',
        value: {},
        format: [],
        colorStyleId: null,
      },
      border: {
        ts: ts,
        top: {
          color: 'rgb(214, 214, 214)',
          width: { unit: 'px', value: '0' },
          borderStyle: 'solid',
        },
        left: {
          color: 'rgb(214, 214, 214)',
          width: { unit: 'px', value: '0' },
          borderStyle: 'solid',
        },
        right: {
          color: 'rgb(214, 214, 214)',
          width: { unit: 'px', value: '0' },
          borderStyle: 'solid',
        },
        bottom: {
          color: 'rgb(214, 214, 214)',
          width: { unit: 'px', value: '0' },
          borderStyle: 'solid',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '20' },
          height: { unit: 'px', value: '20' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '20' },
          height: { unit: 'px', value: '20' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '20' },
          height: { unit: 'px', value: '20' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '20' },
          height: { unit: 'px', value: '20' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 1 },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: {
        ts: ts,
        list: [
          {
            id: 'solid_1747130246769',
            ts: ts,
            from: 'design',
            type: 'solid',
            alpha: 1,
            presetId: null,
            visibility: true,
            backgroundColor: '#d5d7d7',
          } as BgSolidColorPropDto,
        ],
      },
      marginPadding: {
        ts: ts,
        margin: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '5' },
          right: { unit: 'px', value: '5' },
          bottom: { unit: 'px', value: '0' },
          isDetail: true,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  block_1745552368009: {
    id: 'block_1745552368009',
    ts: ts,
    name: 'Block',
    type: ComponentType.Block,
    children: ['block_1745552413616'],
    parentId: '__main__',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      layout: {
        ts: ts,
        flex: {
          flexGrow: '0',
          flexWrap: 'wrap',
          alignItems: '',
          flexShrink: '0',
          hozSpacing: { unit: 'px', value: '30' },
          verSpacing: { unit: 'px', value: '30' },
          alignContent: 'start',
          flexDirection: 'row',
          justifyContent: 'start',
        },
        grid: null,
        type: 'flex',
        carousel: null,
      },
      actions: { ts: ts, list: [] },
      binding: { ts: ts },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      datasource: {
        ts: ts,
        type: 'list',
        sorts: [],
        filters: { match: 'and', conditions: [] },
        collectionId: collectionId,
      },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: true,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '30' },
          left: { unit: 'px', value: '30' },
          right: { unit: 'px', value: '30' },
          bottom: { unit: 'px', value: '30' },
          isDetail: true,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  block_1745552413616: {
    id: 'block_1745552413616',
    ts: ts,
    name: 'Block',
    type: ComponentType.Block,
    children: [
      'link_1746674097069',
      'link_1746674313882',
      'block_1747130106906',
      'link_1746674625039',
    ],
    parentId: 'block_1745552368009',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'px', value: '300' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      layout: {
        ts: ts,
        flex: {
          flexGrow: '0',
          flexWrap: 'nowrap',
          alignItems: 'start',
          flexShrink: '0',
          hozSpacing: { unit: 'px', value: '15' },
          verSpacing: { unit: 'px', value: '5' },
          alignContent: 'start',
          flexDirection: 'column',
          justifyContent: 'start',
        },
        grid: null,
        type: 'flex',
        carousel: null,
      },
      actions: { ts: ts, list: [] },
      binding: { ts: ts },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: true,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: true,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  block_1746682100196: {
    id: 'block_1746682100196',
    ts: ts,
    name: 'Block',
    type: ComponentType.Block,
    children: ['image_1745552760825'],
    parentId: 'link_1746674097069',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '150' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      layout: {
        ts: ts,
        flex: {
          flexGrow: '0',
          flexWrap: 'wrap',
          alignItems: 'center',
          flexShrink: '0',
          hozSpacing: { unit: 'px', value: '0' },
          verSpacing: { unit: 'px', value: '0' },
          alignContent: 'center',
          flexDirection: 'row',
          justifyContent: 'center',
        },
        grid: null,
        type: 'flex',
        carousel: null,
      },
      actions: { ts: ts, list: [] },
      binding: { ts: ts },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  block_1747130106906: {
    id: 'block_1747130106906',
    ts: ts,
    name: 'Block',
    type: ComponentType.Block,
    children: ['text_1747130126250'],
    parentId: 'block_1745552413616',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: 'auto', value: '' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      layout: {
        ts: ts,
        flex: {
          flexGrow: '0',
          flexWrap: 'wrap',
          alignItems: '',
          flexShrink: '0',
          hozSpacing: { unit: 'px', value: '5' },
          verSpacing: { unit: 'px', value: '5' },
          alignContent: 'center',
          flexDirection: 'row',
          justifyContent: 'start',
        },
        grid: null,
        type: 'flex',
        carousel: null,
      },
      actions: { ts: ts, list: [] },
      binding: { ts: ts },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      datasource: {
        ts: ts,
        type: 'list',
        sorts: [],
        filters: {
          match: 'and',
          conditions: [
            {
              id: 1747130171842,
              value: '1744705577720',
              fieldId: 1000,
              operator: 'is-set',
            },
          ],
        },
        collectionId: 4,
      },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: 0,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  image_1745552335970: {
    id: 'image_1745552335970',
    ts: ts,
    name: 'Image',
    type: ComponentType.Image,
    children: [],
    parentId: '__main__',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '150' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'hide',
        maxHeight: { unit: 'px', value: '130' },
        minHeight: { unit: 'auto', value: '' },
      },
      media: {
        ts: ts,
        alt: '',
        url: 'https://pub-8a6bc558fcab4fe4a4ec5f03b0a59802.r2.dev/1-1746610234092-Dự án mới.jpg',
        type: 'image',
        float: 'none',
        position: {
          x: { unit: 'px', value: '' },
          y: { unit: 'px', value: '' },
          offsetX: { unit: 'px', value: '' },
          offsetY: { unit: 'px', value: '' },
        },
        freeRatio: { width: '16', height: '4' },
        objectFit: 'cover',
        aspectRatio: 'auto',
      } as MediaImagePropDto,
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 0 },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      transform: {
        ts: ts,
        move: { x: { unit: 'px', value: '0' }, y: { unit: 'px', value: '0' } },
        expand: { x: { unit: '%', value: '0' } },
        origin: '中心',
        rotate: 0,
        distort: { x: { unit: '%', value: '0' }, y: { unit: '%', value: '0' } },
      },
      backgrounds: {
        ts: ts,
        list: [
          {
            id: 'solid_1745552676129',
            ts: ts,
            from: 'design',
            type: 'solid',
            alpha: 1,
            presetId: null,
            visibility: true,
            backgroundColor: '#101010',
          } as BgSolidColorPropDto,
        ],
      },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: true,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: true,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  image_1745552760825: {
    id: 'image_1745552760825',
    ts: ts,
    name: 'Image',
    type: ComponentType.Image,
    children: [],
    parentId: 'block_1746682100196',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: '%', value: '100' },
        height: { unit: 'px', value: '150' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'hide',
        maxHeight: { unit: 'auto', value: '150' },
        minHeight: { unit: 'px', value: '150' },
      },
      media: {
        ts: ts,
        alt: '',
        url: '',
        type: 'image',
        float: 'none',
        position: {
          x: { unit: 'px', value: '' },
          y: { unit: 'px', value: '' },
          offsetX: { unit: 'px', value: '' },
          offsetY: { unit: 'px', value: '' },
        },
        freeRatio: { width: '4', height: '3' },
        objectFit: 'cover',
        aspectRatio: 'auto',
      } as MediaImagePropDto,
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 1744702445997 },
      effects: { ts: ts, list: [] },
      position: { ts: ts, position: 'relative' },
      transform: {
        ts: ts,
        move: { x: { unit: 'px', value: '0' }, y: { unit: 'px', value: '0' } },
        expand: { x: { unit: '%', value: '0' } },
        origin: '中心',
        rotate: 0,
        distort: { x: { unit: '%', value: '0' }, y: { unit: '%', value: '0' } },
      },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
  heading_1745552812239: {
    id: 'heading_1745552812239',
    ts: ts,
    name: 'Heading',
    type: ComponentType.Heading,
    children: [],
    parentId: 'link_1746674313882',
    properties: {
      ts: ts,
      size: {
        ts: ts,
        width: { unit: '%', value: '100' },
        height: { unit: 'auto', value: '' },
        maxWidth: { unit: 'auto', value: '' },
        minWidth: { unit: 'auto', value: '' },
        overflow: 'unset',
        maxHeight: { unit: 'auto', value: '' },
        minHeight: { unit: 'auto', value: '' },
      },
      text: {
        ts: ts,
        tag: 'h1',
        text: '<h1>Heading</h1>',
        type: 'specification',
        value: {
          fontSize: { unit: 'px', value: '24' },
          fontWeight: '500',
          lineHeight: '1',
        },
        format: [],
        colorStyleId: 4,
      },
      border: {
        ts: ts,
        top: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        left: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        right: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        bottom: {
          color: '#000',
          width: { unit: 'px', value: '0' },
          borderStyle: 'none',
        },
        isDetail: false,
        radiusTopLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusTopRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomLeft: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
        radiusBottomRight: {
          width: { unit: 'px', value: '0' },
          height: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
      filter: { ts: ts, applyTo: 'global' },
      actions: { ts: ts, list: [] },
      binding: { ts: ts, fieldId: 1744702444500 },
      effects: { ts: ts, list: [] },
      flexItem: { ts: ts, alignSelf: 'start', justifySelf: 'start' },
      position: { ts: ts, position: 'relative' },
      paragraph: {
        ts: ts,
        ellipsis: false,
        alignment: 'left',
        indentation: { unit: 'px', value: '' },
        verticalAlign: 'top',
      },
      backgrounds: { ts: ts, list: [] },
      marginPadding: {
        ts: ts,
        margin: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
        padding: {
          ts: ts,
          top: { unit: 'px', value: '0' },
          left: { unit: 'px', value: '0' },
          right: { unit: 'px', value: '0' },
          bottom: { unit: 'px', value: '0' },
          isDetail: false,
        },
      },
    },
    breakpoint: {
      tablet: { ts: ts },
      phone: { ts: ts },
    },
  },
});
