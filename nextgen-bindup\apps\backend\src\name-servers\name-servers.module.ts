import { Modu<PERSON> } from '@nestjs/common';
import { NameServersController } from './name-servers.controller';
import { NameServersService } from './name-servers.service';
import { NameServerEntity } from './entities/name-server.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([NameServerEntity])],
  controllers: [NameServersController],
  providers: [NameServersService],
  exports: [NameServersService],
})
export class NameServersModule {}
