import { OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import { PlanEntity } from './entities/plan.entity';
import { SubscriptionEntity } from './entities/subscription.entity';
import { PlanDto, SubscriptionResponse } from './dto/payment.dto';
import { UserPaymentService } from './user-payment.service';
export declare class PaymentService implements OnModuleInit {
    private readonly configService;
    private readonly userPaymentService;
    private readonly planRepo;
    private readonly subscriptionRepo;
    private readonly userInfoRepo;
    private stripe;
    constructor(configService: ConfigService, userPaymentService: UserPaymentService);
    onModuleInit(): Promise<void>;
    syncPlansFromStripe(): Promise<void>;
    syncPaidInvoices(): Promise<void>;
    getHistorySubscription(userId: string): Promise<SubscriptionResponse[]>;
    getUserSubscription(userId: string): Promise<SubscriptionResponse | null>;
    handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<void>;
    handlePaymentFailed(invoice: Stripe.Invoice): Promise<void>;
    createSubscription(userId: string, planId: string, stripeSubscriptionId: string): Promise<SubscriptionEntity>;
    cancelSubscription(userId: string): Promise<boolean>;
    private calculatePeriodEnd;
    getPlans(): Promise<PlanDto[]>;
    verifyWebhookSignature(rawBody: Buffer, signature: string): Stripe.Event;
    handleWebhookEvent(event: Stripe.Event): Promise<void>;
    handleProductArchived(stripeProductId: string): Promise<void>;
    handleAddOrUpdatePlan(plan: Stripe.Plan): Promise<void>;
    handleDeletePlan(planId: string): Promise<void>;
    getPlanById(planId: string): Promise<PlanEntity | null>;
    createCheckoutSession(userId: string, planId: string): Promise<{
        url: string;
    }>;
    getOrCreateCustomer(userId: string): Promise<string>;
}
