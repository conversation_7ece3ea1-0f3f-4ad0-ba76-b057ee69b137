import { ShopInformationSettingService } from './shop-information-settings.service';
import { ShopInformationSettingEntity } from './entities/shop-information-settings.entity';
export declare class ShopInformationSettingController {
    private readonly shopInformationSettingService;
    constructor(shopInformationSettingService: ShopInformationSettingService);
    create(shopInformationSettingEntity: ShopInformationSettingEntity): Promise<ShopInformationSettingEntity>;
    update(id: string, data: Partial<ShopInformationSettingEntity>): Promise<ShopInformationSettingEntity>;
    getOneBySiteId(siteId: string): Promise<ShopInformationSettingEntity>;
}
