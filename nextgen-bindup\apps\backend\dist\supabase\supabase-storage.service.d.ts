import { SupabaseClient } from '@supabase/supabase-js';
import { SignUploadDto } from './dto/sign-upload.dto';
export declare class SupabaseStorageService {
    private supabase;
    private BUCKET_NAME;
    constructor(supabase: SupabaseClient);
    createBucket(): Promise<void>;
    createSignedUploadUrl(filepath: string): Promise<SignUploadDto>;
    getPublicUrl(filepath: string): Promise<string>;
}
