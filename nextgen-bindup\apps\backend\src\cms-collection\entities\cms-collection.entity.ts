import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import {
  CmsCollectionStruct,
  CmsDataType,
  StructExtra,
  StructSlugSetting,
} from '@nextgen-bindup/common/dto/cms-collection/cms-collection-struct.dto';

@Entity('cms_collection', { schema: process.env.DATABASE_SCHEMA })
export class CmsCollectionEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  name: string;

  @Column({
    name: 'slug',
    type: 'jsonb',
    nullable: false,
  })
  slug: CmsCollectionStruct<StructSlugSetting>;

  @Column({
    name: 'struct',
    type: 'jsonb',
    nullable: true,
  })
  struct: CmsCollectionStruct<StructExtra>[];

  @Column({
    name: 'rootUserId',
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  rootUserId: string;

  @Column({
    name: 'userId',
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  userId: string;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;

  @Column({
    name: 'dataType',
    type: 'integer',
    nullable: false,
  })
  dataType: CmsDataType;
}
