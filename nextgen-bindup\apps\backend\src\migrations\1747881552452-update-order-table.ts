import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateOrderTable1747881552452 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}orders`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    // remove totalAmount column
    await queryRunner.dropColumn(this.TABLE_NAME, 'totalAmount');
    // remove stripeFee column
    await queryRunner.dropColumn(this.TABLE_NAME, 'stripeFee');
    // remove netAmount column
    await queryRunner.dropColumn(this.TABLE_NAME, 'netAmount');
    // remove shippingFee column
    await queryRunner.dropColumn(this.TABLE_NAME, 'shippingFee');
    // add subtotal column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'subtotal',
        type: 'bigint',
        isNullable: true,
      }),
    );
    // add paymentGatewayFee column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'paymentGatewayFee',
        type: 'bigint',
        isNullable: true,
      }),
    );
    // add total column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'total',
        type: 'bigint',
        isNullable: true,
      }),
    );
    // add shopNetPayout column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'shopNetPayout',
        type: 'bigint',
        isNullable: true,
      }),
    );
    // add PaymentMethodType column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'paymentMethodType',
        type: 'varchar',
        length: '50',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // add totalAmount column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'totalAmount',
        type: 'bigint',
        isNullable: true,
      }),
    );
    // add stripeFee column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'stripeFee',
        type: 'bigint',
        isNullable: true,
      }),
    );
    // add netAmount column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'netAmount',
        type: 'bigint',
        isNullable: true,
      }),
    );
    // add shippingFee column
    await queryRunner.addColumn(
      this.TABLE_NAME,
      new TableColumn({
        name: 'shippingFee',
        type: 'bigint',
        isNullable: true,
      }),
    );
    // remove subtotal column
    await queryRunner.dropColumn(this.TABLE_NAME, 'subtotal');
    // remove paymentGatewayFee column
    await queryRunner.dropColumn(this.TABLE_NAME, 'paymentGatewayFee');
    // remove total column
    await queryRunner.dropColumn(this.TABLE_NAME, 'total');
    // remove PaymentMethodType column
    await queryRunner.dropColumn(this.TABLE_NAME, 'paymentMethodType');
    // remove shopNetPayout column
    await queryRunner.dropColumn(this.TABLE_NAME, 'shopNetPayout');
  }
}
