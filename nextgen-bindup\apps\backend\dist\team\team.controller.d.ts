import { TeamService } from './team.service';
import { JwtPayloadDto } from 'src/auth/dto/auth.dto';
import { TeamEntity } from './entities/team.entity';
export declare class TeamController {
    private readonly teamService;
    constructor(teamService: TeamService);
    findById(id: string): Promise<TeamEntity>;
    myTeam(user: JwtPayloadDto): Promise<TeamEntity[]>;
    create(user: JwtPayloadDto, data: TeamEntity): Promise<TeamEntity>;
    update(user: JwtPayloadDto, data: Partial<TeamEntity>, id: string): Promise<TeamEntity>;
}
