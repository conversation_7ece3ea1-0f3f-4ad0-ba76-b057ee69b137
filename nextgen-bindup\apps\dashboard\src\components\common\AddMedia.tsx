import { useEffect, useState, type FC } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  CardActionArea,
  CircularProgress,
  DialogActions,
  DialogContent,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Tab,
  Tabs,
  TextField,
  Typography,
  styled,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import other from '../../assets/file-icons/other.png';
import MaterialsImage from '../../assets/materials-logo.png';
import UnsplashImage from '../../assets/unsplash-logo.png';
import { AssetEntity } from '../../dto/asset.dto';
import { assetsService } from '../../services/assets-service';
import { R2Storage } from '../../services/r2-storage-service';
import { NEW_TS } from '../../utils/number.util';
import Materials from './Materials';
import Unsplash from './Unsplash';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});
export type SearchFileType = 'image' | 'video' | 'all' | 'unknown';

interface AddMediaProps {
  fileType: SearchFileType;
  onCancel: () => void;
  handleSelect: (asset: AssetEntity) => void;
}

const getFileType = (ext: string): SearchFileType => {
  switch (ext) {
    case 'png':
    case 'apng':
    case 'avif':
    case 'gif':
    case 'jpg':
    case 'jpeg':
    case 'jfif':
    case 'pjpeg':
    case 'pjp':
    case 'svg':
    case 'webp':
    case 'bmp':
    case 'tif':
    case 'tiff':
      return 'image';

    case 'mp4':
    case 'm4p':
    case 'm4v':
    case 'webm':
    case '3gpp':
    case '3gpp2':
    case '3gp2':
    case '3g2':
    case 'mpg':
    case 'mpeg':
    case 'm2v':
    case 'mov':
    case 'qt':
    case 'ogg':
    case 'wmv':
    case 'avi':
    case 'flv':
    case 'mkv':
    case 'mts':
    case 'm2ts':
    case 'ts':
    case 'vob':
    case 'ogv':
    case 'drc':
      return 'video';
  }

  return 'unknown';
};

const AddMedia: FC<AddMediaProps> = ({ fileType, onCancel, handleSelect }) => {
  const { t } = useTranslation();

  const tabs = [
    { label: t('asset.media.tab.my_files'), value: 'myFiles' },
    { label: t('asset.media.tab.materials'), value: 'searchOfMaterials' },
  ];
  const [selectedTab, setSelectedTab] = useState('myFiles');
  const subTabs = [
    { label: 'Materials', value: 'materials' },
    { label: 'Unsplash', value: 'unsplash' },
  ];
  const [selectedSubTab, setSelectedSubTab] = useState('materials');

  const [searchFileType, setSearchFileType] =
    useState<SearchFileType>(fileType);
  const [searchTerm, setSearchTerm] = useState('');
  const [assetList, setAssetList] = useState<AssetEntity[]>([]);
  const [displayList, setDisplayList] = useState<AssetEntity[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);

  const [selected, setSelected] = useState<AssetEntity | null>(null);
  const [uploading, setUploading] = useState<AssetEntity | null>(null);

  useEffect(() => {
    loadAssets();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (selectedTab === 'myFiles') {
      loadAssets();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTab]);

  const loadAssets = async () => {
    const fullList: AssetEntity[] = await assetsService.getAllAsset();
    let list: AssetEntity[] = [];
    if (fileType === 'all') {
      list = fullList;
    } else {
      list = fullList.filter(asset => getFileType(asset.type) === fileType);
    }

    setAssetList(list);
  };

  useEffect(() => {
    const searchStr: string = searchTerm.toLowerCase();
    const list: AssetEntity[] = assetList.filter(
      asset =>
        (searchFileType === 'all' ||
          getFileType(asset.type) === searchFileType) &&
        asset.name.toLowerCase().includes(searchStr),
    );
    setDisplayList(list);
  }, [assetList, searchFileType, searchTerm]);

  const handleChangeTab = (_event: React.SyntheticEvent, newValue: string) => {
    setSelectedTab(newValue);
  };

  const handleChangeSubTab = (
    _event: React.SyntheticEvent,
    newValue: string,
  ) => {
    setSelectedSubTab(newValue);
  };

  const uploadFile = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsUploading(true);

    try {
      if (!event.target.files) return;
      const file = event.target.files[0];
      const filename: string = file.name;
      const i: number = filename.lastIndexOf('.');
      const projectId = 1;
      let asset: AssetEntity = {
        id: 0,
        type: filename.substring(i + 1).toLocaleLowerCase(),
        projectId: projectId,
        name: filename.substring(0, i),
        url: null,
      };
      setUploading(asset);

      // const publicUrl: string = await storageService.uploadFile(
      //   file,
      //   `images/${NEW_TS()}-${filename}`,
      // );

      const filnamePath: string = `${projectId}-${NEW_TS()}-${filename}`;

      const publicUrl: string = await R2Storage.uploadFile(filnamePath, file);

      asset.url = publicUrl;
      asset = await assetsService.create(asset);

      const newList: AssetEntity[] = [asset, ...assetList];
      setAssetList(newList);
      setSelected(asset);
    } catch (error) {
      console.log('error', error);
    } finally {
      setIsUploading(false);
      setUploading(null);
    }
  };

  const renderThumbnail = (asset: AssetEntity, isPlayvideo = false) => {
    const assetType: SearchFileType = getFileType(asset.type);

    if (!asset.url) {
      return (
        <div
          style={{
            position: 'absolute',
            top: '0',
            left: '0',
            bottom: '0',
            right: '0',
            background: 'rgba(255,255,255,0.7)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <CircularProgress color="info" />
        </div>
      );
    }

    switch (assetType) {
      case 'image':
        return (
          <img
            key={asset.id}
            src={asset.url}
            alt="thumbnail"
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
            }}
          />
        );

      case 'video':
        return isPlayvideo ? (
          <video
            width="100%"
            height="100%"
            muted
            src={asset.url}
            autoPlay
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
            }}
          />
        ) : (
          <div
            style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                width: '100%',
                height: '100%',
                position: 'absolute',
                zIndex: 0,
              }}
            />
            <video
              width="100%"
              height="100%"
              muted
              src={asset.url}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
              }}
            />
          </div>
        );

      default:
        return (
          <img
            src={other}
            alt="thumbnail"
            width={'100%'}
            height={'100%'}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
            }}
          />
        );
    }
  };

  const renderItem = (asset: AssetEntity) => {
    return (
      <Grid key={asset.id}>
        <CardActionArea
          key={asset.id}
          onClick={() => setSelected(asset)}
          sx={{
            width: '135px',
            height: 'auto',
            padding: '5px',
            overflow: 'hidden',
            backgroundColor:
              selected?.id === asset.id ? '#e6f3ff' : 'transparent',
            border:
              selected?.id === asset.id
                ? '1px solid #ccc'
                : '1px solid transparent',
          }}
        >
          <Stack
            spacing={1}
            sx={{
              justifyContent: 'center',
              alignContent: 'center',
              alignItems: 'center',
            }}
          >
            <Box
              sx={{
                width: '125px',
                height: '80px !important',
              }}
            >
              {renderThumbnail(asset, false)}
            </Box>

            <div
              style={{
                width: '135px',
                textAlign: 'center',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                paddingLeft: '5px',
                paddingRight: '5px',
              }}
            >
              {asset.name}
            </div>
          </Stack>
        </CardActionArea>
      </Grid>
    );
  };

  return (
    <Stack sx={{ width: '1200px' }}>
      <Typography
        sx={{ paddingBlock: 2, paddingInline: 3 }}
        variant="subtitle2"
        color="textPrimary"
      >
        {t('asset.media.title')}
      </Typography>

      <DialogContent sx={{ paddingBlock: 0 }}>
        <Stack spacing={2}>
          <Stack direction="row" spacing={10}>
            <Tabs value={selectedTab} onChange={handleChangeTab}>
              {tabs.map((tab, index) => (
                <Tab key={index} value={tab.value} label={tab.label} />
              ))}
            </Tabs>
            {selectedTab === 'searchOfMaterials' && (
              <Tabs value={selectedSubTab} onChange={handleChangeSubTab}>
                {subTabs.map((tab, index) => (
                  <Tab
                    key={index}
                    value={tab.value}
                    label={
                      <img
                        src={
                          tab.value === 'materials'
                            ? MaterialsImage
                            : UnsplashImage
                        }
                      />
                    }
                  />
                ))}
              </Tabs>
            )}
          </Stack>
          {selectedTab === 'myFiles' && (
            <Stack direction="row" spacing={2}>
              <Box width="898px">
                <Stack spacing={2}>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <FormControl variant="filled" fullWidth>
                      <InputLabel id="simple-select-label">
                        {t('asset.media.kind.label')}
                      </InputLabel>

                      <Select
                        labelId="simple-select-label"
                        id="simple-select"
                        size="small"
                        value={searchFileType}
                        label={t('asset.media.kind.label')}
                        onChange={(e: SelectChangeEvent) => {
                          setSearchFileType(e.target.value as SearchFileType);
                        }}
                      >
                        {fileType === 'all' ? (
                          <MenuItem value={'all'}>
                            {t('asset.media.kind.all')}
                          </MenuItem>
                        ) : null}

                        {fileType === 'all' || fileType === 'image' ? (
                          <MenuItem value={'image'}>
                            {t('asset.media.kind.image')}
                          </MenuItem>
                        ) : null}

                        {fileType === 'all' || fileType === 'video' ? (
                          <MenuItem value={'video'}>
                            {t('asset.media.kind.video')}
                          </MenuItem>
                        ) : null}
                      </Select>
                    </FormControl>

                    <TextField
                      value={searchTerm}
                      onChange={event => setSearchTerm(event.target.value)}
                      fullWidth
                      size="small"
                      id="outlined-basic"
                      label={t('common.search')}
                      variant="filled"
                    />

                    <Box>
                      <Button
                        size="large"
                        color="inherit"
                        component="label"
                        sx={{ width: 'max-content' }}
                        role={undefined}
                        variant="outlined"
                        disabled={isUploading}
                      >
                        {t('common.btn_upload')}
                        <VisuallyHiddenInput
                          type="file"
                          onChange={uploadFile}
                          accept={
                            searchFileType === 'image'
                              ? 'image/*'
                              : searchFileType === 'video'
                                ? 'video/*'
                                : undefined
                          }
                        />
                      </Button>
                    </Box>
                  </Stack>

                  <Box
                    sx={{
                      maxHeight: '700px',
                      overflow: 'auto',
                    }}
                  >
                    <Grid container spacing="16px">
                      {uploading ? renderItem(uploading) : null}

                      {displayList.map((asset: AssetEntity) =>
                        renderItem(asset),
                      )}

                      <div
                        id="infinite-scroll-sentinel"
                        style={{ height: '1px' }}
                      ></div>
                    </Grid>
                  </Box>
                </Stack>
              </Box>

              <Box width="230px">
                <Stack spacing={2}>
                  <CardActionArea
                    key={`selected-${selected?.id}`}
                    sx={{
                      width: '230px',
                      height: '180px',
                      display: 'flex',
                      justifyContent: 'center',
                      alignContent: 'center',
                      alignItems: 'center',
                      padding: '10px',
                      border: '1px solid #ccc',
                    }}
                    className={'wl-color-none-light'}
                  >
                    {selected && renderThumbnail(selected, true)}
                  </CardActionArea>
                </Stack>
              </Box>
            </Stack>
          )}
          {selectedTab === 'searchOfMaterials' &&
            selectedSubTab === 'materials' && <Materials />}
          {selectedTab === 'searchOfMaterials' &&
            selectedSubTab === 'unsplash' && <Unsplash />}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ paddingInline: 2 }}>
        <Button variant="text" onClick={onCancel} disabled={isUploading}>
          {t('common.btn_cancel')}
        </Button>

        <Button
          color="inherit"
          variant="outlined"
          onClick={() => {
            if (!selected) return;
            handleSelect(selected);
          }}
          disabled={!selected || isUploading}
        >
          {t('common.btn_select')}
        </Button>
      </DialogActions>
    </Stack>
  );
};

export default AddMedia;
