"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplatePageEntity = void 0;
const page_type_1 = require("../../page/types/page.type");
const typeorm_1 = require("typeorm");
let TemplatePageEntity = class TemplatePageEntity {
    constructor() {
        this.type = page_type_1.PageType.PAGE;
        this.status = page_type_1.PageStatus.DRAFT;
        this.isPrivate = false;
        this.isHome = false;
        this.isDeleted = false;
    }
};
exports.TemplatePageEntity = TemplatePageEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], TemplatePageEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'templateId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], TemplatePageEntity.prototype, "templateId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'templateSiteId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], TemplatePageEntity.prototype, "templateSiteId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'templatePageId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], TemplatePageEntity.prototype, "templatePageId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'type',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], TemplatePageEntity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'parentId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], TemplatePageEntity.prototype, "parentId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'components',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], TemplatePageEntity.prototype, "components", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'name',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], TemplatePageEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'ts',
        type: 'bigint',
        nullable: true,
    }),
    __metadata("design:type", Number)
], TemplatePageEntity.prototype, "ts", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'status',
        type: 'smallint',
        nullable: false,
    }),
    __metadata("design:type", Number)
], TemplatePageEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'url',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplatePageEntity.prototype, "url", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'title',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplatePageEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'description',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplatePageEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isSearch',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], TemplatePageEntity.prototype, "isSearch", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'thumb',
        type: 'varchar',
        length: 250,
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplatePageEntity.prototype, "thumb", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'headCode',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplatePageEntity.prototype, "headCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'bodyCode',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplatePageEntity.prototype, "bodyCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isPrivate',
        type: 'boolean',
        nullable: false,
        default: false,
    }),
    __metadata("design:type", Boolean)
], TemplatePageEntity.prototype, "isPrivate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isHome',
        type: 'boolean',
        nullable: false,
        default: false,
    }),
    __metadata("design:type", Boolean)
], TemplatePageEntity.prototype, "isHome", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { array: true, default: {} }),
    __metadata("design:type", Array)
], TemplatePageEntity.prototype, "children", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'userId',
        type: 'varchar',
        length: '36',
        nullable: false,
    }),
    __metadata("design:type", String)
], TemplatePageEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isDeleted',
        type: 'boolean',
        nullable: false,
        default: false,
    }),
    __metadata("design:type", Boolean)
], TemplatePageEntity.prototype, "isDeleted", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], TemplatePageEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], TemplatePageEntity.prototype, "updatedAt", void 0);
exports.TemplatePageEntity = TemplatePageEntity = __decorate([
    (0, typeorm_1.Entity)('template_pages', { schema: process.env.DATABASE_SCHEMA })
], TemplatePageEntity);
//# sourceMappingURL=template-page.entity.js.map