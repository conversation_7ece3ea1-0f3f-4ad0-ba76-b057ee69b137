import {
  <PERSON>,
  <PERSON>,
  Req,
  <PERSON><PERSON>,
  Head<PERSON>,
  Get,
  Param,
} from '@nestjs/common';
import { PaymentService } from './payment.service';

@Controller('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @Get('user-subscription/:userId')
  async getUserSubscription(@Param('userId') userId: string) {
    return await this.paymentService.getUserSubscription(userId as string);
  }

  @Get('history-subscription/:userId')
  async getHistorySubscription(@Param('userId') userId: string) {
    return await this.paymentService.getHistorySubscription(userId as string);
  }

  @Post('cancel-subscription')
  async cancelSubscription(@Req() req) {
    const { userId } = req.body;
    return await this.paymentService.cancelSubscription(userId);
  }

  @Get('plans')
  async getPlans() {
    return await this.paymentService.getPlans();
  }

  @Post('checkout')
  async createCheckout(@Req() req) {
    const { userId, planId } = req.body;
    return await this.paymentService.createCheckoutSession(userId, planId);
  }

  @Post('webhook')
  async handleWebhook(
    @Req() req,
    @Res() res,
    @Headers('stripe-signature') signature: string,
  ) {
    try {
      const event = this.paymentService.verifyWebhookSignature(
        req.body as Buffer,
        signature,
      );
      await this.paymentService.handleWebhookEvent(event);
      res.status(200).send('Received');
    } catch (error) {
      console.error(error);
      res.status(400).send(error.message);
    }
  }
}
