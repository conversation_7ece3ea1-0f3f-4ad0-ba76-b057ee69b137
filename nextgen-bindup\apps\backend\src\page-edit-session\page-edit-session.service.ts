import { Injectable } from '@nestjs/common';
import { PageEditSessionEntity } from './entities/page-edit-session.entity';
import { LessThan, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class PageEditSessionService {
  @InjectRepository(PageEditSessionEntity)
  readonly pageRepo: Repository<PageEditSessionEntity>;

  async cronJobDeletePageEditSession() {
    // cron job to delete page edit session
    // delete page edit session if it is not updated for 5s
    try {
      await this.pageRepo.delete({
        updatedAt: LessThan(new Date(Date.now() - 5000)),
      });
    } catch (error) {
      console.log(error);
    }
  }
}
