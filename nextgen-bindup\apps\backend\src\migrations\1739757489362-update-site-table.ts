import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateSitesTable1739757489362 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}sites`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const userIdColumn: TableColumn = new TableColumn({
      name: 'userId',
      type: 'varchar',
      length: '36',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, userIdColumn);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'userId');
  }
}
