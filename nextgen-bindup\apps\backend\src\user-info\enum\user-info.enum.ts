export enum UserType {
  INDIVIDUAL = 1,
  CORPORATION = 2,
}

export enum Sex {
  MALE = 1,
  FEMALE = 2,
  OTHER = 3,
}

export enum CountryCode {
  JAPAN = 1,
}

export interface Prefecture {
  code: string;
  value: number;
}

export const PREFECTURE_LIST: Record<number, Prefecture[]> = {
  0: [],
  1: [
    { code: 'HOKKAIDO', value: 1 },
    { code: 'AOMORI', value: 2 },
    { code: 'IWATE', value: 3 },
    { code: 'MIYAGI', value: 4 },
    { code: 'AKIT<PERSON>', value: 5 },
    { code: 'YAMAGATA', value: 6 },
    { code: 'FUKUSHIMA', value: 7 },
    { code: 'IBARAKI', value: 8 },
    { code: 'TOCHIGI', value: 9 },
    { code: 'GUNMA', value: 10 },
    { code: 'SAITAM<PERSON>', value: 11 },
    { code: 'CHIBA', value: 12 },
    { code: 'TOKYO', value: 13 },
    { code: 'KANAGAWA', value: 14 },
    { code: 'YAM<PERSON>ASHI', value: 15 },
    { code: 'NAGANO', value: 17 },
    { code: 'NIIGATA', value: 18 },
    { code: 'TOYAMA', value: 19 },
    { code: 'ISHIKAWA', value: 20 },
    { code: 'FUKUI', value: 21 },
    { code: 'SHIZUOKA', value: 22 },
    { code: 'AICHI', value: 23 },
    { code: 'GIFU', value: 24 },
    { code: 'MIE', value: 25 },
    { code: 'SHIGA', value: 26 },
    { code: 'KYOTO', value: 27 },
    { code: 'OSAKA', value: 28 },
    { code: 'HYOGO', value: 29 },
    { code: 'NARA', value: 30 },
    { code: 'WAKAYAMA', value: 31 },
    { code: 'SHIMANE', value: 32 },
    { code: 'TOTTORI', value: 33 },
    { code: 'OKAYAMA', value: 34 },
    { code: 'HIROSHIMA', value: 35 },
    { code: 'YAMAGUCHI', value: 36 },
    { code: 'KAGAWA', value: 37 },
    { code: 'TOKUSHIMA', value: 38 },
    { code: 'KOCHI', value: 39 },
    { code: 'EHIME', value: 40 },
    { code: 'FUKUOKA', value: 41 },
    { code: 'SAGA', value: 42 },
    { code: 'NAGASAKI', value: 43 },
    { code: 'OITA', value: 44 },
    { code: 'KUMAMOTO', value: 45 },
    { code: 'MIYAZAKI', value: 46 },
    { code: 'KAGOSHIMA', value: 47 },
    { code: 'OKINAWA', value: 48 },
  ],
};

export enum Occupation {
  NONE = 0,
  COMPANY_EMPLOYEE = 1,
  CIVIL_SERVANT = 2,
  PROFESSOR_LECTURER = 3,
  STUDENT = 4,
  FULLTIME_HOUSEWIFE = 5,
  SELF_EMPLOYED = 9,
  UNEMPLOYED = 10,
  OTHER = 11,
  DESIGNER = 12,
}

export enum Industry {
  NONE = 0,
  DESIGN_INDUSTRY = 1,
  CREATIVE_INDUSTRY = 2,
  MANUFACTURING = 3,
  RETAIL = 4,
  CONSTRUCTION = 5,
  INFORMATION_AND_COMMUNICATIONS = 6,
  FOOD_AND_BEVERAGE_INDUSTRY = 7,
  LEISURE_AND_TOURISM = 8,
  FINANCE_AND_REAL_ESTATE = 9,
  PROFESSIONAL = 10,
  EDUCATION = 11,
  MEDICAL_AND_WELFARE = 12,
  BEAUTY = 13,
  AGRICULTURE_FORESTRY_AND_FISHING = 14,
  CIVIL_SERVANT = 15,
  OTHER = 16,
}

export enum JobType {
  NONE = 0,
  EXECUTIVE = 1,
  COMPANY_OFFICER = 2,
  SELF_EMPLOYED = 3,
  WEB_MANAGER_APPROVAL_PERSON = 4,
  WEB_MANAGER_NONDECISION_MAKER = 5,
  SALES = 6,
  PUBLIC_RELATIONS_AND_PROMOTION = 7,
  ADMINISTRATION = 8,
  MARKETING = 9,
  TEACHING_PROFESSION = 10,
  PROFESSIONAL_WEB_DESIGNER = 11,
  PROFESSIONAL_DESIGNER = 12,
  PROFESSIONAL_GRAPHIC_DESIGNER = 13,
  PROFESSIONAL_ILLUSTRATOR = 14,
  PROFESSIONAL_PHOTOGRAPHER = 15,
  PROFESSIONAL_OCCUPATION_MUSICIAN = 16,
  PROFESSIONAL_ENGINEER = 17,
  PROFESSIONAL_OTHER = 18,
  HOUSEWIFE = 19,
  STUDENT = 20,
  PARTTIME_WORKER = 21,
  UNEMPLOYED = 22,
  OTHER = 23,
}
