import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs/promises';
import { JsftpOpts } from 'jsftp';
import * as path from 'path';
// eslint-disable-next-line @typescript-eslint/no-require-imports
const Ftp = require('jsftp');

@Injectable()
export class FtpService {
  private readonly logger = new Logger(FtpService.name);

  constructor(private readonly configService: ConfigService) {}

  private createFtpConnection(): any {
    const FTP_CONFIG: JsftpOpts = {
      host: this.configService.get('FTP_HOST'),
      user: this.configService.get('FTP_USER'),
      pass: this.configService.get('FTP_PASSWORD'),
    };
    return new Ftp(FTP_CONFIG);
  }

  async uploadFTP(folderPath: string, remotePath: string): Promise<void> {
    console.log(`folderPath:${folderPath}`);
    const ftp = this.createFtpConnection();
    try {
      await this.clearRemoteDirectoryFtp(ftp, remotePath);
      console.log('* clearRemoteDirectoryFtp');
      await this.uploadFolderFtp(ftp, folderPath, remotePath);
    } catch (error) {
      this.logger.error(`Failed to upload folder to FTP: ${error.message}`);
      throw error;
    } finally {
      ftp.raw('quit');
    }
  }

  private async uploadFolderFtp(
    ftp: any,
    folderPath: string,
    remotePath: string,
  ): Promise<void> {
    await this.ensureRemoteDirectoryFtp(ftp, remotePath);

    const items = await fs.readdir(folderPath, { withFileTypes: true });

    for (const item of items) {
      const localItemPath = path.join(folderPath, item.name);
      const remoteItemPath = `${remotePath}/${item.name}`;

      if (item.isDirectory()) {
        await this.uploadFolderFtp(ftp, localItemPath, remoteItemPath);
      } else {
        await this.uploadFileFtp(ftp, localItemPath, remoteItemPath);
      }
    }
  }

  private async ensureRemoteDirectoryFtp(
    ftp: any,
    remoteDir: string,
  ): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      ftp.raw('MKD', remoteDir, err => {
        if (err && (err as any)?.code !== 550) {
          return reject(err);
        }
        resolve();
      });
    });
  }

  private async clearRemoteDirectoryFtp(
    ftp: any,
    remoteDir: string,
  ): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      ftp.ls(remoteDir, async (err, list) => {
        console.log('Clearing remote directory', list);

        if (err) return reject(err);

        try {
          for (const item of list) {
            const fullPath = `${remoteDir}/${item.name}`;
            if (item.type === 1) {
              await this.clearRemoteDirectoryFtp(ftp, fullPath);
              await this.removeDirectoryFtp(ftp, fullPath);
            } else {
              await this.removeFileFtp(ftp, fullPath);
            }
          }
          resolve();
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  private async removeFileFtp(ftp: any, filePath: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      ftp.raw('DELE', filePath, err => {
        if (err && (err as any)?.code !== 550) return reject(err);
        resolve();
      });
    });
  }

  private async removeDirectoryFtp(ftp: any, dirPath: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      ftp.raw('RMD', dirPath, err => {
        if (err && (err as any)?.code !== 550) return reject(err);
        resolve();
      });
    });
  }

  private async uploadFileFtp(
    ftp: any,
    localPath: string,
    remotePath: string,
  ): Promise<void> {
    try {
      const buffer = await fs.readFile(localPath);
      return new Promise<void>((resolve, reject) => {
        ftp.put(buffer, remotePath, err => {
          if (err) {
            this.logger.error(
              `Failed to upload file ${localPath}: ${err.message}`,
            );
            return reject(err);
          }
          this.logger.log(`File ${localPath} uploaded successfully.`);
          resolve();
        });
      });
    } catch (error) {
      this.logger.error(`Error reading file ${localPath}: ${error.message}`);
      throw error;
    }
  }
}
