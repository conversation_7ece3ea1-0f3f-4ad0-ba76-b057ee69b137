{"version": 3, "file": "1748489129472-rename-contents-table-and-add-columns.js", "sourceRoot": "", "sources": ["../../src/migrations/1748489129472-rename-contents-table-and-add-columns.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,wDAAwD;IAArE;QAGE,mBAAc,GAAW,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,UAAU,CAAC;QACtE,mBAAc,GAAW,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,iBAAiB,CAAC;IAiD/E,CAAC;IA/CQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEtC,MAAM,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAGxE,MAAM,YAAY,GAAgB,IAAI,qBAAW,CAAC;YAChD,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,KAAK;YACjB,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;QAGH,MAAM,kBAAkB,GAAgB,IAAI,qBAAW,CAAC;YACtD,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,KAAK;YACb,UAAU,EAAE,KAAK;YACjB,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QAEH,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE;YAChD,YAAY;YACZ,kBAAkB;SACnB,CAAC,CAAC;QAGH,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QAExC,MAAM,eAAe,GAAgB,IAAI,qBAAW,CAAC;YACnD,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,KAAK;YACjB,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;QACH,MAAM,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;QAGlE,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QAClE,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAG5D,MAAM,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAC1E,CAAC;CACF;AArDD,4HAqDC"}