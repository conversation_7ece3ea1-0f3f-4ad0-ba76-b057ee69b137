"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const file_module_1 = require("./file/file.module");
const config_1 = require("@nestjs/config");
const supabase_module_1 = require("./supabase/supabase.module");
const user_module_1 = require("./user/user.module");
const project_module_1 = require("./project/project.module");
const page_module_1 = require("./page/page.module");
const supabase_constant_1 = require("./supabase/supabase.constant");
const storage_module_1 = require("./storage/storage.module");
const typeorm_1 = require("@nestjs/typeorm");
const logger_module_1 = require("./logger/logger.module");
const publish_module_1 = require("./publish/publish.module");
const style_module_1 = require("./style/style.module");
const asset_module_1 = require("./asset/asset.module");
const asset_component_module_1 = require("./asset-component/asset-component.module");
const page_edit_session_module_1 = require("./page-edit-session/page-edit-session.module");
const schedule_1 = require("@nestjs/schedule");
const site_module_1 = require("./site/site.module");
const version_history_module_1 = require("./version-history/version-history.module");
const auth_module_1 = require("./auth/auth.module");
const user_info_module_1 = require("./user-info/user-info.module");
const team_module_1 = require("./team/team.module");
const user_team_module_1 = require("./user-team/user-team.module");
const identity_module_1 = require("./identity/identity.module");
const user_management_module_1 = require("./user-management/user-management.module");
const project_folders_module_1 = require("./project-folders/project-folders.module");
const name_servers_module_1 = require("./name-servers/name-servers.module");
const dns_record_module_1 = require("./dns-record/dns-record.module");
const sub_domain_module_1 = require("./sub-domain/sub-domain.module");
const fontset_module_1 = require("./fontset/fontset.module");
const payment_module_1 = require("./payment/payment.module");
const cms_collection_module_1 = require("./cms-collection/cms-collection.module");
const product_module_1 = require("./product/product.module");
const order_module_1 = require("./order/order.module");
const cms_collection_items_module_1 = require("./cms-collection-items/cms-collection-items.module");
const mail_module_1 = require("./mail/mail.module");
const product_stocks_module_1 = require("./product-stocks/product-stocks.module");
const order_complete_settings_module_1 = require("./order-complete-settings/order-complete-settings.module");
const shop_information_settings_module_1 = require("./shop-information-settings/shop-information-settings.module");
const shipping_note_settings_module_1 = require("./shipping-note-settings/shipping-note-settings.module");
const event_emitter_1 = require("@nestjs/event-emitter");
const payment_method_module_1 = require("./payment-method/payment-method.module");
const proxy_module_1 = require("./proxy/proxy.module");
const delivery_receipt_settings_module_1 = require("./delivery-receipt-settings/delivery-receipt-settings.module");
const template_migration_module_1 = require("./template-migration/template-migration.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            event_emitter_1.EventEmitterModule.forRoot(),
            schedule_1.ScheduleModule.forRoot(),
            file_module_1.FileModule,
            logger_module_1.LoggerModule.forRoot(),
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: '.env',
            }),
            supabase_module_1.SupabaseModule.forRootAsync({
                useFactory: async (configService) => ({
                    url: configService.get(supabase_constant_1.SUPABASE_URL),
                    key: configService.get(supabase_constant_1.SUPABASE_KEY),
                }),
                inject: [config_1.ConfigService],
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                useFactory: async (configService) => ({
                    type: configService.get('DATABASE_TYPE'),
                    host: configService.get('DATABASE_HOST'),
                    port: parseInt(configService.get('DATABASE_PORT')),
                    database: configService.get('DATABASE_NAME'),
                    username: configService.get('DATABASE_USER'),
                    password: configService.get('DATABASE_PASSWORD'),
                    entities: ['dist/**/*.entity.{ts,js}'],
                    migrations: [
                        configService.get('NODE_ENV') === 'production'
                            ? 'dist/migrations/*.{ts,js}'
                            : 'dist/migrations/*.{ts,js}',
                    ],
                    migrationsTableName: `${configService.get('ENTITY_PREFIX')}typeorm_migrations`,
                    logger: (configService.get('NODE_ENV') === 'production'
                        ? 'simple-console'
                        : 'advanced-console'),
                    logging: false,
                    entityPrefix: configService.get('ENTITY_PREFIX') || '',
                    migrationsRun: configService.get('DATABASE_MIGRATIONS_RUN') === 'true',
                }),
                inject: [config_1.ConfigService],
            }),
            user_module_1.UserModule,
            project_module_1.ProjectModule,
            page_module_1.PageModule,
            storage_module_1.StorageModule,
            publish_module_1.PublishModule,
            style_module_1.StyleModule,
            asset_module_1.AssetsModule,
            asset_component_module_1.ContentModule,
            page_edit_session_module_1.PageEditSessionModule,
            site_module_1.SiteModule,
            version_history_module_1.VersionHistoryModule,
            auth_module_1.AuthModule,
            user_info_module_1.UserInfoModule,
            team_module_1.TeamModule,
            user_team_module_1.UserTeamModule,
            identity_module_1.IdentityModule,
            user_management_module_1.UserManagementModule,
            project_folders_module_1.ProjectFoldersModule,
            name_servers_module_1.NameServersModule,
            dns_record_module_1.DnsRecordModule,
            sub_domain_module_1.SubDomainModule,
            fontset_module_1.FontsetModule,
            payment_module_1.PaymentModule,
            cms_collection_module_1.CmsCollectionModule,
            product_module_1.ProductModule,
            order_module_1.OrderModule,
            cms_collection_items_module_1.CmsCollectionItemsModule,
            mail_module_1.MailModule,
            product_stocks_module_1.ProductStocksModule,
            order_complete_settings_module_1.OrderCompletionSettingModule,
            shop_information_settings_module_1.ShopInformationSettingModule,
            shipping_note_settings_module_1.ShippingNoteSettingModule,
            payment_method_module_1.PaymentMethodModule,
            proxy_module_1.ProxyModule,
            delivery_receipt_settings_module_1.DeliveryReceiptSettingModule,
            template_migration_module_1.TemplateMigrationModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map