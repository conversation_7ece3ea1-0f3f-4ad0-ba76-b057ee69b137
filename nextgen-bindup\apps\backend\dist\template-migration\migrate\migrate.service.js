"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrateService = void 0;
const site3_block_dto_1 = require("../dto/site3_block.dto");
const read_file_util_1 = require("../util/read-file.util");
const migrate_util_1 = require("./migrate.util");
const common_util_1 = require("../../utils/common.util");
const component_type_1 = require("../../page/types/component.type");
const migrate_data_block_service_1 = require("./migrate-data-block.service");
const parse_util_1 = require("../util/parse.util");
const migrate_data_content_service_1 = require("./migrate-data-content.service");
class MigrateService {
    constructor(inp) {
        this.components = structuredClone(migrate_util_1.DEFAULT_TEMPLATE_COMPONENT);
        this.ID_INDEX = 1;
        this.dbdata = inp.dbdata;
        this.templatePageIds = inp.templatePageIds;
        this.blocks = read_file_util_1.ReadFileUtil.readBlocks(this.dbdata);
        this.blockDatas = read_file_util_1.ReadFileUtil.readBlockDatas(this.dbdata);
        const srcList1 = read_file_util_1.ReadFileUtil.readSrcList1(this.dbdata);
        this.resources = read_file_util_1.ReadFileUtil.readResources(this.dbdata, srcList1);
    }
    migratePage(inp) {
        this.page = inp.page;
        this.components = inp.components;
        this.ID_INDEX = 1;
        const pageBlocks = this.blocks.filter(block => block.pageId === this.page.pageId);
        const dataBlockService = new migrate_data_block_service_1.MigrateDataBlockService({ components: this.components });
        const dataContentService = new migrate_data_content_service_1.MigrateDataContentService({ components: this.components });
        for (const block of pageBlocks) {
            const blockData = this.blockDatas.find(blockData => blockData.blockdataId === block.blockdataId);
            const parentSide = this.checkParentSide(block.areaId);
            const blockMargin = dataBlockService.migrateBlockData({
                parentSide,
                block,
                blockData,
            });
            const dataContents = parse_util_1.ParseUtil.parseContent(blockData, this.resources);
            const dataGroups = parse_util_1.ParseUtil.groupChild(dataContents);
            for (const dataGroup of dataGroups) {
                dataContentService.migrateDataGroup({
                    parentComponentId: blockMargin.id,
                    block,
                    blockData,
                    dataGroup,
                });
            }
        }
        return this.components;
    }
    createBillboard() {
        const id = 'block_billboard';
        if (this.components[id])
            return;
        const ts = (0, common_util_1.NEW_TS)();
        const properties = migrate_util_1.MigrateUtil.blockProps();
        properties.size.width = {
            value: '100',
            unit: '%',
        };
        const blockBillboard = {
            id: id,
            type: component_type_1.ComponentType.Block,
            name: 'Billboard',
            parentId: '__main__',
            properties: properties,
            children: [],
            breakpoint: {
                tablet: { ts: ts },
                phone: { ts: ts },
            },
            ts: ts,
        };
        this.components[blockBillboard.id] = blockBillboard;
        this.components['__main__'].children.push(blockBillboard.id);
    }
    createSideA() {
        const id = '__left-side__';
        if (this.components[id])
            return;
        const ts = (0, common_util_1.NEW_TS)();
        const properties = migrate_util_1.MigrateUtil.blockProps();
        properties.size.width = {
            value: '100',
            unit: '%',
        };
        properties.sideArea = { ts: ts, show: true };
        const sideA = {
            id: id,
            type: component_type_1.ComponentType.LeftSide,
            name: 'Left Side',
            parentId: '__page__',
            properties: properties,
            children: [],
            breakpoint: {
                tablet: { ts: ts },
                phone: { ts: ts },
            },
            ts: ts,
        };
        this.components[sideA.id] = sideA;
        this.components['__page__'].children.push(sideA.id);
    }
    createSideB() {
        const id = '__right-side__';
        if (this.components[id])
            return;
        const ts = (0, common_util_1.NEW_TS)();
        const properties = migrate_util_1.MigrateUtil.blockProps();
        properties.size.width = {
            value: '100',
            unit: '%',
        };
        properties.size.minHeight = { unit: 'dvh', value: '100' };
        properties.sideArea = { ts: ts, show: true };
        const sideB = {
            id: id,
            type: component_type_1.ComponentType.LeftSide,
            name: 'Right Side',
            parentId: '__page__',
            properties: properties,
            children: [],
            breakpoint: {
                tablet: { ts: ts },
                phone: { ts: ts },
            },
            ts: ts,
        };
        this.components[sideB.id] = sideB;
        this.components['__page__'].children.push(sideB.id);
    }
    createHeader() {
        const id = '__header__';
        if (this.components[id])
            return;
        const ts = (0, common_util_1.NEW_TS)();
        const properties = migrate_util_1.MigrateUtil.blockProps();
        properties.size.height = {
            value: '70',
            unit: 'px',
        };
        const header = {
            id: id,
            type: component_type_1.ComponentType.Header,
            name: 'Header',
            parentId: '__page__',
            properties: properties,
            children: [],
            breakpoint: {
                tablet: { ts: ts },
                phone: { ts: ts },
            },
            ts: ts,
        };
        this.components[header.id] = header;
        this.components['__page__'].children.push(header.id);
    }
    createFooter() {
        const id = '__footer__';
        if (this.components[id])
            return;
        const ts = (0, common_util_1.NEW_TS)();
        const properties = migrate_util_1.MigrateUtil.blockProps();
        properties.size.height = {
            value: '70',
            unit: 'px',
        };
        const footer = {
            id: id,
            type: component_type_1.ComponentType.Footer,
            name: 'Footer',
            parentId: '__page__',
            properties: properties,
            children: [],
            breakpoint: {
                tablet: { ts: ts },
                phone: { ts: ts },
            },
            ts: ts,
        };
        this.components[footer.id] = footer;
        this.components['__page__'].children.push(footer.id);
    }
    checkParentSide(areaId) {
        switch (areaId) {
            case site3_block_dto_1.Block_Area.HEADER:
                this.createHeader();
                return '__header__';
            case site3_block_dto_1.Block_Area.BILLBOARD:
                this.createBillboard();
                return 'block_billboard';
            case site3_block_dto_1.Block_Area.MAIN:
                return '__main__';
            case site3_block_dto_1.Block_Area.SIDE_A:
                this.createSideA();
                return '__left-side__';
            case site3_block_dto_1.Block_Area.SIDE_B:
                this.createSideB();
                return '__right-side__';
            case site3_block_dto_1.Block_Area.FOOTER:
                this.createFooter();
                return '__footer__';
            default:
                return '__UNDEFINED__';
        }
    }
}
exports.MigrateService = MigrateService;
//# sourceMappingURL=migrate.service.js.map