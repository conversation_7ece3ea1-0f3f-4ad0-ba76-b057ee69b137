"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateStylesTable1747970237555 = void 0;
const typeorm_1 = require("typeorm");
class UpdateStylesTable1747970237555 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}styles`;
    }
    async up(queryRunner) {
        const siteIdColumn = new typeorm_1.TableColumn({
            name: 'siteId',
            type: 'integer',
            isNullable: false,
            default: '1',
        });
        await queryRunner.addColumn(this.TABLE_NAME, siteIdColumn);
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_styles_siteId',
            columnNames: ['siteId'],
            isUnique: false,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_styles_siteId');
        await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
    }
}
exports.UpdateStylesTable1747970237555 = UpdateStylesTable1747970237555;
//# sourceMappingURL=1747970237555-create-styles-table.js.map