"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePageVersionTable1740974195689 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePageVersionTable1740974195689 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}page_versions`;
    }
    async up(queryRunner) {
        const userIdColumn = new typeorm_1.TableColumn({
            name: 'userId',
            type: 'varchar',
            length: '36',
            isNullable: true,
        });
        await queryRunner.addColumn(this.TABLE_NAME, userIdColumn);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'userId');
    }
}
exports.UpdatePageVersionTable1740974195689 = UpdatePageVersionTable1740974195689;
//# sourceMappingURL=1740974195689-update-page-version-table.js.map