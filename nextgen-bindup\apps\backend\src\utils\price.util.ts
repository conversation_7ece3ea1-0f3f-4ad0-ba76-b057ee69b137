import { ProductEntity } from 'src/product/entities/product.entity';
import { OrderItemEntity } from 'src/order/entites/order-item.entity';
import { PaymentMethodType } from 'src/order/enum/payment-method-type.enum';
import { ShippingNoteSettingEntity } from 'src/shipping-note-settings/entities/shipping-note--settings.entity';
import { ShopInformationSettingEntity } from 'src/shop-information-settings/entities/shop-information-settings.entity';
import { ProductType } from 'src/product/enum/product.enum';
import { TaxMode, TaxRegulation } from 'src/shop-information-settings/enums';

export interface Fee {
  subtotal: number;
  shippingFee: number;
  total: number;
  platformFee: number;
  paymentGatewayFee: number;
  shopNetPayout: number;
}

export const calculateFees = (
  orderItems: OrderItemEntity[],
  shippingNoteSetting: ShippingNoteSettingEntity,
  platformFeeRate: number,
  paymentGatewayFeeRate: number,
  paymentMethodType: PaymentMethodType,
  shippingPrefecture: string,
) => {
  // calculate subtotal
  const subtotal = orderItems.reduce(
    (total, item) => total + Number(item.displayPrice) * Number(item.quantity),
    0,
  );

  // calculate shipping fee
  let shippingFee = 0;

  const isOnlyDigitalProducts = orderItems.every(
    item => item.productType === ProductType.DIGITAL,
  );

  if (!isOnlyDigitalProducts) {
    // Calculate total individual shipping charges
    const individualShippingChargesTotal = orderItems.reduce((total, item) => {
      if (
        item.individualShippingCharges &&
        item.individualShippingCharges > 0 &&
        item.productType === ProductType.NORMAL
      ) {
        return total + Number(item.individualShippingCharges);
      }
      return total;
    }, 0);

    // Check if there are any items without individual shipping charges
    const hasItemsWithoutIndividualCharges = orderItems.some(
      item =>
        !item.individualShippingCharges || item.individualShippingCharges === 0,
    );

    let commonShippingFee = 0;
    if (hasItemsWithoutIndividualCharges) {
      if (
        !(
          shippingNoteSetting.isFreeShippingCondition &&
          subtotal > shippingNoteSetting.freeShippingCondition
        )
      ) {
        const shippingFeeDetail = shippingNoteSetting.shippingFeeDetail;
        commonShippingFee = Number(shippingFeeDetail[shippingPrefecture] || 0);
      }
    }

    // Total shipping fee is sum of individual charges and common fee
    shippingFee =
      Number(individualShippingChargesTotal) + Number(commonShippingFee);
  }

  // calculate total
  const total = subtotal + shippingFee;

  // calculate platform fee & payment gateway fee
  let platformFee = 0;
  let paymentGatewayFee = 0;
  let shopNetPayout = total;
  if (paymentMethodType === PaymentMethodType.CREDIT_CARD) {
    platformFee = Math.ceil(total * platformFeeRate);
    paymentGatewayFee = Math.ceil(total * paymentGatewayFeeRate);
    shopNetPayout = total - platformFee - paymentGatewayFee;
  }
  return {
    subtotal,
    shippingFee,
    total,
    platformFee,
    paymentGatewayFee,
    shopNetPayout,
  };
};

export const getShippingFee = (
  showShippingAddress: boolean,
  shippingPrefecture: string,
  orderItems: OrderItemEntity[],
  shippingNoteSetting: ShippingNoteSettingEntity,
): number => {
  if (!showShippingAddress || !shippingNoteSetting) {
    return 0;
  }
  if (!shippingPrefecture || !orderItems.length) {
    return 0;
  }

  // Calculate total individual shipping charges
  const individualShippingChargesTotal = orderItems.reduce((total, item) => {
    if (item.individualShippingCharges && item.individualShippingCharges > 0) {
      return total + Number(item.individualShippingCharges);
    }
    return total;
  }, 0);

  // Check if there are any items without individual shipping charges
  const hasItemsWithoutIndividualCharges = orderItems.some(
    item =>
      !item.individualShippingCharges ||
      Number(item.individualShippingCharges) === 0,
  );

  // Get common shipping fee if needed
  let commonShippingFee = 0;
  if (hasItemsWithoutIndividualCharges) {
    if (shippingNoteSetting.isFreeShippingCondition) {
      const totalPrice = orderItems.reduce(
        (total, item) =>
          total + Number(item.displayPrice) * Number(item.quantity),
        0,
      );
      if (totalPrice <= shippingNoteSetting.freeShippingCondition) {
        const shippingFeeDetail = shippingNoteSetting.shippingFeeDetail;
        commonShippingFee = Number(shippingFeeDetail[shippingPrefecture] || 0);
      }
    } else {
      const shippingFeeDetail = shippingNoteSetting.shippingFeeDetail;
      commonShippingFee = Number(shippingFeeDetail[shippingPrefecture] || 0);
    }
  }

  // Total shipping fee is sum of individual charges and common fee
  return individualShippingChargesTotal + commonShippingFee;
};

export const calculatePriceWithTax = (
  price: number,
  taxMode?: TaxMode,
  taxRate?: number,
  taxRegulation?: TaxRegulation,
): number => {
  // If no tax settings provided, assume price already includes tax
  if (!taxMode || !taxRate || !taxRegulation) {
    return price;
  }

  if (taxMode === TaxMode.INCLUSIVE) {
    return price;
  } else {
    const taxRateDecimal = taxRate / 100;
    if (taxRegulation === TaxRegulation.TRUNCATE) {
      return Math.trunc(price * (1 + taxRateDecimal));
    } else if (taxRegulation === TaxRegulation.ROUND_UP) {
      return Math.ceil(price * (1 + taxRateDecimal));
    } else if (taxRegulation === TaxRegulation.ROUND_DOWN) {
      return Math.floor(price * (1 + taxRateDecimal));
    }
    return Math.round(price * (1 + taxRateDecimal));
  }
};

export const formatPriceWithTax = (price: number): string => {
  return `¥ ${price.toLocaleString()}（税込）`;
};

export const formatDisplayPrice = (
  price: number,
  salePrice: number | null,
): string => {
  const formattedPrice = formatPriceWithTax(price);
  if (salePrice) {
    const formattedSalePrice = formatPriceWithTax(salePrice);
    return `${formattedPrice} → ${formattedSalePrice}`;
  }
  return formattedPrice;
};

// For backward compatibility
export const getDisplayPrice = (
  product: ProductEntity,
  shopInformation?: ShopInformationSettingEntity,
): number => {
  const price =
    Number(product.sale) > 0 ? Number(product.sale) : Number(product.price);

  if (!shopInformation) {
    return price;
  }

  return calculatePriceWithTax(
    price,
    shopInformation.taxMode,
    shopInformation.taxRate,
    shopInformation.taxRegulation,
  );
};
