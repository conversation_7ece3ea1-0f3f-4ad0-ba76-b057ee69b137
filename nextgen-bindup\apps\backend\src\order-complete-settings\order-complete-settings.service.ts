import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppException } from 'src/common/exceptions/app.exception';
import { OrderCompletionSettingEntity } from './entities/order-complete-settings.entity';
import { SiteService } from 'src/site/site.service';

@Injectable()
export class OrderCompletionSettingService {
  @InjectRepository(OrderCompletionSettingEntity)
  readonly orderCompletionSettingRepo: Repository<OrderCompletionSettingEntity>;

  constructor(private readonly siteService: SiteService) {}

  async create(
    orderCompletionSettingEntity: OrderCompletionSettingEntity,
  ): Promise<OrderCompletionSettingEntity> {
    const now: Date = new Date();
    const setting = new OrderCompletionSettingEntity();
    setting.siteId = orderCompletionSettingEntity.siteId;
    setting.displayText = orderCompletionSettingEntity.displayText;
    setting.emailSubject = orderCompletionSettingEntity.emailSubject;
    setting.emailHeader = orderCompletionSettingEntity.emailHeader;
    setting.emailFooter = orderCompletionSettingEntity.emailFooter;
    setting.createdAt = now;
    setting.updatedAt = now;
    return await this.orderCompletionSettingRepo.save(setting);
  }

  async update(
    id: number,
    settingData: Partial<OrderCompletionSettingEntity>,
  ): Promise<OrderCompletionSettingEntity> {
    const setting = await this.orderCompletionSettingRepo.findOneBy({ id: id });
    if (!setting)
      throw new AppException('api.error.order_completion_setting_not_found');

    delete settingData.id;
    delete settingData.siteId;
    delete settingData.createdAt;
    settingData.updatedAt = new Date();

    await this.orderCompletionSettingRepo.update(id, settingData);
    return { ...setting, ...settingData };
  }

  async findById(id: number): Promise<OrderCompletionSettingEntity> {
    return await this.orderCompletionSettingRepo.findOneBy({ id });
  }

  async findOneBySiteId(siteId: number): Promise<OrderCompletionSettingEntity> {
    return await this.orderCompletionSettingRepo.findOneBy({ siteId });
  }

  async delete(id: number): Promise<boolean> {
    const setting = await this.orderCompletionSettingRepo.findOneBy({ id });
    if (!setting)
      throw new AppException('api.error.order_completion_setting_not_found');

    await this.orderCompletionSettingRepo.delete(id);
    return true;
  }
}
