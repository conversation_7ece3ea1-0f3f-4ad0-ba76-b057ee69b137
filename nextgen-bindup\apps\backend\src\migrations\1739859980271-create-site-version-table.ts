import { SiteStatus } from 'src/site/types/site.type';
import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateSiteVersion1739859980271 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}site_versions`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'siteId',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'projectId',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'projectFolderId',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'managementName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'smallint',
            isNullable: false,
            default: `'${SiteStatus.DRAFT}'`,
          },
          {
            name: 'url',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isSearch',
            type: 'boolean',
            isNullable: true,
          },
          {
            name: 'thumb',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'headCode',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'bodyCode',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
          },
          {
            name: 'isArchived',
            type: 'boolean',
            isNullable: true,
          },
          {
            name: 'versionCreatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'versionName',
            type: 'varchar',
            length: '255',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
