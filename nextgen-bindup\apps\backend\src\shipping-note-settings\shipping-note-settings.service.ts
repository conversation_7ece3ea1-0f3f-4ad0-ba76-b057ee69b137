import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppException } from 'src/common/exceptions/app.exception';
import { ShippingNoteSettingEntity } from './entities/shipping-note--settings.entity';

@Injectable()
export class ShippingNoteSettingService {
  @InjectRepository(ShippingNoteSettingEntity)
  readonly shippingNoteSettingRepo: Repository<ShippingNoteSettingEntity>;

  constructor() {}

  async create(
    shippingNoteSettingEntity: ShippingNoteSettingEntity,
  ): Promise<ShippingNoteSettingEntity> {
    const now: Date = new Date();
    const setting = new ShippingNoteSettingEntity();
    setting.siteId = shippingNoteSettingEntity.siteId;
    setting.shippingFee = shippingNoteSettingEntity.shippingFee;
    setting.isFreeShippingCondition =
      shippingNoteSettingEntity.isFreeShippingCondition;
    setting.shippingFeeDetail = shippingNoteSettingEntity.shippingFeeDetail;
    setting.note = shippingNoteSettingEntity.note;
    setting.createdAt = now;
    setting.updatedAt = now;
    return await this.shippingNoteSettingRepo.save(setting);
  }

  async update(
    id: number,
    settingData: Partial<ShippingNoteSettingEntity>,
  ): Promise<ShippingNoteSettingEntity> {
    const setting = await this.shippingNoteSettingRepo.findOneBy({ id: id });
    if (!setting)
      throw new AppException('api.error.shipping_note_setting_not_found');

    delete settingData.id;
    delete settingData.siteId;
    delete settingData.createdAt;
    settingData.updatedAt = new Date();

    await this.shippingNoteSettingRepo.update(id, settingData);
    return { ...setting, ...settingData };
  }

  async findById(id: number): Promise<ShippingNoteSettingEntity> {
    return await this.shippingNoteSettingRepo.findOneBy({ id });
  }

  async findOneBySiteId(siteId: number): Promise<ShippingNoteSettingEntity> {
    return await this.shippingNoteSettingRepo.findOneBy({ siteId });
  }

  async delete(id: number): Promise<boolean> {
    const setting = await this.shippingNoteSettingRepo.findOneBy({ id });
    if (!setting)
      throw new AppException('api.error.shipping_note_setting_not_found');

    await this.shippingNoteSettingRepo.delete(id);
    return true;
  }
}
