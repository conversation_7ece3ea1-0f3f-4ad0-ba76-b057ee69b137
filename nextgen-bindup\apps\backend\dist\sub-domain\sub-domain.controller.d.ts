import { SubDomainService } from './sub-domain.service';
import { SubDomainEntity } from './entities/sub-domain.entity';
export declare class SubDomainController {
    private readonly subDomainService;
    constructor(subDomainService: SubDomainService);
    create(data: SubDomainEntity): Promise<SubDomainEntity>;
    update(id: string, data: Partial<SubDomainEntity>): Promise<SubDomainEntity>;
    delete(id: string): Promise<boolean>;
    findBySiteId(siteId: string): Promise<SubDomainEntity[]>;
    findByProjectId(projectId: string): Promise<SubDomainEntity[]>;
}
