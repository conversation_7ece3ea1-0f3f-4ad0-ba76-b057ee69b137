import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdatePageTable1730961329209 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}pages`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const childrenColumn: TableColumn = new TableColumn({
      name: 'children',
      type: 'jsonb',
      isNullable: true,
      isArray: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, childrenColumn);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'children');
  }
}
