import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { SiteAuthService } from './site-auth.service';

@Injectable()
export class SiteGuard implements CanActivate {
  constructor(private readonly siteAuthService: SiteAuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = request.headers.authorization?.split(' ')[1];
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }
    try {
      const { siteId } =
        await this.siteAuthService.validateTokenAndGetSite(token);
      request.site = {
        siteId: siteId,
      };
      return true;
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
  }
}
