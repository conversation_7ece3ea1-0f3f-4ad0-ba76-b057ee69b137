"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePageTable1747793781806 = void 0;
class UpdatePageTable1747793781806 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}pages`;
    }
    async up(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'cmsCollectionId');
    }
    async down(queryRunner) {
        console.log(queryRunner);
    }
}
exports.UpdatePageTable1747793781806 = UpdatePageTable1747793781806;
//# sourceMappingURL=1747793781806-update-page-table.js.map