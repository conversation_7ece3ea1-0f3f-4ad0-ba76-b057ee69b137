"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePageVersionsTable1747793781805 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePageVersionsTable1747793781805 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}page_versions`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'datasource',
            type: 'jsonb',
            isNullable: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'datasource');
    }
}
exports.UpdatePageVersionsTable1747793781805 = UpdatePageVersionsTable1747793781805;
//# sourceMappingURL=1747793781805-update-page-versions-table.js.map