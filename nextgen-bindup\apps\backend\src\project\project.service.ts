import { Injectable } from '@nestjs/common';
import { CreateProjectReq, Project, ProjectRawData } from './dto/project.dto';
import { DataSource, Repository } from 'typeorm';
import { ProjectEntity } from './entities/project.entity';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { AppException } from 'src/common/exceptions/app.exception';

@Injectable()
export class ProjectService {
  @InjectRepository(ProjectEntity)
  readonly projectRepo: Repository<ProjectEntity>;

  constructor(@InjectDataSource() private readonly dataSource: DataSource) {}

  async findById(id: number): Promise<ProjectEntity> {
    return await this.projectRepo.findOneBy({ id });
  }

  async getProjectsByUser(userId: string): Promise<Project[]> {
    const query = `
      SELECT
        p.id,
        p.name,
        pf.id AS "projectFolders.id",
        pf.name AS "projectFolders.name"
      FROM projects p
      LEFT JOIN project_folders pf ON p.id = pf."projectId"
      WHERE p."userId" = $1
        OR p."userId" IN (SELECT ut."rootUserId"
                          FROM user_team ut
                          WHERE ut."userId" = $1)
    `;

    const projectRaws = await this.dataSource.query<ProjectRawData[]>(query, [
      userId,
    ]);
    const groupedProjects = this.groupProjectsWithFolders(projectRaws);
    return groupedProjects;
  }

  private groupProjectsWithFolders(projects: ProjectRawData[]): Project[] {
    const projectMap = new Map();

    projects.forEach(row => {
      const projectId = row.id;

      if (!projectMap.has(projectId)) {
        projectMap.set(projectId, {
          ...row,
          projectFolders: row['projectFolders.id']
            ? [
                {
                  id: row['projectFolders.id'],
                  name: row['projectFolders.name'],
                },
              ]
            : [],
        });
        delete projectMap.get(projectId)['projectFolders.id'];
        delete projectMap.get(projectId)['projectFolders.name'];
      } else {
        if (row['projectFolders.id']) {
          projectMap.get(projectId).projectFolders.push({
            id: row['projectFolders.id'],
            name: row['projectFolders.name'],
          });
        }
      }
    });

    return Array.from(projectMap.values());
  }

  async createProject(
    userId: string,
    projectData: CreateProjectReq,
  ): Promise<ProjectEntity> {
    const now = new Date();

    const project: ProjectEntity = new ProjectEntity();
    project.userId = userId;
    project.name = projectData.name;
    project.createdAt = now;
    project.updatedAt = now;
    return await this.projectRepo.save(project);
  }

  async updateProject(
    id: number,
    data: Partial<ProjectEntity>,
  ): Promise<ProjectEntity> {
    const project: ProjectEntity = await this.findById(id);
    if (!project) throw new AppException('error.project_not_found');

    delete data.id;
    await this.projectRepo.update(id, { ...data });
    return { ...project, ...data };
  }

  async deleteProject(id: number): Promise<boolean> {
    await this.projectRepo.delete(id);
    return true;
  }
}
