"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShippingNoteSettingModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const shipping_note__settings_entity_1 = require("./entities/shipping-note--settings.entity");
const shipping_note_settings_controller_1 = require("./shipping-note-settings.controller");
const shipping_note_settings_service_1 = require("./shipping-note-settings.service");
let ShippingNoteSettingModule = class ShippingNoteSettingModule {
};
exports.ShippingNoteSettingModule = ShippingNoteSettingModule;
exports.ShippingNoteSettingModule = ShippingNoteSettingModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([shipping_note__settings_entity_1.ShippingNoteSettingEntity])],
        controllers: [shipping_note_settings_controller_1.ShippingNoteSettingController],
        providers: [shipping_note_settings_service_1.ShippingNoteSettingService],
        exports: [shipping_note_settings_service_1.ShippingNoteSettingService],
    })
], ShippingNoteSettingModule);
//# sourceMappingURL=shipping-note-settings.module.js.map