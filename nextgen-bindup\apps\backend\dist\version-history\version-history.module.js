"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionHistoryModule = void 0;
const common_1 = require("@nestjs/common");
const version_history_controller_1 = require("./version-history.controller");
const version_history_service_1 = require("./version-history.service");
const page_module_1 = require("../page/page.module");
const site_module_1 = require("../site/site.module");
const typeorm_1 = require("@nestjs/typeorm");
const site_entity_1 = require("../site/entities/site.entity");
const page_entity_1 = require("../page/entities/page.entity");
const site_version_entity_1 = require("./entities/site-version.entity");
const page_version_entity_1 = require("./entities/page-version.entity");
let VersionHistoryModule = class VersionHistoryModule {
};
exports.VersionHistoryModule = VersionHistoryModule;
exports.VersionHistoryModule = VersionHistoryModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([site_entity_1.SiteEntity]),
            typeorm_1.TypeOrmModule.forFeature([page_entity_1.PageEntity]),
            typeorm_1.TypeOrmModule.forFeature([site_version_entity_1.SiteVersionEntity]),
            typeorm_1.TypeOrmModule.forFeature([page_version_entity_1.PageVersionEntity]),
            (0, common_1.forwardRef)(() => page_module_1.PageModule),
            site_module_1.SiteModule,
        ],
        controllers: [version_history_controller_1.VersionHistoryController],
        providers: [version_history_service_1.VersionHistoryService],
        exports: [version_history_service_1.VersionHistoryService],
    })
], VersionHistoryModule);
//# sourceMappingURL=version-history.module.js.map