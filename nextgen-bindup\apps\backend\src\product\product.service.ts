import { Injectable } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { ProductEntity } from './entities/product.entity';
import { QueryRunner, Repository, In } from 'typeorm';
import { AppException } from 'src/common/exceptions/app.exception';
import { PaginatedResponse } from 'src/common/paginated-response';
import { ProductType, ProductVariantType } from './enum/product.enum';
import { ValidateException } from 'src/common/exceptions/validate.exception';
import { DataSource } from 'typeorm';
import { ProductVariantQuantity } from './dto/product-variant.dto';
import { ProductStockEntity } from 'src/product-stocks/entities/product-stock.entity';
import { ProductStocksService } from 'src/product-stocks/product-stocks.service';
import {
  isEmptyNumber,
  isInteger,
  MAX_PRICE_VALUE,
} from 'src/utils/common.util';
import { CsvService } from './csv.service';
import * as fs from 'fs';
import Stream from 'stream';
import { GetProductsQueryDto } from './dto/get-product.dto';

@Injectable()
export class ProductService {
  @InjectRepository(ProductEntity)
  readonly productRepo: Repository<ProductEntity>;

  constructor(
    @InjectDataSource() private dataSource: DataSource,
    private readonly productStockService: ProductStocksService,
    private readonly csvService: CsvService,
  ) {}

  async getAllProducts(siteId: number): Promise<ProductEntity[]> {
    const products = await this.productRepo.find({
      where: { siteId: siteId, isDeleted: null },
      order: { createdAt: 'DESC' },
    });

    const quantities: ProductStockEntity[] =
      await this.productStockService.getBySite(siteId);
    const productQuantities: { [key: number]: ProductVariantQuantity[] } = {};
    quantities.forEach(stock => {
      if (!productQuantities[stock.productId]) {
        productQuantities[stock.productId] = [];
      }
      productQuantities[stock.productId].push(stock);
    });

    products.forEach(product => {
      product.variants.quantities = productQuantities[product.id] || [];
    });

    return products;
  }

  async searchProducts(
    siteId: number,
    page: number,
    limit: number,
    search?: string,
    productType?: string,
    isOrderable?: boolean,
  ): Promise<PaginatedResponse<ProductEntity>> {
    const queryBuilder = this.productRepo.createQueryBuilder('products');
    queryBuilder.where('products.siteId = :siteId', { siteId });
    queryBuilder.andWhere('products.isDeleted IS NULL');

    // Add search filter
    if (search && search.trim()) {
      queryBuilder.andWhere(
        '(products.name ILIKE :search OR products.code ILIKE :search OR products.title ILIKE :search)',
        { search: `%${search.trim()}%` },
      );
    }

    // Add product type filter
    if (productType) {
      queryBuilder.andWhere('products.productType = :productType', {
        productType,
      });
    }

    // Add orderable filter
    if (isOrderable !== undefined) {
      queryBuilder.andWhere('products.isOrderable = :isOrderable', {
        isOrderable,
      });
    }

    queryBuilder
      .orderBy('products.updatedAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const [products, total] = await queryBuilder.getManyAndCount();

    const productIds: number[] = products.map(product => product.id);
    const quantities: ProductStockEntity[] =
      await this.productStockService.getByProductIds(siteId, productIds);
    const productQuantities: { [key: number]: ProductVariantQuantity[] } = {};
    quantities.forEach(stock => {
      if (!productQuantities[stock.productId]) {
        productQuantities[stock.productId] = [];
      }
      productQuantities[stock.productId].push(stock);
    });

    products.forEach(product => {
      product.variants.quantities = productQuantities[product.id] || [];
    });

    return {
      data: products,
      total: total,
      page: page,
      limit: limit,
      totalPage: Math.ceil(total / limit),
    };
  }

  private async updateProductStocks(
    queryRunner: QueryRunner,
    siteId: number,
    productId: number,
    variantQuantites: ProductVariantQuantity[],
  ): Promise<void> {
    const now: Date = new Date();
    const stocks: ProductStockEntity[] = await queryRunner.manager
      .getRepository(ProductStockEntity)
      .find({
        where: { productId: productId },
      });

    for (const variant of variantQuantites) {
      const stock: ProductStockEntity = stocks.find(
        stock => stock.x === variant.x && stock.y === variant.y,
      );

      if (stock) {
        stock.quantity = variant.quantity;
        this.productStockService.validateProductStockData(stock);
        await queryRunner.manager
          .getRepository(ProductStockEntity)
          .update(stock.id, {
            quantity: variant.quantity,
            updatedAt: now,
          });
      } else {
        const newStock = new ProductStockEntity();
        newStock.productId = productId;
        newStock.siteId = siteId;
        newStock.x = variant.x;
        newStock.y = variant.y;
        newStock.quantity = variant.quantity;
        newStock.createdAt = now;
        newStock.updatedAt = now;
        this.productStockService.validateProductStockData(newStock);
        await queryRunner.manager
          .getRepository(ProductStockEntity)
          .save(newStock);
      }
    }
  }

  private async validateProductData(
    product: Partial<ProductEntity>,
    isUpdate = false,
    currentId?: number,
    ignoreValidateFileDownload?: boolean,
  ) {
    if (!product.code) {
      throw new ValidateException(
        'cart_management.product.error.code.required',
      );
    } else {
      const existingProduct = await this.productRepo.findOneBy({
        siteId: product.siteId,
        code: product.code,
      });

      if (existingProduct && (!isUpdate || existingProduct.id !== currentId)) {
        throw new ValidateException(
          'cart_management.product.error.code.duplicated',
        );
      }

      if (product.code.length > 50) {
        throw new ValidateException(
          'cart_management.product.error.code.max_length',
        );
      }

      if (product.code.includes(' ')) {
        throw new ValidateException(
          'cart_management.product.error.code.contain_space',
        );
      }
      const codeRegex = /^[a-zA-Z0-9-_]+$/;
      if (!codeRegex.test(product.code)) {
        throw new ValidateException(
          'cart_management.product.error.code.contain_special_characters',
        );
      }
    }

    if (!product.name) {
      throw new ValidateException(
        'cart_management.product.error.name.required',
      );
    }
    if (product.name.length > 100) {
      throw new ValidateException(
        'cart_management.product.error.name.max_length',
      );
    }

    if (product.title?.length > 100) {
      throw new ValidateException(
        'cart_management.product.error.title.max_length',
      );
    }

    if (product.images?.length > 6) {
      throw new ValidateException(
        'cart_management.product.error.images.max_length',
      );
    }

    if (isEmptyNumber(product.price)) {
      throw new ValidateException(
        'cart_management.product.error.price.required',
      );
    }

    if (!isInteger(product.price, { min: 0, max: MAX_PRICE_VALUE })) {
      throw new ValidateException(
        'cart_management.product.error.price.invalid',
      );
    }

    if (
      !isEmptyNumber(product.sale) &&
      !isInteger(product.sale, { min: 0, max: MAX_PRICE_VALUE })
    ) {
      throw new ValidateException('cart_management.product.error.sale.invalid');
    }

    if (
      !ignoreValidateFileDownload &&
      product.productType === ProductType.DIGITAL &&
      !product.fileDownload
    ) {
      throw new ValidateException(
        'cart_management.product.error.fileDownload.required',
      );
    }

    //== validate variant =====================================
    switch (product.productVariantType) {
      case ProductVariantType.NO_VARIANT: {
        const quantity = product.variants.quantities.find(
          v => v.x === 0 && v.y === 0,
        );

        if (!quantity || quantity.quantity === null) {
          throw new ValidateException(
            'cart_management.product.error.quantity.required',
          );
        }
        break;
      }

      case ProductVariantType.ONE_VARIANT: {
        if (product.variants.variantName1.trim().length === 0) {
          throw new ValidateException(
            'cart_management.product.error.variant_name.required',
          );
        }

        for (let i = 0; i < product.variants.attributes1.length; i++) {
          const attrLen = product.variants.attributes1[i].trim().length;
          const quantity = product.variants.quantities.find(
            v => v.x === i && v.y === 0,
          );
          if (attrLen === 0 && !quantity?.quantity) continue;

          if (attrLen > 0 && (!quantity || quantity.quantity === null)) {
            throw new ValidateException(
              'cart_management.product.error.stock.quantity.required',
            );
          } else if (attrLen === 0 && quantity?.quantity) {
            throw new ValidateException(
              'cart_management.product.error.variant_name.required',
            );
          }
        }
        break;
      }

      case ProductVariantType.TWO_VARIANT: {
        if (product.variants.variantName1.trim().length === 0) {
          throw new ValidateException(
            'cart_management.product.error.variant_name.required',
          );
        }

        if (product.variants.variantName2.trim().length === 0) {
          throw new ValidateException(
            'cart_management.product.error.variant_name.required',
          );
        }

        for (let i = 0; i < product.variants.attributes1.length; i++) {
          const attr1Len = product.variants.attributes1[i].trim().length;
          for (let j = 0; j < product.variants.attributes2.length; j++) {
            const attr2Len = product.variants.attributes2[j].trim().length;
            const quantity = product.variants.quantities.find(
              v => v.x === i && v.y === j,
            );

            if (attr1Len === 0 && attr2Len === 0 && !quantity?.quantity)
              continue;

            if (quantity?.quantity) {
              if (attr1Len === 0 || attr2Len === 0) {
                throw new ValidateException(
                  'cart_management.product.error.variant_name.required',
                );
              }
            }
          }
        }
      }
    }
  }

  async create(productEntity: ProductEntity): Promise<ProductEntity> {
    const now: Date = new Date();

    const queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await this.validateProductData(productEntity);

      const variantQuantites: ProductVariantQuantity[] = structuredClone(
        productEntity.variants.quantities,
      );
      delete productEntity.variants.quantities;

      let product = new ProductEntity();
      product.siteId = productEntity.siteId;
      product.isOrderable = productEntity.isOrderable;
      product.code = productEntity.code;
      product.name = productEntity.name;
      product.name = productEntity.name;
      product.title = productEntity.title;
      product.description = productEntity.description;
      product.images = productEntity.images;
      product.price = productEntity.price;
      product.sale = productEntity.sale;
      product.purchaseLimitQuantity = productEntity.purchaseLimitQuantity;
      product.individualShippingCharges =
        productEntity.individualShippingCharges;
      product.fileDownload = productEntity.fileDownload;
      product.unlimitedPurchase = productEntity.unlimitedPurchase;
      product.productType = productEntity.productType;
      product.productVariantType = productEntity.productVariantType;
      product.variants = productEntity.variants;
      product.createdAt = now;
      product.updatedAt = now;
      product.priceLabel = productEntity.priceLabel;
      product.saleLabel = productEntity.saleLabel;
      product = await queryRunner.manager
        .getRepository(ProductEntity)
        .save(product);

      await this.updateProductStocks(
        queryRunner,
        product.siteId,
        product.id,
        variantQuantites,
      );

      await queryRunner.commitTransaction();
      product.variants.quantities = variantQuantites;
      return product;
    } catch (e) {
      console.log(e);
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async update(
    id: number,
    productData: Partial<ProductEntity>,
  ): Promise<ProductEntity> {
    const product = await this.productRepo.findOneBy({ id: id });
    if (!product) throw new AppException('api.error.product_not_found');

    await this.validateProductData(productData, true, id);

    const queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const variantQuantites: ProductVariantQuantity[] = structuredClone(
        productData.variants.quantities,
      );

      delete productData.id;
      delete productData.variants.quantities;
      productData.updatedAt = new Date();

      await queryRunner.manager
        .getRepository(ProductEntity)
        .update(id, productData);

      await this.updateProductStocks(
        queryRunner,
        product.siteId,
        product.id,
        variantQuantites,
      );

      await queryRunner.commitTransaction();
      productData.variants.quantities = variantQuantites;
      return { ...product, ...productData };
    } catch (e) {
      console.log(e);
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async findById(id: number): Promise<ProductEntity> {
    const product: ProductEntity = await this.productRepo.findOneBy({ id });

    const quantities: ProductStockEntity[] =
      await this.productStockService.getByProduct(product.siteId, product.id);
    product.variants.quantities = quantities || [];

    return product;
  }

  async duplicate(id: number): Promise<ProductEntity> {
    const originalProduct = await this.findById(id);
    if (!originalProduct) {
      throw new AppException('api.error.product_not_found');
    }

    // Create a copy of the product with modifications
    const duplicatedProduct: ProductEntity = {
      ...originalProduct,
      id: undefined, // Remove ID to create new product
      code: await this.generateUniqueCode(
        originalProduct.code,
        originalProduct.siteId,
      ),
      name: `${originalProduct.name} - Copy`,
      createdAt: undefined,
      updatedAt: undefined,
      isDeleted: false,
    };

    // Create the duplicated product
    return await this.create(duplicatedProduct);
  }

  private async generateUniqueCode(
    originalCode: string,
    siteId: number,
  ): Promise<string> {
    let counter = 1;
    let newCode = `${originalCode}-copy`;

    // Check if the code already exists and increment counter if needed
    while (await this.productRepo.findOneBy({ siteId, code: newCode })) {
      counter++;
      newCode = `${originalCode}-copy-${counter}`;
    }

    return newCode;
  }

  async delete(id: number): Promise<boolean> {
    const product = await this.productRepo.findOneBy({ id });
    if (!product) throw new AppException('api.error.product_not_found');

    await this.productRepo.update(id, {
      isDeleted: true,
      updatedAt: new Date(),
    });

    return true;

    // const queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    // await queryRunner.connect();
    // await queryRunner.startTransaction();

    // try {
    //   await queryRunner.manager.getRepository(ProductEntity).delete(id);

    //   await queryRunner.manager.getRepository(ProductStockEntity).delete({
    //     productId: product.id,
    //   });

    //   await queryRunner.commitTransaction();
    // } catch (e) {
    //   console.log(e);
    //   await queryRunner.rollbackTransaction();
    //   throw e;
    // } finally {
    //   await queryRunner.release();
    // }
    // return true;
  }

  async findByIds(siteId: number, ids: number[]): Promise<ProductEntity[]> {
    const products: ProductEntity[] = await this.productRepo.find({
      where: { siteId: siteId, id: In(ids), isDeleted: null },
    });
    return products;
  }

  async uploadCSV(siteId: number, file: Express.Multer.File) {
    const columns = [
      '商品ID', //商品ID
      '注文可能', //IsOrderable
      '商品管理番号', //Code
      '商品名', //Name
      '商品説明（見出し）', //Title
      '商品説明（本文）', //Description
      '価格見出し', //PriceLabel
      '価格', //Price
      'セール価格見出し', //SaleLabel
      'セール価格', //Sale
      '購入制限数量', //PurchaseLimitQuantity
      '個別配送料', //IndividualShippingCharges
      '無制限購入', //UnlimitedPurchase
      '商品種別', //ProductType
    ];

    let results: any[];
    try {
      results = await this.csvService.parseCsv(file.path, columns);
    } finally {
      // Ensure the uploaded file is deleted even if parsing fails
      fs.unlinkSync(file.path);
    }

    if (!results || results.length === 0) {
      throw new ValidateException('cart_management.product.error.csv.empty');
    }

    const queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const productCodesInCsv = new Set<string>();

    try {
      for (const row of results) {
        const productId = row['商品ID']
          ? parseInt(row['商品ID'], 10)
          : undefined;
        const code = row['商品管理番号']
          ? String(row['商品管理番号']).trim()
          : '';
        const name = row['商品名'] ? String(row['商品名']).trim() : '';
        const title = row['商品説明（見出し）']
          ? String(row['商品説明（見出し）']).trim()
          : undefined;
        const description = row['商品説明（本文）']
          ? String(row['商品説明（本文）']).trim()
          : undefined;
        const priceLabel = row['価格見出し']
          ? String(row['価格見出し']).trim()
          : undefined;
        const saleLabel = row['セール価格見出し']
          ? String(row['セール価格見出し']).trim()
          : undefined;
        const price = row['価格'] ? parseInt(row['価格'], 10) : undefined;
        const sale = row['セール価格']
          ? parseInt(row['セール価格'], 10)
          : undefined;
        const purchaseLimitQuantity = row['購入制限数量']
          ? parseInt(row['購入制限数量'], 10)
          : undefined;
        const individualShippingCharges = row['個別配送料']
          ? parseInt(row['個別配送料'], 10)
          : undefined;
        const unlimitedPurchaseStr = row['無制限購入']
          ? String(row['無制限購入']).trim()
          : '';
        const productTypeStr = row['商品種別']
          ? String(row['商品種別']).trim().toUpperCase()
          : '';

        const isOrderableStr = row['注文可能']
          ? String(row['注文可能']).trim()
          : '';

        // --- Validation for each row ---

        // Validate Code uniqueness within CSV
        if (productCodesInCsv.has(code)) {
          throw new ValidateException(
            `cart_management.product.error.code.duplicated_in_csv: ${code}`,
          );
        }
        productCodesInCsv.add(code);

        // Validate ProductType
        if (
          productTypeStr &&
          ![ProductType.DIGITAL, ProductType.NORMAL].includes(
            productTypeStr as ProductType,
          )
        ) {
          throw new ValidateException(
            `cart_management.product.error.product_type.invalid: ${productTypeStr} for code ${code}`,
          );
        }
        const productType: ProductType = productTypeStr
          ? (productTypeStr as ProductType)
          : ProductType.NORMAL; // Default if empty

        // Validate UnlimitedPurchase and convert to boolean
        if (!['1', '0'].includes(unlimitedPurchaseStr)) {
          throw new ValidateException(
            `cart_management.product.error.unlimited_purchase.invalid: ${unlimitedPurchaseStr} for code ${code}`,
          );
        }
        const unlimitedPurchase: boolean = unlimitedPurchaseStr === '1';

        // Validate IsOrderable and convert to boolean
        if (!['1', '0'].includes(isOrderableStr)) {
          throw new ValidateException(
            `cart_management.product.error.is_orderable.invalid: ${isOrderableStr} for code ${code}`,
          );
        }
        const isOrderable: boolean = isOrderableStr === '1';

        // Basic validations (re-using existing validateProductData checks where applicable)
        const productDataPartial: Partial<ProductEntity> = {
          siteId: siteId,
          code: code,
          name: name,
          title: title,
          description: description,
          images: [], // Assuming images are not part of CSV for now, or need a specific parsing logic
          priceLabel: priceLabel,
          saleLabel: saleLabel,
          price: price,
          sale: sale ?? 0,
          purchaseLimitQuantity: purchaseLimitQuantity ?? 0,
          individualShippingCharges: individualShippingCharges ?? 0,
          fileDownload: undefined, // Assuming fileDownload is not directly from CSV
          unlimitedPurchase: unlimitedPurchase,
          productType: productType,
          productVariantType: ProductVariantType.NO_VARIANT,
          isOrderable: isOrderable,
          variants: {
            variantName1: 'Color',
            attributes1: ['White'],
            variantName2: 'Size',
            attributes2: ['XXL'],
            quantities: [
              {
                x: 0,
                y: 0,
                quantity: 0,
              },
            ],
          },
        };

        // Reuse existing validation logic
        await this.validateProductData(
          productDataPartial,
          !!productId,
          productId,
          true,
        );

        let existingProduct: ProductEntity | null = null;
        if (productId) {
          existingProduct = await queryRunner.manager
            .getRepository(ProductEntity)
            .findOneBy({ id: productId, siteId: siteId });
          if (!existingProduct) {
            throw new ValidateException(
              `cart_management.product.error.id.not_found: Product with ID ${productId} not found for update.`,
            );
          }
        }

        if (existingProduct) {
          // Update existing product
          delete productDataPartial.variants;
          delete productDataPartial.productVariantType;
          delete productDataPartial.images;
          delete productDataPartial.fileDownload;
          await queryRunner.manager
            .getRepository(ProductEntity)
            .update(productId, {
              ...productDataPartial,
              updatedAt: new Date(),
            });
        } else {
          // Insert new product
          let newProduct = new ProductEntity();
          Object.assign(newProduct, productDataPartial);

          newProduct.createdAt = new Date();
          newProduct.updatedAt = new Date();

          newProduct = await queryRunner.manager
            .getRepository(ProductEntity)
            .save(newProduct);

          const variantQuantites: ProductVariantQuantity[] = structuredClone(
            productDataPartial.variants.quantities,
          );

          await this.updateProductStocks(
            queryRunner,
            siteId,
            newProduct.id,
            variantQuantites,
          );
        }
      }

      await queryRunner.commitTransaction();
      return { success: true, message: 'Products uploaded successfully.' };
    } catch (e) {
      await queryRunner.rollbackTransaction();
      console.error('CSV Upload Error:', e);
      throw e; // Re-throw the validation or app exception
    } finally {
      await queryRunner.release();
    }
  }

  async getCSVTemplate(): Promise<Stream.Readable> {
    const headers = [
      '商品ID', //商品ID
      '注文可能', //IsOrderable
      '商品管理番号', //Code
      '商品名', //Name
      '商品説明（見出し）', //Title
      '商品説明（本文）', //Description
      '価格見出し', //PriceLabel
      '価格', //Price
      'セール価格見出し', //SaleLabel
      'セール価格', //Sale
      '購入制限数量', //PurchaseLimitQuantity
      '個別配送料', //IndividualShippingCharges
      '無制限購入', //UnlimitedPurchase
      '商品種別', //ProductType
    ];

    return await this.csvService.exportToCsv([], headers);
  }

  async downloadCSV(
    siteId: number,
    query?: GetProductsQueryDto,
  ): Promise<Stream.Readable> {
    const queryBuilder = this.productRepo.createQueryBuilder('products');
    queryBuilder.where('products.siteId = :siteId', { siteId });
    queryBuilder.andWhere('products.isDeleted IS NULL');

    // Add search filter
    if (query?.search?.trim()) {
      queryBuilder.andWhere(
        '(products.name ILIKE :search OR products.code ILIKE :search OR products.title ILIKE :search)',
        { search: `%${query.search.trim()}%` },
      );
    }

    // Add product type filter
    if (query?.productType) {
      queryBuilder.andWhere('products.productType = :productType', {
        productType: query.productType,
      });
    }

    // Add orderable filter
    if (query?.isOrderable !== undefined) {
      queryBuilder.andWhere('products.isOrderable = :isOrderable', {
        isOrderable: query.isOrderable,
      });
    }

    const products = await queryBuilder.getMany();

    const headers = [
      '商品ID', //商品ID
      '注文可能', //IsOrderable
      '商品管理番号', //Code
      '商品名', //Name
      '商品説明（見出し）', //Title
      '商品説明（本文）', //Description
      '価格見出し', //PriceLabel
      '価格', //Price
      'セール価格見出し', //SaleLabel
      'セール価格', //Sale
      '購入制限数量', //PurchaseLimitQuantity
      '個別配送料', //IndividualShippingCharges
      '無制限購入', //UnlimitedPurchase
      '商品種別', //ProductType
    ];

    const rows = products.map(product => ({
      商品ID: product.id,
      注文可能: product.isOrderable ? '1' : '0',
      商品管理番号: product.code,
      商品名: product.name,
      '商品説明（見出し）': product.title || '',
      '商品説明（本文）': product.description || '',
      価格見出し: product.priceLabel || '',
      価格: product.price,
      セール価格見出し: product.saleLabel || '',
      セール価格: product.sale || '',
      購入制限数量: product.purchaseLimitQuantity || '',
      個別配送料: product.individualShippingCharges || '',
      無制限購入: product.unlimitedPurchase ? '1' : '0',
      商品種別: product.productType,
    }));

    return await this.csvService.exportToCsv(rows, headers);
  }
}
