import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as AWS from 'aws-sdk';

@Injectable()
export class R2StorageService {
  private s3: AWS.S3;
  private bucketName = 'weblife';
  private bucketNamePrivate = 'weblife-private';

  constructor(private readonly configService: ConfigService) {
    this.s3 = new AWS.S3({
      endpoint: `https://${this.configService.get('CLOUDFLARE_ACCOUNT_ID')}.r2.cloudflarestorage.com`,
      accessKeyId: `${this.configService.get('CLOUDFLARE_ACCESS_KEY_ID')}`,
      secretAccessKey: `${this.configService.get('CLOUDFLARE_SECRECT_ACCESS_KEY_ID')}`,
      region: 'auto',
      signatureVersion: 'v4',
      s3ForcePathStyle: true,
    });
  }

  async generatePresignedUrl(fileName: string): Promise<string> {
    const bucket = this.bucketName;
    const params = {
      Bucket: bucket,
      Key: fileName,
      Expires: 60 * 5,
      ContentType: 'application/octet-stream',
    };

    try {
      const url = await this.s3.getSignedUrlPromise('putObject', params);
      return url;
    } catch {
      throw new Error('Failed to generate presigned URL');
    }
  }

  async generatePresignedUrlForUploadPrivate(
    fileName: string,
  ): Promise<string> {
    const params = {
      Bucket: this.bucketNamePrivate,
      Key: fileName,
      Expires: 60 * 5,
      ContentType: 'application/octet-stream',
    };

    try {
      const url = await this.s3.getSignedUrlPromise('putObject', params);
      return url;
    } catch {
      throw new Error('Failed to generate presigned URL');
    }
  }
}
