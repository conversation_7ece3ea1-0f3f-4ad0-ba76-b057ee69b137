import { useEffect, type FC, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import SearchIcon from '@mui/icons-material/Search';
import { debounce } from '@mui/material';
import Box from '@mui/material/Box';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { useAuth } from '../../auth';
import { SiteEntity } from '../../dto/site.type';
import { UserInfoEntity } from '../../dto/user-info.type';
import { siteService } from '../../services/site-service';
import { userInfoService } from '../../services/user-info.service';
import SiteCard from './SiteCard';

const RecentlySiteList: FC = () => {
  const { session } = useAuth();
  const { t } = useTranslation();
  const [sites, setSites] = useState<SiteEntity[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');

  const [filteredSites, setFilteredSites] = useState<SiteEntity[]>([]);
  useEffect(() => {
    debounceFilter(sites, searchTerm);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm, sites]);

  const filterItems = (items: SiteEntity[], searchTerm: string) => {
    if (!searchTerm) {
      setFilteredSites(items);
      return;
    }

    const text: string = searchTerm.toLowerCase();
    const filteredItems = items.filter(item =>
      item.managementName.toLowerCase().includes(text),
    );
    setFilteredSites(filteredItems);
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceFilter = useCallback(
    debounce((items, searchTerm) => filterItems(items, searchTerm), 500),
    [],
  );

  useEffect(() => {
    fetchSites();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchSites = async () => {
    if (!session) {
      setSites([]);
      return;
    }

    const user: UserInfoEntity | null = await userInfoService.findByUserId();
    if (!user || !user.recentlySite || user.recentlySite.length === 0) {
      setSites([]);
      return;
    }

    const data = await siteService.findByIds(user.recentlySite);
    setSites(data);
  };

  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="h4">{t('sidebar.recently_used_sites')}</Typography>
        <Box sx={{ display: 'inherit', gap: 2 }}>
          <TextField
            size="small"
            placeholder={t('common.search')}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              },
            }}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </Box>
      </Box>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
        {filteredSites.map(site => (
          <SiteCard key={site.id} data={site} />
        ))}
      </Box>
    </>
  );
};

export default RecentlySiteList;
