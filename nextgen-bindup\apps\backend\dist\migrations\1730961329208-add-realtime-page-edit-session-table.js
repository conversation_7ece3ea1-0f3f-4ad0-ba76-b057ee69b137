"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddRealtimePageEditSessionTable1730961329208 = void 0;
class AddRealtimePageEditSessionTable1730961329208 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}page_edit_sessions`;
    }
    async up(queryRunner) {
        await queryRunner.query(`alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
    async down(queryRunner) {
        await queryRunner.query(`alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.AddRealtimePageEditSessionTable1730961329208 = AddRealtimePageEditSessionTable1730961329208;
//# sourceMappingURL=1730961329208-add-realtime-page-edit-session-table.js.map