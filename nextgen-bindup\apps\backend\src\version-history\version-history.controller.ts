import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
} from '@nestjs/common';
import { VersionHistoryService } from './version-history.service';
import { SiteVersionDto } from './version-history.dto';
import { SiteVersionEntity } from './entities/site-version.entity';

@Controller('version-history')
export class VersionHistoryController {
  constructor(private readonly versionHistoryService: VersionHistoryService) {}

  @Get('get-site-version/:siteVersionId')
  async getSiteVersion(
    @Param('siteVersionId', ParseIntPipe) siteVersionId: number,
  ) {
    return this.versionHistoryService.getSiteVersion(siteVersionId);
  }

  @Delete('delete-site-version/:siteVersionId')
  async deleteSiteVersion(
    @Param('siteVersionId', ParseIntPipe) siteVersionId: number,
  ) {
    return this.versionHistoryService.deleteSiteVersion(siteVersionId);
  }

  @Post('update-version-name/:siteVersionId')
  async updateVersionName(
    @Param('siteVersionId', ParseIntPipe) siteVersionId: number,
    @Body('versionName') versionName: string,
  ) {
    return this.versionHistoryService.updateVersionName(
      siteVersionId,
      versionName,
    );
  }

  @Get('list-site/:siteId')
  async getSiteVersions(
    @Param('siteId') siteId: number,
    @Query('year') year?: number,
    @Query('month') month?: number,
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('pageSize', ParseIntPipe) pageSize: number = 100,
  ): Promise<SiteVersionDto> {
    return this.versionHistoryService.getSiteVersions(
      siteId,
      page,
      pageSize,
      +year,
      +month,
    );
  }

  @Get('list-page/:siteVersionId')
  async getPagesBySiteVersionId(
    @Param('siteVersionId', ParseIntPipe) siteVersionId: number,
  ) {
    return this.versionHistoryService.getPagesBySiteVersionId(siteVersionId);
  }

  @Get('page-detail/:siteVersionId/:pageId')
  async getPageBySiteVersionId(
    @Param('siteVersionId', ParseIntPipe) siteVersionId: number,
    @Param('pageId', ParseIntPipe) pageId: number,
  ) {
    return this.versionHistoryService.getPageBySiteVersionId(
      siteVersionId,
      pageId,
    );
  }

  @Post('backup-site/:siteId')
  async backupSite(
    @Param('siteId') siteId: number,
    @Body('name') name: string = '',
  ): Promise<SiteVersionEntity> {
    return this.versionHistoryService.backupSite(siteId, name);
  }

  @Post('restore/site/:siteId/:siteVersionId')
  async restoreSite(
    @Param('siteId', ParseIntPipe) siteId: number,
    @Param('siteVersionId', ParseIntPipe) siteVersionId: number,
  ): Promise<boolean> {
    return this.versionHistoryService.restore(siteId, siteVersionId);
  }
}
