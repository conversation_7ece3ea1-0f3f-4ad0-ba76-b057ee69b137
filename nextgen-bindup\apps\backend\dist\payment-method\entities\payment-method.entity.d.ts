export declare class PaymentMethodEntity {
    id: number;
    siteId: number;
    bankTransfer: {
        isEnabled: boolean;
        bankAccount: string;
        description: string;
    };
    postalTransfer: {
        isEnabled: boolean;
        bankAccount: string;
        description: string;
    };
    cashOnDelivery: {
        isEnabled: boolean;
        description: string;
        fee: {
            fromAmount: number;
            codFee: number;
        }[];
    };
    stripePaymentGateway: {
        isEnabled: boolean;
        description: string;
        stripeAccountId: string;
    };
    createdAt: Date;
    updatedAt: Date;
}
