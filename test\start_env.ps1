# NextGen BindUp - Complete Environment Setup and Start Script
# Run from the test folder to keep everything contained

param(
    [switch]$Help,
    [switch]$CheckOnly,
    [switch]$StopOnly
)

# Configuration
$ProjectRoot = "../nextgen-bindup"
$Services = @(
    @{Name="Backend"; Path="$ProjectRoot/apps/backend"; Port=4000; URL="http://localhost:4000"; EnvFile="backend.env"},
    @{Name="Live Editor"; Path="$ProjectRoot/apps/live-editor"; Port=3000; URL="http://localhost:3000"; EnvFile="live-editor.env"},
    @{Name="Design Editor"; Path="$ProjectRoot/apps/design-editor"; Port=5173; URL="http://localhost:5173"; EnvFile="design-editor.env"},
    @{Name="Dashboard"; Path="$ProjectRoot/apps/dashboard"; Port=5174; URL="http://localhost:5174"; EnvFile="dashboard.env"},
    @{Name="SSG"; Path="$ProjectRoot/apps/ssg"; Port=4321; URL="http://localhost:4321"; EnvFile=""}
)

if ($Help) {
    Write-Host ""
    Write-Host "NextGen BindUp - Environment Setup and Start Script" -ForegroundColor Green
    Write-Host "===================================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "USAGE (run from test folder):" -ForegroundColor Cyan
    Write-Host "  .\start_env.ps1                # Setup and start everything (default)"
    Write-Host "  .\start_env.ps1 -CheckOnly     # Only check service status"
    Write-Host "  .\start_env.ps1 -StopOnly      # Only stop all services"
    Write-Host "  .\start_env.ps1 -Help          # Show this help"
    Write-Host ""
    Write-Host "DEFAULT BEHAVIOR (no parameters):" -ForegroundColor Cyan
    Write-Host "  1. Check prerequisites (Node.js, Yarn)"
    Write-Host "  2. Create test directories and environment files"
    Write-Host "  3. Install all dependencies"
    Write-Host "  4. Start all services"
    Write-Host ""
    Write-Host "SERVICE URLS (after starting):" -ForegroundColor Cyan
    foreach ($service in $Services) {
        Write-Host "  $($service.Name): $($service.URL)" -ForegroundColor White
    }
    Write-Host ""
    Write-Host "NOTE: All test files and configurations are kept within the test folder." -ForegroundColor Cyan
    Write-Host "      The source code in nextgen-bindup remains untouched except for .env files."
    Write-Host ""
    exit 0
}

if ($CheckOnly) {
    Write-Host ""
    Write-Host "Checking Service Status" -ForegroundColor Green
    Write-Host "=======================" -ForegroundColor Green
    
    $allRunning = $true
    foreach ($service in $Services) {
        try {
            $response = Invoke-WebRequest -Uri $service.URL -TimeoutSec 3 -ErrorAction Stop
            Write-Host "✅ $($service.Name) is running on $($service.URL)" -ForegroundColor Green
        } catch {
            Write-Host "❌ $($service.Name) is not running on $($service.URL)" -ForegroundColor Red
            $allRunning = $false
        }
    }
    
    if ($allRunning) {
        Write-Host ""
        Write-Host "SUCCESS: All services are running!" -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "WARNING: Some services are not running. Run .\start_env.ps1 to start them." -ForegroundColor Yellow
    }
    exit 0
}

if ($StopOnly) {
    Write-Host ""
    Write-Host "Stopping All Services" -ForegroundColor Green
    Write-Host "=====================" -ForegroundColor Green
    
    foreach ($service in $Services) {
        try {
            $processes = Get-NetTCPConnection -LocalPort $service.Port -ErrorAction SilentlyContinue | 
                         Select-Object -ExpandProperty OwningProcess | 
                         Get-Process -Id { $_ } -ErrorAction SilentlyContinue
            
            if ($processes) {
                foreach ($process in $processes) {
                    Write-Host "Stopping $($service.Name) (PID: $($process.Id))..." -ForegroundColor Cyan
                    Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
                    Write-Host "✅ Stopped $($service.Name)" -ForegroundColor Green
                }
            }
        } catch {
            # Ignore errors when stopping
        }
    }
    
    Write-Host ""
    Write-Host "SUCCESS: All services stopped!" -ForegroundColor Green
    exit 0
}

# Default behavior: Setup and Start everything
Write-Host ""
Write-Host "NextGen BindUp - Complete Environment Setup and Start" -ForegroundColor Green
Write-Host "=====================================================" -ForegroundColor Green
Write-Host "Running from test folder - keeping everything contained" -ForegroundColor Cyan

# Check prerequisites
Write-Host ""
Write-Host "Checking prerequisites..." -ForegroundColor Cyan

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js is installed (version: $nodeVersion)" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js first:" -ForegroundColor Red
    Write-Host "   https://nodejs.org/" -ForegroundColor Cyan
    exit 1
}

# Check Yarn
try {
    $yarnVersion = yarn --version
    Write-Host "✅ Yarn is installed (version: $yarnVersion)" -ForegroundColor Green
} catch {
    Write-Host "❌ Yarn is not installed. Please install Yarn first:" -ForegroundColor Red
    Write-Host "   https://yarnpkg.com/getting-started/install" -ForegroundColor Cyan
    exit 1
}

# Check project directory
if (!(Test-Path $ProjectRoot)) {
    Write-Host "❌ Project directory not found: $ProjectRoot" -ForegroundColor Red
    Write-Host "   Make sure you're running this from the test folder" -ForegroundColor Cyan
    exit 1
}
Write-Host "✅ Project directory found" -ForegroundColor Green

# Setup environment
Write-Host ""
Write-Host "Setting up NextGen BindUp Test Environment" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

# Create test directories
Write-Host "Creating test directories..." -ForegroundColor Cyan
$directories = @("test-data", "test-results", "test-configs")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created $dir directory" -ForegroundColor Green
    } else {
        Write-Host "$dir directory already exists" -ForegroundColor Yellow
    }
}

# Create environment files
Write-Host "Creating environment configuration files in test-configs..." -ForegroundColor Cyan

# Live-editor environment
$liveEditorEnv = @(
    "NEXT_PUBLIC_SUPABASE_URL=https://qxgdiywidkdhfwnqhaxv.supabase.co",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.akAFcPxyaN4dQEP0ybm2b-UmcWUpv4JkONYDMb2GKRw",
    "NEXT_API_URL=http://localhost:4000"
)
if (!(Test-Path "test-configs/live-editor.env")) {
    $liveEditorEnv | Out-File -FilePath "test-configs/live-editor.env" -Encoding UTF8
    Write-Host "Created environment file: test-configs/live-editor.env" -ForegroundColor Green
}

# Design-editor environment
$designEditorEnv = @(
    "VITE_SUPABASE_URL=https://qxgdiywidkdhfwnqhaxv.supabase.co",
    "VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.akAFcPxyaN4dQEP0ybm2b-UmcWUpv4JkONYDMb2GKRw",
    "VITE_FONTS_API_KEY=AIzaSyBfadDQvcYbblL_YRVKUKpEgK11E-aUVWU",
    "VITE_API_URL=http://localhost:4000",
    "VITE_USING_API=0"
)
if (!(Test-Path "test-configs/design-editor.env")) {
    $designEditorEnv | Out-File -FilePath "test-configs/design-editor.env" -Encoding UTF8
    Write-Host "Created environment file: test-configs/design-editor.env" -ForegroundColor Green
}

# Backend environment
$backendEnv = @(
    "APP_JWT=5VuiEbDh4RsOaQVLQvcAgwlgc0BqCqhP",
    "CLOUDFLARE_API_TOKEN=****************************************",
    "CLOUDFLARE_ACCOUNT_ID=9fbbaf4a5d38283722a157b10c2ed59a",
    "SUPABASE_URL=https://qxgdiywidkdhfwnqhaxv.supabase.co",
    "SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.akAFcPxyaN4dQEP0ybm2b-UmcWUpv4JkONYDMb2GKRw",
    "DB_TYPE=postgres",
    "DB_HOST=db.qxgdiywidkdhfwnqhaxv.supabase.co",
    "DB_PORT=5432",
    "DB_USERNAME=postgres",
    "DB_PASSWORD=your_db_password_here",
    "DB_DATABASE=postgres"
)
if (!(Test-Path "test-configs/backend.env")) {
    $backendEnv | Out-File -FilePath "test-configs/backend.env" -Encoding UTF8
    Write-Host "Created environment file: test-configs/backend.env" -ForegroundColor Green
}

# Dashboard environment
$dashboardEnv = @(
    "VITE_API_URL=http://localhost:4000",
    "VITE_SUPABASE_URL=https://qxgdiywidkdhfwnqhaxv.supabase.co",
    "VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.akAFcPxyaN4dQEP0ybm2b-UmcWUpv4JkONYDMb2GKRw"
)
if (!(Test-Path "test-configs/dashboard.env")) {
    $dashboardEnv | Out-File -FilePath "test-configs/dashboard.env" -Encoding UTF8
    Write-Host "Created environment file: test-configs/dashboard.env" -ForegroundColor Green
}

# Install test dependencies
if (Test-Path "package.json") {
    Write-Host "Installing test dependencies..." -ForegroundColor Cyan
    yarn install | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Test dependencies installed" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Test dependencies installation had issues (continuing anyway)" -ForegroundColor Yellow
    }
}

# Install project dependencies with better error handling
Write-Host "Installing project dependencies..." -ForegroundColor Cyan

if (Test-Path $ProjectRoot) {
    Push-Location $ProjectRoot
    Write-Host "Installing root dependencies..." -ForegroundColor Cyan
    
    # Try different yarn install approaches based on version
    yarn install 2>$null

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Root dependencies installed successfully" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Root dependencies had some issues, trying alternative approach..." -ForegroundColor Yellow

        # Try with network timeout for Yarn 4.x
        yarn install --network-timeout 300000 2>$null

        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Root dependencies installed with extended timeout" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Root dependencies had some issues but continuing..." -ForegroundColor Yellow
            Write-Host "   This is often due to optional native dependencies that aren't critical" -ForegroundColor Cyan
            Write-Host "   Services will install their own dependencies during startup" -ForegroundColor Cyan
        }
    }
    Pop-Location
}

# Install app dependencies
foreach ($service in $Services) {
    if (Test-Path $service.Path) {
        Write-Host "Installing dependencies for $($service.Name)..." -ForegroundColor Cyan
        Push-Location $service.Path
        
        yarn install 2>$null

        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $($service.Name) dependencies installed" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $($service.Name) dependencies had issues (will retry during startup)" -ForegroundColor Yellow
        }
        Pop-Location
    } else {
        Write-Host "⚠️  $($service.Name) directory not found, skipping..." -ForegroundColor Yellow
    }
}

Write-Host "Dependency installation completed. Services will handle any missing dependencies automatically." -ForegroundColor Cyan

# Copy environment files and start services
Write-Host ""
Write-Host "Starting NextGen BindUp Services" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

Write-Host "Copying environment files from test-configs to project locations..." -ForegroundColor Cyan

foreach ($service in $Services) {
    if ($service.EnvFile -and (Test-Path $service.Path)) {
        $sourceEnv = "test-configs/$($service.EnvFile)"
        $targetEnv = "$($service.Path)/.env"

        if (Test-Path $sourceEnv) {
            Copy-Item $sourceEnv $targetEnv -Force
            Write-Host "Copied environment for $($service.Name)" -ForegroundColor Green
        }
    }
}

Write-Host "Starting services (dependencies will be installed automatically if needed)..." -ForegroundColor Cyan

# Start backend first
$backendService = $Services | Where-Object { $_.Name -eq "Backend" }
if ($backendService -and (Test-Path $backendService.Path)) {
    Write-Host "Starting $($backendService.Name)..." -ForegroundColor Cyan

    $startCommand = "cd '$($backendService.Path)'; if (!(Test-Path 'node_modules')) { Write-Host 'Installing dependencies...'; yarn install }; yarn dev"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", $startCommand -WindowStyle Normal
    Start-Sleep -Seconds 5
}

# Start other services
foreach ($service in $Services) {
    if ($service.Name -ne "Backend" -and (Test-Path $service.Path)) {
        Write-Host "Starting $($service.Name)..." -ForegroundColor Cyan

        $startCommand = "cd '$($service.Path)'; if (!(Test-Path 'node_modules')) { Write-Host 'Installing dependencies...'; yarn install }; yarn dev"
        Start-Process powershell -ArgumentList "-NoExit", "-Command", $startCommand -WindowStyle Normal
        Start-Sleep -Seconds 2
    }
}

Write-Host ""
Write-Host "SUCCESS: All services started!" -ForegroundColor Green
Write-Host "Service URLs:" -ForegroundColor Cyan
foreach ($service in $Services) {
    Write-Host "   $($service.Name): $($service.URL)" -ForegroundColor White
}
Write-Host ""
Write-Host "Environment is ready for development!" -ForegroundColor Green
Write-Host "All test files are contained within the test folder." -ForegroundColor Cyan
Write-Host ""
Write-Host "Note: Each service window will show its own dependency installation and startup process." -ForegroundColor Cyan
Write-Host "   If a service fails to start, check its individual window for error details." -ForegroundColor Cyan
Write-Host "   Use '.\start_env.ps1 -CheckOnly' in a few minutes to verify all services are running." -ForegroundColor Cyan
Write-Host ""
Write-Host "Use the following commands from the test folder:" -ForegroundColor Cyan
Write-Host "   .\start_env.ps1 -CheckOnly    # Check service status"
Write-Host "   .\start_env.ps1 -StopOnly     # Stop all services"
Write-Host "   .\start_env.ps1 -Help         # Show help"
