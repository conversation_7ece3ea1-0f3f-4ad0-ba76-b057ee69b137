"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SiteController = void 0;
const common_1 = require("@nestjs/common");
const site_service_1 = require("./site.service");
const site_entity_1 = require("./entities/site.entity");
const auth_guard_1 = require("../auth/auth.guard");
let SiteController = class SiteController {
    constructor(siteService) {
        this.siteService = siteService;
    }
    async create(siteEntity) {
        return await this.siteService.create(siteEntity);
    }
    async update(siteId, siteData) {
        return await this.siteService.update(+siteId, siteData);
    }
    async getUrl(siteId) {
        return await this.siteService.getURLSite(+siteId);
    }
    async getById(siteId) {
        return await this.siteService.findById(+siteId);
    }
    async getByProjectId(projectId) {
        return await this.siteService.findByProjectId(+projectId);
    }
    async getByProjectFolderId(projectId, projectFolderId) {
        if (!projectFolderId)
            return await this.siteService.findByProjectId(+projectId);
        return await this.siteService.findByProjectFolderId(+projectId, +projectFolderId);
    }
    async getByIds(data) {
        return await this.siteService.findByIds(data.ids);
    }
    async delete(siteId) {
        await this.siteService.updateArchive(+siteId, true);
        return true;
    }
    async clone(siteId) {
        return await this.siteService.clone(+siteId);
    }
};
exports.SiteController = SiteController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [site_entity_1.SiteEntity]),
    __metadata("design:returntype", Promise)
], SiteController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('update/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SiteController.prototype, "update", null);
__decorate([
    (0, common_1.Get)('get-site-url/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SiteController.prototype, "getUrl", null);
__decorate([
    (0, common_1.Get)('one/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SiteController.prototype, "getById", null);
__decorate([
    (0, common_1.Get)('project/:projectId'),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SiteController.prototype, "getByProjectId", null);
__decorate([
    (0, common_1.Get)('project-folder/:projectId/:projectFolderId'),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Param)('projectFolderId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SiteController.prototype, "getByProjectFolderId", null);
__decorate([
    (0, common_1.Post)('many'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SiteController.prototype, "getByIds", null);
__decorate([
    (0, common_1.Delete)(':siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SiteController.prototype, "delete", null);
__decorate([
    (0, common_1.Post)('clone/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SiteController.prototype, "clone", null);
exports.SiteController = SiteController = __decorate([
    (0, common_1.Controller)('sites'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [site_service_1.SiteService])
], SiteController);
//# sourceMappingURL=site.controller.js.map