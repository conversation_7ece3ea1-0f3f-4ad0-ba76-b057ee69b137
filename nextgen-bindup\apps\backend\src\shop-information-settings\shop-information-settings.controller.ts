import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { ShopInformationSettingService } from './shop-information-settings.service';
import { ShopInformationSettingEntity } from './entities/shop-information-settings.entity';

@Controller('shop-information-settings')
@UseGuards(AuthGuard)
export class ShopInformationSettingController {
  constructor(
    private readonly shopInformationSettingService: ShopInformationSettingService,
  ) {}

  @Post('create')
  async create(
    @Body() shopInformationSettingEntity: ShopInformationSettingEntity,
  ) {
    return await this.shopInformationSettingService.create(
      shopInformationSettingEntity,
    );
  }

  @Put('update/:id')
  async update(
    @Param('id') id: string,
    @Body() data: Partial<ShopInformationSettingEntity>,
  ) {
    return await this.shopInformationSettingService.update(+id, data);
  }

  @Get('one-by-site/:siteId')
  async getOneBySiteId(@Param('siteId') siteId: string) {
    return await this.shopInformationSettingService.findOneBySiteId(+siteId);
  }
}
