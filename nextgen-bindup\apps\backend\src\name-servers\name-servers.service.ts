import { Injectable } from '@nestjs/common';
import { NameServerEntity } from './entities/name-server.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { AppException } from 'src/common/exceptions/app.exception';

@Injectable()
export class NameServersService {
  @InjectRepository(NameServerEntity)
  readonly nameServerRepo: Repository<NameServerEntity>;

  async create(data: NameServerEntity): Promise<NameServerEntity> {
    delete data.id;

    const now = new Date();
    data.createdAt = now;
    data.updatedAt = now;

    return await this.nameServerRepo.save(data);
  }

  async update(
    id: number,
    data: Partial<NameServerEntity>,
  ): Promise<NameServerEntity> {
    const entity = await this.nameServerRepo.findOneBy({ id });
    if (!entity) throw new AppException('api.error.nameserver_not_found');

    const now = new Date();
    data.updatedAt = now;

    delete data.id;
    await this.nameServerRepo.update(id, data);
    return { ...entity, ...data };
  }

  async delete(id: number): Promise<boolean> {
    const entity = await this.nameServerRepo.findOneBy({ id });
    if (!entity) throw new AppException('api.error.nameserver_not_found');

    await this.nameServerRepo.delete(id);
    return true;
  }

  async findBySiteId(siteId: number): Promise<NameServerEntity[]> {
    return await this.nameServerRepo.findBy({ siteId });
  }

  async findByProjectId(projectId: number): Promise<NameServerEntity[]> {
    return await this.nameServerRepo.findBy({ projectId });
  }
}
