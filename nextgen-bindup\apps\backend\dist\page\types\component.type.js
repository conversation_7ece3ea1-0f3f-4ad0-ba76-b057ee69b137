"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComponentType = void 0;
var ComponentType;
(function (ComponentType) {
    ComponentType["Page"] = "page";
    ComponentType["Header"] = "header";
    ComponentType["Footer"] = "footer";
    ComponentType["Main"] = "main";
    ComponentType["LeftSide"] = "left-side";
    ComponentType["RightSide"] = "right-side";
    ComponentType["Block"] = "block";
    ComponentType["Columns"] = "columns";
    ComponentType["Text"] = "text";
    ComponentType["Heading"] = "heading";
    ComponentType["Link"] = "link";
    ComponentType["Button"] = "btn";
    ComponentType["Media"] = "media";
    ComponentType["Image"] = "image";
    ComponentType["Video"] = "video";
    ComponentType["TextInput"] = "txt-in";
    ComponentType["NumberInput"] = "num-in";
    ComponentType["PasswordInput"] = "pwd-in";
    ComponentType["TextAreaInput"] = "txt-area-in";
    ComponentType["CheckboxInput"] = "chk-in";
    ComponentType["Section"] = "section";
    ComponentType["ButtonGroup"] = "btn-group";
    ComponentType["HtmlVideo"] = "html-video";
    ComponentType["Html"] = "html";
    ComponentType["YoutubeVideo"] = "youtube-video";
    ComponentType["SelectInput"] = "select-in";
    ComponentType["DatetimePickerInput"] = "dt-picker-in";
    ComponentType["RadioGroupInput"] = "radio-group-in";
    ComponentType["Icon"] = "icon";
    ComponentType["AssetComponent"] = "asset-component";
    ComponentType["SlideShow"] = "slide-show";
    ComponentType["None"] = "";
    ComponentType["Component"] = "component";
    ComponentType["Grid"] = "grid";
    ComponentType["Form"] = "form";
    ComponentType["Table"] = "table";
    ComponentType["DataDetail"] = "data-detail";
    ComponentType["Card"] = "card";
    ComponentType["Chart"] = "chart";
    ComponentType["Calendar"] = "calendar";
    ComponentType["Collapse"] = "collapse";
    ComponentType["Accordion"] = "accordion";
    ComponentType["Alert"] = "alert";
    ComponentType["Marquee"] = "marquee";
    ComponentType["Slider"] = "slider";
    ComponentType["UploadInput"] = "upload-in";
    ComponentType["DialogInput"] = "dialog";
    ComponentType["ComponentAlias"] = "component-alias";
})(ComponentType || (exports.ComponentType = ComponentType = {}));
//# sourceMappingURL=component.type.js.map