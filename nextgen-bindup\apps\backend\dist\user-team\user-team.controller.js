"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTeamController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../auth/auth.guard");
const user_team_service_1 = require("./user-team.service");
let UserTeamController = class UserTeamController {
    constructor(userTeamService) {
        this.userTeamService = userTeamService;
    }
    async getAllByRootUserId(rootUserId) {
        return await this.userTeamService.getAllByRootUserId(rootUserId);
    }
    async getAllByTeamId(rootUserId, teamId) {
        return await this.userTeamService.getAllByTeamId(rootUserId, +teamId);
    }
    async getMemberInfoByTeamId(rootUserId, teamId) {
        return await this.userTeamService.getMemberInfoByTeamId(rootUserId, +teamId);
    }
    async getTeamsOfMember(rootUserId) {
        return await this.userTeamService.getTeamsOfMember(rootUserId);
    }
};
exports.UserTeamController = UserTeamController;
__decorate([
    (0, common_1.Get)('by-root/:rootUserId'),
    __param(0, (0, common_1.Param)('rootUserId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserTeamController.prototype, "getAllByRootUserId", null);
__decorate([
    (0, common_1.Get)('by-team/:rootUserId/:teamId'),
    __param(0, (0, common_1.Param)('rootUserId')),
    __param(1, (0, common_1.Param)('teamId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UserTeamController.prototype, "getAllByTeamId", null);
__decorate([
    (0, common_1.Get)('member-info-by-team/:rootUserId/:teamId'),
    __param(0, (0, common_1.Param)('rootUserId')),
    __param(1, (0, common_1.Param)('teamId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UserTeamController.prototype, "getMemberInfoByTeamId", null);
__decorate([
    (0, common_1.Get)('teams-by-member/:rootUserId'),
    __param(0, (0, common_1.Param)('rootUserId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserTeamController.prototype, "getTeamsOfMember", null);
exports.UserTeamController = UserTeamController = __decorate([
    (0, common_1.Controller)('user-team'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [user_team_service_1.UserTeamService])
], UserTeamController);
//# sourceMappingURL=user-team.controller.js.map