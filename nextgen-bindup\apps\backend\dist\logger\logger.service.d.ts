import { WinstonLoggerService } from './winston-logger.service';
export declare class LoggerService {
    private winstonLoggerService;
    constructor(winstonLoggerService: WinstonLoggerService);
    info(shop: string, message: string, meta?: any): Promise<void>;
    error(shop: string, message: string, meta?: any): Promise<void>;
    warn(shop: string, message: string, meta?: any): Promise<void>;
    debug(shop: string, message: string, meta?: any): Promise<void>;
}
