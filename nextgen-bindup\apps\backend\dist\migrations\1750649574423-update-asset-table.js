"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateAssetTable1750649574423 = void 0;
const typeorm_1 = require("typeorm");
class UpdateAssetTable1750649574423 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}assets`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'designState',
            type: 'jsonb',
            isNullable: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'designState');
    }
}
exports.UpdateAssetTable1750649574423 = UpdateAssetTable1750649574423;
//# sourceMappingURL=1750649574423-update-asset-table.js.map