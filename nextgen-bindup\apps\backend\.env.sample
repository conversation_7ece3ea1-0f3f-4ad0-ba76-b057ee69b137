# JWT
APP_JWT=5VuiEbDh4RsOaQVLQvcAgwlgc0BqCqhP

# CLOUDFLARE
CLOUDFLARE_ACCOUNT_ID=
CLOUDFLARE_ACCESS_KEY_ID=
CLOUDFLARE_SECRECT_ACCESS_KEY_ID=

# SUPABASE
SUPABASE_URL=
SUPABASE_KEY=

# DATABASE
DATABASE_TYPE=postgres
DATABASE_HOST=aws-0-ap-southeast-1.pooler.supabase.com
DATABASE_PORT=6543
DATABASE_NAME=postgres
DATABASE_USER=postgres.oweldyjojmpoetfiubsz
DATABASE_PASSWORD=A53@42WPJBuKxAoIEYdl!6lD
DATABASE_MIGRATIONS_RUN=true
DATABASE_SCHEMA=public
ENTITY_PREFIX=

#FTP
FTP_HOST=
FTP_USER=
FTP_PASSWORD=
SITE_URL=


#STRIPE
STRIPE_SECRET_KEY=sk_test_51RBs554YFlyyuJavt3cSpKo6YjnaVEZpQ5zQTHstzPpjH9BzPk1gnUeHJyd9OCkmoLFGqSTpU7DFZAdnXv1AdDp800w8SqOZDd
STRIPE_WEBHOOK_SECRET=whsec_063fcff80aef5f517471beff5b434fdeb72b5cc06945c44738bdc82773929347


#EMAIL
EMAIL_HOST=
EMAIL_PORT=
EMAIL_USER=
EMAIL_PASSWORD=

# TEMPLATE
TEST_TEMPLATE_PATH=/media/thanh-doan/data/project/BiND-NextGen/source-main/nextgen-bindup/templates/b02_002_pc/312c
TEST_TEMPLATE_PROJECT_ID=
TEST_TEMPLATE_SITE_ID=
TEST_TEMPLATE_PAGE_ID=