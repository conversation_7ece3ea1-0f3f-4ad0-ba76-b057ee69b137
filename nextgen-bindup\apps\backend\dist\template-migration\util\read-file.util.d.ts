import { Site0_Site } from '../dto/site0_site.dto';
import { Site1_Corner } from '../dto/site1_corner.dto';
import { Site2_Page } from '../dto/site2_page.dto';
import { Site3_Block } from '../dto/site3_block.dto';
import { Site4_BlockData } from '../dto/site4_blockdata.dto';
import { Site5_Resource } from '../dto/site5_resource.dto';
import { Site6_Srclist1 } from '../dto/site6_srclist1.dto';
import { SiteSharedCss } from '../dto/site_shared_css.dto';
import { Template_Site } from '../dto/template_site.dto';
export declare class ReadFileUtil {
    static readSharedCss(dbdata: string): SiteSharedCss[];
    static readSites(dbdata: string): Site0_Site[];
    static readCorners(dbdata: string): Site1_Corner[];
    static readPages(dbdata: string): Site2_Page[];
    static readBlocks(dbdata: string): Site3_Block[];
    static readBlockDatas(dbdata: string): Site4_BlockData[];
    static readSrcList1(dbdata: string): Site6_Srclist1[];
    static readResources(dbdata: string, srcList1: Site6_Srclist1[]): Site5_Resource[];
    static readTemplateSites(dbdata: string): Template_Site[];
}
