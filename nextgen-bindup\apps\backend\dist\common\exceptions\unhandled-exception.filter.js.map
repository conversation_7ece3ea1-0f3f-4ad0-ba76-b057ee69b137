{"version": 3, "file": "unhandled-exception.filter.js", "sourceRoot": "", "sources": ["../../../src/common/exceptions/unhandled-exception.filter.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAMwB;AAGxB,gEAA4D;AAGrD,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACnC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAE7D,KAAK,CAAC,SAAc,EAAE,IAAmB;QACvC,IAAI,CAAC;YACH,MAAM,GAAG,GAAsB,IAAI,CAAC,YAAY,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAa,GAAG,CAAC,WAAW,EAAY,CAAC;YACvD,MAAM,MAAM,GACV,SAAS,YAAY,sBAAa;gBAChC,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE;gBACvB,CAAC,CAAC,mBAAU,CAAC,WAAW,CAAC;YAE7B,IAAI,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBAC/B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,EAAE;oBAChE,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU;iBACjC,CAAC,CAAC;gBACH,QAAQ,CAAC,MAAM,CAAC,UAAU,GAAG,SAAS,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,WAAW,GAAW,uBAAuB,CAAC;YAClD,IACE,SAAS,CAAC,IAAI,KAAK,mBAAmB;gBACtC,SAAS,CAAC,IAAI,KAAK,cAAc,EACjC,CAAC;gBACD,WAAW,GAAG,SAAS,CAAC,OAAO,CAAC;YAClC,CAAC;YAED,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC;YACjD,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;QACtD,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;CACF,CAAA;AAjCY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,cAAK,GAAE;qCAEsC,8BAAa;GAD9C,wBAAwB,CAiCpC"}