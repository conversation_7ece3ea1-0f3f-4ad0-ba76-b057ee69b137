import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePagesTable1743410233712 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}pages`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}
      ALTER COLUMN type TYPE VARCHAR(15);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log(!!queryRunner);
  }
}
