"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PROP_MEDIA_IMAGE_DEFAULT_VALUE = void 0;
const PROP_MEDIA_IMAGE_DEFAULT_VALUE = (ts, prop) => ({
    type: 'image',
    url: '',
    position: {
        x: { unit: 'px', value: '' },
        y: { unit: 'px', value: '' },
        offsetX: { unit: 'px', value: '' },
        offsetY: { unit: 'px', value: '' },
    },
    alt: '',
    aspectRatio: 'auto',
    freeRatio: {
        width: '4',
        height: '3',
    },
    objectFit: 'fill',
    float: 'none',
    ts: ts,
    ...(prop || undefined),
});
exports.PROP_MEDIA_IMAGE_DEFAULT_VALUE = PROP_MEDIA_IMAGE_DEFAULT_VALUE;
//# sourceMappingURL=prop-media-default-value.js.map