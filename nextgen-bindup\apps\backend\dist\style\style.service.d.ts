import { StyleEntity } from './entities/style.entity';
import { Repository } from 'typeorm';
import { ProjectService } from 'src/project/project.service';
export declare class StyleService {
    private readonly projectService;
    readonly styleRepo: Repository<StyleEntity>;
    constructor(projectService: ProjectService);
    create(styleEntity: StyleEntity): Promise<StyleEntity>;
    update(id: number, styleData: Partial<StyleEntity>): Promise<StyleEntity>;
    findById(id: number): Promise<StyleEntity>;
    findByProjectId(projectId: number): Promise<StyleEntity[]>;
    findBySiteId(projectId: number, siteId: number): Promise<StyleEntity[]>;
    delete(id: number): Promise<boolean>;
}
