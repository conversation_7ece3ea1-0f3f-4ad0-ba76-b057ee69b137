export class PaymentMethodResponseDto {
  siteId: number;
  bankTransfer: {
    isEnabled: boolean;
    bankAccount: string;
    description: string;
  };
  postalTransfer: {
    isEnabled: boolean;
    bankAccount: string;
    description: string;
  };
  stripePaymentGateway: {
    isEnabled: boolean;
    description: string;
    stripeAccountId: string;
  };
  cashOnDelivery: {
    isEnabled: boolean;
    description: string;
    fee: {
      fromAmount: number;
      codFee: number;
    }[];
  };
}
