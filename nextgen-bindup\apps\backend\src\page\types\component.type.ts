export enum ComponentType {
  Page = 'page',
  Header = 'header',
  Footer = 'footer',
  Main = 'main',
  LeftSide = 'left-side',
  RightSide = 'right-side',
  //---------------------
  Block = 'block',
  Columns = 'columns',
  Text = 'text',
  Heading = 'heading',
  Link = 'link',
  Button = 'btn',
  Media = 'media',
  Image = 'image',
  Video = 'video',
  TextInput = 'txt-in',
  NumberInput = 'num-in',
  PasswordInput = 'pwd-in',
  TextAreaInput = 'txt-area-in',
  CheckboxInput = 'chk-in',
  Section = 'section',
  ButtonGroup = 'btn-group',
  HtmlVideo = 'html-video',
  Html = 'html',
  YoutubeVideo = 'youtube-video',
  SelectInput = 'select-in',
  DatetimePickerInput = 'dt-picker-in',
  RadioGroupInput = 'radio-group-in',
  Icon = 'icon',
  AssetComponent = 'asset-component',
  SlideShow = 'slide-show',
  //---------------------
  None = '',
  Component = 'component',
  Grid = 'grid',
  Form = 'form',
  Table = 'table',
  DataDetail = 'data-detail',
  Card = 'card',
  Chart = 'chart',
  Calendar = 'calendar',
  Collapse = 'collapse',
  Accordion = 'accordion',
  Alert = 'alert',
  Marquee = 'marquee',
  Slider = 'slider',
  UploadInput = 'upload-in',
  DialogInput = 'dialog',
  ComponentAlias = 'component-alias',
}
