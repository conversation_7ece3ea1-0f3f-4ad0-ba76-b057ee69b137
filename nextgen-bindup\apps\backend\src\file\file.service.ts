import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import {
  DeleteImageResponseDto,
  MetadataDto,
  PresignedUrlResponseDto,
} from './dto/file.dto';
import * as fs from 'fs';
import * as FormData from 'form-data';

@Injectable()
export class FileService {
  apiUrl: string;
  accountId: string;
  apiToken: string;

  constructor(private readonly configService: ConfigService) {
    this.apiUrl = `https://api.cloudflare.com/client/v4/accounts`;
    this.accountId = this.configService.get('CLOUDFLARE_ACCOUNT_ID');
    this.apiToken = this.configService.get('CLOUDFLARE_API_TOKEN');
  }

  async uploadImage(file: Express.Multer.File, metadata: any) {
    console.log('metadata', metadata);
    const formData = this.createFormData(file);

    try {
      const response = await this.sendImageToCloudFlare(formData);
      return response.data;
    } catch (error) {
      this.handleUploadError(error);
    } finally {
      this.removeFileLocal(file.path);
    }
  }

  async uploadVideo(file: Express.Multer.File, metadata: any) {
    console.log('metadata', metadata);
    const formData = this.createFormData(file);

    try {
      const response = await this.sendVideoToCloudFlare(formData);
      return response.data;
    } catch (error) {
      this.handleUploadError(error);
    } finally {
      this.removeFileLocal(file.path);
    }
  }

  private createFormData(file: any): FormData {
    const formData = new FormData();
    formData.append('file', fs.createReadStream(file.path), file.originalname);
    return formData;
  }

  private async sendVideoToCloudFlare(formData: FormData): Promise<any> {
    return axios.post(`${this.apiUrl}/${this.accountId}/stream`, formData, {
      headers: {
        Authorization: `Bearer ${this.apiToken}`,
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  private async sendImageToCloudFlare(formData: FormData): Promise<any> {
    return axios.post(`${this.apiUrl}/${this.accountId}/images/v1`, formData, {
      headers: {
        Authorization: `Bearer ${this.apiToken}`,
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  private handleUploadError(error: any): void {
    throw new HttpException(
      `Upload failed: ${error.message}`,
      HttpStatus.BAD_REQUEST,
    );
  }

  private removeFileLocal(filePath: string) {
    fs.unlink(filePath, err => {
      if (err) {
        console.error(`Failed to delete file: ${filePath}`, err);
      }
    });
  }

  async getImages(page: number, perPage: number, order: string) {
    try {
      const response = await axios.get(
        `${this.apiUrl}/${this.accountId}/images/v1`,
        {
          headers: {
            Authorization: `Bearer ${this.apiToken}`,
          },
          params: {
            page: page || 1,
            per_page: perPage || 10,
            order: order || 'uploaded',
          },
        },
      );
      return response.data;
    } catch (error) {
      throw new HttpException(
        `Failed to retrieve images: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async deleteImage(imageId: string): Promise<DeleteImageResponseDto> {
    try {
      const response = await axios.delete(
        `${this.apiUrl}/${this.accountId}/images/v1/${imageId}`,
        {
          headers: {
            Authorization: `Bearer ${this.apiToken}`,
          },
        },
      );
      return response.data;
    } catch (error) {
      throw new HttpException(
        'Error deleting image',
        error.response?.status || 500,
      );
    }
  }

  async createPresignedUrl(
    metadata: MetadataDto,
  ): Promise<PresignedUrlResponseDto> {
    const url = `${this.apiUrl}/${this.accountId}/images/v2/direct_upload`;
    try {
      const response = await axios.post(
        url,
        {
          metadata,
        },
        {
          headers: {
            Authorization: `Bearer ${this.apiToken}`,
          },
        },
      );
      return response.data;
    } catch (error) {
      throw new HttpException(
        'Error creating pre-signed URL',
        error.response?.status || 500,
      );
    }
  }
}
