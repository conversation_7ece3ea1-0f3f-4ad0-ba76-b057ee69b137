import { ConfigService } from '@nestjs/config';
import Strip<PERSON> from 'stripe';
import { OrderService } from 'src/order/order.service';
import { CreateOrderDto } from 'src/order/dto/create-order.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { ShopInformationSettingService } from 'src/shop-information-settings/shop-information-settings.service';
import { OrderCompletionSettingService } from 'src/order-complete-settings/order-complete-settings.service';
export declare class UserPaymentService {
    private readonly configService;
    private readonly orderService;
    private readonly paymentMethodService;
    private readonly shopInformationService;
    private readonly orderCompletionSettingService;
    private eventEmitter;
    private stripe;
    private readonly userInfoRepo;
    constructor(configService: ConfigService, orderService: OrderService, paymentMethodService: PaymentMethodService, shopInformationService: ShopInformationSettingService, orderCompletionSettingService: OrderCompletionSettingService, eventEmitter: EventEmitter2);
    private getOrderEmailContext;
    enableStripe(userId: string): Promise<{
        url: string;
    }>;
    checkStripeStatus(userId: string): Promise<{
        connected: boolean;
        id?: string;
        email?: string;
        name?: string;
        type?: string;
        stripeDashboardUrl?: string;
    }>;
    handleEventCheckoutSessionCompleted(metadata: Stripe.MetadataParam): Promise<void>;
    checkoutWithCreaditCard(domain: string, createOrderDto: CreateOrderDto): Promise<{
        url: string;
    }>;
    checkoutWithBankTransfer(createOrderDto: CreateOrderDto): Promise<{
        orderId: number;
    }>;
    checkoutWithCashOnDelivery(createOrderDto: CreateOrderDto): Promise<{
        orderId: number;
    }>;
    checkoutWithPostalTransfer(createOrderDto: CreateOrderDto): Promise<{
        orderId: number;
    }>;
    retryHandleWaitingPendingOrder(): Promise<void>;
}
