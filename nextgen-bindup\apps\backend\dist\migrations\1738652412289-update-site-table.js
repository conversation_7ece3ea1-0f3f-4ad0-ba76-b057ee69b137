"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSitesTable1738652412289 = void 0;
const typeorm_1 = require("typeorm");
class UpdateSitesTable1738652412289 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}sites`;
    }
    async up(queryRunner) {
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_sites_projectId',
            columnNames: ['projectId'],
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_sites_projectId');
    }
}
exports.UpdateSitesTable1738652412289 = UpdateSitesTable1738652412289;
//# sourceMappingURL=1738652412289-update-site-table.js.map