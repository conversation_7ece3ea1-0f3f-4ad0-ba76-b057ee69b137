"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParseUtil = void 0;
const xmldom_1 = require("@xmldom/xmldom");
const COMMENT_CLASSNAME = 'comment';
const TAG_KAIDAN = 'kaidan';
class ParseUtil {
    static parseContent(blockData, resources) {
        const result = [];
        const content = `<bind>${blockData.content}</bind>`;
        const doc = new xmldom_1.DOMParser().parseFromString(content, 'text/xml');
        const root = doc.documentElement;
        for (const node of root.childNodes) {
            if (node.nodeType !== node.ELEMENT_NODE)
                continue;
            const elem = node;
            const content = ParseUtil.parseChildContent(blockData.blockdataId, elem, resources);
            if (content.attributes.class === COMMENT_CLASSNAME &&
                content.nodeName !== 'H1')
                continue;
            result.push(content);
        }
        return result;
    }
    static parseChildContent(blockdataId, elem, resources) {
        let index = 1;
        const attributes = {};
        if (elem.hasAttributes()) {
            for (const attr of elem.attributes) {
                attributes[attr.name] = attr.value;
            }
        }
        const content = {
            index: index++,
            nodeName: elem.nodeName,
            text: '',
            attributes: attributes,
            children: [],
        };
        if (content.attributes.tag) {
            const resourceId = Number(content.attributes.tag);
            content.resource = resources.find(resource => resource.resourceId === resourceId &&
                resource.blockdataId === blockdataId);
        }
        let childIndex = 1;
        for (const node of elem.childNodes) {
            if (node.nodeType === 3) {
                const text = node.textContent;
                if (text && text.trim()) {
                    const processedText = text.replace(/\u00A0/g, '&nbsp;');
                    content.children.push({
                        index: childIndex++,
                        nodeName: '#text',
                        text: processedText,
                        attributes: {},
                        children: [],
                    });
                }
            }
            else if (node.nodeType === 1) {
                const childElement = node;
                const childContent = ParseUtil.parseChildContent(blockdataId, childElement, resources);
                if (childContent.attributes.class === COMMENT_CLASSNAME &&
                    childContent.nodeName !== 'H1')
                    continue;
                content.children.push(childContent);
            }
        }
        return content;
    }
    static groupChild(dataContents) {
        let index = 0;
        const result = [{ index: index + 1, contents: [] }];
        for (const child of dataContents) {
            let isKaidan = false;
            if (child.attributes.tag === TAG_KAIDAN)
                isKaidan = true;
            else if (child.children.length &&
                child.children[0].attributes.tag === TAG_KAIDAN)
                isKaidan = true;
            if (isKaidan) {
                index++;
                result.push({ index: index + 1, contents: [] });
            }
            else {
                result[index].contents.push(child);
            }
        }
        return result;
    }
    static parseStyleString(styleString) {
        return styleString
            .split(';')
            .filter(s => s.trim() !== '')
            .reduce((acc, item) => {
            const [key, value] = item.split(':');
            if (key && value) {
                acc[key.trim()] = value.trim();
            }
            return acc;
        }, {});
    }
}
exports.ParseUtil = ParseUtil;
//# sourceMappingURL=parse.util.js.map