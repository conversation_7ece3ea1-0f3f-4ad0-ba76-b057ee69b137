import { useCallback, useEffect, useState, type FC } from 'react';
import { useTranslation } from 'react-i18next';
import SearchIcon from '@mui/icons-material/Search';
import { debounce, Stack } from '@mui/material';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import InputAdornment from '@mui/material/InputAdornment';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { getErrMsg } from '@nextgen-bindup/common/utility';
import { useAuth } from '../../auth';
import { TeamDisplayDto, TeamDto } from '../../dto/team.type';
import { UserTeamEntity } from '../../dto/user-team.type';
import { teamService } from '../../services/team.service';
import { userTeamService } from '../../services/user-team.service';
import { InviteMember } from './members/InviteMember';
import { EditTeamMember } from './teams/EditTeamMember';

const Teams: FC = () => {
  const { t } = useTranslation();
  const { session } = useAuth();
  const [teams, setTeams] = useState<TeamDisplayDto[]>([]);
  const [displayTeams, setDisplayTeams] = useState<TeamDisplayDto[]>([]);
  const [errorMsg, setErrorMsg] = useState<string>('');
  const [search, setSearch] = useState<string>('');
  const [isInvite, setIsInvite] = useState<boolean>(false);
  const [editId, setEditId] = useState<number>(0);

  useEffect(() => {
    getListTeams();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getListTeams = async () => {
    setErrorMsg('');
    if (!session?.user.userId) return;

    try {
      const teamList: TeamDto[] = await teamService.getAll();
      const userTeamList: UserTeamEntity[] =
        await userTeamService.getAllByRootUserId(session?.user.userId);

      const list: TeamDisplayDto[] = [];
      for (const team of teamList) {
        const data: TeamDisplayDto = {
          id: team.id || 0,
          name: team.name,
          permission: '',
          administrator: '',
          numberOfMembers: 0,
        };

        for (const userTeam of userTeamList) {
          if (userTeam.teamId === team.id) {
            data.numberOfMembers++;
            if (userTeam.isAdmin) data.administrator = userTeam.email;
          }
        }

        list.push(data);
      }

      setTeams(list);
    } catch (e) {
      setErrorMsg(t(getErrMsg(e)));
    }
  };

  useEffect(() => {
    debounceFilter(teams, search);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [teams, search]);

  const filterMembers = (list: TeamDisplayDto[], search: string) => {
    if (!search) {
      setDisplayTeams(list);
      return;
    }

    const text: string = search.toLowerCase();
    const filteredList = list.filter(team =>
      (team.name || '').toLowerCase().includes(text),
    );
    setDisplayTeams(filteredList);
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceFilter = useCallback(
    debounce((list, text) => filterMembers(list, text), 500),
    [],
  );

  return errorMsg ? (
    <Typography color="error" align="center">
      {errorMsg}
    </Typography>
  ) : (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="h4">{t('team.title')}</Typography>
        <Box sx={{ display: 'inherit', gap: 2 }}>
          <TextField
            size="small"
            placeholder={t('common.search')}
            value={search}
            onChange={e => setSearch(e.target.value)}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              },
            }}
          />
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => setIsInvite(true)}
          >
            {t('common.btn_invite')}
          </Button>
        </Box>
      </Box>

      <Table aria-label="teams table">
        <TableHead sx={{ backgroundColor: '#f6f6f6' }}>
          <TableRow>
            <TableCell>{t('team.name')}</TableCell>
            <TableCell>{t('team.permission_set')}</TableCell>
            <TableCell>{t('team.administrator')}</TableCell>
            <TableCell>{t('team.number_of_members')}</TableCell>
            <TableCell>{t('common.action')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {displayTeams.map(team => (
            <TableRow
              key={team.id}
              sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
            >
              <TableCell component="th" scope="row">
                {team.name}
              </TableCell>
              <TableCell>{team.permission || ''}</TableCell>
              <TableCell>{team.administrator || ''}</TableCell>
              <TableCell>{team.numberOfMembers || 0}</TableCell>
              <TableCell>
                <Stack direction="row" spacing={1}>
                  <Button
                    variant="outlined"
                    color="inherit"
                    onClick={() => {
                      setEditId(team.id);
                    }}
                  >
                    {t('team.btn_member_edit')}
                  </Button>
                  <Button variant="outlined" color="inherit" onClick={() => {}}>
                    {t('team.btn_authority')}
                  </Button>
                </Stack>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {isInvite ? (
        <InviteMember
          onUpdate={status => {
            if (status === 'invite') {
              getListTeams();
            }
            setIsInvite(false);
          }}
        />
      ) : null}

      {editId ? (
        <EditTeamMember
          teamId={editId}
          onUpdate={status => {
            if (status === 'invite') {
              getListTeams();
            }
            setEditId(0);
          }}
        />
      ) : null}
    </>
  );
};

export default Teams;
