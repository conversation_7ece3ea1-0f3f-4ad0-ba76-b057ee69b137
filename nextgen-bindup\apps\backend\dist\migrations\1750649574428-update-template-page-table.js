"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateTemplatePagesTable1750649574428 = void 0;
const typeorm_1 = require("typeorm");
class UpdateTemplatePagesTable1750649574428 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}template_pages`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'templateId',
            type: 'integer',
            isNullable: false,
            default: '0',
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'templateId');
    }
}
exports.UpdateTemplatePagesTable1750649574428 = UpdateTemplatePagesTable1750649574428;
//# sourceMappingURL=1750649574428-update-template-page-table.js.map