import { Module } from '@nestjs/common';
import { UserInfoService } from './user-info.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserInfoEntity } from './entities/user-info.entity';
import { UserInfoController } from './user-info.controller';

@Module({
  imports: [TypeOrmModule.forFeature([UserInfoEntity])],
  providers: [UserInfoService],
  exports: [UserInfoService],
  controllers: [UserInfoController],
})
export class UserInfoModule {}
