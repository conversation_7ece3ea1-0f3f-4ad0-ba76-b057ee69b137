"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPaymentController = void 0;
const common_1 = require("@nestjs/common");
const user_payment_service_1 = require("./user-payment.service");
const schedule_1 = require("@nestjs/schedule");
let UserPaymentController = class UserPaymentController {
    constructor(userPaymentService) {
        this.userPaymentService = userPaymentService;
    }
    async enableStripe(userId) {
        const result = await this.userPaymentService.enableStripe(userId);
        return {
            success: true,
            onboardingUrl: result.url,
        };
    }
    async checkStripeStatus(userId) {
        const result = await this.userPaymentService.checkStripeStatus(userId);
        return result;
    }
    handleUpdateOrderStatus() {
        this.userPaymentService.retryHandleWaitingPendingOrder();
    }
};
exports.UserPaymentController = UserPaymentController;
__decorate([
    (0, common_1.Post)('enable'),
    __param(0, (0, common_1.Body)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserPaymentController.prototype, "enableStripe", null);
__decorate([
    (0, common_1.Get)('status'),
    __param(0, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserPaymentController.prototype, "checkStripeStatus", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_MINUTE),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UserPaymentController.prototype, "handleUpdateOrderStatus", null);
exports.UserPaymentController = UserPaymentController = __decorate([
    (0, common_1.Controller)('user-payment'),
    __metadata("design:paramtypes", [user_payment_service_1.UserPaymentService])
], UserPaymentController);
//# sourceMappingURL=user-payment.controller.js.map