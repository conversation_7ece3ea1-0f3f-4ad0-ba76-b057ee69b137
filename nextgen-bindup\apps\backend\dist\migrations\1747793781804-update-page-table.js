"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePageTable1747793781804 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePageTable1747793781804 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}pages`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'datasource',
            type: 'jsonb',
            isNullable: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'datasource');
    }
}
exports.UpdatePageTable1747793781804 = UpdatePageTable1747793781804;
//# sourceMappingURL=1747793781804-update-page-table.js.map