import { Module, Global, DynamicModule } from '@nestjs/common';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT, SUPABASE_TABLE } from './supabase.constant';
import { SupabaseStorageService } from './supabase-storage.service';

export interface SupabaseModuleOptions {
  url: string;
  key: string;
}

export interface SupabaseModuleAsyncOptions {
  useFactory: (
    ...args: any[]
  ) => Promise<SupabaseModuleOptions> | SupabaseModuleOptions;
  inject?: any[];
}

@Global()
@Module({})
export class SupabaseModule {
  static forRoot(options: SupabaseModuleOptions): DynamicModule {
    const supabaseProvider = {
      provide: SUPABASE_CLIENT,
      useFactory: (): SupabaseClient => {
        return createClient(options.url, options.key);
      },
    };

    return {
      module: SupabaseModule,
      providers: [supabaseProvider, SupabaseStorageService],
      exports: [supabaseProvider, SupabaseStorageService],
    };
  }

  static forRootAsync(options: SupabaseModuleAsyncOptions): DynamicModule {
    const supabaseProvider = {
      provide: SUPABASE_CLIENT,
      useFactory: async (...args: any[]) => {
        const { url, key } = await options.useFactory(...args);
        return createClient(url, key);
      },
      inject: options.inject || [],
    };

    return {
      module: SupabaseModule,
      providers: [supabaseProvider, SupabaseStorageService],
      exports: [supabaseProvider, SupabaseStorageService],
    };
  }

  // Dynamic table interaction
  static forFeature(tableName: string): DynamicModule {
    const tableProvider = {
      provide: SUPABASE_TABLE,
      useValue: tableName,
    };

    return {
      module: SupabaseModule,
      providers: [tableProvider],
      exports: [tableProvider],
    };
  }
}
