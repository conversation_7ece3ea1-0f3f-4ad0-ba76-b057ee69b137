"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductStocksController = void 0;
const common_1 = require("@nestjs/common");
const product_stocks_service_1 = require("./product-stocks.service");
let ProductStocksController = class ProductStocksController {
    constructor(productStockService) {
        this.productStockService = productStockService;
    }
    async getAll(siteId, productId) {
        return await this.productStockService.getByProduct(+siteId, +productId);
    }
    async checkStock(siteId, { cartItems }) {
        return await this.productStockService.checkStock(+siteId, cartItems);
    }
};
exports.ProductStocksController = ProductStocksController;
__decorate([
    (0, common_1.Get)('product/:siteId/:productId'),
    __param(0, (0, common_1.Param)('siteId')),
    __param(1, (0, common_1.Param)('productId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ProductStocksController.prototype, "getAll", null);
__decorate([
    (0, common_1.Post)('check-stock/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProductStocksController.prototype, "checkStock", null);
exports.ProductStocksController = ProductStocksController = __decorate([
    (0, common_1.Controller)('product-stocks'),
    __metadata("design:paramtypes", [product_stocks_service_1.ProductStocksService])
], ProductStocksController);
//# sourceMappingURL=product-stocks.controller.js.map