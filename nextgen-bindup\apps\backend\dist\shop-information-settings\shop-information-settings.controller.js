"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShopInformationSettingController = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("../auth/auth.guard");
const shop_information_settings_service_1 = require("./shop-information-settings.service");
const shop_information_settings_entity_1 = require("./entities/shop-information-settings.entity");
let ShopInformationSettingController = class ShopInformationSettingController {
    constructor(shopInformationSettingService) {
        this.shopInformationSettingService = shopInformationSettingService;
    }
    async create(shopInformationSettingEntity) {
        return await this.shopInformationSettingService.create(shopInformationSettingEntity);
    }
    async update(id, data) {
        return await this.shopInformationSettingService.update(+id, data);
    }
    async getOneBySiteId(siteId) {
        return await this.shopInformationSettingService.findOneBySiteId(+siteId);
    }
};
exports.ShopInformationSettingController = ShopInformationSettingController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [shop_information_settings_entity_1.ShopInformationSettingEntity]),
    __metadata("design:returntype", Promise)
], ShopInformationSettingController.prototype, "create", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ShopInformationSettingController.prototype, "update", null);
__decorate([
    (0, common_1.Get)('one-by-site/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ShopInformationSettingController.prototype, "getOneBySiteId", null);
exports.ShopInformationSettingController = ShopInformationSettingController = __decorate([
    (0, common_1.Controller)('shop-information-settings'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:paramtypes", [shop_information_settings_service_1.ShopInformationSettingService])
], ShopInformationSettingController);
//# sourceMappingURL=shop-information-settings.controller.js.map