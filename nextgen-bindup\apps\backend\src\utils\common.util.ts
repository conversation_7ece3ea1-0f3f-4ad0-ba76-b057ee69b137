export const NEW_TS = (): number => new Date().getTime();
export const MAX_PRICE_VALUE = 999999;
export const MAX_PRODUCT_QUANTITY_VALUE = 99999;

export const ConvertToSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/ /g, '-')
    .replace(/[^\w-]+/g, '');
};

export const isInteger = (
  value: number | null | undefined,
  opt?: {
    min?: number;
    max?: number;
  },
): boolean => {
  if (value === null || value === undefined) return false;
  if (value.toString() === '') return false;
  if (!Number.isInteger(value)) return false;

  if (opt) {
    if (opt.min && value < opt.min) return false;
    if (opt.max && value > opt.max) return false;
  }

  return true;
};

export const isEmptyNumber = (value: number | null | undefined): boolean => {
  if (value === null || value === undefined) return true;
  if (value.toString() === '') return true;
  return false;
};
