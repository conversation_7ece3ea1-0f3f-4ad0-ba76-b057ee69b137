{"version": 3, "file": "user-team.service.js", "sourceRoot": "", "sources": ["../../src/user-team/user-team.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,kEAA6D;AAC7D,qCAAiD;AACjD,6CAAqE;AAI9D,IAAM,eAAe,GAArB,MAAM,eAAe;IAI1B,YAAwC,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAElE,KAAK,CAAC,kBAAkB,CAAC,UAAkB;QACzC,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IACjE,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,UAAkB,EAClB,MAAc;QAEd,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YAClC,KAAK,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE;SAClD,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,UAAkB,EAClB,MAAc;QAEd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CACjD;;;;;;;;;;;;;;;;;;OAkBC,EACD,CAAC,UAAU,EAAE,MAAM,CAAC,CACrB,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;YAC3B,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC;YACxC,IAAI,IAAI,CAAC,YAAY;gBACnB,QAAQ,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;YACxD,IAAI,CAAC,QAAQ;gBAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;YAE9C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC3B,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,UAAkB;QACvC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CACjD;;;;;;;;;;;;;;;;KAgBD,EACC,CAAC,UAAU,CAAC,CACb,CAAC;QAEF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CACtD;;;;;;;;;;;;;;KAcD,EACC,CAAC,UAAU,CAAC,CACb,CAAC;QAEF,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,MAAM,GAAgC,EAAE,CAAC;QAC/C,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;YAC3B,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC;YACxC,IAAI,IAAI,CAAC,YAAY;gBACnB,QAAQ,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;YACxD,IAAI,CAAC,QAAQ;gBAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;YAE9C,MAAM,IAAI,GAAgB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI;gBAC/C,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,KAAK;aAC/B,CAAC;YAEF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/D,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;CACF,CAAA;AA9HY,0CAAe;AAEjB;IADR,IAAA,0BAAgB,EAAC,iCAAc,CAAC;8BACV,oBAAU;qDAAiB;0BAFvC,eAAe;IAD3B,IAAA,mBAAU,GAAE;IAKE,WAAA,IAAA,0BAAgB,GAAE,CAAA;qCAAqB,oBAAU;GAJnD,eAAe,CA8H3B"}