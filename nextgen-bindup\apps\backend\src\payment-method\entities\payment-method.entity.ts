import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity(`payment_methods`, {
  schema: process.env.DATABASE_SCHEMA,
})
export class PaymentMethodEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @Column({
    name: 'bankTransfer',
    type: 'jsonb',
    nullable: true,
  })
  bankTransfer: {
    isEnabled: boolean;
    bankAccount: string;
    description: string;
  };

  @Column({
    name: 'postalTransfer',
    type: 'jsonb',
    nullable: true,
  })
  postalTransfer: {
    isEnabled: boolean;
    bankAccount: string;
    description: string;
  };

  @Column({
    name: 'cashOnDelivery',
    type: 'jsonb',
    nullable: true,
  })
  cashOnDelivery: {
    isEnabled: boolean;
    description: string;
    fee: {
      fromAmount: number;
      codFee: number;
    }[];
  };

  @Column({
    name: 'stripePaymentGateway',
    type: 'jsonb',
    nullable: true,
  })
  stripePaymentGateway: {
    isEnabled: boolean;
    description: string;
    stripeAccountId: string;
  };

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
