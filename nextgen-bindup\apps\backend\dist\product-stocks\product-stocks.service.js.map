{"version": 3, "file": "product-stocks.service.js", "sourceRoot": "", "sources": ["../../src/product-stocks/product-stocks.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAgE;AAChE,0EAAqE;AACrE,6CAAmD;AACnD,qCAAyC;AACzC,sEAAmE;AACnE,gFAA6E;AAC7E,sDAA8E;AAC9E,+DAKiC;AACjC,gEAA6D;AAGtD,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAI/B,YAEmB,cAA8B;QAA9B,mBAAc,GAAd,cAAc,CAAgB;IAC9C,CAAC;IAEJ,KAAK,CAAC,SAAS,CAAC,MAAc;QAC5B,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACtC,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;YACzB,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE;SAChD,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,MAAc,EACd,SAAiB;QAEjB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACtC,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE;YAC/C,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE;SAChD,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,UAAoB;QAEpB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACtC,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,IAAA,YAAE,EAAC,UAAU,CAAC,EAAE;YACpD,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE;SAChD,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CACX,MAAc,EACd,SAAiB,EACjB,OAAe;QAEf,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;YAC3C,EAAE,EAAE,OAAO;YACX,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,SAAS;SACrB,CAAC,CAAC;IACL,CAAC;IAED,wBAAwB,CAAC,kBAAsC;QAC7D,IAAI,CAAC,kBAAkB,CAAC,MAAM;YAC5B,MAAM,IAAI,sCAAiB,CAAC,wBAAwB,CAAC,CAAC;QAExD,IAAI,CAAC,kBAAkB,CAAC,SAAS;YAC/B,MAAM,IAAI,sCAAiB,CACzB,gDAAgD,CACjD,CAAC;QAEJ,IAAI,CAAC,IAAA,uBAAS,EAAC,kBAAkB,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,sCAAiB,CAAC,mBAAmB,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,IAAA,uBAAS,EAAC,kBAAkB,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,sCAAiB,CAAC,mBAAmB,CAAC,CAAC;QACnD,CAAC;QAED,IACE,CAAC,IAAA,uBAAS,EAAC,kBAAkB,CAAC,QAAQ,EAAE;YACtC,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,wCAA0B;SAChC,CAAC,EACF,CAAC;YACD,MAAM,IAAI,sCAAiB,CACzB,gDAAgD,CACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,MAA0B;QACrC,MAAM,GAAG,GAAS,IAAI,IAAI,EAAE,CAAC;QAE7B,MAAM,YAAY,GAAG,IAAI,yCAAkB,EAAE,CAAC;QAC9C,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QACpC,YAAY,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QAC1C,YAAY,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAC1B,YAAY,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAC1B,YAAY,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QACxC,YAAY,CAAC,SAAS,GAAG,GAAG,CAAC;QAC7B,YAAY,CAAC,SAAS,GAAG,GAAG,CAAC;QAE7B,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAC5C,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,MAAM,CACV,EAAU,EACV,MAA0B;QAE1B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;YACzD,EAAE,EAAE,EAAE;SACP,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,4BAAY,CAAC,mCAAmC,CAAC,CAAC;QAE9D,YAAY,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAC1B,YAAY,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAC1B,YAAY,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QACxC,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAEpC,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAC5C,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;QACrD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,SAAiB;QACvD,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACjC,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,SAAS;SACrB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CACd,MAAc,EACd,SAAqB;QAErB,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC;YACvB,OAAO,EAAE,iBAAiB,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,CAAC;QAC3D,CAAC;QAGD,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAGvE,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC9C,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC;YACjD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,UAAU,CAAC;SACzC,CAAC,CAAC;QAGH,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,MAAM,SAAS,GAAG,IAAI,GAAG,CACvB,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACrB,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,EAAE;YAC1C,KAAK;SACN,CAAC,CACH,CAAC;QAEF,MAAM,gBAAgB,GAAuB,EAAE,CAAC;QAGhD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACpD,IAAI,CAAC,OAAO;gBAAE,SAAS;YAEvB,IAAI,SAAqC,CAAC;YAC1C,IAAI,SAAS,GAAG,QAAQ,CAAC;YAGzB,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBACzB,SAAS,GAAG,kCAAc,CAAC,aAAa,CAAC;gBACzC,SAAS,GAAG,CAAC,CAAC;YAChB,CAAC;iBAEI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAEpC,IACE,OAAO,CAAC,qBAAqB,GAAG,CAAC;oBACjC,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAC,qBAAqB,EACjD,CAAC;oBACD,SAAS,GAAG,kCAAc,CAAC,qBAAqB,CAAC;oBACjD,SAAS,GAAG,OAAO,CAAC,qBAAqB,CAAC;gBAC5C,CAAC;qBAEI,CAAC;oBACJ,MAAM,QAAQ,GAAG,GAAG,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC;oBACrE,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACtC,MAAM,aAAa,GAAG,KAAK,EAAE,QAAQ,IAAI,CAAC,CAAC;oBAE3C,IAAI,QAAQ,CAAC,QAAQ,GAAG,aAAa,EAAE,CAAC;wBACtC,SAAS,GAAG,kCAAc,CAAC,YAAY,CAAC;wBACxC,SAAS,GAAG,aAAa,CAAC;oBAC5B,CAAC;gBACH,CAAC;YACH,CAAC;YAGD,IAAI,SAAS,EAAE,CAAC;gBACd,gBAAgB,CAAC,IAAI,CAAC;oBACpB,SAAS,EAAE,QAAQ,CAAC,SAAS;oBAC7B,MAAM,EAAE,MAAM;oBACd,CAAC,EAAE,QAAQ,CAAC,CAAC;oBACb,CAAC,EAAE,QAAQ,CAAC,CAAC;oBACb,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,SAAS;oBACT,SAAS;iBACV,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,iBAAiB,EAAE,gBAAgB,CAAC,MAAM,KAAK,CAAC;YAChD,gBAAgB;SACjB,CAAC;IACJ,CAAC;CACF,CAAA;AA5MY,oDAAoB;AAEtB;IADR,IAAA,0BAAgB,EAAC,yCAAkB,CAAC;8BACV,oBAAU;8DAAqB;+BAF/C,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAMR,WAAA,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,gCAAc,CAAC,CAAC,CAAA;qCACR,gCAAc;GANtC,oBAAoB,CA4MhC"}