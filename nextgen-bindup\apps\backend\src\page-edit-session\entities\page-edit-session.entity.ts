import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('page_edit_sessions', { schema: process.env.DATABASE_SCHEMA })
export class PageEditSessionEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'sessionId',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  sessonId: string;

  @Column({
    name: 'pageId',
    type: 'integer',
    nullable: true,
  })
  pageId: number;

  @Column({
    name: 'userId',
    type: 'integer',
    nullable: true,
  })
  userId: number;

  @Column({
    name: 'componentId',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  componentId: string;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  cursorPosition: Record<string, number>;

  @Column({
    name: 'components',
    type: 'jsonb',
    nullable: true,
  })
  editorSize: Record<string, unknown>;

  @Column({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: true,
  })
  updatedAt: Date;
}
