import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TeamEntity } from './entities/team.entity';
import { AppException } from 'src/common/exceptions/app.exception';

@Injectable()
export class TeamService {
  @InjectRepository(TeamEntity)
  readonly teamRepo: Repository<TeamEntity>;

  constructor() {}

  async findById(id: number): Promise<TeamEntity> {
    return await this.teamRepo.findOneBy({ id });
  }

  async getAllByRootUserId(rootUserId: string): Promise<TeamEntity[]> {
    return await this.teamRepo.find({ where: { rootUserId } });
  }

  async create(userId: string, data: TeamEntity): Promise<TeamEntity> {
    delete data.id;

    const now = new Date();
    data.createdAt = now;
    data.updatedAt = now;
    data.rootUserId = userId;
    return await this.teamRepo.save(data);
  }

  async update(
    id: number,
    userId: string,
    data: Partial<TeamEntity>,
  ): Promise<TeamEntity> {
    const team = await this.teamRepo.findOneBy({ id: id, rootUserId: userId });
    if (!team) throw new AppException('api.error.team_not_found');

    data.updatedAt = new Date();
    await this.teamRepo.update({ id }, data);

    return { ...team, ...data };
  }
}
