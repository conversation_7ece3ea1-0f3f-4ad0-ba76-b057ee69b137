import { NameServerEntity } from './entities/name-server.entity';
import { Repository } from 'typeorm';
export declare class NameServersService {
    readonly nameServerRepo: Repository<NameServerEntity>;
    create(data: NameServerEntity): Promise<NameServerEntity>;
    update(id: number, data: Partial<NameServerEntity>): Promise<NameServerEntity>;
    delete(id: number): Promise<boolean>;
    findBySiteId(siteId: number): Promise<NameServerEntity[]>;
    findByProjectId(projectId: number): Promise<NameServerEntity[]>;
}
