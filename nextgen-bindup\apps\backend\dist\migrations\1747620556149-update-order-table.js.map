{"version": 3, "file": "1747620556149-update-order-table.js", "sourceRoot": "", "sources": ["../../src/migrations/1747620556149-update-order-table.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,6BAA6B;IAA1C;QACE,eAAU,GAAW,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,QAAQ,CAAC;IA6HlE,CAAC;IA3HQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,YAAY,CAC5B,IAAI,CAAC,UAAU,EACf,cAAc,EACd,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,KAAK;YACb,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,YAAY,CAC5B,IAAI,CAAC,UAAU,EACf,eAAe,EACf,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,KAAK;YACb,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE;YAC5C,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;aACjB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;aACjB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,sBAAsB;gBAC5B,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;aACjB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,uBAAuB;gBAC7B,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;aACjB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;aACjB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,IAAI;aACjB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;aACjB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,sBAAsB;gBAC5B,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;aACjB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,sBAAsB;gBAC5B,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;aACjB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACd,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,IAAI;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,YAAY,CAC5B,IAAI,CAAC,UAAU,EACf,cAAc,EACd,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,KAAK;YACb,UAAU,EAAE,KAAK;SAClB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,YAAY,CAC5B,IAAI,CAAC,UAAU,EACf,eAAe,EACf,IAAI,qBAAW,CAAC;YACd,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,KAAK;YACb,UAAU,EAAE,KAAK;SAClB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE;YAC7C,kBAAkB;YAClB,mBAAmB;YACnB,sBAAsB;YACtB,uBAAuB;YACvB,eAAe;YACf,oBAAoB;YACpB,oBAAoB;YACpB,sBAAsB;YACtB,sBAAsB;YACtB,qBAAqB;SACtB,CAAC,CAAC;IACL,CAAC;CACF;AA9HD,sEA8HC"}