@echo off
REM NextGen BindUp - Environment Setup Batch Wrapper

setlocal

REM Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell is available'" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PowerShell is not available or not in PATH
    echo Please ensure PowerShell is installed and accessible
    pause
    exit /b 1
)

REM Parse command line arguments
set "ARGS="
if "%1"=="setup" set "ARGS="
if "%1"=="start" set "ARGS="
if "%1"=="check" set "ARGS=-CheckOnly"
if "%1"=="stop" set "ARGS=-StopOnly"
if "%1"=="help" set "ARGS=-Help"
if "%1"=="-h" set "ARGS=-Help"
if "%1"=="--help" set "ARGS=-Help"

REM If no valid argument provided, show help
if "%1"=="" (
    echo NextGen BindUp - Environment Setup
    echo.
    echo Usage: start_env.bat [command]
    echo.
    echo Commands:
    echo   setup    - Run complete setup and start ^(default^)
    echo   start    - Run complete setup and start ^(same as setup^)
    echo   check    - Check status of all services
    echo   stop     - Stop all running services
    echo   help     - Show detailed help
    echo.
    echo Examples:
    echo   start_env.bat setup
    echo   start_env.bat check
    echo   start_env.bat stop
    echo.
    echo Note: Running without parameters will show this help.
    echo       Use 'setup' or 'start' to actually start the environment.
    pause
    exit /b 0
)

REM Execute the PowerShell script with the appropriate arguments
echo Executing: powershell -ExecutionPolicy Bypass -File start_env.ps1 %ARGS%
powershell -ExecutionPolicy Bypass -File start_env.ps1 %ARGS%

REM Check if the PowerShell script executed successfully
if errorlevel 1 (
    echo.
    echo ERROR: The operation failed. Please check the output above for details.
    pause
    exit /b 1
)

echo.
echo Operation completed successfully.
if not "%1"=="check" if not "%1"=="help" pause
