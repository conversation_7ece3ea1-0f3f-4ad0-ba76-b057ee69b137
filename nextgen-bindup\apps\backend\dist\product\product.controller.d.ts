import { ProductService } from './product.service';
import { ProductEntity } from './entities/product.entity';
import { GetProductsQueryDto } from './dto/get-product.dto';
import { Response } from 'express';
export declare class ProductController {
    private readonly productService;
    constructor(productService: ProductService);
    downloadTemplate(res: Response): Promise<void>;
    uploadCSV(siteId: string, file: Express.Multer.File): Promise<{
        success: boolean;
        message: string;
    }>;
    downloadCsv(siteId: string, query: GetProductsQueryDto, res: Response): Promise<void>;
    getAll(siteId: number): Promise<ProductEntity[]>;
    getList(siteId: number, query: GetProductsQueryDto): Promise<import("../common/paginated-response").PaginatedResponse<ProductEntity>>;
    create(productEntity: ProductEntity): Promise<ProductEntity>;
    update(id: string, data: Partial<ProductEntity>): Promise<ProductEntity>;
    getById(id: string): Promise<ProductEntity>;
    duplicate(id: string): Promise<ProductEntity>;
    delete(id: string): Promise<boolean>;
}
