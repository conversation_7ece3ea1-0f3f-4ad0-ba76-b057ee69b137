import { Site2_Page } from '../dto/site2_page.dto';
import { Block_Area, Site3_Block } from '../dto/site3_block.dto';
import { Site4_BlockData } from '../dto/site4_blockdata.dto';
import { Site5_Resource } from '../dto/site5_resource.dto';
import { Site6_Srclist1 } from '../dto/site6_srclist1.dto';
import { TemplatePageIdDto } from '../dto/template_page_id.dto';
import { ReadFileUtil } from '../util/read-file.util';
import { Component } from '@nextgen-bindup/common/dto/component';
import { DEFAULT_TEMPLATE_COMPONENT, MigrateUtil } from './migrate.util';
import { NEW_TS } from 'src/utils/common.util';
import { PropertyDto } from '@nextgen-bindup/common/dto/component-properties/general-prop.dto';
import { ComponentType } from 'src/page/types/component.type';
import { MigrateDataBlockService } from './migrate-data-block.service';
import {
  BlockData_Content,
  BlockData_Group,
} from '../dto/blockdata-content.dto';
import { ParseUtil } from '../util/parse.util';
import { MigrateDataContentService } from './migrate-data-content.service';

export class MigrateService {
  dbdata: string;
  components: Record<string, Component> = structuredClone(
    DEFAULT_TEMPLATE_COMPONENT,
  );
  templatePageIds: TemplatePageIdDto[];
  page: Site2_Page;
  blocks: Site3_Block[];
  blockDatas: Site4_BlockData[];
  resources: Site5_Resource[];
  ID_INDEX: number = 1;

  constructor(inp: { dbdata: string; templatePageIds: TemplatePageIdDto[] }) {
    this.dbdata = inp.dbdata;
    this.templatePageIds = inp.templatePageIds;

    this.blocks = ReadFileUtil.readBlocks(this.dbdata);
    this.blockDatas = ReadFileUtil.readBlockDatas(this.dbdata);
    const srcList1: Site6_Srclist1[] = ReadFileUtil.readSrcList1(this.dbdata);
    this.resources = ReadFileUtil.readResources(this.dbdata, srcList1);
  }

  migratePage(inp: {
    page: Site2_Page;
    components: Record<string, Component>;
  }): Record<string, Component> {
    this.page = inp.page;
    this.components = inp.components;
    this.ID_INDEX = 1;

    const pageBlocks: Site3_Block[] = this.blocks.filter(
      block => block.pageId === this.page.pageId,
    );

    const dataBlockService: MigrateDataBlockService =
      new MigrateDataBlockService({ components: this.components });
    const dataContentService: MigrateDataContentService =
      new MigrateDataContentService({ components: this.components });

    for (const block of pageBlocks) {
      const blockData: Site4_BlockData = this.blockDatas.find(
        blockData => blockData.blockdataId === block.blockdataId,
      );

      const parentSide: string = this.checkParentSide(block.areaId);
      const blockMargin: Component = dataBlockService.migrateBlockData({
        parentSide,
        block,
        blockData,
      });

      // Render content
      const dataContents: BlockData_Content[] = ParseUtil.parseContent(
        blockData,
        this.resources,
      );
      const dataGroups: BlockData_Group[] = ParseUtil.groupChild(dataContents);
      for (const dataGroup of dataGroups) {
        dataContentService.migrateDataGroup({
          parentComponentId: blockMargin.id,
          block,
          blockData,
          dataGroup,
        });
      }
    }

    return this.components;
  }

  //======================================================================
  private createBillboard() {
    const id = 'block_billboard';
    if (this.components[id]) return;
    const ts: number = NEW_TS();

    const properties: PropertyDto = MigrateUtil.blockProps();
    properties.size.width = {
      value: '100',
      unit: '%',
    };

    const blockBillboard: Component = {
      id: id,
      type: ComponentType.Block,
      name: 'Billboard',
      parentId: '__main__',
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };

    this.components[blockBillboard.id] = blockBillboard;
    this.components['__main__'].children.push(blockBillboard.id);
  }

  private createSideA() {
    const id = '__left-side__';
    if (this.components[id]) return;
    const ts: number = NEW_TS();

    const properties: PropertyDto = MigrateUtil.blockProps();
    properties.size.width = {
      value: '100',
      unit: '%',
    };
    properties.sideArea = { ts: ts, show: true };

    const sideA: Component = {
      id: id,
      type: ComponentType.LeftSide,
      name: 'Left Side',
      parentId: '__page__',
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };

    this.components[sideA.id] = sideA;
    this.components['__page__'].children.push(sideA.id);
  }

  private createSideB() {
    const id = '__right-side__';
    if (this.components[id]) return;
    const ts: number = NEW_TS();

    const properties: PropertyDto = MigrateUtil.blockProps();
    properties.size.width = {
      value: '100',
      unit: '%',
    };
    properties.size.minHeight = { unit: 'dvh', value: '100' };
    properties.sideArea = { ts: ts, show: true };

    const sideB: Component = {
      id: id,
      type: ComponentType.LeftSide,
      name: 'Right Side',
      parentId: '__page__',
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };

    this.components[sideB.id] = sideB;
    this.components['__page__'].children.push(sideB.id);
  }

  private createHeader() {
    const id = '__header__';
    if (this.components[id]) return;
    const ts: number = NEW_TS();

    const properties: PropertyDto = MigrateUtil.blockProps();
    properties.size.height = {
      value: '70',
      unit: 'px',
    };

    const header: Component = {
      id: id,
      type: ComponentType.Header,
      name: 'Header',
      parentId: '__page__',
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };

    this.components[header.id] = header;
    this.components['__page__'].children.push(header.id);
  }

  private createFooter() {
    const id = '__footer__';
    if (this.components[id]) return;
    const ts: number = NEW_TS();

    const properties: PropertyDto = MigrateUtil.blockProps();
    properties.size.height = {
      value: '70',
      unit: 'px',
    };

    const footer: Component = {
      id: id,
      type: ComponentType.Footer,
      name: 'Footer',
      parentId: '__page__',
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: ts },
        phone: { ts: ts },
      },
      ts: ts,
    };

    this.components[footer.id] = footer;
    this.components['__page__'].children.push(footer.id);
  }

  private checkParentSide(areaId: Block_Area): string {
    switch (areaId) {
      case Block_Area.HEADER:
        this.createHeader();
        return '__header__';

      case Block_Area.BILLBOARD:
        this.createBillboard();
        return 'block_billboard';

      case Block_Area.MAIN:
        return '__main__';

      case Block_Area.SIDE_A:
        this.createSideA();
        return '__left-side__';

      case Block_Area.SIDE_B:
        this.createSideB();
        return '__right-side__';

      case Block_Area.FOOTER:
        this.createFooter();
        return '__footer__';

      default:
        return '__UNDEFINED__';
    }
  }
}
