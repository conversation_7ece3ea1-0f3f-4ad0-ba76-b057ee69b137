"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateProductTable1747793781803 = void 0;
const typeorm_1 = require("typeorm");
class UpdateProductTable1747793781803 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}products`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'isDeleted',
            type: 'boolean',
            isNullable: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'isDeleted');
    }
}
exports.UpdateProductTable1747793781803 = UpdateProductTable1747793781803;
//# sourceMappingURL=1747793781803-update-product-table.js.map