"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateSiteEntity = void 0;
const site_type_1 = require("../../site/types/site.type");
const typeorm_1 = require("typeorm");
let TemplateSiteEntity = class TemplateSiteEntity {
    constructor() {
        this.status = site_type_1.SiteStatus.DRAFT;
    }
};
exports.TemplateSiteEntity = TemplateSiteEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], TemplateSiteEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'templateId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], TemplateSiteEntity.prototype, "templateId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'managementName',
        type: 'varchar',
        length: 255,
        nullable: false,
    }),
    __metadata("design:type", String)
], TemplateSiteEntity.prototype, "managementName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'status',
        type: 'smallint',
        nullable: false,
    }),
    __metadata("design:type", Number)
], TemplateSiteEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'url',
        type: 'varchar',
        length: 255,
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplateSiteEntity.prototype, "url", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'title',
        type: 'varchar',
        length: 255,
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplateSiteEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'description',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplateSiteEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isSearch',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], TemplateSiteEntity.prototype, "isSearch", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'thumb',
        type: 'varchar',
        length: 255,
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplateSiteEntity.prototype, "thumb", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'headCode',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplateSiteEntity.prototype, "headCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'bodyCode',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], TemplateSiteEntity.prototype, "bodyCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isArchived',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], TemplateSiteEntity.prototype, "isArchived", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'userId',
        type: 'varchar',
        length: '36',
        nullable: false,
    }),
    __metadata("design:type", String)
], TemplateSiteEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], TemplateSiteEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], TemplateSiteEntity.prototype, "updatedAt", void 0);
exports.TemplateSiteEntity = TemplateSiteEntity = __decorate([
    (0, typeorm_1.Entity)('template_sites', { schema: process.env.DATABASE_SCHEMA })
], TemplateSiteEntity);
//# sourceMappingURL=template-site.entity.js.map