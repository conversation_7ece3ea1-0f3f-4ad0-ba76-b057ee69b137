"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderTable1748424195394 = void 0;
class UpdateOrderTable1748424195394 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}orders`;
    }
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE IF EXISTS "${this.TABLE_NAME}"
      DROP COLUMN IF EXISTS "projectId"
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE IF EXISTS "${this.TABLE_NAME}"
      ADD COLUMN "projectId" integer
    `);
    }
}
exports.UpdateOrderTable1748424195394 = UpdateOrderTable1748424195394;
//# sourceMappingURL=1748424195394-update-order-table.js.map