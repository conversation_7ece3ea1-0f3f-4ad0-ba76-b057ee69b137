{"name": "extend-object", "description": "Underscore's extend method as a standalone Common JS module.", "version": "1.0.0", "author": "<PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/henrikjoreteg/extend-object/issues"}, "homepage": "https://github.com/henrikjoreteg/extend-object", "keywords": ["extend", "object", "underscore"], "license": "MIT", "main": "extend-object.js", "repository": {"type": "git", "url": "git://github.com/henrikjoreteg/extend-object"}, "scripts": {"test": "node test.js"}}