"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const storage_service_1 = require("./storage/storage.service");
const logger_service_1 = require("./logger/logger.service");
const app_exception_filter_1 = require("./common/exceptions/app-exception.filter");
const validate_exception_filter_1 = require("./common/exceptions/validate-exception.filter");
const unhandled_exception_filter_1 = require("./common/exceptions/unhandled-exception.filter");
const bodyParser = require("body-parser");
const common_1 = require("@nestjs/common");
async function bootstrap() {
    const nestApp = await core_1.NestFactory.create(app_module_1.AppModule);
    nestApp.enableCors();
    nestApp.use('/payment/webhook', bodyParser.raw({ type: 'application/json' }));
    const loggerService = nestApp.get(logger_service_1.LoggerService);
    nestApp.useGlobalPipes(new common_1.ValidationPipe());
    nestApp.useGlobalFilters(new app_exception_filter_1.AppExceptionFilter(loggerService));
    nestApp.useGlobalFilters(new validate_exception_filter_1.ValidateExceptionFilter(loggerService));
    nestApp.useGlobalFilters(new unhandled_exception_filter_1.UnhandledExceptionFilter(loggerService));
    const storageService = nestApp.get(storage_service_1.StorageService);
    await storageService.initBucket();
    await nestApp.listen(4000);
}
bootstrap();
//# sourceMappingURL=main.js.map