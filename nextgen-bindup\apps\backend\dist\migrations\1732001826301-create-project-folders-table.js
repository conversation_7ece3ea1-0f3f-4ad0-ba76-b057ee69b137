"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateProjectFoldersTable1732001826301 = void 0;
const typeorm_1 = require("typeorm");
class CreateProjectFoldersTable1732001826301 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}project_folders`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'projectId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
        await queryRunner.query(`alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
    async down(queryRunner) {
        await queryRunner.query(`alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateProjectFoldersTable1732001826301 = CreateProjectFoldersTable1732001826301;
//# sourceMappingURL=1732001826301-create-project-folders-table.js.map