import { useEffect, useState, type FC } from 'react';
import { useTranslation } from 'react-i18next';
import ImageIcon from '@mui/icons-material/Image';
import { Button, Dialog } from '@mui/material';
import Box from '@mui/material/Box';
import Checkbox from '@mui/material/Checkbox';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { AssetEntity } from '../../dto/asset.dto';
import { SiteEntity, SiteStatus } from '../../dto/site.type';
import { siteService } from '../../services/site-service';
import AddMedia from '../common/AddMedia';

/**
 * サイト設定
 */
interface SettingsProps {
  fetchSiteData: () => void;
  initSite: SiteEntity | null;
}

const Settings: FC<SettingsProps> = ({ fetchSiteData, initSite }) => {
  const { t } = useTranslation();
  const [site, setSite] = useState<SiteEntity | null>(initSite);
  const [initialSite, setInitialSite] = useState<SiteEntity | null>(null); // State lưu giá trị ban đầu
  const [openMedia, setOpenMedia] = useState(false);

  useEffect(() => {
    const fetchPageData = async () => {
      setInitialSite(initSite);
    };
    fetchPageData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value, type, checked } = event.target as HTMLInputElement;
    setSite(prevSite => {
      if (!prevSite) return prevSite;
      return {
        ...prevSite,
        [name]: type === 'checkbox' ? checked : value,
      };
    });
  };

  const handleSave = async () => {
    if (!site) return;
    try {
      if (site.id) {
        const updateData: Partial<SiteEntity> = {
          managementName: site.managementName,
          status: site.status,
          url: site.url,
          title: site.title,
          description: site.description,
          isSearch: site.isSearch,
          thumb: site.thumb,
          headCode: site.headCode,
          bodyCode: site.bodyCode,
        };
        await siteService.update(site.id, updateData);
        setInitialSite({ ...site });
      } else {
        await siteService.create(site);
        setInitialSite({ ...site });
      }
      fetchSiteData();
    } catch (error) {
      console.error('Lỗi khi lưu trang:', error);
    }
  };

  const handleSelectChange = (event: SelectChangeEvent<SiteStatus>) => {
    const { name, value } = event.target;
    setSite(prevSite => {
      if (!prevSite) return prevSite;
      return {
        ...prevSite,
        [name]: value,
      };
    });
  };

  const hasChanges = () => {
    if (!initialSite || !site) return false; // Chưa có dữ liệu để so sánh
    return (
      initialSite.managementName !== site.managementName ||
      initialSite.status !== site.status ||
      initialSite.url !== site.url ||
      initialSite.title !== site.title ||
      initialSite.description !== site.description ||
      initialSite.isSearch !== site.isSearch ||
      initialSite.headCode !== site.headCode ||
      initialSite.bodyCode !== site.bodyCode ||
      initialSite.thumb !== site.thumb
    );
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <TextField
        label={t('site.site_setting.site_name')}
        variant="filled"
        fullWidth
        name="managementName"
        value={site?.managementName || ''}
        onChange={handleChange}
      />
      <FormControl size="medium" variant="filled" fullWidth>
        <InputLabel id="select-label">
          {t('site.site_setting.label')}
        </InputLabel>
        <Select
          labelId="select-label"
          id="select-demo"
          value={site?.status || SiteStatus.DRAFT}
          label={t('site.site_setting.label')}
          name="status"
          onChange={handleSelectChange}
        >
          <MenuItem value={SiteStatus.DRAFT}>
            {t('site.site_setting.status.draft')}
          </MenuItem>

          <MenuItem value={SiteStatus.PUBLISHED}>
            {t('site.site_setting.status.public')}
          </MenuItem>
        </Select>
      </FormControl>
      <TextField
        label={t('common.url')}
        variant="filled"
        fullWidth
        name="url"
        value={site?.url || ''}
        onChange={handleChange}
      />
      <TextField
        label={t('site.site_setting.page_title')}
        variant="filled"
        fullWidth
        name="title"
        value={site?.title || ''}
        onChange={handleChange}
      />
      <TextField
        label={t('site.site_setting.description')}
        variant="filled"
        multiline
        rows={2}
        fullWidth
        name="description"
        value={site?.description || ''}
        onChange={handleChange}
      />
      <FormControlLabel
        control={
          <Checkbox
            value={site?.isSearch || false}
            checked={site?.isSearch || false}
            name="isSearch"
            onChange={handleChange}
          />
        }
        labelPlacement="end"
        label={t('site.site_setting.search_for')}
      />
      <Typography variant="body2">
        {t('site.site_setting.sharing_thumbnail')}
      </Typography>
      <Box
        sx={{
          width: 100,
          height: 64,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#0000008F',
          backgroundUrl: site?.thumb,
          color: '#fff',
          overflow: 'hidden',
        }}
        onClick={() => setOpenMedia(true)}
      >
        {site?.thumb ? (
          <img src={site?.thumb} width={50} height={50} alt="サムネイル" />
        ) : (
          <ImageIcon sx={{ fontSize: 36 }} />
        )}
      </Box>
      <TextField
        label={t('site.site_setting.head_tag')}
        variant="filled"
        multiline
        rows={2}
        fullWidth
        name="headCode"
        value={site?.headCode || ''}
        onChange={handleChange}
      />
      <TextField
        label={t('site.site_setting.body_tag')}
        variant="filled"
        multiline
        rows={2}
        fullWidth
        name="bodyCode"
        value={site?.bodyCode || ''}
        onChange={handleChange}
      />

      <Button
        disabled={!hasChanges()}
        variant="contained"
        color="primary"
        onClick={handleSave}
      >
        {t('common.btn_save')}
      </Button>

      <Dialog
        key={'dialog-media'}
        maxWidth={false}
        open={openMedia}
        onClose={() => setOpenMedia(false)}
      >
        <AddMedia
          fileType="all"
          onCancel={() => setOpenMedia(false)}
          handleSelect={(asset: AssetEntity) => {
            console.log('asset', asset);
            setSite(prevSite => {
              if (!prevSite) return prevSite;
              return {
                ...prevSite,
                thumb: asset.url || '',
              };
            });
            setOpenMedia(false);
          }}
        />
      </Dialog>
    </Box>
  );
};

export default Settings;
