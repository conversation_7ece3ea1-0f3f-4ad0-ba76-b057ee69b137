"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserManagementService = void 0;
const common_1 = require("@nestjs/common");
const identity_service_1 = require("../identity/identity.service");
const team_entity_1 = require("../team/entities/team.entity");
const app_exception_1 = require("../common/exceptions/app.exception");
const user_team_entity_1 = require("../user-team/entities/user-team.entity");
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../user/entities/user.entity");
const uuid_1 = require("uuid");
const user_info_entity_1 = require("../user-info/entities/user-info.entity");
const user_info_enum_1 = require("../user-info/enum/user-info.enum");
const typeorm_2 = require("@nestjs/typeorm");
let UserManagementService = class UserManagementService {
    constructor(identityService, dataSource) {
        this.identityService = identityService;
        this.dataSource = dataSource;
    }
    async inviteMemberToTeam(rootUserId, input) {
        const now = new Date();
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            let team;
            if (!input.team.id) {
                team = new team_entity_1.TeamEntity();
                team.rootUserId = rootUserId;
                team.name = input.team.name;
                team.createdAt = now;
                team.updatedAt = now;
                team = await queryRunner.manager.getRepository(team_entity_1.TeamEntity).create(team);
            }
            else {
                team = await queryRunner.manager
                    .getRepository(team_entity_1.TeamEntity)
                    .findOneBy({ id: input.team.id });
                if (!team)
                    throw new app_exception_1.AppException('api.error.team_not_found');
            }
            const userTeams = await queryRunner.manager
                .getRepository(user_team_entity_1.UserTeamEntity)
                .find({ where: { rootUserId: rootUserId, teamId: team.id } });
            for (const email of input.emails) {
                if (userTeams.findIndex(item => item.email === email) !== -1) {
                    continue;
                }
                const user = await queryRunner.manager
                    .getRepository(user_entity_1.UserEntity)
                    .findOneBy({ email });
                if (user) {
                    const userTeam = new user_team_entity_1.UserTeamEntity();
                    userTeam.rootUserId = rootUserId;
                    userTeam.teamId = team.id;
                    userTeam.userId = user.userid;
                    userTeam.email = email;
                    userTeam.isAdmin = false;
                    await queryRunner.manager
                        .getRepository(user_team_entity_1.UserTeamEntity)
                        .save(userTeam);
                }
                else {
                    try {
                        const identity = await this.identityService.createIdentity({
                            email: email,
                            password: (0, uuid_1.v4)(),
                        });
                        const userInfo = await queryRunner.manager
                            .getRepository(user_info_entity_1.UserInfoEntity)
                            .findOneBy({
                            userId: identity.user.userId,
                        });
                        if (userInfo) {
                            await queryRunner.manager.getRepository(user_info_entity_1.UserInfoEntity).update({
                                userId: userInfo.userId,
                            }, {
                                name: email,
                                updatedAt: now,
                            });
                        }
                        else {
                            const newUserInfo = new user_info_entity_1.UserInfoEntity();
                            newUserInfo.userId = identity.user.userId;
                            newUserInfo.name = email;
                            newUserInfo.type = user_info_enum_1.UserType.INDIVIDUAL;
                            newUserInfo.countryCode = user_info_enum_1.CountryCode.JAPAN;
                            newUserInfo.createdAt = now;
                            newUserInfo.updatedAt = now;
                            await queryRunner.manager
                                .getRepository(user_info_entity_1.UserInfoEntity)
                                .save(newUserInfo);
                        }
                        const userTeam = new user_team_entity_1.UserTeamEntity();
                        userTeam.rootUserId = rootUserId;
                        userTeam.teamId = team.id;
                        userTeam.userId = identity.user.userId;
                        userTeam.email = email;
                        userTeam.isAdmin = false;
                        await queryRunner.manager
                            .getRepository(user_team_entity_1.UserTeamEntity)
                            .save(userTeam);
                    }
                    catch (e) {
                        console.log(e);
                    }
                }
            }
            await queryRunner.commitTransaction();
        }
        catch (e) {
            console.log(e);
            await queryRunner.rollbackTransaction();
            throw e;
        }
        finally {
            await queryRunner.release();
        }
    }
    async updateMemberOfTeam(rootUserId, input) {
        const now = new Date();
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const team = await queryRunner.manager
                .getRepository(team_entity_1.TeamEntity)
                .findOneBy({ id: input.teamId });
            if (!team)
                throw new app_exception_1.AppException('api.error.team_not_found');
            const userTeams = await queryRunner.manager
                .getRepository(user_team_entity_1.UserTeamEntity)
                .find({ where: { rootUserId: rootUserId, teamId: input.teamId } });
            const orgTeamMember = {};
            for (const member of userTeams) {
                orgTeamMember[member.id || 0] = member;
            }
            const newTeamMembers = [...input.members];
            for (const member of input.members) {
                const id = member.id || 0;
                const index = newTeamMembers.findIndex(item => item.id === id);
                try {
                    if (member.isDeleted) {
                        await queryRunner.manager
                            .getRepository(user_team_entity_1.UserTeamEntity)
                            .delete({ rootUserId: rootUserId, id: id });
                        if (index >= 0)
                            newTeamMembers.splice(index, 1);
                        continue;
                    }
                    if (id < 0) {
                        const user = await queryRunner.manager
                            .getRepository(user_entity_1.UserEntity)
                            .findOneBy({ email: member.email });
                        if (user) {
                            const userTeam = new user_team_entity_1.UserTeamEntity();
                            userTeam.rootUserId = rootUserId;
                            userTeam.teamId = team.id || 0;
                            userTeam.userId = user.userid;
                            userTeam.email = member.email;
                            userTeam.isAdmin = member.isAdmin || false;
                            await queryRunner.manager
                                .getRepository(user_team_entity_1.UserTeamEntity)
                                .save(userTeam);
                            if (index >= 0)
                                newTeamMembers[index].id = userTeam.id;
                        }
                        else {
                            try {
                                const identity = await this.identityService.createIdentity({
                                    email: member.email,
                                    password: (0, uuid_1.v4)(),
                                });
                                const userInfo = await queryRunner.manager
                                    .getRepository(user_info_entity_1.UserInfoEntity)
                                    .findOneBy({
                                    userId: identity.user.userId,
                                });
                                if (userInfo) {
                                    await queryRunner.manager
                                        .getRepository(user_info_entity_1.UserInfoEntity)
                                        .update({
                                        userId: userInfo.userId,
                                    }, {
                                        name: member.email,
                                        updatedAt: now,
                                    });
                                }
                                else {
                                    const newUserInfo = new user_info_entity_1.UserInfoEntity();
                                    newUserInfo.userId = identity.user.userId;
                                    newUserInfo.name = member.email;
                                    newUserInfo.type = user_info_enum_1.UserType.INDIVIDUAL;
                                    newUserInfo.countryCode = user_info_enum_1.CountryCode.JAPAN;
                                    newUserInfo.createdAt = now;
                                    newUserInfo.updatedAt = now;
                                    await queryRunner.manager
                                        .getRepository(user_info_entity_1.UserInfoEntity)
                                        .save(newUserInfo);
                                }
                                const userTeam = new user_team_entity_1.UserTeamEntity();
                                userTeam.rootUserId = rootUserId;
                                userTeam.teamId = team.id || 0;
                                userTeam.userId = identity.user.userId;
                                userTeam.email = member.email;
                                userTeam.isAdmin = member.isAdmin || false;
                                await queryRunner.manager
                                    .getRepository(user_team_entity_1.UserTeamEntity)
                                    .save(userTeam);
                                if (index >= 0)
                                    newTeamMembers[index].id = userTeam.id;
                            }
                            catch (e) {
                                console.log(e);
                            }
                        }
                        continue;
                    }
                    if (id > 0) {
                        const oldMember = orgTeamMember[id];
                        const oldIsAdmin = oldMember?.isAdmin || false;
                        const newIsAdmin = member.isAdmin || false;
                        if (oldIsAdmin !== newIsAdmin) {
                            await queryRunner.manager
                                .getRepository(user_team_entity_1.UserTeamEntity)
                                .update(newTeamMembers[index].id, {
                                rootUserId: rootUserId,
                                email: member.email,
                                isAdmin: member.isAdmin,
                            });
                            if (index >= 0)
                                orgTeamMember[id].isAdmin = newIsAdmin;
                        }
                    }
                }
                catch (e) {
                    console.log(e);
                }
            }
            await queryRunner.commitTransaction();
        }
        catch (e) {
            console.log(e);
            await queryRunner.rollbackTransaction();
            throw e;
        }
        finally {
            await queryRunner.release();
        }
    }
};
exports.UserManagementService = UserManagementService;
exports.UserManagementService = UserManagementService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_2.InjectDataSource)()),
    __metadata("design:paramtypes", [identity_service_1.IdentityService,
        typeorm_1.DataSource])
], UserManagementService);
//# sourceMappingURL=user-management.service.js.map