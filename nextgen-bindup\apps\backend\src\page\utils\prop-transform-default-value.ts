import { TransformPropDto } from '@nextgen-bindup/common/dto/setting-properties/transform-prop.dto';

export const PROP_TRANSFORM_DEFAULT_VALUE = (
  ts: number,
  prop?: Partial<TransformPropDto>,
): TransformPropDto => ({
  move: {
    x: { value: '0', unit: 'px' },
    y: { value: '0', unit: 'px' },
  },
  expand: {
    x: { value: '0', unit: '%' },
  },
  rotate: 0,
  distort: {
    x: { value: '0', unit: '%' },
    y: { value: '0', unit: '%' },
  },
  origin: '中心',
  ts: ts,
  ...(prop || undefined),
});
