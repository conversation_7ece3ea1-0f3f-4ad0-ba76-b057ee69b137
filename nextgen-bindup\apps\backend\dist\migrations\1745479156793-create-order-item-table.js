"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOrderItemTable1745479156793 = void 0;
const typeorm_1 = require("typeorm");
class CreateOrderItemTable1745479156793 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}order_items`;
        this.TABLE_ORDER_NAME = `${process.env.ENTITY_PREFIX || ''}orders`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'order_items',
            columns: [
                {
                    name: 'id',
                    type: 'int',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'orderId',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'productId',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'productName',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'unitPrice',
                    type: 'bigint',
                    isNullable: false,
                },
                {
                    name: 'quantity',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'subtotal',
                    type: 'bigint',
                    isNullable: false,
                },
                {
                    name: 'createdAt',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false,
                },
                {
                    name: 'updatedAt',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false,
                },
            ],
        }), true);
        await queryRunner.createForeignKey(this.TABLE_NAME, new typeorm_1.TableForeignKey({
            columnNames: ['orderId'],
            referencedColumnNames: ['id'],
            referencedTableName: this.TABLE_ORDER_NAME,
            onDelete: 'CASCADE',
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable(this.TABLE_NAME);
    }
}
exports.CreateOrderItemTable1745479156793 = CreateOrderItemTable1745479156793;
//# sourceMappingURL=1745479156793-create-order-item-table.js.map