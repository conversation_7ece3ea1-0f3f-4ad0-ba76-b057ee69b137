"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShopInformationSettingModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const shop_information_settings_entity_1 = require("./entities/shop-information-settings.entity");
const shop_information_settings_controller_1 = require("./shop-information-settings.controller");
const shop_information_settings_service_1 = require("./shop-information-settings.service");
const site_module_1 = require("../site/site.module");
const shipping_note__settings_entity_1 = require("../shipping-note-settings/entities/shipping-note--settings.entity");
const delivery_receipt_settings_entity_1 = require("../delivery-receipt-settings/entities/delivery-receipt-settings.entity");
const order_complete_settings_entity_1 = require("../order-complete-settings/entities/order-complete-settings.entity");
const payment_method_entity_1 = require("../payment-method/entities/payment-method.entity");
let ShopInformationSettingModule = class ShopInformationSettingModule {
};
exports.ShopInformationSettingModule = ShopInformationSettingModule;
exports.ShopInformationSettingModule = ShopInformationSettingModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                shop_information_settings_entity_1.ShopInformationSettingEntity,
                shipping_note__settings_entity_1.ShippingNoteSettingEntity,
                payment_method_entity_1.PaymentMethodEntity,
                delivery_receipt_settings_entity_1.DeliveryReceiptSettingEntity,
                order_complete_settings_entity_1.OrderCompletionSettingEntity,
            ]),
            site_module_1.SiteModule,
        ],
        controllers: [shop_information_settings_controller_1.ShopInformationSettingController],
        providers: [shop_information_settings_service_1.ShopInformationSettingService],
        exports: [shop_information_settings_service_1.ShopInformationSettingService],
    })
], ShopInformationSettingModule);
//# sourceMappingURL=shop-information-settings.module.js.map