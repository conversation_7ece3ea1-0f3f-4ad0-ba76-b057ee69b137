"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSubscriptionTable1743410233708 = void 0;
const typeorm_1 = require("typeorm");
class CreateSubscriptionTable1743410233708 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}subscriptions`;
        this.PLANS_TABLE = `${process.env.ENTITY_PREFIX || ''}plans`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'userId',
                    type: 'varchar',
                    length: '36',
                    isNullable: false,
                },
                {
                    name: 'planId',
                    type: 'integer',
                },
                {
                    name: 'stripeSubscriptionId',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'status',
                    type: 'varchar',
                    length: '50',
                    isNullable: false,
                },
                {
                    name: 'currentPeriodStart',
                    type: 'timestamptz',
                    isNullable: false,
                },
                {
                    name: 'currentPeriodEnd',
                    type: 'timestamptz',
                    isNullable: false,
                },
                {
                    name: 'cancelAt',
                    type: 'timestamptz',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateSubscriptionTable1743410233708 = CreateSubscriptionTable1743410233708;
//# sourceMappingURL=1743410233708-create-subscription-table.js.map