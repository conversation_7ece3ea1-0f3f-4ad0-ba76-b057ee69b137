import { TemplateMigrationService } from './template-migration.service';
export declare class TemplateMigrationController {
    private readonly templateMigrationService;
    constructor(templateMigrationService: TemplateMigrationService);
    importTemplate(): Promise<void>;
    importStructure(): Promise<number>;
    createSite(templateId: string): Promise<void>;
    importPorfolioTemplate(): Promise<void>;
    importContactTemplate(): Promise<void>;
    importContactPhoto(): Promise<void>;
    importProfileTemplate(): Promise<void>;
    importTempTemplate(): Promise<void>;
}
