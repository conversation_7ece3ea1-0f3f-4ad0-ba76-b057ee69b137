"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateContentsTable1747970237557 = void 0;
const typeorm_1 = require("typeorm");
class UpdateContentsTable1747970237557 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}contents`;
    }
    async up(queryRunner) {
        const siteIdColumn = new typeorm_1.TableColumn({
            name: 'siteId',
            type: 'integer',
            isNullable: false,
            default: '1',
        });
        await queryRunner.addColumn(this.TABLE_NAME, siteIdColumn);
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_contents_siteId',
            columnNames: ['siteId'],
            isUnique: false,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_contents_siteId');
        await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
    }
}
exports.UpdateContentsTable1747970237557 = UpdateContentsTable1747970237557;
//# sourceMappingURL=1747970237557-update-contents-table.js.map