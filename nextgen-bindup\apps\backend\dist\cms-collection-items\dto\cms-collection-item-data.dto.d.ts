export interface CmsCollectionItemData {
    id: number;
    value: CollectionItemValue | CollectionItemValue[];
}
export type CollectionItemValue = string | number | boolean | Date | string[] | number[] | boolean[] | Date[] | CollectionItemExtraValue | null;
export type CollectionItemExtraValueType = 'media' | 'db-linkage';
export interface CollectionItemExtraValue {
    type: CollectionItemExtraValueType;
    vid?: number;
}
export interface CollectionItemExtraValueMedia extends CollectionItemExtraValue {
    type: 'media';
    url: string[];
}
export interface CollectionItemExtraValueDBLink extends CollectionItemExtraValue {
    type: 'db-linkage';
    id: number[];
}
