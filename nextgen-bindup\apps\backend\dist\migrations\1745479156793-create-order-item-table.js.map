{"version": 3, "file": "1745479156793-create-order-item-table.js", "sourceRoot": "", "sources": ["../../src/migrations/1745479156793-create-order-item-table.ts"], "names": [], "mappings": ";;;AAAA,qCAKiB;AAEjB,MAAa,iCAAiC;IAA9C;QACE,eAAU,GAAW,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,aAAa,CAAC;QACrE,qBAAgB,GAAW,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,QAAQ,CAAC;IA6ExE,CAAC;IA3EQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,WAAW,CAC3B,IAAI,eAAK,CAAC;YACR,IAAI,EAAE,aAAa;YACnB,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,KAAK;oBACX,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,IAAI;oBACjB,kBAAkB,EAAE,WAAW;iBAChC;gBACD;oBACE,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,KAAK;oBACX,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,KAAK;oBACX,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,KAAK;oBACX,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;oBAC5B,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;oBAC5B,UAAU,EAAE,KAAK;iBAClB;aACF;SACF,CAAC,EACF,IAAI,CACL,CAAC;QAGF,MAAM,WAAW,CAAC,gBAAgB,CAChC,IAAI,CAAC,UAAU,EACf,IAAI,yBAAe,CAAC;YAClB,WAAW,EAAE,CAAC,SAAS,CAAC;YACxB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,IAAI,CAAC,gBAAgB;YAC1C,QAAQ,EAAE,SAAS;SACpB,CAAC,CACH,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC/C,CAAC;CACF;AA/ED,8EA+EC"}