import { Module } from '@nestjs/common';
import { CmsCollectionItemsController } from './cms-collection-items.controller';
import { CmsCollectionItemsService } from './cms-collection-items.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CmsCollectionItemEntity } from './entities/cms-collection-items.entity';
import { CmsCollectionModule } from 'src/cms-collection/cms-collection.module';
import { ProductModule } from 'src/product/product.module';
import { ShopInformationSettingModule } from 'src/shop-information-settings/shop-information-settings.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CmsCollectionItemEntity]),
    CmsCollectionModule,
    ProductModule,
    ShopInformationSettingModule,
  ],
  controllers: [CmsCollectionItemsController],
  providers: [CmsCollectionItemsService],
  exports: [CmsCollectionItemsService],
})
export class CmsCollectionItemsModule {}
