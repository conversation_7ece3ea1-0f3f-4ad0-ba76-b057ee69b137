"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateTemplatePagesTable1750649574429 = void 0;
const typeorm_1 = require("typeorm");
class UpdateTemplatePagesTable1750649574429 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}template_pages`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'templatePageId',
            type: 'integer',
            isNullable: false,
            default: '0',
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'templatePageId');
    }
}
exports.UpdateTemplatePagesTable1750649574429 = UpdateTemplatePagesTable1750649574429;
//# sourceMappingURL=1750649574429-update-template-page-table.js.map