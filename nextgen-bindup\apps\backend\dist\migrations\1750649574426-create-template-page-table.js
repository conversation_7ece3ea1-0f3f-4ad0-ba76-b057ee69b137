"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTemplatePagesTable1750649574426 = void 0;
const page_type_1 = require("../page/types/page.type");
const typeorm_1 = require("typeorm");
class CreateTemplatePagesTable1750649574426 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}template_pages`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'templateSiteId',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'type',
                    type: 'varchar',
                    length: '10',
                    isNullable: false,
                    default: `'${page_type_1.PageType.PAGE}'`,
                },
                {
                    name: 'parentId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'components',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'ts',
                    type: 'bigint',
                    isNullable: true,
                },
                {
                    name: 'status',
                    type: 'smallint',
                    isNullable: false,
                    default: `'${page_type_1.PageStatus.DRAFT}'`,
                },
                {
                    name: 'url',
                    type: 'varchar',
                    length: '255',
                },
                {
                    name: 'title',
                    type: 'varchar',
                    length: '255',
                },
                {
                    name: 'description',
                    type: 'text',
                },
                {
                    name: 'isSearch',
                    type: 'boolean',
                },
                {
                    name: 'thumb',
                    type: 'varchar',
                    length: '255',
                },
                {
                    name: 'headCode',
                    type: 'text',
                },
                {
                    name: 'bodyCode',
                    type: 'text',
                },
                {
                    name: 'isPrivate',
                    type: 'boolean',
                    isNullable: false,
                    default: 'false',
                },
                {
                    name: 'isHome',
                    type: 'boolean',
                    isNullable: false,
                    default: 'false',
                },
                {
                    name: 'children',
                    type: 'jsonb',
                    isNullable: true,
                    isArray: true,
                },
                {
                    name: 'userId',
                    type: 'varchar',
                    length: '36',
                    isNullable: false,
                },
                {
                    name: 'isDeleted',
                    type: 'boolean',
                    isNullable: true,
                    default: 'false',
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateTemplatePagesTable1750649574426 = CreateTemplatePagesTable1750649574426;
//# sourceMappingURL=1750649574426-create-template-page-table.js.map