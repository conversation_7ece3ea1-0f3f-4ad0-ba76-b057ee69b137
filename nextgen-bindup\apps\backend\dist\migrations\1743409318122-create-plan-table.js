"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePlanTable1743409318122 = void 0;
const typeorm_1 = require("typeorm");
class CreatePlanTable1743409318122 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}plans`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'stripeProductId',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'stripePriceId',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'price',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'currency',
                    type: 'varchar',
                    length: '10',
                    isNullable: false,
                },
                {
                    name: 'interval',
                    type: 'varchar',
                    length: '50',
                    isNullable: false,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'isActive',
                    type: 'boolean',
                    isNullable: false,
                    default: true,
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreatePlanTable1743409318122 = CreatePlanTable1743409318122;
//# sourceMappingURL=1743409318122-create-plan-table.js.map