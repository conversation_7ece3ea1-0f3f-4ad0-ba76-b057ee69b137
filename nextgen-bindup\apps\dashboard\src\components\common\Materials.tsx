import axios from 'axios';
import queryString from 'query-string';
import { useEffect, useState, type FC } from 'react';
import { useTranslation } from 'react-i18next';
import LogoutIcon from '@mui/icons-material/Logout';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import {
  Alert,
  Box,
  CardActionArea,
  CircularProgress,
  Pagination,
  Snackbar,
  Stack,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import { AssetEntity } from '../../dto/asset.dto';
import { assetsService } from '../../services/assets-service';
import { R2Storage } from '../../services/r2-storage-service';
import { NEW_TS } from '../../utils/number.util';

type MaterialImage = {
  sozaiNo: string;
  sozaiName: string;
  favoriteImagePath: string;
  materialsUsedPointKey: string;
};

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface MaterialsProps {}

const Materials: FC<MaterialsProps> = () => {
  const { t } = useTranslation();

  const tabs = [
    { label: t('asset.media.material.tab.search'), value: 'searchImages' },
    { label: t('asset.media.material.tab.owned'), value: 'ownedImages' },
  ];
  const [selectedTab, setSelectedTab] = useState('searchImages');
  const [searchTerm, setSearchTerm] = useState('');
  const [images, setImages] = useState<MaterialImage[]>([]);
  const [ownedImages, setOwnedImages] = useState<MaterialImage[]>([]);
  const [hoverId, setHoverId] = useState<string | undefined>();
  const [selectedId, setSelectedId] = useState<string | undefined>();
  const [pageData, setPageData] = useState({
    page: 1,
    count: 0,
  });

  const [loading, setLoading] = useState(false);
  const [alertImport, setAlertImport] = useState(false);

  const handleChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPageData({ ...pageData, page: value });
  };

  const getImages = async () => {
    try {
      if (images.length < 1) {
        setLoading(true);
      }
      const { data } = await axios.post(
        'https://materials.digitalstage.jp/sozai/list/search',
        queryString.stringify({
          displayCount: 50,
          sortSearch: 2,
          chkSozaiType: 'photo',
          keywordTxt: searchTerm,
          pageNo: pageData.page,
        }),
      );
      setPageData({
        page: pageData.page,
        count: data.ttlPageCount,
      });
      setImages(data.sozaiList);
      setLoading(false);
    } catch (error) {
      console.error('getImages', error);
      setLoading(false);
    }
  };

  const getOwnedImages = async () => {
    try {
      if (ownedImages.length < 1) {
        setLoading(true);
      }
      const { data } = await axios.post(
        'https://materials.digitalstage.jp/api/purchased/',
        queryString.stringify({
          mak: 'iHGJ+CTcQJ9st4y+XbU90xbt+fyB3wkXaAtYCmzyr98=',
        }),
      );
      setPageData({
        page: pageData.page,
        count: data.ttlPageCount,
      });
      setOwnedImages(data.items);
      setLoading(false);
    } catch (error) {
      console.error('getOwnedImages', error);
      setLoading(false);
    }
  };

  const importImage = async (item: MaterialImage) => {
    const response = await axios.post(
      'https://materials.digitalstage.jp/api/download/',
      queryString.stringify({
        mak: 'iHGJ+CTcQJ9st4y+XbU90xbt+fyB3wkXaAtYCmzyr98=',
        downloadKey: item.materialsUsedPointKey,
      }),
      {
        responseType: 'arraybuffer',
      },
    );
    const imageData = response.data;
    const contentType = response.headers['content-type'];

    const blob = new Blob([imageData], { type: contentType });
    const file = new File([blob], `${item.sozaiNo}.jpg`, { type: contentType });

    const filename: string = file.name;
    const i: number = filename.lastIndexOf('.');
    const projectId = 1;
    let asset: AssetEntity = {
      id: 0,
      type: filename.substring(i + 1).toLocaleLowerCase(),
      projectId: projectId,
      name: filename.substring(0, i),
      url: null,
    };
    const fileNamePath: string = `${projectId}-${NEW_TS()}-${filename}`;
    const publicUrl: string = await R2Storage.uploadFile(fileNamePath, file);
    asset.url = publicUrl;
    asset = await assetsService.create(asset);
    setAlertImport(true);
  };

  useEffect(() => {
    switch (selectedTab) {
      case 'searchImages':
        getImages();
        break;
      case 'ownedImages':
        getOwnedImages();
        break;
      default:
        break;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTab, searchTerm, pageData.page]);

  const handleChangeTab = (_event: React.SyntheticEvent, newValue: string) => {
    setSelectedTab(newValue);
  };

  return (
    <Box>
      <Tabs
        value={selectedTab}
        onChange={handleChangeTab}
        aria-label="basic tabs"
      >
        {tabs.map((tab, index) => (
          <Tab
            key={index}
            value={tab.value}
            label={
              <Typography textTransform="none" variant="body1">
                {tab.label}
              </Typography>
            }
          />
        ))}
      </Tabs>
      {selectedTab === 'searchImages' && (
        <Stack spacing={1} sx={{ marginTop: 2 }}>
          <TextField
            value={searchTerm}
            onChange={event => setSearchTerm(event.target.value)}
            size="small"
            id="outlined-basic"
            label={t('common.search')}
            variant="filled"
            sx={{ width: '500px', marginTop: '10px' }}
          />
          {loading ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                marginTop: '20px',
              }}
            >
              <CircularProgress />
            </Box>
          ) : (
            <Stack spacing={1}>
              <Box
                sx={{
                  maxHeight: 'calc(100vh - 400px)',
                  overflow: 'auto',
                }}
              >
                <Grid container spacing="10px">
                  {images.map((item, index) => (
                    <Grid key={index}>
                      <CardActionArea
                        onMouseOver={() => setHoverId(item.sozaiNo)}
                        onMouseOut={() => setHoverId(undefined)}
                        onClick={() => {
                          setSelectedId(item.sozaiNo);
                        }}
                        key={index}
                        sx={{
                          backgroundColor:
                            selectedId === item.sozaiNo ? '#E7E7E7' : 'white',
                        }}
                        title={item.sozaiName}
                      >
                        <Stack
                          alignItems="center"
                          sx={{
                            position: 'relative',
                          }}
                        >
                          <Box
                            sx={{
                              width: '270px',
                              height: '195px',
                            }}
                          >
                            <img
                              src={
                                item.favoriteImagePath.startsWith('http')
                                  ? item.favoriteImagePath
                                  : `https://materials.digitalstage.jp${item.favoriteImagePath}`
                              }
                              alt={item.sozaiNo}
                              style={{
                                objectFit: 'contain',
                                padding: '5px',
                              }}
                              width="100%"
                              height="100%"
                              loading="lazy"
                            />
                          </Box>
                          <Typography variant="body2">
                            {item.sozaiNo}
                          </Typography>
                          {(hoverId === item.sozaiNo ||
                            selectedId === item.sozaiNo) && (
                            <Box
                              sx={{
                                position: 'absolute',
                                bottom: '0',
                                left: '0',
                                backgroundColor:
                                  selectedId === item.sozaiNo
                                    ? '#E7E7E7'
                                    : 'white',
                                padding: '5px',
                                width: '100%',
                                height: '20%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                              onClick={() => {
                                window.open(
                                  `https://materials.digitalstage.jp/api/detail/${item.sozaiNo}?mak=iHGJ%2BCTcQJ9st4y%2BXbU90xbt%2BfyB3wkXaAtYCmzyr98%3D`,
                                  '_blank',
                                );
                              }}
                            >
                              <ShoppingCartIcon />
                              <Typography variant="body2">
                                {t('asset.media.selectSize')}
                              </Typography>
                            </Box>
                          )}
                        </Stack>
                      </CardActionArea>
                    </Grid>
                  ))}
                </Grid>
              </Box>
              <Pagination
                page={pageData.page}
                count={pageData.count}
                variant="outlined"
                shape="rounded"
                onChange={handleChange}
              />
            </Stack>
          )}
        </Stack>
      )}
      {selectedTab === 'ownedImages' && (
        <Box
          sx={{
            maxHeight: 'calc(100vh - 400px)',
            overflow: 'auto',
            paddingTop: 1,
          }}
        >
          <Grid container spacing="10px">
            {ownedImages.map((item, index) => (
              <Grid key={index}>
                <CardActionArea
                  onMouseOver={() => setHoverId(item.sozaiNo)}
                  onMouseOut={() => setHoverId(undefined)}
                  onClick={() => {
                    setSelectedId(item.sozaiNo);
                  }}
                  key={index}
                  sx={{
                    backgroundColor:
                      selectedId === item.sozaiNo ? '#E7E7E7' : 'white',
                  }}
                  title={item.sozaiName}
                >
                  <Stack
                    alignItems="center"
                    sx={{
                      position: 'relative',
                    }}
                  >
                    <Box
                      sx={{
                        width: '270px',
                        height: '195px',
                      }}
                    >
                      <img
                        src={
                          item.favoriteImagePath.startsWith('http')
                            ? item.favoriteImagePath
                            : `https://materials.digitalstage.jp${item.favoriteImagePath}`
                        }
                        alt={item.sozaiNo}
                        style={{
                          objectFit: 'contain',
                          padding: '5px',
                        }}
                        width="100%"
                        height="100%"
                        loading="lazy"
                      />
                    </Box>
                    <Typography variant="body2">{item.sozaiNo}</Typography>
                    {(hoverId === item.sozaiNo ||
                      selectedId === item.sozaiNo) && (
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: '0',
                          left: '0',
                          backgroundColor:
                            selectedId === item.sozaiNo ? '#E7E7E7' : 'white',
                          padding: '5px',
                          width: '100%',
                          height: '20%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onClick={() => {
                          importImage(item);
                        }}
                      >
                        <LogoutIcon />
                        <Typography variant="body2">
                          {t('asset.media.material.importImage')}
                        </Typography>
                      </Box>
                    )}
                  </Stack>
                </CardActionArea>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
      <Snackbar
        open={alertImport}
        autoHideDuration={3000}
        onClose={() => setAlertImport(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setAlertImport(false)}
          severity="success"
          sx={{ width: '100%' }}
        >
          {t('asset.media.importSuccess')}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Materials;
