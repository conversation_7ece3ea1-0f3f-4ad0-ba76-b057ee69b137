import { Modu<PERSON> } from '@nestjs/common';
import { UserManagementService } from './user-management.service';
import { UserManagementController } from './user-management.controller';
import { IdentityModule } from 'src/identity/identity.module';
import { TeamModule } from 'src/team/team.module';
import { UserInfoModule } from 'src/user-info/user-info.module';
import { UserTeamModule } from 'src/user-team/user-team.module';

@Module({
  imports: [IdentityModule, TeamModule, UserInfoModule, UserTeamModule],
  providers: [UserManagementService],
  controllers: [UserManagementController],
  exports: [UserManagementService],
})
export class UserManagementModule {}
