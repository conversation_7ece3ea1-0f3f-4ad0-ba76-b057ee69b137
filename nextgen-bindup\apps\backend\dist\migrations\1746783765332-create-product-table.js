"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateProductTable1746783765332 = void 0;
const typeorm_1 = require("typeorm");
class CreateProductTable1746783765332 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}products`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'siteId',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'isOrderable',
                    type: 'boolean',
                    isNullable: false,
                },
                {
                    name: 'code',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'title',
                    type: 'varchar',
                    length: '250',
                    isNullable: true,
                },
                {
                    name: 'description',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'images',
                    type: 'text',
                    isArray: true,
                    isNullable: true,
                },
                {
                    name: 'price',
                    type: 'bigint',
                    isNullable: false,
                },
                {
                    name: 'sale',
                    type: 'bigint',
                    isNullable: false,
                },
                {
                    name: 'purchaseLimitQuantity',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'individualShippingCharges',
                    type: 'bigint',
                    isNullable: false,
                },
                {
                    name: 'unlimitedPurchase',
                    type: 'boolean',
                    isNullable: false,
                },
                {
                    name: 'productType',
                    type: 'varchar',
                    length: '10',
                    isNullable: false,
                },
                {
                    name: 'stockQuantity',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'productVariantType',
                    type: 'varchar',
                    length: '50',
                    isNullable: false,
                },
                {
                    name: 'fileDownload',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'attributes',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'stockVariants',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateProductTable1746783765332 = CreateProductTable1746783765332;
//# sourceMappingURL=1746783765332-create-product-table.js.map