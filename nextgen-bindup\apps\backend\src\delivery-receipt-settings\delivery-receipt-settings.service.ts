import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeliveryReceiptSettingEntity } from './entities/delivery-receipt-settings.entity';
import { Repository } from 'typeorm';
import { AppException } from 'src/common/exceptions/app.exception';
import { getDefaultDeliveryReceiptSettings } from './delivery-receipt-settings.util';

@Injectable()
export class DeliveryReceiptSettingsService {
  @InjectRepository(DeliveryReceiptSettingEntity)
  readonly deliveryReceiptSettingRepo: Repository<DeliveryReceiptSettingEntity>;

  constructor() {}

  async update(
    id: number,
    settingData: Partial<DeliveryReceiptSettingEntity>,
  ): Promise<DeliveryReceiptSettingEntity> {
    const setting = await this.deliveryReceiptSettingRepo.findOneBy({ id: id });
    if (!setting) {
      throw new AppException('api.error.delivery_receipt_setting_not_found');
    }

    delete settingData.id;
    delete settingData.siteId;
    delete settingData.createdAt;
    settingData.updatedAt = new Date();

    await this.deliveryReceiptSettingRepo.update(id, settingData);
    return { ...setting, ...settingData };
  }

  async findById(id: number): Promise<DeliveryReceiptSettingEntity> {
    return await this.deliveryReceiptSettingRepo.findOneBy({ id });
  }

  async findOneBySiteId(siteId: number): Promise<DeliveryReceiptSettingEntity> {
    let setting = await this.deliveryReceiptSettingRepo.findOneBy({ siteId });
    if (!setting) {
      setting = this.getDefaultSetting(siteId);
      setting = await this.deliveryReceiptSettingRepo.save(setting);
    }
    return setting;
  }

  private getDefaultSetting(siteId: number): DeliveryReceiptSettingEntity {
    const now: Date = new Date();
    const setting = new DeliveryReceiptSettingEntity();
    const defaultDeliveryReceipt = getDefaultDeliveryReceiptSettings('', '');
    setting.siteId = siteId;
    setting.header = defaultDeliveryReceipt.header;
    setting.footer = defaultDeliveryReceipt.footer;
    setting.createdAt = now;
    setting.updatedAt = now;
    return setting;
  }

  async delete(id: number): Promise<boolean> {
    const setting = await this.deliveryReceiptSettingRepo.findOneBy({ id });
    if (!setting) {
      throw new AppException('api.error.delivery_receipt_setting_not_found');
    }

    await this.deliveryReceiptSettingRepo.delete(id);
    return true;
  }
}
