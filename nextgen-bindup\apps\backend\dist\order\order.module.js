"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderModule = void 0;
const common_1 = require("@nestjs/common");
const order_service_1 = require("./order.service");
const typeorm_1 = require("@nestjs/typeorm");
const order_entity_1 = require("./entites/order.entity");
const order_item_entity_1 = require("./entites/order-item.entity");
const order_controller_1 = require("./order.controller");
const product_module_1 = require("../product/product.module");
const shipping_note_settings_module_1 = require("../shipping-note-settings/shipping-note-settings.module");
const shop_information_settings_module_1 = require("../shop-information-settings/shop-information-settings.module");
const csv_service_1 = require("../product/csv.service");
let OrderModule = class OrderModule {
};
exports.OrderModule = OrderModule;
exports.OrderModule = OrderModule = __decorate([
    (0, common_1.Module)({
        imports: [
            product_module_1.ProductModule,
            shipping_note_settings_module_1.ShippingNoteSettingModule,
            shop_information_settings_module_1.ShopInformationSettingModule,
            typeorm_1.TypeOrmModule.forFeature([order_entity_1.OrderEntity]),
            typeorm_1.TypeOrmModule.forFeature([order_item_entity_1.OrderItemEntity]),
        ],
        providers: [order_service_1.OrderService, csv_service_1.CsvService],
        exports: [order_service_1.OrderService],
        controllers: [order_controller_1.OrderController],
    })
], OrderModule);
//# sourceMappingURL=order.module.js.map