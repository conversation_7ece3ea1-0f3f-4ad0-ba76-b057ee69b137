"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserTeam1740982117025 = void 0;
const typeorm_1 = require("typeorm");
class CreateUserTeam1740982117025 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}user_team`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'rootUserId',
                    type: 'varchar',
                    length: '36',
                    isNullable: false,
                },
                {
                    name: 'userId',
                    type: 'varchar',
                    length: '36',
                    isNullable: true,
                },
                {
                    name: 'email',
                    type: 'varchar',
                    length: '500',
                    isNullable: false,
                },
                {
                    name: 'teamId',
                    type: 'integer',
                    isNullable: true,
                },
            ],
        }), true);
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_user_team_unique',
            columnNames: ['rootUserId', 'email', 'teamId'],
            isUnique: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_user_team_unique');
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateUserTeam1740982117025 = CreateUserTeam1740982117025;
//# sourceMappingURL=1740982117025-create-user_team-table.js.map