import { PaymentMethodService } from './payment-method.service';
import { PaymentMethodResponseDto } from './dto/payment-method-response.dto';
import { PaymentMethodEntity } from './entities/payment-method.entity';
export declare class PaymentMethodController {
    private readonly paymentMethodService;
    constructor(paymentMethodService: PaymentMethodService);
    getPaymentMethod(siteId: number): Promise<PaymentMethodResponseDto>;
    updatePaymentMethod(siteId: number, paymentMethod: Partial<PaymentMethodEntity>): Promise<boolean>;
    enableStripePaymentGateway(siteId: string): Promise<{
        success: boolean;
        onboardingUrl: string;
    }>;
    checkStripeStatus(siteId: string): Promise<import("./dto/stripe-infomation.dto").StripeInformation>;
    removeStripeAccount(siteId: string): Promise<boolean>;
}
