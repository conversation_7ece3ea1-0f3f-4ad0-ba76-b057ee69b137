import { ArgumentsHost, Catch, ExceptionFilter } from '@nestjs/common';
import { AppException } from './app.exception';
import { Response } from 'express';
import { HttpArgumentsHost } from '@nestjs/common/interfaces';
import { LoggerService } from '../../logger/logger.service';

@Catch(AppException)
export class AppExceptionFilter implements ExceptionFilter {
  constructor(private readonly loggerService: LoggerService) {}

  catch(exception: AppException, host: ArgumentsHost) {
    const ctx: HttpArgumentsHost = host.switchToHttp();
    const response: Response = ctx.getResponse<Response>();
    const status: number = exception.getStatus();

    if (response.locals.requestLog) {
      this.loggerService.error(response.locals.shop, exception.message, {
        meta: response.locals.requestLog,
      });
      response.locals.requestLog = undefined;
    }

    console.error('App exception:', exception);
    response.status(status).json({ data: exception.message });
  }
}
