"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderCompletionSettingEntity = void 0;
const typeorm_1 = require("typeorm");
let OrderCompletionSettingEntity = class OrderCompletionSettingEntity {
};
exports.OrderCompletionSettingEntity = OrderCompletionSettingEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], OrderCompletionSettingEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'siteId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], OrderCompletionSettingEntity.prototype, "siteId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'displayText',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], OrderCompletionSettingEntity.prototype, "displayText", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'emailSubject',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], OrderCompletionSettingEntity.prototype, "emailSubject", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'emailHeader',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], OrderCompletionSettingEntity.prototype, "emailHeader", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'emailFooter',
        type: 'text',
        nullable: true,
    }),
    __metadata("design:type", String)
], OrderCompletionSettingEntity.prototype, "emailFooter", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], OrderCompletionSettingEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], OrderCompletionSettingEntity.prototype, "updatedAt", void 0);
exports.OrderCompletionSettingEntity = OrderCompletionSettingEntity = __decorate([
    (0, typeorm_1.Entity)('order_completion_settings', { schema: process.env.DATABASE_SCHEMA })
], OrderCompletionSettingEntity);
//# sourceMappingURL=order-complete-settings.entity.js.map