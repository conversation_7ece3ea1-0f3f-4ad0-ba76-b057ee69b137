import { CollectionItemStatus } from '../types/cms-collection-item.type';
import { CmsCollectionItemData } from '../dto/cms-collection-item-data.dto';
export declare class CmsCollectionItemEntity {
    id: number;
    cmsCollectionId: number;
    title: string;
    slug: string;
    data: Record<number, CmsCollectionItemData>;
    status: CollectionItemStatus;
    siteId: number;
    rootUserId: string;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
}
