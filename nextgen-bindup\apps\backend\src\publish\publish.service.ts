import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import { promisify } from 'util';
import * as path from 'path';
import { exec } from 'child_process';
import { PageService } from 'src/page/page.service';
import { FtpService } from './ftp.service';
import { PageType } from 'src/page/types/page.type';
import { UserPaymentService } from 'src/payment/user-payment.service';
import { PublishPagesReq } from '@nextgen-bindup/common/dto/publish/publish-page.dto';
import { v4 as uuidv4 } from 'uuid';
import { CmsCollectionItemsService } from 'src/cms-collection-items/cms-collection-items.service';
import { CmsCollectionItemEntity } from 'src/cms-collection-items/entities/cms-collection-items.entity';
import { RenderPage } from 'src/page/dto/page.dto';
import { SiteAuthService } from 'src/proxy/site-auth.service';
const execPromise = promisify(exec);

@Injectable()
export class PublishService {
  private baseDir: string;

  constructor(
    private readonly pageService: PageService,
    private readonly ftpService: FtpService,
    private readonly userPaymentService: UserPaymentService,
    private readonly cmsCollectionItemsService: CmsCollectionItemsService,
    private readonly siteAuthService: SiteAuthService,
  ) {
    this.baseDir = path.resolve(
      __dirname,
      '..',
      '..',
      '..',
      '..',
      'apps',
      'ssg',
    );
  }

  async publish(
    projectId: number,
    siteId: number,
    req: PublishPagesReq,
  ): Promise<boolean> {
    const buildDir = await this.buildStaticSite(projectId, siteId, req);
    if (buildDir) {
      await this.ftpService.uploadFTP(buildDir, '/');
      fs.rmSync(buildDir, { recursive: true, force: true });
      console.log('Published successfully');
      return true;
    }
    return false;
  }

  async buildStaticSite(
    projectId: number,
    siteId: number,
    req: PublishPagesReq,
  ) {
    const buildOutput: string = `out/${uuidv4()}`;
    const fullBuildOutput: string = path.join(this.baseDir, buildOutput);
    console.log('fullBuildOutput:', fullBuildOutput);

    const [allPages, collectionItems] = await Promise.all([
      this.pageService.getBySiteIdForRender(siteId) as unknown as RenderPage[],
      this.cmsCollectionItemsService.findBySiteId(siteId),
    ]);

    const pagesStructure: RenderPage[] = this.buildStructure(
      allPages,
      collectionItems,
    );

    // STEP 1: BUILD SSG
    let pageIds: string = '';
    if (req.pageIds) pageIds = req.pageIds.join(',');

    const siteToken = await this.siteAuthService.generateToken(`${siteId}`);
    const { stdout, stderr } = await execPromise(
      `cd ../ssg && yarn build --outDir ${buildOutput}`,
      {
        env: {
          ...process.env,
          NEXT_PUBLIC_SUPABASE_URL: process.env.SUPABASE_URL,
          NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.SUPABASE_KEY,
          NEXT_PUBLIC_PROJECT_ID: `${projectId}`,
          NEXT_PUBLIC_SITE_ID: `${siteId}`,
          NEXT_PUBLIC_PAGE_ID: pageIds,
          NEXT_PUBLIC_SITE_TOKEN: siteToken,
        },
      },
    );

    console.log(stdout);

    if (stderr) {
      throw new Error(stderr);
    }

    if (!fs.existsSync(fullBuildOutput)) {
      console.log(`NOT FOUND: ${fullBuildOutput}`);
      return null;
    }

    const refactorOutput: string = `${fullBuildOutput}_upload`;
    fs.mkdirSync(refactorOutput);
    for (const page of pagesStructure) {
      const newPath: string = this.createPath(refactorOutput, page.paths);
      this.moveFile(fullBuildOutput, page, newPath);
    }

    const fixFolders: string[] = [
      '_astro',
      'cart',
      'checkout',
      'order-complete',
    ];
    for (const folder of fixFolders) {
      fs.cpSync(`${fullBuildOutput}/${folder}`, `${refactorOutput}/${folder}`, {
        recursive: true,
      });
    }

    const fixFiles: string[] = ['favicon.ico'];
    for (const file of fixFiles) {
      fs.copyFileSync(
        `${fullBuildOutput}/${file}`,
        `${refactorOutput}/${file}`,
        fs.constants.COPYFILE_EXCL,
      );
    }

    fs.rmSync(fullBuildOutput, { recursive: true, force: true });

    return refactorOutput;
  }

  private buildStructure(
    allPages: RenderPage[],
    collectionItems: Record<string, CmsCollectionItemEntity[]>,
  ): RenderPage[] {
    const result: RenderPage[] = [];
    for (const page of allPages) {
      if (page.type === PageType.ROOT) continue;

      page.paths = [];
      page.ssgID = `${page.id}`;
      if (page.type === PageType.BLOG || page.type === PageType.DIRECTORY) {
        page.paths.push(page.url || this.sanitizedUrl(page.name));
      }

      let parent: RenderPage = null;
      if (page.parentId) parent = allPages.find(p => p.id === page.parentId);
      if (parent && parent.type === PageType.ROOT) parent = null;
      while (parent) {
        if (parent.paths && parent.paths.length) {
          page.paths.unshift(...parent.paths);
          break;
        } else {
          page.paths.unshift(parent.url || this.sanitizedUrl(parent.name));
        }

        if (parent.parentId)
          parent = allPages.find(p => p.id === parent.parentId);
        if (parent && parent.type === PageType.ROOT) parent = null;
      }

      if (page.datasource?.collectionId && page.url) {
        const items: CmsCollectionItemEntity[] =
          collectionItems[page.datasource.collectionId];

        for (const item of items) {
          const blogPage: RenderPage = structuredClone(page);
          blogPage.ssgID = `${blogPage.id}-${item.cmsCollectionId}-${item.id}`;
          blogPage.url = blogPage.url
            .replace('{slug}', item.slug)
            .replace('{id}', `${item.id || '0'}`);

          result.push(blogPage);
        }
      }

      if (page.type === PageType.PAGE || page.type === PageType.BLOG_LIST) {
        if (page.isHome) page.url = 'index';
        else if (!page.url) page.url = this.sanitizedUrl(page.name);
      }
      result.push(page);
    }

    return result;
  }

  private sanitizedUrl(url: string): string {
    return url
      .toLocaleLowerCase()
      .trim()
      .replace(/ +/g, '_')
      .replace(/_+/g, '-');
  }

  private createPath(base: string, paths: string[]): string {
    let path = base;
    for (const p of paths) {
      path += `/${p}`;
      if (!fs.existsSync(path)) fs.mkdirSync(path);
    }

    return path;
  }

  private moveFile(base: string, page: RenderPage, newPath: string) {
    const oldFile: string = `${base}/${page.ssgID}/index.html`;
    const newFile: string = `${newPath}/${page.url}.html`;

    if (fs.existsSync(oldFile)) {
      fs.copyFileSync(oldFile, newFile, fs.constants.COPYFILE_EXCL);
    }
  }
}
