import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { DesignState } from '@nextgen-bindup/common/dto/design-state';
@Entity('assets', { schema: process.env.DATABASE_SCHEMA })
export class AssetEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'type',
    type: 'varchar',
    length: 20,
    nullable: false,
  })
  type: string;

  @Column({
    name: 'projectId',
    type: 'integer',
    nullable: true,
  })
  projectId: number;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  name: string;

  @Column({
    name: 'url',
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  url: string;

  @Column({
    name: 'designState',
    type: 'jsonb',
    nullable: true,
  })
  designState: DesignState;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
