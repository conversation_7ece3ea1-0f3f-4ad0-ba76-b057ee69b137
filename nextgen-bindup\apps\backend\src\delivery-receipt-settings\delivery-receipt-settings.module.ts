import { Module } from '@nestjs/common';
import { DeliveryReceiptSettingsController } from './delivery-receipt-settings.controller';
import { DeliveryReceiptSettingsService } from './delivery-receipt-settings.service';
import { DeliveryReceiptSettingEntity } from './entities/delivery-receipt-settings.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([DeliveryReceiptSettingEntity])],
  controllers: [DeliveryReceiptSettingsController],
  providers: [DeliveryReceiptSettingsService],
  exports: [DeliveryReceiptSettingsService],
})
export class DeliveryReceiptSettingModule {}
