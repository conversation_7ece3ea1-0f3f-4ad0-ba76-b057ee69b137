"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CmsCollectionItemEntity = void 0;
const typeorm_1 = require("typeorm");
let CmsCollectionItemEntity = class CmsCollectionItemEntity {
    constructor() {
        this.status = 'draft';
    }
};
exports.CmsCollectionItemEntity = CmsCollectionItemEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], CmsCollectionItemEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'cmsCollectionId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], CmsCollectionItemEntity.prototype, "cmsCollectionId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'title',
        type: 'varchar',
        length: 1000,
        nullable: false,
    }),
    __metadata("design:type", String)
], CmsCollectionItemEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'slug',
        type: 'varchar',
        length: 1000,
        nullable: true,
    }),
    __metadata("design:type", String)
], CmsCollectionItemEntity.prototype, "slug", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'data',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], CmsCollectionItemEntity.prototype, "data", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'status',
        type: 'varchar',
        length: 15,
        nullable: false,
        default: 'draft',
    }),
    __metadata("design:type", String)
], CmsCollectionItemEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'siteId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], CmsCollectionItemEntity.prototype, "siteId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'rootUserId',
        type: 'varchar',
        length: 36,
        nullable: false,
    }),
    __metadata("design:type", String)
], CmsCollectionItemEntity.prototype, "rootUserId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'userId',
        type: 'varchar',
        length: 36,
        nullable: false,
    }),
    __metadata("design:type", String)
], CmsCollectionItemEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], CmsCollectionItemEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], CmsCollectionItemEntity.prototype, "updatedAt", void 0);
exports.CmsCollectionItemEntity = CmsCollectionItemEntity = __decorate([
    (0, typeorm_1.Entity)('cms_collection_items', { schema: process.env.DATABASE_SCHEMA })
], CmsCollectionItemEntity);
//# sourceMappingURL=cms-collection-items.entity.js.map