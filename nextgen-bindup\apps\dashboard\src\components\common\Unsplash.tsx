import axios from 'axios';
import { useEffect, useState, type FC } from 'react';
import { useTranslation } from 'react-i18next';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import {
  Alert,
  Box,
  CardActionArea,
  CircularProgress,
  FormControl,
  InputLabel,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  MenuItem,
  Pagination,
  Select,
  SelectChangeEvent,
  Snackbar,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import { AssetEntity } from '../../dto/asset.dto';
import { assetsService } from '../../services/assets-service';
import { R2Storage } from '../../services/r2-storage-service';
import { NEW_TS } from '../../utils/number.util';

type UnsplashImage = {
  id: string;
  urls: {
    raw: string;
    regular: string;
  };
  description: string;
  user: {
    name: string;
    links: {
      html: string;
    };
  };
};

type UnsplashTopic = {
  slug: string;
  title: string;
};

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface UnsplashProps {}

const Unsplash: FC<UnsplashProps> = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [images, setImages] = useState<UnsplashImage[]>([]);
  const [hoverId, setHoverId] = useState<string | undefined>();
  const [selectedId, setSelectedId] = useState<string | undefined>();
  const [pageData, setPageData] = useState({
    page: 1,
    count: 0,
  });
  const [topics, setTopics] = useState<UnsplashTopic[]>([]);
  const [selectedTopic, setSelectedTopic] = useState('');

  const [loading, setLoading] = useState(false);
  const [sizeSelectedId, setSizeSelectedId] = useState('');
  const [alertImport, setAlertImport] = useState(false);

  const handleChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPageData({ ...pageData, page: value });
  };

  const getImages = async () => {
    try {
      if (images.length < 1) {
        setLoading(true);
      }
      const { data } = await axios.get(
        'https://api.unsplash.com/search/photos?client_id=I8ClUO0NubEE2z4cF-VtPWK777LSiv4XXolfP_Rl65s',
        {
          params: {
            query: searchTerm || selectedTopic || 'wallpapers',
            page: pageData.page,
            per_page: 30,
          },
        },
      );
      setPageData({
        page: pageData.page,
        count: data.total_pages,
      });
      setImages(data.results);
      setLoading(false);
    } catch (error) {
      console.error('getImages', error);
      setLoading(false);
    }
  };

  const getTopics = async () => {
    try {
      const { data } = await axios.get(
        'https://api.unsplash.com/topics?client_id=I8ClUO0NubEE2z4cF-VtPWK777LSiv4XXolfP_Rl65s',
      );
      setTopics(data);
      if (data.length > 0) {
        setSelectedTopic(data[0].slug);
      }
      setLoading(false);
    } catch (error) {
      console.error('getOwnedImages', error);
      setLoading(false);
    }
  };

  const importImage = async (item: UnsplashImage, width?: number) => {
    let url = item.urls.raw;
    let name = item.id;
    if (width) {
      url += `&w=${width}`;
      name += `-${width}`;
    }
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
    });
    const imageData = response.data;
    const contentType = response.headers['content-type'];

    const blob = new Blob([imageData], { type: contentType });
    const file = new File([blob], `${name}.jpg`, { type: contentType });
    const filename: string = file.name;
    const i: number = filename.lastIndexOf('.');
    const projectId = 1;
    let asset: AssetEntity = {
      id: 0,
      type: filename.substring(i + 1).toLocaleLowerCase(),
      projectId: projectId,
      name: filename.substring(0, i),
      url: null,
    };
    const fileNamePath: string = `${projectId}-${NEW_TS()}-${filename}`;
    const publicUrl: string = await R2Storage.uploadFile(fileNamePath, file);
    asset.url = publicUrl;
    asset = await assetsService.create(asset);
    setAlertImport(true);
  };

  useEffect(() => {
    getTopics();
  }, []);

  useEffect(() => {
    getImages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTopic, searchTerm, pageData.page]);

  return (
    <Box>
      <Stack spacing={1} sx={{ marginTop: 2 }}>
        <Stack direction="row" spacing={1}>
          <FormControl variant="filled">
            <InputLabel id="simple-select-label">
              {t('asset.media.unsplash.topic')}
            </InputLabel>
            <Select
              labelId="simple-select-label"
              id="simple-select"
              size="small"
              value={selectedTopic}
              label={t('asset.media.unsplash.topic')}
              onChange={(e: SelectChangeEvent) => {
                setSelectedTopic(e.target.value);
              }}
              sx={{ width: '300px' }}
            >
              {topics.map((topic, index) => (
                <MenuItem key={index} value={topic.slug}>
                  {topic.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <TextField
            value={searchTerm}
            onChange={event => setSearchTerm(event.target.value)}
            size="small"
            id="outlined-basic"
            label={t('common.search')}
            variant="filled"
            sx={{ width: '500px', marginTop: '10px' }}
          />
        </Stack>
        {loading ? (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              marginTop: '20px',
            }}
          >
            <CircularProgress />
          </Box>
        ) : (
          <Stack spacing={1}>
            <Box
              sx={{
                maxHeight: 'calc(100vh - 400px)',
                overflow: 'auto',
              }}
            >
              <Grid container spacing="10px">
                {images.map((item, index) => (
                  <Grid key={index}>
                    <CardActionArea
                      onMouseOver={() => setHoverId(item.id)}
                      onMouseOut={() => setHoverId(undefined)}
                      onClick={() => {
                        setSelectedId(item.id);
                      }}
                      key={index}
                      sx={{
                        backgroundColor:
                          selectedId === item.id ? '#E7E7E7' : 'white',
                      }}
                    >
                      <Stack
                        alignItems="center"
                        sx={{
                          position: 'relative',
                        }}
                      >
                        <Box
                          sx={{
                            width: '270px',
                            height: '195px',
                          }}
                        >
                          <img
                            src={item.urls.regular}
                            alt={item.description}
                            style={{
                              objectFit: 'contain',
                              padding: '5px',
                            }}
                            width="100%"
                            height="100%"
                            loading="lazy"
                          />
                        </Box>
                        {(hoverId === item.id || selectedId === item.id) && (
                          <>
                            <Box
                              sx={{
                                position: 'absolute',
                                bottom: '55px',
                                left: '0',
                                backgroundColor: '#cbc7c7',
                                padding: '5px',
                                width: 'calc(100% - 20px)',
                                height: '20%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '5px',
                                marginInline: '10px',
                              }}
                              onClick={() => {
                                window.open(item.user.links.html, '_blank');
                              }}
                            >
                              <Typography variant="body2">
                                {item.user.name}
                              </Typography>
                            </Box>
                            <Box
                              sx={{
                                position: 'absolute',
                                bottom: '10px',
                                left: '0',
                                backgroundColor: '#cbc7c7',
                                padding: '5px',
                                width: 'calc(100% - 20px)',
                                height: '20%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '5px',
                                marginInline: '10px',
                              }}
                              onClick={() => {
                                if (sizeSelectedId === item.id) {
                                  setSizeSelectedId('');
                                } else {
                                  setSizeSelectedId(item.id);
                                }
                              }}
                            >
                              <ShoppingCartIcon />
                              <Typography variant="body2">
                                {t('asset.media.selectSize')}
                              </Typography>
                              <Box
                                sx={{
                                  position: 'absolute',
                                  top: '40px',
                                  left: '0',
                                  padding: '5px',
                                  borderRadius: '5px',
                                  backgroundColor: 'white',
                                  cursor: 'pointer',
                                  width: '100%',
                                  zIndex: 100,
                                  display:
                                    selectedId === item.id &&
                                    sizeSelectedId === item.id
                                      ? 'block'
                                      : 'none',
                                }}
                              >
                                <List>
                                  <ListItem disablePadding>
                                    <ListItemButton>
                                      <ListItemText
                                        primary={t(
                                          'asset.media.unsplash.size.original',
                                        )}
                                        onClick={() => importImage(item)}
                                      />
                                    </ListItemButton>
                                  </ListItem>
                                  <ListItem disablePadding>
                                    <ListItemButton>
                                      <ListItemText
                                        primary={t(
                                          'asset.media.unsplash.size.1080',
                                        )}
                                        onClick={() => importImage(item, 1080)}
                                      />
                                    </ListItemButton>
                                  </ListItem>
                                  <ListItem disablePadding>
                                    <ListItemButton>
                                      <ListItemText
                                        primary={t(
                                          'asset.media.unsplash.size.400',
                                        )}
                                        onClick={() => importImage(item, 400)}
                                      />
                                    </ListItemButton>
                                  </ListItem>
                                  <ListItem disablePadding>
                                    <ListItemButton>
                                      <ListItemText
                                        primary={t(
                                          'asset.media.unsplash.size.200',
                                        )}
                                        onClick={() => importImage(item, 200)}
                                      />
                                    </ListItemButton>
                                  </ListItem>
                                </List>
                              </Box>
                            </Box>
                          </>
                        )}
                      </Stack>
                    </CardActionArea>
                  </Grid>
                ))}
              </Grid>
            </Box>
            <Pagination
              page={pageData.page}
              count={pageData.count}
              variant="outlined"
              shape="rounded"
              onChange={handleChange}
            />
          </Stack>
        )}
      </Stack>
      <Snackbar
        open={alertImport}
        autoHideDuration={3000}
        onClose={() => setAlertImport(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setAlertImport(false)}
          severity="success"
          sx={{ width: '100%' }}
        >
          {t('asset.media.importSuccess')}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Unsplash;
