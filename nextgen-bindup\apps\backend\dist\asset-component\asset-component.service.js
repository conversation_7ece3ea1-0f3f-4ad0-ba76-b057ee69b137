"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetComponentService = void 0;
const common_1 = require("@nestjs/common");
const asset_component_entity_1 = require("./entities/asset-component.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const project_service_1 = require("../project/project.service");
const app_exception_1 = require("../common/exceptions/app.exception");
let AssetComponentService = class AssetComponentService {
    constructor(projectService) {
        this.projectService = projectService;
    }
    async create(assetComponent) {
        const project = await this.projectService.findById(assetComponent.projectId);
        if (!project)
            throw new app_exception_1.AppException('error.project_not_found');
        const now = new Date();
        const component = new asset_component_entity_1.AssetComponent();
        component.type = assetComponent.type;
        component.projectId = assetComponent.projectId;
        component.siteId = assetComponent.siteId;
        component.name = assetComponent.name;
        component.data = assetComponent.data;
        component.createdAt = now;
        component.updatedAt = now;
        return await this.assetComponentRepository.save(component);
    }
    async update(id, assetComponent) {
        const component = await this.assetComponentRepository.findOneBy({ id: id });
        if (!component)
            throw new app_exception_1.AppException('error.asset_component_not_found');
        delete assetComponent.id;
        assetComponent.updatedAt = new Date();
        await this.assetComponentRepository.update(id, assetComponent);
        return { ...component, ...assetComponent };
    }
    async findById(id) {
        return await this.assetComponentRepository.findOneBy({ id });
    }
    async findByProjectId(projectId) {
        return await this.assetComponentRepository.findBy({ projectId });
    }
    async findBySiteId(projectId, siteId) {
        return await this.assetComponentRepository.findBy({
            projectId: projectId,
            siteId: siteId,
        });
    }
    async delete(id) {
        const asset = await this.assetComponentRepository.findOneBy({ id });
        if (!asset)
            throw new app_exception_1.AppException('error.asset_component_not_found');
        await this.assetComponentRepository.delete(id);
        return true;
    }
};
exports.AssetComponentService = AssetComponentService;
__decorate([
    (0, typeorm_1.InjectRepository)(asset_component_entity_1.AssetComponent),
    __metadata("design:type", typeorm_2.Repository)
], AssetComponentService.prototype, "assetComponentRepository", void 0);
exports.AssetComponentService = AssetComponentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [project_service_1.ProjectService])
], AssetComponentService);
//# sourceMappingURL=asset-component.service.js.map