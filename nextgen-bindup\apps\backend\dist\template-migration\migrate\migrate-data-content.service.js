"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrateDataContentService = void 0;
const common_util_1 = require("../../utils/common.util");
const site4_blockdata_dto_1 = require("../dto/site4_blockdata.dto");
const migrate_util_1 = require("./migrate.util");
const component_type_1 = require("../../page/types/component.type");
const prop_background_default_value_1 = require("../../page/utils/prop-background-default-value");
class MigrateDataContentService {
    constructor(inp) {
        this.components = inp.components;
    }
    migrateDataGroup(inp) {
        this.block = inp.block;
        this.blockData = inp.blockData;
        this.dataGroup = inp.dataGroup;
        this.ts = (0, common_util_1.NEW_TS)();
        const id = `group${this.block.areaId}_${this.block.blockId}_${this.dataGroup.index}`;
        const properties = this.createGroupProp();
        const groupBlock = {
            id: id,
            type: component_type_1.ComponentType.Block,
            name: id,
            parentId: inp.parentComponentId,
            properties: properties,
            children: [],
            breakpoint: {
                tablet: { ts: this.ts },
                phone: { ts: this.ts },
            },
            ts: this.ts,
        };
        this.components[groupBlock.id] = groupBlock;
        this.components[groupBlock.parentId].children.push(groupBlock.id);
        return groupBlock;
    }
    createGroupProp() {
        let properties = migrate_util_1.MigrateUtil.blockProps();
        const layoutID = this.blockData.blockdataInfoJson.blockdata_layoutID;
        switch (layoutID) {
            case site4_blockdata_dto_1.BlockData_Layout.PLAIN:
                properties = this.createGroupPropPlain(properties);
                break;
            case site4_blockdata_dto_1.BlockData_Layout.ASYMM:
                properties = this.createGroupPropAsymm(properties);
                break;
            case site4_blockdata_dto_1.BlockData_Layout.TABLE:
                properties = this.createGroupPropTable(properties);
                break;
            case site4_blockdata_dto_1.BlockData_Layout.ALBUM:
                properties = this.createGroupPropAlbum(properties);
                break;
            case site4_blockdata_dto_1.BlockData_Layout.TAB:
                properties = this.createGroupPropTab(properties);
                break;
            case site4_blockdata_dto_1.BlockData_Layout.ACCORDION:
                properties = this.createGroupPropAccordion(properties);
                break;
        }
        return properties;
    }
    createGroupPropPlain(properties) {
        return properties;
    }
    createGroupPropAsymm(properties) {
        const layoutOptID = this.blockData.blockdataInfoJson.blockdata_layoutOptID;
        properties.flexItem = {
            flexGrow: layoutOptID === site4_blockdata_dto_1.BlockData_LayoutOpt.RIGHT_WIDEL &&
                this.dataGroup.index === 2
                ? '2'
                : '1',
            ts: this.ts,
        };
        return properties;
    }
    createGroupPropTable(properties) {
        const infoJson = this.blockData.blockdataInfoJson;
        if (infoJson.blockdata_skinNo !== '1')
            return properties;
        const bgProps = (0, prop_background_default_value_1.PROP_BACKGROUND_LIST_DEFAULT_VALUE)(this.ts);
        bgProps.list.push({
            type: 'image',
            url: 'https://tuekgiwcwyuckzgwxejb.supabase.co/storage/v1/object/public/bind/theme/default08/blockskin/skin-3/index_bg.gif',
            position: {
                x: { value: '0', unit: 'px' },
                y: { value: '0', unit: 'px' },
                offsetX: { value: '0', unit: 'px' },
                offsetY: { value: '0', unit: 'px' },
            },
            repeat: 'repeat',
            visibility: true,
        });
        properties.border.top = {
            color: '#bbb',
            width: { value: '1', unit: 'px' },
            borderStyle: 'solid',
        };
        const columns = migrate_util_1.MigrateUtil.getColumns(infoJson.blockdata_layoutOptID);
        if (this.dataGroup.index % columns !== 1) {
            properties.border.isDetail = true;
            properties.border.right = {
                color: '#bbb',
                width: { value: '1', unit: 'px' },
                borderStyle: 'solid',
            };
            properties.border.bottom = {
                color: '#bbb',
                width: { value: '1', unit: 'px' },
                borderStyle: 'solid',
            };
        }
        properties.marginPadding.padding = {
            ts: this.ts,
            top: {
                unit: 'px',
                value: '10',
            },
            left: {
                unit: 'px',
                value: '10',
            },
            right: {
                unit: 'px',
                value: '10',
            },
            bottom: {
                unit: 'px',
                value: '0',
            },
            isDetail: true,
        };
        return properties;
    }
    createGroupPropAlbum(properties) {
        return properties;
    }
    createGroupPropTab(properties) {
        return properties;
    }
    createGroupPropAccordion(properties) {
        return properties;
    }
}
exports.MigrateDataContentService = MigrateDataContentService;
//# sourceMappingURL=migrate-data-content.service.js.map