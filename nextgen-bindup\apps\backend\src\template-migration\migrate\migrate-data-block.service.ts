import { Site3_Block } from '../dto/site3_block.dto';
import {
  BgSet_Ilay,
  BlockData_BGSets,
  BlockData_Info,
  BlockData_Info_BwType,
  BlockData_Info_FrameType,
  BlockData_Layout,
  BlockData_LayoutOpt,
  Site4_BlockData,
} from '../dto/site4_blockdata.dto';
import { NEW_TS } from 'src/utils/common.util';
import { Component } from '@nextgen-bindup/common/dto/component';
import { DEFAULT_FLEX_PROP, MigrateUtil } from './migrate.util';
import { PropertyDto } from '@nextgen-bindup/common/dto/component-properties/general-prop.dto';
import {
  BorderPropDto,
  BorderRadiusUnit,
} from '@nextgen-bindup/common/dto/setting-properties/border-prop.dto';
import { PROP_BORDER_DEFAULT_VALUE } from 'src/page/utils/prop-border-default-value';
import {
  BackgroundListPropDto,
  BgGradientPropDto,
  BgImagePropDto,
  BgSolidColorPropDto,
} from '@nextgen-bindup/common/dto/setting-properties/background-prop.dto';
import { PROP_BACKGROUND_LIST_DEFAULT_VALUE } from 'src/page/utils/prop-background-default-value';
import { UnitObject } from '@nextgen-bindup/common/dto/setting-properties/unit-object.dto';
import {
  BlankSpacePropDto,
  MarginPaddingPropDto,
} from '@nextgen-bindup/common/dto/setting-properties/margin-padding-prop.dto';
import { PROP_SPACING_DEFAULT_VALUE } from 'src/page/utils/prop-spacing-default-value';
import { BorderUnit } from '@nextgen-bindup/common/dto/setting-properties/border-unit.dto';
import { ComponentType } from 'src/page/types/component.type';
import { PROP_SIZE_DEFAULT_VALUE } from 'src/page/utils/prop-size-default-value';
import { LayoutPropDto } from '@nextgen-bindup/common/dto/setting-properties/layout-prop.dto';

export class MigrateDataBlockService {
  components: Record<string, Component>;
  parentSide: string;
  block: Site3_Block;
  blockData: Site4_BlockData;
  ts: number;

  constructor(inp: { components: Record<string, Component> }) {
    this.components = inp.components;
  }

  migrateBlockData(inp: {
    parentSide: string;
    block: Site3_Block;
    blockData: Site4_BlockData;
  }): Component {
    this.parentSide = inp.parentSide;
    this.block = inp.block;
    this.blockData = inp.blockData;
    this.ts = NEW_TS();

    const backgroundBlock: Component = this.createBackgroundDiv();
    const sizeBlock: Component = this.createSizeDiv(backgroundBlock.id);
    const marginBlock: Component = this.createMarginDiv(sizeBlock.id);

    return marginBlock;
  }

  // Stage 1 - Background div =========================================
  private createBackgroundDiv(): Component {
    const id: string = `block${this.block.areaId}_${this.block.blockId}`;
    const properties: PropertyDto = MigrateUtil.blockProps();

    const borderProps: BorderPropDto = PROP_BORDER_DEFAULT_VALUE(this.ts);
    const backgroundProps: BackgroundListPropDto =
      PROP_BACKGROUND_LIST_DEFAULT_VALUE(this.ts);
    const marginBottom: UnitObject = { value: '0', unit: 'px' };
    const padding: BlankSpacePropDto = PROP_SPACING_DEFAULT_VALUE(
      this.ts,
    ).padding;

    const blockdataInfo: BlockData_Info = this.blockData.blockdataInfoJson;
    const bgSets: BlockData_BGSets = this.blockData.bgSetsJson;

    switch (blockdataInfo.blockdata_frameType) {
      case BlockData_Info_FrameType.NONE:
        if (bgSets?.hasc && bgSets?.cval) {
          backgroundProps.list.push({
            id: '1',
            type: 'solid',
            from: 'design',
            backgroundColor: bgSets.cval.startsWith('rgb')
              ? bgSets.cval
              : '#' + bgSets.cval,
            presetId: null,
            alpha: 1,
            visibility: true,
            ts: this.ts,
          } as BgSolidColorPropDto);
        }

        if (bgSets?.hasi && bgSets?.imgf) {
          backgroundProps.list.push({
            id: '2',
            type: 'image',
            url: `https://edit3.bindcloud.jp/bindcld/site_data/338884/_src/90214562/${bgSets.imgf}`,
            position: {
              x: { value: '0', unit: 'px' },
              y: { value: '0', unit: 'px' },
              offsetX: { value: '0', unit: 'px' },
              offsetY: { value: '0', unit: 'px' },
            },
            size: '',
            sizeLength: {
              width: { value: '', unit: 'auto' },
              height: { value: '', unit: 'auto' },
            },
            repeat: [
              BgSet_Ilay.LEFT_JUSTIFIED,
              BgSet_Ilay.VERTICAL_REPEAT,
              BgSet_Ilay.RIGHT_JUSTIFIED,
              BgSet_Ilay.TOP_ALIGNMENT,
              BgSet_Ilay.HORIZONTAL_REPEAT,
              BgSet_Ilay.BOTTOM_ALIGNMENT,
            ].includes(bgSets.ilay)
              ? 'repeat'
              : '',
            attachmentFixed: false,
            clipText: false,
            visibility: true,
            ts: this.ts,
          } as BgImagePropDto);
        }
        break;

      case BlockData_Info_FrameType.BORDER_ONLY: {
        borderProps.isDetail = true;
        const borderValue: BorderUnit = {
          color: '#E4E4E4',
          width: { value: '1', unit: 'px' },
          borderStyle: 'solid',
        };
        borderProps.top = borderValue;
        borderProps.right = borderValue;
        borderProps.bottom = borderValue;
        borderProps.left = borderValue;
        padding.top = { value: '10', unit: 'px' };
        padding.right = { value: '10', unit: 'px' };
        padding.bottom = { value: '10', unit: 'px' };
        padding.left = { value: '10', unit: 'px' };
        padding.isDetail = true;
        break;
      }

      case BlockData_Info_FrameType.BORDER_RADIUS_ONLY: {
        borderProps.isDetail = true;
        const borderValue: BorderUnit = {
          color: '#E4E4E4',
          width: { value: '1', unit: 'px' },
          borderStyle: 'solid',
        };
        borderProps.top = borderValue;
        borderProps.right = borderValue;
        borderProps.bottom = borderValue;
        borderProps.left = borderValue;

        const radiusValue: BorderRadiusUnit = {
          width: { value: '6', unit: 'px' },
          height: { value: '6', unit: 'px' },
          isDetail: true,
        };
        borderProps.radiusTopLeft = radiusValue;
        borderProps.radiusTopRight = radiusValue;
        borderProps.radiusBottomLeft = radiusValue;
        borderProps.radiusBottomRight = radiusValue;
        break;
      }

      case BlockData_Info_FrameType.BORDER_GRADIENT: {
        borderProps.isDetail = true;
        borderProps.bottom = {
          color: '#E4E4E4',
          width: { value: '2', unit: 'px' },
          borderStyle: 'solid',
        };

        const radiusValue: BorderRadiusUnit = {
          width: { value: '6', unit: 'px' },
          height: { value: '6', unit: 'px' },
          isDetail: true,
        };
        borderProps.radiusTopLeft = radiusValue;
        borderProps.radiusTopRight = radiusValue;
        borderProps.radiusBottomLeft = radiusValue;
        borderProps.radiusBottomRight = radiusValue;

        backgroundProps.list.push({
          id: '3',
          type: 'gradient',
          from: `design`,
          background: 'linear-gradient(180deg, #fdfbfb 0%, #ebedee 100%)',
          presetId: null,
          size: '',
          sizeLength: {
            width: { value: '', unit: 'auto' },
            height: { value: '', unit: 'auto' },
          },
          repeat: '',
          attachmentFixed: false,
          clipText: false,
          visibility: true,
          ts: this.ts,
        } as BgGradientPropDto);
        break;
      }
    }

    if (blockdataInfo.blockdata_frameType !== BlockData_Info_FrameType.NONE) {
      marginBottom.value = '10';
    }

    properties.marginPadding = PROP_SPACING_DEFAULT_VALUE(this.ts, {
      margin: {
        top: { value: '0', unit: 'px' },
        left: { value: '0', unit: 'px' },
        right: { value: '0', unit: 'px' },
        bottom: marginBottom,
        isDetail: true,
        ts: this.ts,
      },
      padding: padding,
    });
    properties.backgrounds = backgroundProps;
    properties.border = borderProps;

    //--------------------------------------------------------
    const blockBg: Component = {
      id: `${id}`,
      type: ComponentType.Block,
      name: `${id}`,
      parentId: this.parentSide,
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: this.ts },
        phone: { ts: this.ts },
      },
      ts: this.ts,
    };
    this.components[blockBg.id] = blockBg;
    this.components[this.parentSide].children.push(blockBg.id);
    return blockBg;
  }

  // Stage 2 - Size =========================================
  private createSizeDiv(parentId: string): Component {
    const id: string = `block${this.block.areaId}_${this.block.blockId}_Size`;
    const blockdataInfo: BlockData_Info = this.blockData.blockdataInfoJson;
    const properties: PropertyDto = MigrateUtil.blockProps();

    properties.size = PROP_SIZE_DEFAULT_VALUE(this.ts);
    if (this.parentSide === '__left-side__') {
      properties.size.width = {
        value: '',
        unit: 'auto',
      };
    } else {
      properties.size.width = {
        value: `${blockdataInfo?.bwVal ? blockdataInfo.bwVal : ''}`,
        unit: `${!blockdataInfo?.bwVal ? 'auto' : blockdataInfo?.bwType === BlockData_Info_BwType.PIXEL ? 'px' : '%'}`,
      };
    }

    //--------------------------------------------------------
    const blockSize: Component = {
      id: `${id}`,
      type: ComponentType.Block,
      name: `${id}`,
      parentId: parentId,
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: this.ts },
        phone: { ts: this.ts },
      },
      ts: this.ts,
    };
    this.components[blockSize.id] = blockSize;
    this.components[blockSize.parentId].children.push(blockSize.id);

    return blockSize;
  }

  // Stage 3 - Margin =========================================
  private createMarginDiv(parentId: string): Component {
    const id: string = `block${this.block.areaId}_${this.block.blockId}_Margin`;
    const blockdataInfo: BlockData_Info = this.blockData.blockdataInfoJson;
    const properties: PropertyDto = MigrateUtil.blockProps();

    properties.layout = this.createLayout();

    const marginInf: string[] = blockdataInfo?.blockdata_marginInf
      ? blockdataInfo?.blockdata_marginInf.split(',')
      : ['0'];

    const marginPadding: MarginPaddingPropDto = PROP_SPACING_DEFAULT_VALUE(
      this.ts,
    );
    if (marginInf[0] === '1') {
      marginPadding.padding = {
        left: { value: marginInf[2], unit: 'px' },
        top: { value: marginInf[3], unit: 'px' },
        right: { value: marginInf[4], unit: 'px' },
        bottom: { value: marginInf[5], unit: 'px' },
        isDetail: true,
        ts: this.ts,
      };
    }

    properties.marginPadding = marginPadding;

    //--------------------------------------------------------
    const blockMargin: Component = {
      id: `${id}`,
      type: ComponentType.Block,
      name: `${id}`,
      parentId: parentId,
      properties: properties,
      children: [],
      breakpoint: {
        tablet: { ts: this.ts },
        phone: { ts: this.ts },
      },
      ts: this.ts,
    };
    this.components[blockMargin.id] = blockMargin;
    this.components[blockMargin.parentId].children.push(blockMargin.id);

    return blockMargin;
  }

  //-------------------------------------------
  private createLayout(): LayoutPropDto {
    let layout: LayoutPropDto;

    const layoutID: BlockData_Layout =
      this.blockData.blockdataInfoJson.blockdata_layoutID;

    switch (layoutID) {
      case BlockData_Layout.PLAIN:
        layout = this.createLayoutPlain();
        break;

      case BlockData_Layout.ASYMM:
        layout = this.createLayoutAsymm();
        break;

      case BlockData_Layout.TABLE:
        layout = this.createLayoutTable();
        break;

      case BlockData_Layout.ALBUM:
        layout = this.createLayoutAlbum();
        break;

      case BlockData_Layout.TAB:
        layout = this.createLayoutTab();
        break;

      case BlockData_Layout.ACCORDION:
        layout = this.createLayoutAccordion();
        break;
    }

    return layout;
  }

  private createLayoutPlain(): LayoutPropDto {
    let layout: LayoutPropDto;

    const layoutOptID: BlockData_LayoutOpt =
      this.blockData.blockdataInfoJson.blockdata_layoutOptID;

    if (layoutOptID === BlockData_LayoutOpt.STEP_1) {
      layout = {
        type: 'flex',
        flex: {
          flexDirection: 'row',
          verSpacing: { value: '0', unit: 'px' },
          hozSpacing: { value: '0', unit: 'px' },
          justifyContent: '',
          alignContent: '',
          alignItems: '',
          flexGrow: '1',
          flexShrink: '0',
          flexWrap: 'nowrap',
        },
        grid: null,
        carousel: null,
        ts: this.ts,
      };
    } else {
      const column = MigrateUtil.getColumns(layoutOptID);

      layout = {
        type: 'grid',
        flex: DEFAULT_FLEX_PROP,
        grid: {
          cols: `${column}`,
          columnGap: {
            value: '0',
            unit: 'px',
          },
          rowGap: {
            value: '0',
            unit: 'px',
          },
          rows: '',
          sizePerCol: {
            list: Array.from({ length: column }, () => ({
              id: '1',
              size: { unit: 'fr', value: '1' },
            })),
          },
        },
        carousel: null,
        ts: this.ts,
      };
    }

    return layout;
  }

  private createLayoutAsymm(): LayoutPropDto {
    const layout: LayoutPropDto = {
      type: 'flex',
      flex: {
        flexDirection: 'row',
        verSpacing: { value: '0', unit: 'px' },
        hozSpacing: { value: '0', unit: 'px' },
        justifyContent: '',
        alignContent: '',
        alignItems: '',
        flexGrow: '0',
        flexShrink: '0',
        flexWrap: 'nowrap',
      },
      grid: null,
      carousel: null,
      ts: this.ts,
    };

    return layout;
  }

  private createLayoutTable(): LayoutPropDto {
    const layoutOptID: BlockData_LayoutOpt =
      this.blockData.blockdataInfoJson.blockdata_layoutOptID;
    const column = MigrateUtil.getColumns(layoutOptID);

    const layout: LayoutPropDto = {
      type: 'grid',
      flex: DEFAULT_FLEX_PROP,
      grid: {
        cols: `${column}`,
        columnGap: {
          value: '0',
          unit: 'px',
        },
        rowGap: {
          value: '10',
          unit: 'px',
        },
        rows: '',
        sizePerCol: {
          list: Array.from({ length: column }, () => ({
            id: '1',
            size: { unit: 'fr', value: '1' },
          })),
        },
      },
      carousel: null,
      ts: this.ts,
    };

    return layout;
  }

  private createLayoutAlbum(): LayoutPropDto {
    return null;
  }

  private createLayoutTab(): LayoutPropDto {
    return null;
  }

  private createLayoutAccordion(): LayoutPropDto {
    return null;
  }
}
