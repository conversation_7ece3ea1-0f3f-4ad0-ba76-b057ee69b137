import { PageService } from 'src/page/page.service';
import { FtpService } from './ftp.service';
import { UserPaymentService } from 'src/payment/user-payment.service';
import { PublishPagesReq } from '@nextgen-bindup/common/dto/publish/publish-page.dto';
import { CmsCollectionItemsService } from 'src/cms-collection-items/cms-collection-items.service';
import { SiteAuthService } from 'src/proxy/site-auth.service';
export declare class PublishService {
    private readonly pageService;
    private readonly ftpService;
    private readonly userPaymentService;
    private readonly cmsCollectionItemsService;
    private readonly siteAuthService;
    private baseDir;
    constructor(pageService: PageService, ftpService: FtpService, userPaymentService: UserPaymentService, cmsCollectionItemsService: CmsCollectionItemsService, siteAuthService: SiteAuthService);
    publish(projectId: number, siteId: number, req: PublishPagesReq): Promise<boolean>;
    buildStaticSite(projectId: number, siteId: number, req: PublishPagesReq): Promise<string>;
    private buildStructure;
    private sanitizedUrl;
    private createPath;
    private moveFile;
}
