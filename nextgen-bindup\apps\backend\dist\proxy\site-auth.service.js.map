{"version": 3, "file": "site-auth.service.js", "sourceRoot": "", "sources": ["../../src/proxy/site-auth.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmE;AACnE,6BAA6B;AAGtB,IAAM,eAAe,GAArB,MAAM,eAAe;IAC1B,gBAAe,CAAC;IAEhB,KAAK,CAAC,uBAAuB,CAAC,KAAa;QAGzC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAC3D,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACxD,OAAO;gBACL,MAAM,EAAE,OAAO,CAAC,MAAgB;aACjC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACxD,MAAM,IAAI,8BAAqB,CAAC,0BAA0B,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAc;QAChC,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAC3D,MAAM,GAAG,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;aAC3C,kBAAkB,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;aACpC,WAAW,EAAE;aACb,IAAI,CAAC,MAAM,CAAC,CAAC;QAChB,OAAO,GAAG,CAAC;IACb,CAAC;CACF,CAAA;AA1BY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;;GACA,eAAe,CA0B3B"}