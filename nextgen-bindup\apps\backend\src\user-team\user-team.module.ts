import { Module } from '@nestjs/common';
import { UserTeamService } from './user-team.service';
import { UserTeamEntity } from './entities/user-team.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserTeamController } from './user-team.controller';

@Module({
  imports: [TypeOrmModule.forFeature([UserTeamEntity])],
  providers: [UserTeamService],
  exports: [UserTeamService],
  controllers: [UserTeamController],
})
export class UserTeamModule {}
