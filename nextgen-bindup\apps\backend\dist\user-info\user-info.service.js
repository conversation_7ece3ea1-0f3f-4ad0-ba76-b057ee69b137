"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserInfoService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_info_entity_1 = require("./entities/user-info.entity");
const typeorm_2 = require("typeorm");
let UserInfoService = class UserInfoService {
    constructor() { }
    async updateRecently(userId, siteId) {
        const user = await this.userInfoRepo.findOneBy({ userId });
        if (!user)
            throw new Error('api.error.user_not_found');
        if (!user.recentlySite)
            user.recentlySite = [];
        const index = user.recentlySite.indexOf(siteId);
        if (index >= 0) {
            user.recentlySite.splice(index, 1);
        }
        user.recentlySite.unshift(siteId);
        while (user.recentlySite.length > 10)
            user.recentlySite.pop();
        await this.userInfoRepo.update({ userId }, { recentlySite: user.recentlySite });
        return true;
    }
    async findByUserId(userId) {
        return await this.userInfoRepo.findOneBy({ userId });
    }
    async update(userId, data) {
        const userInfo = await this.userInfoRepo.findOneBy({ userId });
        if (!userInfo)
            throw new Error('api.error.user_not_found');
        await this.userInfoRepo.update({ userId }, data);
        return { ...userInfo, ...data };
    }
};
exports.UserInfoService = UserInfoService;
__decorate([
    (0, typeorm_1.InjectRepository)(user_info_entity_1.UserInfoEntity),
    __metadata("design:type", typeorm_2.Repository)
], UserInfoService.prototype, "userInfoRepo", void 0);
exports.UserInfoService = UserInfoService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], UserInfoService);
//# sourceMappingURL=user-info.service.js.map