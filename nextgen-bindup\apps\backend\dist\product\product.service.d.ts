import { ProductEntity } from './entities/product.entity';
import { Repository } from 'typeorm';
import { PaginatedResponse } from 'src/common/paginated-response';
import { DataSource } from 'typeorm';
import { ProductStocksService } from 'src/product-stocks/product-stocks.service';
import { CsvService } from './csv.service';
import Stream from 'stream';
import { GetProductsQueryDto } from './dto/get-product.dto';
export declare class ProductService {
    private dataSource;
    private readonly productStockService;
    private readonly csvService;
    readonly productRepo: Repository<ProductEntity>;
    constructor(dataSource: DataSource, productStockService: ProductStocksService, csvService: CsvService);
    getAllProducts(siteId: number): Promise<ProductEntity[]>;
    searchProducts(siteId: number, page: number, limit: number, search?: string, productType?: string, isOrderable?: boolean): Promise<PaginatedResponse<ProductEntity>>;
    private updateProductStocks;
    private validateProductData;
    create(productEntity: ProductEntity): Promise<ProductEntity>;
    update(id: number, productData: Partial<ProductEntity>): Promise<ProductEntity>;
    findById(id: number): Promise<ProductEntity>;
    duplicate(id: number): Promise<ProductEntity>;
    private generateUniqueCode;
    delete(id: number): Promise<boolean>;
    findByIds(siteId: number, ids: number[]): Promise<ProductEntity[]>;
    uploadCSV(siteId: number, file: Express.Multer.File): Promise<{
        success: boolean;
        message: string;
    }>;
    getCSVTemplate(): Promise<Stream.Readable>;
    downloadCSV(siteId: number, query?: GetProductsQueryDto): Promise<Stream.Readable>;
}
