"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlanEntity = void 0;
const typeorm_1 = require("typeorm");
let PlanEntity = class PlanEntity {
};
exports.PlanEntity = PlanEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", String)
], PlanEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'name',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], PlanEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'stripeProductId',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], PlanEntity.prototype, "stripeProductId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'stripePriceId',
        type: 'varchar',
        length: 250,
        nullable: false,
    }),
    __metadata("design:type", String)
], PlanEntity.prototype, "stripePriceId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'price',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], PlanEntity.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'currency',
        type: 'varchar',
        length: 10,
        nullable: false,
    }),
    __metadata("design:type", String)
], PlanEntity.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'interval',
        type: 'varchar',
        length: 50,
        nullable: false,
    }),
    __metadata("design:type", String)
], PlanEntity.prototype, "interval", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isActive',
        type: 'boolean',
        nullable: false,
        default: true,
    }),
    __metadata("design:type", Boolean)
], PlanEntity.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], PlanEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], PlanEntity.prototype, "updatedAt", void 0);
exports.PlanEntity = PlanEntity = __decorate([
    (0, typeorm_1.Entity)('plans', { schema: process.env.DATABASE_SCHEMA })
], PlanEntity);
//# sourceMappingURL=plan.entity.js.map