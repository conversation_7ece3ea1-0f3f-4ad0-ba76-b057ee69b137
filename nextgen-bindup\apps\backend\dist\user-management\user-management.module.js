"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserManagementModule = void 0;
const common_1 = require("@nestjs/common");
const user_management_service_1 = require("./user-management.service");
const user_management_controller_1 = require("./user-management.controller");
const identity_module_1 = require("../identity/identity.module");
const team_module_1 = require("../team/team.module");
const user_info_module_1 = require("../user-info/user-info.module");
const user_team_module_1 = require("../user-team/user-team.module");
let UserManagementModule = class UserManagementModule {
};
exports.UserManagementModule = UserManagementModule;
exports.UserManagementModule = UserManagementModule = __decorate([
    (0, common_1.Module)({
        imports: [identity_module_1.IdentityModule, team_module_1.TeamModule, user_info_module_1.UserInfoModule, user_team_module_1.UserTeamModule],
        providers: [user_management_service_1.UserManagementService],
        controllers: [user_management_controller_1.UserManagementController],
        exports: [user_management_service_1.UserManagementService],
    })
], UserManagementModule);
//# sourceMappingURL=user-management.module.js.map