import { Body, Controller, Post, Put, UseGuards } from '@nestjs/common';
import { AuthService } from './auth.service';
import {
  CreateTempTokenReq,
  JwtPayloadDto,
  VerifyTempTokenReq,
} from './dto/auth.dto';
import { LoginReq } from 'src/auth/dto/login.dto';
import { SignUpReq } from 'src/auth/dto/sign-up.dto';
import { ForgotPwdReq } from 'src/auth/dto/forgot-pwd.dto';
import { ResetPwdReq } from 'src/auth/dto/reset-pwd.dto';
import { AuthGuard } from './auth.guard';
import { ExtractUser } from './user.decorator';
import { ChangePwdReq } from './dto/change-pwd.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  async signUp(@Body() reqDto: SignUpReq) {
    return await this.authService.signUp(reqDto);
  }

  @Post('login')
  async login(@Body() reqDto: LoginReq) {
    return await this.authService.login(reqDto);
  }

  @Post('forgot-pwd')
  async forgotPwd(@Body() reqDto: ForgotPwdReq) {
    return await this.authService.forgotPwd(reqDto);
  }

  @Put('reset-pwd')
  async resetPwd(@Body() reqDto: ResetPwdReq) {
    return await this.authService.resetPwd(reqDto);
  }

  @Post('tmp-access')
  @UseGuards(AuthGuard)
  async createTempToken(
    @ExtractUser() user: JwtPayloadDto,
    @Body() reqDto: CreateTempTokenReq,
  ) {
    return await this.authService.createTempToken(user.userId, reqDto);
  }

  @Post('verify-tmp-access')
  async verifyRemoteToken(@Body() reqDto: VerifyTempTokenReq) {
    return await this.authService.verifyTempToken(reqDto.token);
  }

  @Post('verify-design-editor-access')
  async verifyDesignEditorToken(@Body() reqDto: VerifyTempTokenReq) {
    return await this.authService.verifyDesignEditorToken(reqDto.token);
  }

  @Put('change-pwd')
  async changePassword(
    @ExtractUser() user: JwtPayloadDto,
    @Body() data: ChangePwdReq,
  ) {
    await this.authService.changePassword(user.email, data);
    return true;
  }
}
