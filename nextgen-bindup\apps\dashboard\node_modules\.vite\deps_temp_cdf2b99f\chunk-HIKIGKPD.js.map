{"version": 3, "sources": ["../../../../../node_modules/@mui/material/InputAdornment/InputAdornment.js", "../../../../../node_modules/@mui/material/InputAdornment/inputAdornmentClasses.js"], "sourcesContent": ["'use client';\n\nvar _span;\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from \"../utils/capitalize.js\";\nimport Typography from \"../Typography/index.js\";\nimport FormControlContext from \"../FormControl/FormControlContext.js\";\nimport useFormControl from \"../FormControl/useFormControl.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport inputAdornmentClasses, { getInputAdornmentUtilityClass } from \"./inputAdornmentClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, styles[`position${capitalize(ownerState.position)}`], ownerState.disablePointerEvents === true && styles.disablePointerEvents, styles[ownerState.variant]];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disablePointerEvents,\n    hiddenLabel,\n    position,\n    size,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', disablePointerEvents && 'disablePointerEvents', position && `position${capitalize(position)}`, variant, hiddenLabel && 'hiddenLabel', size && `size${capitalize(size)}`]\n  };\n  return composeClasses(slots, getInputAdornmentUtilityClass, classes);\n};\nconst InputAdornmentRoot = styled('div', {\n  name: 'MuiInputAdornment',\n  slot: 'Root',\n  overridesResolver\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  maxHeight: '2em',\n  alignItems: 'center',\n  whiteSpace: 'nowrap',\n  color: (theme.vars || theme).palette.action.active,\n  variants: [{\n    props: {\n      variant: 'filled'\n    },\n    style: {\n      [`&.${inputAdornmentClasses.positionStart}&:not(.${inputAdornmentClasses.hiddenLabel})`]: {\n        marginTop: 16\n      }\n    }\n  }, {\n    props: {\n      position: 'start'\n    },\n    style: {\n      marginRight: 8\n    }\n  }, {\n    props: {\n      position: 'end'\n    },\n    style: {\n      marginLeft: 8\n    }\n  }, {\n    props: {\n      disablePointerEvents: true\n    },\n    style: {\n      pointerEvents: 'none'\n    }\n  }]\n})));\nconst InputAdornment = /*#__PURE__*/React.forwardRef(function InputAdornment(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiInputAdornment'\n  });\n  const {\n    children,\n    className,\n    component = 'div',\n    disablePointerEvents = false,\n    disableTypography = false,\n    position,\n    variant: variantProp,\n    ...other\n  } = props;\n  const muiFormControl = useFormControl() || {};\n  let variant = variantProp;\n  if (variantProp && muiFormControl.variant) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (variantProp === muiFormControl.variant) {\n        console.error('MUI: The `InputAdornment` variant infers the variant prop ' + 'you do not have to provide one.');\n      }\n    }\n  }\n  if (muiFormControl && !variant) {\n    variant = muiFormControl.variant;\n  }\n  const ownerState = {\n    ...props,\n    hiddenLabel: muiFormControl.hiddenLabel,\n    size: muiFormControl.size,\n    disablePointerEvents,\n    position,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FormControlContext.Provider, {\n    value: null,\n    children: /*#__PURE__*/_jsx(InputAdornmentRoot, {\n      as: component,\n      ownerState: ownerState,\n      className: clsx(classes.root, className),\n      ref: ref,\n      ...other,\n      children: typeof children === 'string' && !disableTypography ? /*#__PURE__*/_jsx(Typography, {\n        color: \"textSecondary\",\n        children: children\n      }) : /*#__PURE__*/_jsxs(React.Fragment, {\n        children: [position === 'start' ? (/* notranslate needed while Google Translate will not fix zero-width space issue */_span || (_span = /*#__PURE__*/_jsx(\"span\", {\n          className: \"notranslate\",\n          \"aria-hidden\": true,\n          children: \"\\u200B\"\n        }))) : null, children]\n      })\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? InputAdornment.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `IconButton` or string.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Disable pointer events on the root.\n   * This allows for the content of the adornment to focus the `input` on click.\n   * @default false\n   */\n  disablePointerEvents: PropTypes.bool,\n  /**\n   * If children is a string then disable wrapping in a Typography component.\n   * @default false\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * The position this adornment should appear relative to the `Input`.\n   */\n  position: PropTypes.oneOf(['end', 'start']).isRequired,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * Note: If you are using the `TextField` component or the `FormControl` component\n   * you do not have to set this manually.\n   */\n  variant: PropTypes.oneOf(['filled', 'outlined', 'standard'])\n} : void 0;\nexport default InputAdornment;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getInputAdornmentUtilityClass(slot) {\n  return generateUtilityClass('MuiInputAdornment', slot);\n}\nconst inputAdornmentClasses = generateUtilityClasses('MuiInputAdornment', ['root', 'filled', 'standard', 'outlined', 'positionStart', 'positionEnd', 'disablePointerEvents', 'hiddenLabel', 'sizeSmall']);\nexport default inputAdornmentClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,YAAuB;AACvB,wBAAsB;;;ACFf,SAAS,8BAA8B,MAAM;AAClD,SAAO,qBAAqB,qBAAqB,IAAI;AACvD;AACA,IAAM,wBAAwB,uBAAuB,qBAAqB,CAAC,QAAQ,UAAU,YAAY,YAAY,iBAAiB,eAAe,wBAAwB,eAAe,WAAW,CAAC;AACxM,IAAO,gCAAQ;;;ADSf,yBAA2C;AAb3C,IAAI;AAcJ,IAAM,oBAAoB,CAAC,OAAO,WAAW;AAC3C,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,CAAC,OAAO,MAAM,OAAO,WAAW,mBAAW,WAAW,QAAQ,CAAC,EAAE,GAAG,WAAW,yBAAyB,QAAQ,OAAO,sBAAsB,OAAO,WAAW,OAAO,CAAC;AAChL;AACA,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,wBAAwB,wBAAwB,YAAY,WAAW,mBAAW,QAAQ,CAAC,IAAI,SAAS,eAAe,eAAe,QAAQ,OAAO,mBAAW,IAAI,CAAC,EAAE;AAAA,EACxL;AACA,SAAO,eAAe,OAAO,+BAA+B,OAAO;AACrE;AACA,IAAM,qBAAqB,eAAO,OAAO;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AAAA,EACN;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,QAAQ,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EAC5C,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,CAAC,KAAK,8BAAsB,aAAa,UAAU,8BAAsB,WAAW,GAAG,GAAG;AAAA,QACxF,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,aAAa;AAAA,IACf;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,sBAAsB;AAAA,IACxB;AAAA,IACA,OAAO;AAAA,MACL,eAAe;AAAA,IACjB;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,iBAAoC,iBAAW,SAASA,gBAAe,SAAS,KAAK;AACzF,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,uBAAuB;AAAA,IACvB,oBAAoB;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,IACT,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,iBAAiB,eAAe,KAAK,CAAC;AAC5C,MAAI,UAAU;AACd,MAAI,eAAe,eAAe,SAAS;AACzC,QAAI,MAAuC;AACzC,UAAI,gBAAgB,eAAe,SAAS;AAC1C,gBAAQ,MAAM,2FAAgG;AAAA,MAChH;AAAA,IACF;AAAA,EACF;AACA,MAAI,kBAAkB,CAAC,SAAS;AAC9B,cAAU,eAAe;AAAA,EAC3B;AACA,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH,aAAa,eAAe;AAAA,IAC5B,MAAM,eAAe;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,2BAAmB,UAAU;AAAA,IACpD,OAAO;AAAA,IACP,cAAuB,mBAAAA,KAAK,oBAAoB;AAAA,MAC9C,IAAI;AAAA,MACJ;AAAA,MACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,MACvC;AAAA,MACA,GAAG;AAAA,MACH,UAAU,OAAO,aAAa,YAAY,CAAC,wBAAiC,mBAAAA,KAAK,oBAAY;AAAA,QAC3F,OAAO;AAAA,QACP;AAAA,MACF,CAAC,QAAiB,mBAAAC,MAAY,gBAAU;AAAA,QACtC,UAAU,CAAC,aAAa;AAAA;AAAA,UAA8F,UAAU,YAAqB,mBAAAD,KAAK,QAAQ;AAAA,YAChK,WAAW;AAAA,YACX,eAAe;AAAA,YACf,UAAU;AAAA,UACZ,CAAC;AAAA,YAAM,MAAM,QAAQ;AAAA,MACvB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AACD,OAAwC,eAAe,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxF,UAAU,kBAAAE,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,sBAAsB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI7B,UAAU,kBAAAA,QAAU,MAAM,CAAC,OAAO,OAAO,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA,EAI5C,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtJ,SAAS,kBAAAA,QAAU,MAAM,CAAC,UAAU,YAAY,UAAU,CAAC;AAC7D,IAAI;AACJ,IAAO,yBAAQ;", "names": ["InputAdornment", "_jsx", "_jsxs", "PropTypes"]}