"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RenameContentsToAssetComponentAndAddColumns1747980000000 = void 0;
const typeorm_1 = require("typeorm");
class RenameContentsToAssetComponentAndAddColumns1747980000000 {
    constructor() {
        this.OLD_TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}contents`;
        this.NEW_TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}asset_component`;
    }
    async up(queryRunner) {
        await queryRunner.renameTable(this.OLD_TABLE_NAME, this.NEW_TABLE_NAME);
        const folderColumn = new typeorm_1.TableColumn({
            name: 'folder',
            type: 'boolean',
            isNullable: false,
            default: 'false',
        });
        const parentFolderColumn = new typeorm_1.TableColumn({
            name: 'parentFolder',
            type: 'varchar',
            length: '255',
            isNullable: false,
            default: "''",
        });
        await queryRunner.addColumns(this.NEW_TABLE_NAME, [
            folderColumn,
            parentFolderColumn,
        ]);
        await queryRunner.dropColumn(this.NEW_TABLE_NAME, 'isDeleted');
    }
    async down(queryRunner) {
        const isDeletedColumn = new typeorm_1.TableColumn({
            name: 'isDeleted',
            type: 'boolean',
            isNullable: false,
            default: 'false',
        });
        await queryRunner.addColumn(this.NEW_TABLE_NAME, isDeletedColumn);
        await queryRunner.dropColumn(this.NEW_TABLE_NAME, 'parentFolder');
        await queryRunner.dropColumn(this.NEW_TABLE_NAME, 'folder');
        await queryRunner.renameTable(this.NEW_TABLE_NAME, this.OLD_TABLE_NAME);
    }
}
exports.RenameContentsToAssetComponentAndAddColumns1747980000000 = RenameContentsToAssetComponentAndAddColumns1747980000000;
//# sourceMappingURL=1748489129472-rename-contents-table-and-add-columns.js.map