import { Component } from '@nextgen-bindup/common/dto/component';
import { BlockData_Group } from 'src/template-migration/dto/blockdata-content.dto';
import { Site3_Block } from 'src/template-migration/dto/site3_block.dto';
import { Site4_BlockData } from 'src/template-migration/dto/site4_blockdata.dto';
export declare class MigrateDataContentService {
    components: Record<string, Component>;
    block: Site3_Block;
    blockData: Site4_BlockData;
    dataGroup: BlockData_Group;
    ts: number;
    constructor(inp: {
        components: Record<string, Component>;
    });
    migrateDataGroup(inp: {
        parentComponentId: string;
        block: Site3_Block;
        blockData: Site4_BlockData;
        dataGroup: BlockData_Group;
    }): Component;
    private createGroupProp;
    private createGroupPropPlain;
    private createGroupPropAsymm;
    private createGroupPropTable;
    private createGroupPropAlbum;
    private createGroupPropTab;
    private createGroupPropAccordion;
}
