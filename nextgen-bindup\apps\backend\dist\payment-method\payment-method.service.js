"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentMethodService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const payment_method_entity_1 = require("./entities/payment-method.entity");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const stripe_1 = require("stripe");
let PaymentMethodService = class PaymentMethodService {
    constructor(configService) {
        this.configService = configService;
        if (this.configService.get('STRIPE_SECRET_KEY')) {
            this.stripe = new stripe_1.default(this.configService.get('STRIPE_SECRET_KEY'), {
                apiVersion: '2025-05-28.basil',
            });
        }
    }
    async getPaymentMethodBySiteId(siteId) {
        return await this.paymentMethodRepo.findOneBy({ siteId });
    }
    async updatePaymentMethod(siteId, paymentMethod) {
        delete paymentMethod.id;
        paymentMethod.updatedAt = new Date();
        await this.paymentMethodRepo.update({ siteId }, { ...paymentMethod });
        return true;
    }
    async enableStripePaymentGateway(siteId) {
        if (!this.stripe) {
            throw new Error('Stripe is not configured');
        }
        const account = await this.stripe.accounts.create({
            type: 'standard',
            country: 'JP',
            capabilities: {
                transfers: { requested: true },
                card_payments: { requested: true },
            },
            business_type: 'individual',
            metadata: {
                siteId: siteId.toString(),
            },
        });
        const stripeAccountId = account.id;
        const paymentMethods = await this.getPaymentMethodBySiteId(siteId);
        await this.updatePaymentMethod(siteId, {
            stripePaymentGateway: {
                isEnabled: paymentMethods.stripePaymentGateway.isEnabled,
                description: paymentMethods.stripePaymentGateway.description,
                stripeAccountId,
            },
        });
        const accountLink = await this.stripe.accountLinks.create({
            account: stripeAccountId,
            refresh_url: `http://localhost:5173/cart-management/shop-setting`,
            return_url: `http://localhost:5173/cart-management/shop-setting`,
            type: 'account_onboarding',
        });
        return { url: accountLink.url };
    }
    async checkStripeStatus(siteId) {
        if (!this.stripe) {
            throw new Error('Stripe is not configured');
        }
        const paymentMethods = await this.getPaymentMethodBySiteId(siteId);
        const stripeAccountId = paymentMethods?.stripePaymentGateway?.stripeAccountId;
        if (!stripeAccountId) {
            return {
                connected: false,
            };
        }
        const account = await this.stripe.accounts.retrieve(stripeAccountId);
        console.log('account', account);
        return {
            connected: true,
            email: account.email,
            type: account.type,
            id: account.id,
            stripeDashboardUrl: `https://dashboard.stripe.com/${account.id}`,
        };
    }
    async removeStripeAccount(siteId) {
        const paymentMethods = await this.getPaymentMethodBySiteId(siteId);
        const stripeAccountId = paymentMethods?.stripePaymentGateway?.stripeAccountId;
        if (!stripeAccountId) {
            return false;
        }
        await this.updatePaymentMethod(siteId, {
            stripePaymentGateway: {
                isEnabled: false,
                description: paymentMethods.stripePaymentGateway.description,
                stripeAccountId: '',
            },
        });
        return true;
    }
};
exports.PaymentMethodService = PaymentMethodService;
__decorate([
    (0, typeorm_1.InjectRepository)(payment_method_entity_1.PaymentMethodEntity),
    __metadata("design:type", typeorm_2.Repository)
], PaymentMethodService.prototype, "paymentMethodRepo", void 0);
exports.PaymentMethodService = PaymentMethodService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], PaymentMethodService);
//# sourceMappingURL=payment-method.service.js.map