"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateFontSetTable1747970237556 = void 0;
const typeorm_1 = require("typeorm");
class UpdateFontSetTable1747970237556 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}font_sets`;
    }
    async up(queryRunner) {
        const siteIdColumn = new typeorm_1.TableColumn({
            name: 'siteId',
            type: 'integer',
            isNullable: false,
            default: '1',
        });
        await queryRunner.addColumn(this.TABLE_NAME, siteIdColumn);
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_font_sets_siteId',
            columnNames: ['siteId'],
            isUnique: false,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_font_sets_siteId');
        await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
    }
}
exports.UpdateFontSetTable1747970237556 = UpdateFontSetTable1747970237556;
//# sourceMappingURL=1747970237556-update-font-set-table.js.map