"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageVersionEntity = void 0;
const typeorm_1 = require("typeorm");
const page_type_1 = require("../../page/types/page.type");
let PageVersionEntity = class PageVersionEntity {
    constructor() {
        this.type = page_type_1.PageType.PAGE;
        this.status = page_type_1.PageStatus.DRAFT;
        this.isPrivate = false;
        this.isHome = false;
    }
};
exports.PageVersionEntity = PageVersionEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], PageVersionEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'siteVersionId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], PageVersionEntity.prototype, "siteVersionId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'pageId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], PageVersionEntity.prototype, "pageId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'type',
        type: 'varchar',
        length: 15,
        nullable: false,
        default: page_type_1.PageType.PAGE,
    }),
    __metadata("design:type", String)
], PageVersionEntity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'parentId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], PageVersionEntity.prototype, "parentId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'projectId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], PageVersionEntity.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'siteId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], PageVersionEntity.prototype, "siteId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'datasource',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], PageVersionEntity.prototype, "datasource", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'name',
        type: 'varchar',
        length: 255,
        nullable: false,
    }),
    __metadata("design:type", String)
], PageVersionEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'components',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], PageVersionEntity.prototype, "components", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'ts',
        type: 'bigint',
        nullable: true,
    }),
    __metadata("design:type", Number)
], PageVersionEntity.prototype, "ts", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'status',
        type: 'smallint',
        nullable: false,
        default: page_type_1.PageStatus.DRAFT,
    }),
    __metadata("design:type", Number)
], PageVersionEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'url',
        type: 'varchar',
        length: 255,
    }),
    __metadata("design:type", String)
], PageVersionEntity.prototype, "url", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'title',
        type: 'varchar',
        length: 255,
    }),
    __metadata("design:type", String)
], PageVersionEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'description',
        type: 'text',
    }),
    __metadata("design:type", String)
], PageVersionEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isSearch',
        type: 'boolean',
    }),
    __metadata("design:type", Boolean)
], PageVersionEntity.prototype, "isSearch", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'thumb',
        type: 'varchar',
        length: 255,
    }),
    __metadata("design:type", String)
], PageVersionEntity.prototype, "thumb", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'headCode',
        type: 'text',
    }),
    __metadata("design:type", String)
], PageVersionEntity.prototype, "headCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'bodyCode',
        type: 'text',
    }),
    __metadata("design:type", String)
], PageVersionEntity.prototype, "bodyCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isPrivate',
        type: 'boolean',
        nullable: false,
        default: false,
    }),
    __metadata("design:type", Boolean)
], PageVersionEntity.prototype, "isPrivate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isHome',
        type: 'boolean',
        nullable: false,
        default: false,
    }),
    __metadata("design:type", Boolean)
], PageVersionEntity.prototype, "isHome", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { array: true, default: {} }),
    __metadata("design:type", Array)
], PageVersionEntity.prototype, "children", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
    }),
    __metadata("design:type", Date)
], PageVersionEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
    }),
    __metadata("design:type", Date)
], PageVersionEntity.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'versionCreatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], PageVersionEntity.prototype, "versionCreatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'userId',
        type: 'varchar',
        length: '36',
    }),
    __metadata("design:type", String)
], PageVersionEntity.prototype, "userId", void 0);
exports.PageVersionEntity = PageVersionEntity = __decorate([
    (0, typeorm_1.Entity)('page_versions', { schema: process.env.DATABASE_SCHEMA })
], PageVersionEntity);
//# sourceMappingURL=page-version.entity.js.map