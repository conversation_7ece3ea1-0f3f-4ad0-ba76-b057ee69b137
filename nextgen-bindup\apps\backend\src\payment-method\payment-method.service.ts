import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PaymentMethodEntity } from './entities/payment-method.entity';
import { Repository } from 'typeorm';
import { PaymentMethodResponseDto } from './dto/payment-method-response.dto';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import { StripeInformation } from './dto/stripe-infomation.dto';

@Injectable()
export class PaymentMethodService {
  private stripe: Stripe;

  @InjectRepository(PaymentMethodEntity)
  readonly paymentMethodRepo: Repository<PaymentMethodEntity>;

  constructor(private readonly configService: ConfigService) {
    if (this.configService.get('STRIPE_SECRET_KEY')) {
      this.stripe = new Stripe(this.configService.get('STRIPE_SECRET_KEY'), {
        apiVersion: '2025-05-28.basil',
      });
    }
  }

  async getPaymentMethodBySiteId(
    siteId: number,
  ): Promise<PaymentMethodResponseDto> {
    return await this.paymentMethodRepo.findOneBy({ siteId });
  }

  async updatePaymentMethod(
    siteId: number,
    paymentMethod: Partial<PaymentMethodEntity>,
  ): Promise<boolean> {
    delete paymentMethod.id;
    paymentMethod.updatedAt = new Date();
    await this.paymentMethodRepo.update({ siteId }, { ...paymentMethod });
    return true;
  }

  async enableStripePaymentGateway(siteId: number): Promise<{ url: string }> {
    if (!this.stripe) {
      throw new Error('Stripe is not configured');
    }
    const account = await this.stripe.accounts.create({
      type: 'standard',
      country: 'JP',
      capabilities: {
        transfers: { requested: true },
        card_payments: { requested: true },
      },
      business_type: 'individual',
      metadata: {
        siteId: siteId.toString(),
      },
    });
    const stripeAccountId = account.id;
    const paymentMethods = await this.getPaymentMethodBySiteId(siteId);
    await this.updatePaymentMethod(siteId, {
      stripePaymentGateway: {
        isEnabled: paymentMethods.stripePaymentGateway.isEnabled,
        description: paymentMethods.stripePaymentGateway.description,
        stripeAccountId,
      },
    });

    const accountLink = await this.stripe.accountLinks.create({
      account: stripeAccountId,
      refresh_url: `http://localhost:5173/cart-management/shop-setting`,
      return_url: `http://localhost:5173/cart-management/shop-setting`,
      type: 'account_onboarding',
    });
    return { url: accountLink.url };
  }

  async checkStripeStatus(siteId: number): Promise<StripeInformation> {
    if (!this.stripe) {
      throw new Error('Stripe is not configured');
    }
    const paymentMethods = await this.getPaymentMethodBySiteId(siteId);
    const stripeAccountId =
      paymentMethods?.stripePaymentGateway?.stripeAccountId;
    if (!stripeAccountId) {
      return {
        connected: false,
      };
    }
    const account = await this.stripe.accounts.retrieve(stripeAccountId);
    console.log('account', account);
    return {
      connected: true,
      email: account.email,
      type: account.type,
      id: account.id,
      stripeDashboardUrl: `https://dashboard.stripe.com/${account.id}`,
    };
  }

  async removeStripeAccount(siteId: number): Promise<boolean> {
    const paymentMethods = await this.getPaymentMethodBySiteId(siteId);

    const stripeAccountId =
      paymentMethods?.stripePaymentGateway?.stripeAccountId;
    if (!stripeAccountId) {
      return false;
    }

    await this.updatePaymentMethod(siteId, {
      stripePaymentGateway: {
        isEnabled: false,
        description: paymentMethods.stripePaymentGateway.description,
        stripeAccountId: '',
      },
    });
    return true;
  }
}
