"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewMigraton1747970237553 = void 0;
const typeorm_1 = require("typeorm");
class NewMigraton1747970237553 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}payment_methods`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'siteId',
                    type: 'int',
                    isUnique: true,
                    isNullable: false,
                },
                {
                    name: 'bankTransfer',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'postalTransfer',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'stripePaymentGateway',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'cashOnDelivery',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'deletedAt',
                    type: 'timestamptz',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(this.TABLE_NAME);
    }
}
exports.NewMigraton1747970237553 = NewMigraton1747970237553;
//# sourceMappingURL=1747970237553-create-payment-method-table.js.map