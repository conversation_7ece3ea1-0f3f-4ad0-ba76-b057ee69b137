"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderItemTable1749443132777 = void 0;
const typeorm_1 = require("typeorm");
class UpdateOrderItemTable1749443132777 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}order_items`;
    }
    async up(queryRunner) {
        await queryRunner.addColumn(this.TABLE_NAME, new typeorm_1.TableColumn({
            name: 'individualShippingCharges',
            type: 'bigint',
            isNullable: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'individualShippingCharges');
    }
}
exports.UpdateOrderItemTable1749443132777 = UpdateOrderItemTable1749443132777;
//# sourceMappingURL=1749443132777-update-order-item-table.js.map