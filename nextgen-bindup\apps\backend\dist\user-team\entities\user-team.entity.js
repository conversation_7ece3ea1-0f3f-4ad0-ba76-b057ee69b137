"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTeamEntity = void 0;
const typeorm_1 = require("typeorm");
let UserTeamEntity = class UserTeamEntity {
};
exports.UserTeamEntity = UserTeamEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], UserTeamEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'rootUserId',
        type: 'varchar',
        length: 36,
        nullable: false,
    }),
    __metadata("design:type", String)
], UserTeamEntity.prototype, "rootUserId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'userId',
        type: 'varchar',
        length: 36,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserTeamEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'email',
        type: 'varchar',
        length: 500,
        nullable: false,
    }),
    __metadata("design:type", String)
], UserTeamEntity.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'teamId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], UserTeamEntity.prototype, "teamId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'isAdmin',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], UserTeamEntity.prototype, "isAdmin", void 0);
exports.UserTeamEntity = UserTeamEntity = __decorate([
    (0, typeorm_1.Entity)('user_team', { schema: process.env.DATABASE_SCHEMA })
], UserTeamEntity);
//# sourceMappingURL=user-team.entity.js.map