import { DeliveryReceiptSettingEntity } from './entities/delivery-receipt-settings.entity';
import { Repository } from 'typeorm';
export declare class DeliveryReceiptSettingsService {
    readonly deliveryReceiptSettingRepo: Repository<DeliveryReceiptSettingEntity>;
    constructor();
    update(id: number, settingData: Partial<DeliveryReceiptSettingEntity>): Promise<DeliveryReceiptSettingEntity>;
    findById(id: number): Promise<DeliveryReceiptSettingEntity>;
    findOneBySiteId(siteId: number): Promise<DeliveryReceiptSettingEntity>;
    private getDefaultSetting;
    delete(id: number): Promise<boolean>;
}
