import { JWTPayload } from 'jose';
import { SiteVersionEntity } from 'src/version-history/entities/site-version.entity';

export type TempTokenType = 'site_design_edit' | 'site_content_edit';

export interface CreateTempTokenReq {
  type: TempTokenType;
  siteId: number;
  projectId: number;
  versionId: number;
}

export interface CreateTempTokenRes {
  token: string;
}

export interface JwtRemotePayloadDto extends JWTPayload {
  userId: string;
  type: TempTokenType;
  siteId: number;
  projectId: number;
  versionId: number;
}

export interface VerifyTempTokenReq {
  token: string;
}

export interface VerifyTempTokenRes {
  token: string;
  userId: string;
  type: TempTokenType;
  siteId: number;
  projectId: number;
  siteVersion: SiteVersionEntity;
}

export interface JwtPayloadDto extends JWTPayload {
  userId: string;
  email: string;
}
