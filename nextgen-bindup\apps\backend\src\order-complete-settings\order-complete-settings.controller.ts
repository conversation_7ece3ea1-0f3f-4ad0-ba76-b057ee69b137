import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { OrderCompletionSettingService } from './order-complete-settings.service';
import { OrderCompletionSettingEntity } from './entities/order-complete-settings.entity';

@Controller('order-completion-settings')
@UseGuards(AuthGuard)
export class OrderCompletionSettingController {
  constructor(
    private readonly orderCompletionSettingService: OrderCompletionSettingService,
  ) {}

  @Post('create')
  async create(
    @Body() orderCompletionSettingEntity: OrderCompletionSettingEntity,
  ) {
    return await this.orderCompletionSettingService.create(
      orderCompletionSettingEntity,
    );
  }

  @Put('update/:id')
  async update(
    @Param('id') id: string,
    @Body() data: Partial<OrderCompletionSettingEntity>,
  ) {
    return await this.orderCompletionSettingService.update(+id, data);
  }

  @Get('one-by-site/:siteId')
  async getOneBySiteId(@Param('siteId') siteId: string) {
    return await this.orderCompletionSettingService.findOneBySiteId(+siteId);
  }
}
