"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDnsRecordsTable1738652405179 = void 0;
const typeorm_1 = require("typeorm");
class CreateDnsRecordsTable1738652405179 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}dns_records`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'int',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'projectId',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'type',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'subdomain',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'value',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'ttl',
                    type: 'int',
                    isNullable: true,
                },
                {
                    name: 'status',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
        await queryRunner.query(`alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
    async down(queryRunner) {
        await queryRunner.query(`alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateDnsRecordsTable1738652405179 = CreateDnsRecordsTable1738652405179;
//# sourceMappingURL=1738652405179-create-dns-records-table.js.map