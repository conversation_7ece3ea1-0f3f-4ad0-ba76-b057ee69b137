"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateContentsTable1732001826299 = void 0;
const typeorm_1 = require("typeorm");
class CreateContentsTable1732001826299 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}contents`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'varchar',
                    isPrimary: true,
                    length: '36',
                },
                {
                    name: 'type',
                    type: 'smallint',
                    isNullable: false,
                },
                {
                    name: 'projectId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'data',
                    type: 'jsonb',
                    isNullable: false,
                },
                {
                    name: 'ts',
                    type: 'bigint',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
        await queryRunner.query(`alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
    async down(queryRunner) {
        await queryRunner.query(`alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateContentsTable1732001826299 = CreateContentsTable1732001826299;
//# sourceMappingURL=1732001826299-create-contents-table.js.map