import { MigrationInterface, QueryRunner, TableIndex } from 'typeorm';

export class CreateUpdateProductTable1746783765335
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}products`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'IDX_product_siteId_code',
        columnNames: ['siteId', 'code'],
        isUnique: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_product_siteId_code');
  }
}
