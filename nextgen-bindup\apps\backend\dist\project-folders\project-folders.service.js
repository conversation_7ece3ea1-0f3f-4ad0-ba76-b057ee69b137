"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectFoldersService = void 0;
const common_1 = require("@nestjs/common");
const project_folders_entity_1 = require("./entities/project-folders.entity");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
let ProjectFoldersService = class ProjectFoldersService {
    async create(data) {
        const now = new Date();
        data.id = undefined;
        data.createdAt = now;
        data.updatedAt = now;
        return await this.projectRepo.save(data);
    }
    async findByProjectId(projectId) {
        return await this.projectRepo.find({ where: { projectId } });
    }
};
exports.ProjectFoldersService = ProjectFoldersService;
__decorate([
    (0, typeorm_2.InjectRepository)(project_folders_entity_1.ProjectFolderEntity),
    __metadata("design:type", typeorm_1.Repository)
], ProjectFoldersService.prototype, "projectRepo", void 0);
exports.ProjectFoldersService = ProjectFoldersService = __decorate([
    (0, common_1.Injectable)()
], ProjectFoldersService);
//# sourceMappingURL=project-folders.service.js.map