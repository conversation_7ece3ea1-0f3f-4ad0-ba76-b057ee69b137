export declare const NEW_TS: () => number;
export declare const MAX_PRICE_VALUE = 999999;
export declare const MAX_PRODUCT_QUANTITY_VALUE = 99999;
export declare const ConvertToSlug: (text: string) => string;
export declare const isInteger: (value: number | null | undefined, opt?: {
    min?: number;
    max?: number;
}) => boolean;
export declare const isEmptyNumber: (value: number | null | undefined) => boolean;
