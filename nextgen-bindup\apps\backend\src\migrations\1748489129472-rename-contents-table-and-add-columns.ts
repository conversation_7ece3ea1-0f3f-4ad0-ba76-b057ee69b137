import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class RenameContentsToAssetComponentAndAddColumns1747980000000
  implements MigrationInterface
{
  OLD_TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}contents`;
  NEW_TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}asset_component`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Rename table
    await queryRunner.renameTable(this.OLD_TABLE_NAME, this.NEW_TABLE_NAME);

    // Add folder column
    const folderColumn: TableColumn = new TableColumn({
      name: 'folder',
      type: 'boolean',
      isNullable: false,
      default: 'false',
    });

    // Add parentFolder column
    const parentFolderColumn: TableColumn = new TableColumn({
      name: 'parentFolder',
      type: 'varchar',
      length: '255',
      isNullable: false,
      default: "''",
    });

    await queryRunner.addColumns(this.NEW_TABLE_NAME, [
      folderColumn,
      parentFolderColumn,
    ]);

    // Drop isDeleted column if exists
    await queryRunner.dropColumn(this.NEW_TABLE_NAME, 'isDeleted');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Re-add isDeleted column
    const isDeletedColumn: TableColumn = new TableColumn({
      name: 'isDeleted',
      type: 'boolean',
      isNullable: false,
      default: 'false',
    });
    await queryRunner.addColumn(this.NEW_TABLE_NAME, isDeletedColumn);

    // Remove added columns
    await queryRunner.dropColumn(this.NEW_TABLE_NAME, 'parentFolder');
    await queryRunner.dropColumn(this.NEW_TABLE_NAME, 'folder');

    // Rename table back
    await queryRunner.renameTable(this.NEW_TABLE_NAME, this.OLD_TABLE_NAME);
  }
}
