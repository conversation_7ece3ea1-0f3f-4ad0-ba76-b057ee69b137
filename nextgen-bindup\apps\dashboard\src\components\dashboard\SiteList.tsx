import { useEffect, type FC, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import SearchIcon from '@mui/icons-material/Search';
import { Dialog, SelectChangeEvent, debounce } from '@mui/material';
import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { useAuth } from '../../auth';
import { ProjectEntity } from '../../dto/project.type';
import { SiteEntity, SiteStatus } from '../../dto/site.type';
import { ProjectInfo } from '../../routes/index.route';
import { projectService } from '../../services/project-service';
import { siteService } from '../../services/site-service';
import SiteCard from './SiteCard';
import { SiteForm } from './site-form';

type Props = {
  projectInfo?: ProjectInfo;
};

const SiteList: FC<Props> = ({ projectInfo }) => {
  const { t } = useTranslation();
  const { session } = useAuth();
  const [project, setProject] = useState<ProjectEntity>();
  const [sites, setSites] = useState<SiteEntity[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');

  const [newSite, setNewSite] = useState<SiteEntity>({
    managementName: '',
    status: SiteStatus.DRAFT,
  });

  const [isOpenDialog, setIsOpenDialog] = useState(false);

  const [filteredSites, setFilteredSites] = useState<SiteEntity[]>([]);
  useEffect(() => {
    debounceFilter(sites, searchTerm);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm, sites]);

  const filterItems = (items: SiteEntity[], searchTerm: string) => {
    if (!searchTerm) {
      setFilteredSites(items);
      return;
    }

    const text: string = searchTerm.toLowerCase();
    const filteredItems = items.filter(item =>
      item.managementName.toLowerCase().includes(text),
    );
    setFilteredSites(filteredItems);
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceFilter = useCallback(
    debounce((items, searchTerm) => filterItems(items, searchTerm), 500),
    [],
  );

  useEffect(() => {
    if (!projectInfo?.id) return;
    const fetchProject = async () => {
      const data = await projectService.findById(projectInfo.id as number);
      setProject(data);
      setNewSite({
        ...newSite,
        projectId: data.id,
        userId: session?.user.userId,
      });
    };
    fetchProject();
    fetchSites();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectInfo]);

  const fetchSites = async () => {
    let data;
    if (projectInfo?.projectFolderId) {
      data = await siteService.findByProjectFolderId(
        projectInfo.id as number,
        projectInfo.projectFolderId as number,
      );
    } else {
      data = await siteService.findByProjectId(projectInfo?.id as number);
    }
    setSites(data);
  };

  const handleInputChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = event.target;
    setNewSite({ ...newSite, [name]: value });
  };

  const handleSelectChange = (event: SelectChangeEvent) => {
    const { name, value } = event.target;
    let newValue: string | undefined = value;
    if (name === 'projectFolderId') {
      newValue = value === ' ' ? undefined : value;
    }
    setNewSite({ ...newSite, [name]: newValue });
  };

  const handleCreate = async () => {
    try {
      await siteService.create(newSite);
      setIsOpenDialog(false);
      fetchSites();
    } catch (error) {
      console.error('Error creating Site:', error);
    }
  };

  if (!project) return null;

  return (
    <>
      <Alert severity="info" variant="outlined">
        Lorem ipsum dolor sit amet, consecte
      </Alert>
      <Alert severity="info" variant="outlined">
        Lorem ipsum dolor sit amet, consecte
      </Alert>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="h4">{project?.name}</Typography>
        <Box sx={{ display: 'inherit', gap: 2 }}>
          <TextField
            size="small"
            placeholder={t('common.search')}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              },
            }}
            onChange={e => setSearchTerm(e.target.value)}
          />
          <Button
            variant="outlined"
            onClick={() => {
              setIsOpenDialog(true);
            }}
          >
            {t('site.new_site')}
          </Button>
          <Button variant="outlined">{t('common.import')}</Button>
          <Button variant="outlined">{t('project.setting')}</Button>
        </Box>
      </Box>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
        {filteredSites.map(site => (
          <SiteCard key={site.id} data={site} />
        ))}
      </Box>
      <Dialog open={isOpenDialog} onClose={() => setIsOpenDialog(false)}>
        <SiteForm
          isEditing={false}
          initialSite={newSite}
          handleInputChange={handleInputChange}
          handleSelectChange={handleSelectChange}
          onCreate={handleCreate}
          onUpdate={() => {}}
          onCancel={() => {
            setIsOpenDialog(false);
          }}
        />
      </Dialog>
    </>
  );
};

export default SiteList;
