import { PageService } from 'src/page/page.service';
import { DataSource } from 'typeorm';
import { TemplatePageIdDto } from './dto/template_page_id.dto';
export declare class TemplateMigrationService {
    private readonly dataSource;
    private pageService;
    constructor(dataSource: DataSource, pageService: PageService);
    importTemplate(): Promise<void>;
    importPorfolioTemplate(): Promise<void>;
    importContactTemplate(): Promise<void>;
    importPhotoTemplate(): Promise<void>;
    importProfileTemplate(): Promise<void>;
    importTempTemplate(): Promise<void>;
    importTemplateToDB(userId: string): Promise<number>;
    createSite(templateId: number, userId: string, projectId: number, siteId: number, projectFolderId?: number): Promise<Array<TemplatePageIdDto>>;
    private updateRealPageChildren;
    private createAllPagesFromTemplate;
    private sortTemplatePagesByHierarchy;
    private processTemplateData;
    private generateSlug;
    private createDefaultPageComponents;
    private insertProcessedData;
    private createFolderTemplatePages;
    private createTemplatePages;
    private updateFolderTemplatePageChildren;
    private sortFoldersByHierarchy;
}
