import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateOrderCompletionSettingsTable1748401492924
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}order_completion_settings`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isGenerated: true,
            generationStrategy: 'increment',
            isPrimary: true,
          },
          {
            name: 'siteId',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'displayText',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'emailSubject',
            type: 'varchar',
            length: '250',
            isNullable: false,
          },
          {
            name: 'emailHeader',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'emailFooter',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
