"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StyleService = void 0;
const common_1 = require("@nestjs/common");
const style_entity_1 = require("./entities/style.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const project_service_1 = require("../project/project.service");
const app_exception_1 = require("../common/exceptions/app.exception");
let StyleService = class StyleService {
    constructor(projectService) {
        this.projectService = projectService;
    }
    async create(styleEntity) {
        const project = await this.projectService.findById(styleEntity.projectId);
        if (!project)
            throw new app_exception_1.AppException('error.project_not_found');
        const now = new Date();
        const style = new style_entity_1.StyleEntity();
        style.type = styleEntity.type;
        style.projectId = styleEntity.projectId;
        style.siteId = styleEntity.siteId;
        style.name = styleEntity.name;
        style.data = styleEntity.data;
        style.ts = styleEntity.ts || 0;
        style.createdAt = now;
        style.updatedAt = now;
        return await this.styleRepo.save(style);
    }
    async update(id, styleData) {
        const style = await this.styleRepo.findOneBy({ id: id });
        if (!style)
            throw new app_exception_1.AppException('error.style_not_found');
        delete styleData.id;
        styleData.updatedAt = new Date();
        await this.styleRepo.update(id, styleData);
        return { ...style, ...styleData };
    }
    async findById(id) {
        return await this.styleRepo.findOneBy({ id });
    }
    async findByProjectId(projectId) {
        return await this.styleRepo.findBy({ projectId });
    }
    async findBySiteId(projectId, siteId) {
        return await this.styleRepo.findBy({
            projectId: projectId,
            siteId: siteId,
        });
    }
    async delete(id) {
        const style = await this.styleRepo.findOneBy({ id });
        if (!style)
            throw new app_exception_1.AppException('error.style_not_found');
        await this.styleRepo.delete(style.id);
        return true;
    }
};
exports.StyleService = StyleService;
__decorate([
    (0, typeorm_1.InjectRepository)(style_entity_1.StyleEntity),
    __metadata("design:type", typeorm_2.Repository)
], StyleService.prototype, "styleRepo", void 0);
exports.StyleService = StyleService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [project_service_1.ProjectService])
], StyleService);
//# sourceMappingURL=style.service.js.map