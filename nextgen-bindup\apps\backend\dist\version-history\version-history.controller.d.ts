import { VersionHistoryService } from './version-history.service';
import { SiteVersionDto } from './version-history.dto';
import { SiteVersionEntity } from './entities/site-version.entity';
export declare class VersionHistoryController {
    private readonly versionHistoryService;
    constructor(versionHistoryService: VersionHistoryService);
    getSiteVersion(siteVersionId: number): Promise<SiteVersionEntity>;
    deleteSiteVersion(siteVersionId: number): Promise<void>;
    updateVersionName(siteVersionId: number, versionName: string): Promise<void>;
    getSiteVersions(siteId: number, year?: number, month?: number, page?: number, pageSize?: number): Promise<SiteVersionDto>;
    getPagesBySiteVersionId(siteVersionId: number): Promise<import("../page/entities/page.entity").PageEntity[]>;
    getPageBySiteVersionId(siteVersionId: number, pageId: number): Promise<import("../page/entities/page.entity").PageEntity>;
    backupSite(siteId: number, name?: string): Promise<SiteVersionEntity>;
    restoreSite(siteId: number, siteVersionId: number): Promise<boolean>;
}
