"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionHistoryController = void 0;
const common_1 = require("@nestjs/common");
const version_history_service_1 = require("./version-history.service");
let VersionHistoryController = class VersionHistoryController {
    constructor(versionHistoryService) {
        this.versionHistoryService = versionHistoryService;
    }
    async getSiteVersion(siteVersionId) {
        return this.versionHistoryService.getSiteVersion(siteVersionId);
    }
    async deleteSiteVersion(siteVersionId) {
        return this.versionHistoryService.deleteSiteVersion(siteVersionId);
    }
    async updateVersionName(siteVersionId, versionName) {
        return this.versionHistoryService.updateVersionName(siteVersionId, versionName);
    }
    async getSiteVersions(siteId, year, month, page = 1, pageSize = 100) {
        return this.versionHistoryService.getSiteVersions(siteId, page, pageSize, +year, +month);
    }
    async getPagesBySiteVersionId(siteVersionId) {
        return this.versionHistoryService.getPagesBySiteVersionId(siteVersionId);
    }
    async getPageBySiteVersionId(siteVersionId, pageId) {
        return this.versionHistoryService.getPageBySiteVersionId(siteVersionId, pageId);
    }
    async backupSite(siteId, name = '') {
        return this.versionHistoryService.backupSite(siteId, name);
    }
    async restoreSite(siteId, siteVersionId) {
        return this.versionHistoryService.restore(siteId, siteVersionId);
    }
};
exports.VersionHistoryController = VersionHistoryController;
__decorate([
    (0, common_1.Get)('get-site-version/:siteVersionId'),
    __param(0, (0, common_1.Param)('siteVersionId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VersionHistoryController.prototype, "getSiteVersion", null);
__decorate([
    (0, common_1.Delete)('delete-site-version/:siteVersionId'),
    __param(0, (0, common_1.Param)('siteVersionId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VersionHistoryController.prototype, "deleteSiteVersion", null);
__decorate([
    (0, common_1.Post)('update-version-name/:siteVersionId'),
    __param(0, (0, common_1.Param)('siteVersionId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)('versionName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], VersionHistoryController.prototype, "updateVersionName", null);
__decorate([
    (0, common_1.Get)('list-site/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __param(1, (0, common_1.Query)('year')),
    __param(2, (0, common_1.Query)('month')),
    __param(3, (0, common_1.Query)('page', common_1.ParseIntPipe)),
    __param(4, (0, common_1.Query)('pageSize', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number, Number, Number]),
    __metadata("design:returntype", Promise)
], VersionHistoryController.prototype, "getSiteVersions", null);
__decorate([
    (0, common_1.Get)('list-page/:siteVersionId'),
    __param(0, (0, common_1.Param)('siteVersionId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VersionHistoryController.prototype, "getPagesBySiteVersionId", null);
__decorate([
    (0, common_1.Get)('page-detail/:siteVersionId/:pageId'),
    __param(0, (0, common_1.Param)('siteVersionId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('pageId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], VersionHistoryController.prototype, "getPageBySiteVersionId", null);
__decorate([
    (0, common_1.Post)('backup-site/:siteId'),
    __param(0, (0, common_1.Param)('siteId')),
    __param(1, (0, common_1.Body)('name')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], VersionHistoryController.prototype, "backupSite", null);
__decorate([
    (0, common_1.Post)('restore/site/:siteId/:siteVersionId'),
    __param(0, (0, common_1.Param)('siteId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('siteVersionId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], VersionHistoryController.prototype, "restoreSite", null);
exports.VersionHistoryController = VersionHistoryController = __decorate([
    (0, common_1.Controller)('version-history'),
    __metadata("design:paramtypes", [version_history_service_1.VersionHistoryService])
], VersionHistoryController);
//# sourceMappingURL=version-history.controller.js.map