"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSiteVersion1739859980271 = void 0;
const site_type_1 = require("../site/types/site.type");
const typeorm_1 = require("typeorm");
class CreateSiteVersion1739859980271 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}site_versions`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'siteId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'projectId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'projectFolderId',
                    type: 'integer',
                    isNullable: true,
                },
                {
                    name: 'managementName',
                    type: 'varchar',
                    length: '255',
                    isNullable: false,
                },
                {
                    name: 'status',
                    type: 'smallint',
                    isNullable: false,
                    default: `'${site_type_1.SiteStatus.DRAFT}'`,
                },
                {
                    name: 'url',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'title',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'description',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'isSearch',
                    type: 'boolean',
                    isNullable: true,
                },
                {
                    name: 'thumb',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'headCode',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'bodyCode',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                },
                {
                    name: 'isArchived',
                    type: 'boolean',
                    isNullable: true,
                },
                {
                    name: 'versionCreatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'versionName',
                    type: 'varchar',
                    length: '255',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateSiteVersion1739859980271 = CreateSiteVersion1739859980271;
//# sourceMappingURL=1739859980271-create-site-version-table.js.map