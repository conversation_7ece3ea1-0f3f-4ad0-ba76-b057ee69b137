"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StyleEntity = void 0;
const typeorm_1 = require("typeorm");
let StyleEntity = class StyleEntity {
};
exports.StyleEntity = StyleEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], StyleEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'type',
        type: 'smallint',
        nullable: false,
    }),
    __metadata("design:type", Number)
], StyleEntity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'projectId',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], StyleEntity.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'siteId',
        type: 'integer',
        nullable: false,
        default: 1,
    }),
    __metadata("design:type", Number)
], StyleEntity.prototype, "siteId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'name',
        type: 'varchar',
        length: 255,
        nullable: false,
    }),
    __metadata("design:type", String)
], StyleEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'data',
        type: 'jsonb',
        nullable: false,
    }),
    __metadata("design:type", Object)
], StyleEntity.prototype, "data", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'ts',
        type: 'bigint',
        nullable: true,
    }),
    __metadata("design:type", Number)
], StyleEntity.prototype, "ts", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], StyleEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], StyleEntity.prototype, "updatedAt", void 0);
exports.StyleEntity = StyleEntity = __decorate([
    (0, typeorm_1.Entity)('styles', { schema: process.env.DATABASE_SCHEMA })
], StyleEntity);
//# sourceMappingURL=style.entity.js.map