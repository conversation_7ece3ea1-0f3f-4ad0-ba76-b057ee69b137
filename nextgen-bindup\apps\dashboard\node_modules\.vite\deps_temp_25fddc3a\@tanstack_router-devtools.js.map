{"version": 3, "sources": ["../../../../../node_modules/@tanstack/router-devtools-core/src/TanStackRouterDevtoolsCore.tsx", "../../../../../node_modules/@tanstack/router-devtools-core/src/TanStackRouterDevtoolsPanelCore.tsx", "../../../../../node_modules/@tanstack/react-router-devtools/src/TanStackRouterDevtools.tsx", "../../../../../node_modules/@tanstack/react-router-devtools/src/TanStackRouterDevtoolsPanel.tsx", "../../../../../node_modules/@tanstack/react-router-devtools/src/index.ts", "../../../../../node_modules/@tanstack/router-devtools/src/index.tsx"], "sourcesContent": ["import { createSignal, lazy } from 'solid-js'\nimport { render } from 'solid-js/web'\nimport { ShadowDomTargetContext } from './context'\nimport type { AnyRouter } from '@tanstack/router-core'\nimport type { Signal } from 'solid-js'\n\ninterface DevtoolsOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * Use this to add props to the panel. For example, you can add class, style (merge and override default style), etc.\n   */\n  panelProps?: any & {\n    ref?: any\n  }\n  /**\n   * Use this to add props to the close button. For example, you can add class, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: any & {\n    ref?: any\n  }\n  /**\n   * Use this to add props to the toggle button. For example, you can add class, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  toggleButtonProps?: any & {\n    ref?: any\n  }\n  /**\n   * The position of the TanStack Router logo to open and close the devtools panel.\n   * Defaults to 'bottom-left'.\n   */\n  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n  /**\n   * Use this to render the devtools inside a different type of container element for a11y purposes.\n   * Any string which corresponds to a valid intrinsic JSX element is allowed.\n   * Defaults to 'footer'.\n   */\n  containerElement?: string | any\n  /**\n   * A boolean variable indicating if the \"lite\" version of the library is being used\n   */\n  router: AnyRouter\n  routerState: any\n  /**\n   * Use this to attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n}\n\nclass TanStackRouterDevtoolsCore {\n  #router: Signal<AnyRouter>\n  #routerState: Signal<any>\n  #position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n  #initialIsOpen: boolean\n  #shadowDOMTarget?: ShadowRoot\n\n  #panelProps: any\n  #closeButtonProps: any\n  #toggleButtonProps: any\n  #containerElement?: string | any\n\n  #isMounted = false\n  #Component: any\n  #dispose?: () => void\n\n  constructor(config: DevtoolsOptions) {\n    this.#router = createSignal(config.router)\n    this.#routerState = createSignal(config.routerState)\n    this.#position = config.position ?? 'bottom-left'\n    this.#initialIsOpen = config.initialIsOpen ?? false\n    this.#shadowDOMTarget = config.shadowDOMTarget\n\n    this.#panelProps = config.panelProps\n    this.#closeButtonProps = config.closeButtonProps\n    this.#toggleButtonProps = config.toggleButtonProps\n    this.#containerElement = config.containerElement\n  }\n\n  mount<T extends HTMLElement>(el: T) {\n    if (this.#isMounted) {\n      throw new Error('Devtools is already mounted')\n    }\n\n    const dispose = render(() => {\n      const [router] = this.#router\n      const [routerState] = this.#routerState\n      const position = this.#position\n      const initialIsOpen = this.#initialIsOpen\n      const shadowDOMTarget = this.#shadowDOMTarget\n\n      const panelProps = this.#panelProps\n      const closeButtonProps = this.#closeButtonProps\n      const toggleButtonProps = this.#toggleButtonProps\n      const containerElement = this.#containerElement\n\n      let Devtools\n\n      if (this.#Component) {\n        Devtools = this.#Component\n      } else {\n        Devtools = lazy(() => import('./FloatingTanStackRouterDevtools'))\n        this.#Component = Devtools\n      }\n\n      return (\n        <ShadowDomTargetContext.Provider value={shadowDOMTarget}>\n          <Devtools\n            position={position}\n            initialIsOpen={initialIsOpen}\n            router={router}\n            routerState={routerState}\n            shadowDOMTarget={shadowDOMTarget}\n            panelProps={panelProps}\n            closeButtonProps={closeButtonProps}\n            toggleButtonProps={toggleButtonProps}\n            containerElement={containerElement}\n          />\n        </ShadowDomTargetContext.Provider>\n      )\n    }, el)\n\n    this.#isMounted = true\n    this.#dispose = dispose\n  }\n\n  unmount() {\n    if (!this.#isMounted) {\n      throw new Error('Devtools is not mounted')\n    }\n    this.#dispose?.()\n    this.#isMounted = false\n  }\n\n  setRouter(router: AnyRouter) {\n    this.#router[1](router)\n  }\n\n  setRouterState(routerState: any) {\n    this.#routerState[1](routerState)\n  }\n\n  setOptions(options: Partial<DevtoolsOptions>) {\n    if (options.position !== undefined) {\n      this.#position = options.position\n    }\n\n    if (options.initialIsOpen !== undefined) {\n      this.#initialIsOpen = options.initialIsOpen\n    }\n\n    if (options.shadowDOMTarget !== undefined) {\n      this.#shadowDOMTarget = options.shadowDOMTarget\n    }\n\n    if (options.containerElement !== undefined) {\n      this.#containerElement = options.containerElement\n    }\n  }\n}\n\nexport { TanStackRouterDevtoolsCore }\n", "import { render } from 'solid-js/web'\nimport { createSignal, lazy } from 'solid-js'\nimport { DevtoolsOnCloseContext, ShadowDomTargetContext } from './context'\nimport type { JSX } from 'solid-js'\nimport type { AnyRouter } from '@tanstack/router-core'\n\ninterface TanStackRouterDevtoolsPanelCoreOptions {\n  /**\n   * The standard React style object used to style a component with inline styles\n   */\n  style?: JSX.CSSProperties\n  /**\n   * The standard React class property used to style a component with classes\n   */\n  className?: string\n  /**\n   * A boolean variable indicating whether the panel is open or closed\n   */\n  isOpen?: boolean\n  /**\n   * A function that toggles the open and close state of the panel\n   */\n  setIsOpen?: (isOpen: boolean) => void\n  /**\n   * Handles the opening and closing the devtools panel\n   */\n  handleDragStart?: (e: any) => void\n  /**\n   * A boolean variable indicating if the \"lite\" version of the library is being used\n   */\n  router: AnyRouter\n\n  routerState: any\n  /**\n   * Use this to attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n}\n\nclass TanStackRouterDevtoolsPanelCore {\n  #router: any\n  #routerState: any\n  #shadowDOMTarget?: ShadowRoot\n  #isMounted = false\n  #setIsOpen?: (isOpen: boolean) => void\n  #dispose?: () => void\n  #Component: any\n\n  constructor(config: TanStackRouterDevtoolsPanelCoreOptions) {\n    const { router, routerState, shadowDOMTarget, setIsOpen } = config\n\n    this.#router = createSignal(router)\n    this.#routerState = createSignal(routerState)\n    this.#shadowDOMTarget = shadowDOMTarget\n    this.#setIsOpen = setIsOpen\n  }\n\n  mount<T extends HTMLElement>(el: T) {\n    if (this.#isMounted) {\n      throw new Error('Devtools is already mounted')\n    }\n\n    const dispose = render(() => {\n      const [router] = this.#router\n      const [routerState] = this.#routerState\n      const shadowDOMTarget = this.#shadowDOMTarget\n      const setIsOpen = this.#setIsOpen\n\n      let BaseTanStackRouterDevtoolsPanel\n\n      if (this.#Component) {\n        BaseTanStackRouterDevtoolsPanel = this.#Component\n      } else {\n        BaseTanStackRouterDevtoolsPanel = lazy(\n          () => import('./BaseTanStackRouterDevtoolsPanel'),\n        )\n        this.#Component = BaseTanStackRouterDevtoolsPanel\n      }\n\n      return (\n        <ShadowDomTargetContext.Provider value={shadowDOMTarget}>\n          <DevtoolsOnCloseContext.Provider\n            value={{\n              onCloseClick: () => {},\n            }}\n          >\n            <BaseTanStackRouterDevtoolsPanel\n              router={router}\n              routerState={routerState}\n              shadowDOMTarget={shadowDOMTarget}\n              setIsOpen={setIsOpen}\n            />\n          </DevtoolsOnCloseContext.Provider>\n        </ShadowDomTargetContext.Provider>\n      )\n    }, el)\n\n    this.#isMounted = true\n    this.#dispose = dispose\n  }\n\n  unmount() {\n    if (!this.#isMounted) {\n      throw new Error('Devtools is not mounted')\n    }\n    this.#dispose?.()\n    this.#isMounted = false\n  }\n\n  setRouter(router: AnyRouter) {\n    this.#router[1](router)\n  }\n\n  setRouterState(routerState: any) {\n    this.#routerState[1](routerState)\n  }\n\n  setOptions(options: Partial<TanStackRouterDevtoolsPanelCoreOptions>) {\n    if (options.shadowDOMTarget !== undefined) {\n      this.#shadowDOMTarget = options.shadowDOMTarget\n    }\n    if (options.router !== undefined) {\n      this.setRouter(options.router)\n    }\n    if (options.routerState !== undefined) {\n      this.setRouterState(options.routerState)\n    }\n  }\n}\n\nexport { TanStackRouterDevtoolsPanelCore }\n", "import { TanStackRouterDevtoolsCore } from '@tanstack/router-devtools-core'\nimport { Fragment, useEffect, useRef, useState } from 'react'\nimport { useRouter, useRouterState } from '@tanstack/react-router'\nimport type { AnyRouter } from '@tanstack/react-router'\nimport type React from 'react'\nimport type { JSX } from 'solid-js'\n\ninterface DevtoolsOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * Use this to add props to the panel. For example, you can add className, style (merge and override default style), etc.\n   */\n  panelProps?: JSX.HTMLAttributes<HTMLDivElement>\n  /**\n   * Use this to add props to the close button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: JSX.ButtonHTMLAttributes<HTMLButtonElement>\n  /**\n   * Use this to add props to the toggle button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  toggleButtonProps?: JSX.ButtonHTMLAttributes<HTMLButtonElement>\n  /**\n   * The position of the TanStack Router logo to open and close the devtools panel.\n   * Defaults to 'bottom-left'.\n   */\n  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n  /**\n   * Use this to render the devtools inside a different type of container element for a11y purposes.\n   * Any string which corresponds to a valid intrinsic JSX element is allowed.\n   * Defaults to 'footer'.\n   */\n  containerElement?: string | any\n  /**\n   * The router instance to use for the devtools.\n   */\n  router?: AnyRouter\n  /**\n   * Use this to attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n}\n\nexport function TanStackRouterDevtools(\n  props: DevtoolsOptions,\n): React.ReactElement | null {\n  const {\n    initialIsOpen,\n    panelProps,\n    closeButtonProps,\n    toggleButtonProps,\n    position,\n    containerElement,\n    shadowDOMTarget,\n    router: propsRouter,\n  } = props\n\n  const hookRouter = useRouter({ warn: false })\n  const activeRouter = propsRouter ?? hookRouter\n\n  const activeRouterState = useRouterState({ router: activeRouter })\n\n  const devToolRef = useRef<HTMLDivElement>(null)\n  const [devtools] = useState(\n    () =>\n      new TanStackRouterDevtoolsCore({\n        initialIsOpen,\n        panelProps,\n        closeButtonProps,\n        toggleButtonProps,\n        position,\n        containerElement,\n        shadowDOMTarget,\n        router: activeRouter,\n        routerState: activeRouterState,\n      }),\n  )\n\n  // Update devtools when props change\n  useEffect(() => {\n    devtools.setRouter(activeRouter)\n  }, [devtools, activeRouter])\n\n  useEffect(() => {\n    devtools.setRouterState(activeRouterState)\n  }, [devtools, activeRouterState])\n\n  useEffect(() => {\n    devtools.setOptions({\n      initialIsOpen: initialIsOpen,\n      panelProps: panelProps,\n      closeButtonProps: closeButtonProps,\n      toggleButtonProps: toggleButtonProps,\n      position: position,\n      containerElement: containerElement,\n      shadowDOMTarget: shadowDOMTarget,\n    })\n  }, [\n    devtools,\n    initialIsOpen,\n    panelProps,\n    closeButtonProps,\n    toggleButtonProps,\n    position,\n    containerElement,\n    shadowDOMTarget,\n  ])\n\n  useEffect(() => {\n    if (devToolRef.current) {\n      devtools.mount(devToolRef.current)\n    }\n\n    return () => {\n      devtools.unmount()\n    }\n  }, [devtools])\n\n  return (\n    <Fragment>\n      <div ref={devToolRef} />\n    </Fragment>\n  )\n}\n", "import { useRouter, useRouterState } from '@tanstack/react-router'\nimport { TanStackRouterDevtoolsPanelCore } from '@tanstack/router-devtools-core'\nimport React, { useEffect, useRef, useState } from 'react'\nimport type { AnyRouter } from '@tanstack/react-router'\n\nexport interface DevtoolsPanelOptions {\n  /**\n   * The standard React style object used to style a component with inline styles\n   */\n  style?: any\n  /**\n   * The standard React class property used to style a component with classes\n   */\n  className?: string\n  /**\n   * A boolean variable indicating whether the panel is open or closed\n   */\n  isOpen?: boolean\n  /**\n   * A function that toggles the open and close state of the panel\n   */\n  setIsOpen?: (isOpen: boolean) => void\n  /**\n   * Handles the opening and closing the devtools panel\n   */\n  handleDragStart?: (e: any) => void\n  /**\n   * A boolean variable indicating if the \"lite\" version of the library is being used\n   */\n  router?: AnyRouter\n  /**\n   * Use this to attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n}\n\nexport const TanStackRouterDevtoolsPanel: React.FC<DevtoolsPanelOptions> = (\n  props,\n): React.ReactElement | null => {\n  const { router: propsRouter, ...rest } = props\n  const hookRouter = useRouter({ warn: false })\n  const activeRouter = propsRouter ?? hookRouter\n  const activeRouterState = useRouterState({ router: activeRouter })\n\n  const devToolRef = useRef<HTMLDivElement>(null)\n  const [devtools] = useState(\n    () =>\n      new TanStackRouterDevtoolsPanelCore({\n        ...rest,\n        router: activeRouter,\n        routerState: activeRouterState,\n      }),\n  )\n\n  // Update devtools when props change\n  useEffect(() => {\n    devtools.setRouter(activeRouter)\n  }, [devtools, activeRouter])\n\n  useEffect(() => {\n    devtools.setRouterState(activeRouterState)\n  }, [devtools, activeRouterState])\n\n  useEffect(() => {\n    devtools.setOptions({\n      className: props.className,\n      style: props.style,\n      shadowDOMTarget: props.shadowDOMTarget,\n    })\n  }, [devtools, props.className, props.style, props.shadowDOMTarget])\n\n  useEffect(() => {\n    if (devToolRef.current) {\n      devtools.mount(devToolRef.current)\n    }\n\n    return () => {\n      devtools.unmount()\n    }\n  }, [devtools])\n\n  return (\n    <>\n      <div ref={devToolRef} />\n    </>\n  )\n}\n", "import * as Devtools from './TanStackRouterDevtools'\nimport * as DevtoolsPanel from './TanStackRouterDevtoolsPanel'\n\nexport const TanStackRouterDevtools: (typeof Devtools)['TanStackRouterDevtools'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : Devtools.TanStackRouterDevtools\n\nexport const TanStackRouterDevtoolsInProd: (typeof Devtools)['TanStackRouterDevtools'] =\n  Devtools.TanStackRouterDevtools\n\nexport const TanStackRouterDevtoolsPanel: (typeof DevtoolsPanel)['TanStackRouterDevtoolsPanel'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : DevtoolsPanel.TanStackRouterDevtoolsPanel\n\nexport const TanStackRouterDevtoolsPanelInProd: (typeof DevtoolsPanel)['TanStackRouterDevtoolsPanel'] =\n  DevtoolsPanel.TanStackRouterDevtoolsPanel\n", "console.warn(\n  '[@tanstack/router-devtools] This package has moved to @tanstack/react-router-devtools. Please switch to the new package at your earliest convenience, as this package will be dropped in the next major version release.',\n)\n\nexport { TanStackRouterDevtoolsInProd as TanStackRouterDevtools } from '@tanstack/react-router-devtools'\nexport { TanStackRouterDevtoolsPanelInProd as TanStackRouterDevtoolsPanel } from '@tanstack/react-router-devtools'\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA,IAAMA,6BAAN,MAAiC;EAgB/BC,YAAYC,QAAyB;AAfrC,iBAAA,MAAA,OAAA;AACA,iBAAA,MAAA,YAAA;AACA,iBAAA,MAAA,SAAA;AACA,iBAAA,MAAA,cAAA;AACA,iBAAA,MAAA,gBAAA;AAEA,iBAAA,MAAA,WAAA;AACA,iBAAA,MAAA,iBAAA;AACA,iBAAA,MAAA,kBAAA;AACA,iBAAA,MAAA,iBAAA;AAEA,iBAAA,MAAA,YAAa,KAAA;AACb,iBAAA,MAAA,UAAA;AACA,iBAAA,MAAA,QAAA;AAGO,iBAAA,MAAA,SAAUC,aAAaD,OAAOE,MAAM,CAAA;AACpC,iBAAA,MAAA,cAAeD,aAAaD,OAAOG,WAAW,CAAA;AAC9C,iBAAA,MAAA,WAAYH,OAAOI,YAAY,aAAA;AAC/B,iBAAA,MAAA,gBAAiBJ,OAAOK,iBAAiB,KAAA;AAC9C,iBAAA,MAAK,kBAAmBL,OAAOM,eAAAA;AAE/B,iBAAA,MAAK,aAAcN,OAAOO,UAAAA;AAC1B,iBAAA,MAAK,mBAAoBP,OAAOQ,gBAAAA;AAChC,iBAAA,MAAK,oBAAqBR,OAAOS,iBAAAA;AACjC,iBAAA,MAAK,mBAAoBT,OAAOU,gBAAAA;EAAAA;EAGlCC,MAA6BC,IAAO;AAClC,QAAI,aAAA,MAAK,UAAA,GAAY;AACb,YAAA,IAAIC,MAAM,6BAA6B;IAAA;AAGzCC,UAAAA,UAAUC,OAAO,MAAM;AACrB,YAAA,CAACb,MAAM,IAAI,aAAA,MAAK,OAAA;AAChB,YAAA,CAACC,WAAW,IAAI,aAAA,MAAK,YAAA;AAC3B,YAAMC,WAAW,aAAA,MAAK,SAAA;AACtB,YAAMC,gBAAgB,aAAA,MAAK,cAAA;AAC3B,YAAMC,kBAAkB,aAAA,MAAK,gBAAA;AAE7B,YAAMC,aAAa,aAAA,MAAK,WAAA;AACxB,YAAMC,mBAAmB,aAAA,MAAK,iBAAA;AAC9B,YAAMC,oBAAoB,aAAA,MAAK,kBAAA;AAC/B,YAAMC,mBAAmB,aAAA,MAAK,iBAAA;AAE1BM,UAAAA;AAEJ,UAAI,aAAA,MAAK,UAAA,GAAY;AACnBA,mBAAW,aAAA,MAAK,UAAA;MAAA,OACX;AACLA,mBAAWC,KAAK,MAAM,OAAO,8CAAkC,CAAC;AAChE,qBAAA,MAAK,YAAaD,QAAAA;MAAAA;AAGpBE,aAAAA,gBACGC,uBAAuBC,UAAQ;QAACC,OAAOf;QAAe,IAAAgB,WAAA;AAAA,iBAAAJ,gBACpDF,UAAQ;YACPZ;YACAC;YACAH;YACAC;YACAG;YACAC;YACAC;YACAC;YACAC;UAAAA,CAAkC;QAAA;MAAA,CAAA;IAAA,GAIvCE,EAAE;AAEL,iBAAA,MAAK,YAAa,IAAA;AAClB,iBAAA,MAAK,UAAWE,OAAAA;EAAAA;EAGlBS,UAAU;;AACJ,QAAA,CAAC,aAAA,MAAK,UAAA,GAAY;AACd,YAAA,IAAIV,MAAM,yBAAyB;IAAA;AAE3C,KAAA,KAAA,aAAA,MAAK,QAAA,MAAL,OAAA,SAAA,GAAA,KAAA,IAAA;AACA,iBAAA,MAAK,YAAa,KAAA;EAAA;EAGpBW,UAAUtB,QAAmB;AACtB,iBAAA,MAAA,OAAA,EAAQ,CAAC,EAAEA,MAAM;EAAA;EAGxBuB,eAAetB,aAAkB;AAC1B,iBAAA,MAAA,YAAA,EAAa,CAAC,EAAEA,WAAW;EAAA;EAGlCuB,WAAWC,SAAmC;AACxCA,QAAAA,QAAQvB,aAAawB,QAAW;AAClC,mBAAA,MAAK,WAAYD,QAAQvB,QAAAA;IAAAA;AAGvBuB,QAAAA,QAAQtB,kBAAkBuB,QAAW;AACvC,mBAAA,MAAK,gBAAiBD,QAAQtB,aAAAA;IAAAA;AAG5BsB,QAAAA,QAAQrB,oBAAoBsB,QAAW;AACzC,mBAAA,MAAK,kBAAmBD,QAAQrB,eAAAA;IAAAA;AAG9BqB,QAAAA,QAAQjB,qBAAqBkB,QAAW;AAC1C,mBAAA,MAAK,mBAAoBD,QAAQjB,gBAAAA;IAAAA;EACnC;AAEJ;AA5GE,UAAA,oBAAA,QAAA;AACA,eAAA,oBAAA,QAAA;AACA,YAAA,oBAAA,QAAA;AACA,iBAAA,oBAAA,QAAA;AACA,mBAAA,oBAAA,QAAA;AAEA,cAAA,oBAAA,QAAA;AACA,oBAAA,oBAAA,QAAA;AACA,qBAAA,oBAAA,QAAA;AACA,oBAAA,oBAAA,QAAA;AAEA,aAAA,oBAAA,QAAA;AACA,aAAA,oBAAA,QAAA;AACA,WAAA,oBAAA,QAAA;;;;;;;;;;;;;;;;;AC1BF,IAAMmB,kCAAN,MAAsC;EASpCC,YAAYC,QAAgD;AAR5D,IAAAC,cAAA,MAAAC,QAAA;AACA,IAAAD,cAAA,MAAAE,aAAA;AACA,IAAAF,cAAA,MAAAG,iBAAA;AACA,IAAAH,cAAA,MAAAI,aAAa,KAAA;AACb,IAAAJ,cAAA,MAAA,UAAA;AACA,IAAAA,cAAA,MAAAK,SAAA;AACA,IAAAL,cAAA,MAAAM,WAAA;AAGQ,UAAA;MAAEC;MAAQC;MAAaC;MAAiBC;IAAAA,IAAcX;AAEvD,IAAAY,cAAA,MAAAV,UAAUW,aAAaL,MAAM,CAAA;AAC7B,IAAAI,cAAA,MAAAT,eAAeU,aAAaJ,WAAW,CAAA;AAC5C,IAAAG,cAAA,MAAKR,mBAAmBM,eAAAA;AACxB,IAAAE,cAAA,MAAK,YAAaD,SAAAA;EAAAA;EAGpBG,MAA6BC,IAAO;AAClC,QAAIC,cAAA,MAAKX,WAAA,GAAY;AACb,YAAA,IAAIY,MAAM,6BAA6B;IAAA;AAGzCC,UAAAA,UAAUC,OAAO,MAAM;AACrB,YAAA,CAACX,MAAM,IAAIQ,cAAA,MAAKd,QAAA;AAChB,YAAA,CAACO,WAAW,IAAIO,cAAA,MAAKb,aAAA;AAC3B,YAAMO,kBAAkBM,cAAA,MAAKZ,iBAAA;AAC7B,YAAMO,YAAYK,cAAA,MAAK,UAAA;AAEnBI,UAAAA;AAEJ,UAAIJ,cAAA,MAAKT,WAAA,GAAY;AACnBa,0CAAkCJ,cAAA,MAAKT,WAAA;MAAA,OAClC;AACLa,0CAAkCC,KAChC,MAAM,OAAO,+CAAmC,CAClD;AACA,QAAAT,cAAA,MAAKL,aAAaa,+BAAAA;MAAAA;AAGpBE,aAAAA,gBACGC,uBAAuBC,UAAQ;QAACC,OAAOf;QAAe,IAAAgB,WAAA;AAAAJ,iBAAAA,gBACpDK,uBAAuBH,UAAQ;YAC9BC,OAAO;cACLG,cAAcA,MAAM;cAAA;YACtB;YAAC,IAAAF,WAAA;AAAA,qBAAAJ,gBAEAF,iCAA+B;gBAC9BZ;gBACAC;gBACAC;gBACAC;cAAAA,CAAoB;YAAA;UAAA,CAAA;QAAA;MAAA,CAAA;IAAA,GAK3BI,EAAE;AAEL,IAAAH,cAAA,MAAKP,aAAa,IAAA;AAClB,IAAAO,cAAA,MAAKN,WAAWY,OAAAA;EAAAA;EAGlBW,UAAU;;AACJ,QAAA,CAACb,cAAA,MAAKX,WAAA,GAAY;AACd,YAAA,IAAIY,MAAM,yBAAyB;IAAA;AAE3C,KAAA,KAAAD,cAAA,MAAKV,SAAA,MAAL,OAAA,SAAA,GAAA,KAAA,IAAA;AACA,IAAAM,cAAA,MAAKP,aAAa,KAAA;EAAA;EAGpByB,UAAUtB,QAAmB;AACtB,IAAAQ,cAAA,MAAAd,QAAA,EAAQ,CAAC,EAAEM,MAAM;EAAA;EAGxBuB,eAAetB,aAAkB;AAC1B,IAAAO,cAAA,MAAAb,aAAA,EAAa,CAAC,EAAEM,WAAW;EAAA;EAGlCuB,WAAWC,SAA0D;AAC/DA,QAAAA,QAAQvB,oBAAoBwB,QAAW;AACzC,MAAAtB,cAAA,MAAKR,mBAAmB6B,QAAQvB,eAAAA;IAAAA;AAE9BuB,QAAAA,QAAQzB,WAAW0B,QAAW;AAC3BJ,WAAAA,UAAUG,QAAQzB,MAAM;IAAA;AAE3ByB,QAAAA,QAAQxB,gBAAgByB,QAAW;AAChCH,WAAAA,eAAeE,QAAQxB,WAAW;IAAA;EACzC;AAEJ;AAxFEP,WAAA,oBAAA,QAAA;AACAC,gBAAA,oBAAA,QAAA;AACAC,oBAAA,oBAAA,QAAA;AACAC,cAAA,oBAAA,QAAA;AACA,aAAA,oBAAA,QAAA;AACAC,YAAA,oBAAA,QAAA;AACAC,cAAA,oBAAA,QAAA;;;;ACDK,SAAS,uBACd,OAC2B;AACrB,QAAA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ;EAAA,IACN;AAEJ,QAAM,aAAa,UAAU,EAAE,MAAM,MAAA,CAAO;AAC5C,QAAM,eAAe,eAAe;AAEpC,QAAM,oBAAoB,eAAe,EAAE,QAAQ,aAAA,CAAc;AAE3D,QAAA,iBAAa,qBAAuB,IAAI;AACxC,QAAA,CAAC,QAAQ,QAAI;IACjB,MACE,IAAI,2BAA2B;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA,QAAQ;MACR,aAAa;IACd,CAAA;EACL;AAGA,8BAAU,MAAM;AACd,aAAS,UAAU,YAAY;EAAA,GAC9B,CAAC,UAAU,YAAY,CAAC;AAE3B,8BAAU,MAAM;AACd,aAAS,eAAe,iBAAiB;EAAA,GACxC,CAAC,UAAU,iBAAiB,CAAC;AAEhC,8BAAU,MAAM;AACd,aAAS,WAAW;MAClB;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACD;EAAA,GACA;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;AAED,8BAAU,MAAM;AACd,QAAI,WAAW,SAAS;AACb,eAAA,MAAM,WAAW,OAAO;IAAA;AAGnC,WAAO,MAAM;AACX,eAAS,QAAQ;IACnB;EAAA,GACC,CAAC,QAAQ,CAAC;AAEb,aAAA,wBACG,uBACC,EAAA,cAAA,wBAAC,OAAI,EAAA,KAAK,WAAY,CAAA,EAAA,CACxB;AAEJ;;;;;ACzFa,IAAA,8BAA8D,CACzE,UAC8B;AAC9B,QAAM,EAAE,QAAQ,aAAa,GAAG,KAAS,IAAA;AACzC,QAAM,aAAa,UAAU,EAAE,MAAM,MAAA,CAAO;AAC5C,QAAM,eAAe,eAAe;AACpC,QAAM,oBAAoB,eAAe,EAAE,QAAQ,aAAA,CAAc;AAE3D,QAAA,iBAAa,sBAAuB,IAAI;AACxC,QAAA,CAAC,QAAQ,QAAI;IACjB,MACE,IAAI,gCAAgC;MAClC,GAAG;MACH,QAAQ;MACR,aAAa;IACd,CAAA;EACL;AAGA,+BAAU,MAAM;AACd,aAAS,UAAU,YAAY;EAAA,GAC9B,CAAC,UAAU,YAAY,CAAC;AAE3B,+BAAU,MAAM;AACd,aAAS,eAAe,iBAAiB;EAAA,GACxC,CAAC,UAAU,iBAAiB,CAAC;AAEhC,+BAAU,MAAM;AACd,aAAS,WAAW;MAClB,WAAW,MAAM;MACjB,OAAO,MAAM;MACb,iBAAiB,MAAM;IAAA,CACxB;EAAA,GACA,CAAC,UAAU,MAAM,WAAW,MAAM,OAAO,MAAM,eAAe,CAAC;AAElE,+BAAU,MAAM;AACd,QAAI,WAAW,SAAS;AACb,eAAA,MAAM,WAAW,OAAO;IAAA;AAGnC,WAAO,MAAM;AACX,eAAS,QAAQ;IACnB;EAAA,GACC,CAAC,QAAQ,CAAC;AAEb,aAEI,yBAAA,8BAAA,EAAA,cAAA,yBAAC,OAAI,EAAA,KAAK,WAAY,CAAA,EAAA,CACxB;AAEJ;;;AC5EO,IAAM,+BACX4B;AASK,IAAM,oCACXC;;;ACrBF,QAAQ;EACN;AACF;", "names": ["TanStackRouterDevtoolsCore", "constructor", "config", "createSignal", "router", "routerState", "position", "initialIsOpen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "panelProps", "closeButtonProps", "toggleButtonProps", "containerElement", "mount", "el", "Error", "dispose", "render", "Devtools", "lazy", "_$createComponent", "ShadowDomTargetContext", "Provider", "value", "children", "unmount", "setRouter", "setRouterState", "setOptions", "options", "undefined", "TanStackRouterDevtoolsPanelCore", "constructor", "config", "__privateAdd", "_router", "_routerState", "_shadowD<PERSON><PERSON><PERSON>get", "_isMounted", "_dispose", "_Component", "router", "routerState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setIsOpen", "__privateSet", "createSignal", "mount", "el", "__privateGet", "Error", "dispose", "render", "BaseTanStackRouterDevtoolsPanel", "lazy", "_$createComponent", "ShadowDomTargetContext", "Provider", "value", "children", "DevtoolsOnCloseContext", "onCloseClick", "unmount", "setRouter", "setRouterState", "setOptions", "options", "undefined", "Devtools.TanStackRouterDevtools", "DevtoolsPanel.TanStackRouterDevtoolsPanel"]}