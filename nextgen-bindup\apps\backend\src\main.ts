import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { StorageService } from './storage/storage.service';
import { LoggerService } from './logger/logger.service';
import { AppExceptionFilter } from './common/exceptions/app-exception.filter';
import { ValidateExceptionFilter } from './common/exceptions/validate-exception.filter';
import { UnhandledExceptionFilter } from './common/exceptions/unhandled-exception.filter';
import * as bodyParser from 'body-parser';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const nestApp = await NestFactory.create(AppModule);
  nestApp.enableCors();
  nestApp.use('/payment/webhook', bodyParser.raw({ type: 'application/json' }));

  const loggerService = nestApp.get(LoggerService);
  nestApp.useGlobalPipes(new ValidationPipe());
  nestApp.useGlobalFilters(new AppExceptionFilter(loggerService));
  nestApp.useGlobalFilters(new ValidateExceptionFilter(loggerService));
  nestApp.useGlobalFilters(new UnhandledExceptionFilter(loggerService));

  const storageService = nestApp.get(StorageService);
  await storageService.initBucket();

  await nestApp.listen(4000);
}
bootstrap();
