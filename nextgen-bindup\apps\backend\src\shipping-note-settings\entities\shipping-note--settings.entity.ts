import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('shipping_note_settings', { schema: process.env.DATABASE_SCHEMA })
export class ShippingNoteSettingEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'integer',
  })
  id: number;

  @Column({
    name: 'siteId',
    type: 'integer',
    nullable: false,
  })
  siteId: number;

  @Column({
    name: 'shippingFee',
    type: 'bigint',
    nullable: true,
  })
  shippingFee: number;

  @Column({
    name: 'shippingFeeDetail',
    type: 'jsonb',
    nullable: false,
  })
  shippingFeeDetail: Record<string, number>;

  @Column({
    name: 'isFreeShippingCondition',
    type: 'boolean',
    nullable: false,
  })
  isFreeShippingCondition: boolean;

  @Column({
    name: 'freeShippingCondition',
    type: 'bigint',
    nullable: true,
  })
  freeShippingCondition: number;

  @Column({
    name: 'note',
    type: 'text',
    nullable: true,
  })
  note: string;

  @CreateDateColumn({
    name: 'createdAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updatedAt',
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;
}
