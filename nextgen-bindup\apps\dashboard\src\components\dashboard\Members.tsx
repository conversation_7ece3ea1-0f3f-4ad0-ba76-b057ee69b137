import { useCallback, useEffect, useState, type FC } from 'react';
import { useTranslation } from 'react-i18next';
import EditIcon from '@mui/icons-material/Edit';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import PersonIcon from '@mui/icons-material/Person';
import SearchIcon from '@mui/icons-material/Search';
import { debounce } from '@mui/material';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { getErrMsg } from '@nextgen-bindup/common/utility';
import { useAuth } from '../../auth';
import { UserTeamDto } from '../../dto/user-team.type';
import { userTeamService } from '../../services/user-team.service';
import { InviteMember } from './members/InviteMember';

const Members: FC = () => {
  const { t } = useTranslation();
  const { session } = useAuth();
  const [members, setMembers] = useState<UserTeamDto[]>([]);
  const [displayMembers, setDisplayMembers] = useState<UserTeamDto[]>([]);
  const [errorMsg, setErrorMsg] = useState<string>('');
  const [search, setSearch] = useState<string>('');
  const [isInvite, setIsInvite] = useState<boolean>(false);

  useEffect(() => {
    getListMembers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getListMembers = async () => {
    setErrorMsg('');
    if (!session?.user.userId) return;

    try {
      const list = await userTeamService.getTeamsOfMember(session?.user.userId);
      setMembers(list);
    } catch (e) {
      setErrorMsg(t(getErrMsg(e)));
    }
  };

  useEffect(() => {
    debounceFilter(members, search);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [members, search]);

  const filterMembers = (list: UserTeamDto[], search: string) => {
    if (!search) {
      setDisplayMembers(list);
      return;
    }

    const text: string = search.toLowerCase();
    const filteredMembers = list.filter(
      member =>
        (member.fullname || '').toLowerCase().includes(text) ||
        member.email.toLowerCase().includes(text),
    );
    setDisplayMembers(filteredMembers);
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceFilter = useCallback(
    debounce((list, text) => filterMembers(list, text), 500),
    [],
  );

  return errorMsg ? (
    <Typography color="error" align="center">
      {errorMsg}
    </Typography>
  ) : (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="h4">{t('member.title')}</Typography>
        <Box sx={{ display: 'inherit', gap: 2 }}>
          <TextField
            size="small"
            placeholder={t('common.search')}
            value={search}
            onChange={e => setSearch(e.target.value)}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              },
            }}
          />
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => setIsInvite(true)}
          >
            {t('common.btn_invite')}
          </Button>
        </Box>
      </Box>

      <Table aria-label="members table">
        <TableHead sx={{ backgroundColor: '#f6f6f6' }}>
          <TableRow>
            <TableCell>{t('member.reg_order')}</TableCell>
            <TableCell>{t('member.name')}</TableCell>
            <TableCell>{t('member.email')}</TableCell>
            <TableCell>{t('member.group')}</TableCell>
            <TableCell>{t('member.action')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {displayMembers.map((user, index) => (
            <TableRow
              key={user.userId}
              sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
            >
              <TableCell component="th" scope="row">
                {index + 1}
              </TableCell>
              <TableCell sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar>
                  <PersonIcon />
                </Avatar>
                <Typography variant="inherit">{user.fullname}</Typography>
              </TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>{user.teamName}</TableCell>
              <TableCell>
                <IconButton>
                  <EditIcon />
                </IconButton>
                <IconButton>
                  <MoreHorizIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {isInvite ? (
        <InviteMember
          onUpdate={status => {
            if (status === 'invite') {
              getListMembers();
            }
            setIsInvite(false);
          }}
        />
      ) : null}
    </>
  );
};

export default Members;
