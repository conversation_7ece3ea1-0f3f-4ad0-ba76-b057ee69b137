import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableIndex,
} from 'typeorm';

export class CreateUpdateProductTable1746783765334
  implements MigrationInterface
{
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}products`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns(this.TABLE_NAME, [
      'stockQuantity',
      'attributes',
      'stockVariants',
    ]);

    const variants = new TableColumn({
      name: 'variants',
      type: 'jsonb',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, variants);

    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'IDX_product_siteId',
        columnNames: ['siteId'],
        isUnique: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_product_siteId');
  }
}
