export class ListImagesResponseDto {
  success: boolean;
  errors?: string[];
  messages?: string[];
  result: ListImagesResultDto;
}

export class ListImagesResultDto {
  count: number;
  total_count: number;
  page: number;
  per_page: number;
  images: ImageDto[];
}

export class ImageDto {
  id: string;
  filename: string;
  uploaded: string;
  size: number;
  url: string;
  requireSignedURLs: boolean;
}

export class DeleteImageResponseDto {
  success: boolean;
  errors?: string[];
  messages?: string[];
  result: null;
}

export class PresignedUrlResponseDto {
  success: boolean;
  errors: string[];
  messages: string[];
  result: PresignedUrlResultDto;
}

export class PresignedUrlResultDto {
  url: string;
  maxSize: number;
  requireSignedURLs: boolean;
}

export class MetadataDto {
  name?: string;
  description?: string;
}

export class UploadFileDto {
  metadata: any;
  file: Express.Multer.File;
}
