"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubDomainService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const sub_domain_entity_1 = require("./entities/sub-domain.entity");
const app_exception_1 = require("../common/exceptions/app.exception");
let SubDomainService = class SubDomainService {
    async create(data) {
        delete data.id;
        const now = new Date();
        data.createdAt = now;
        data.updatedAt = now;
        return await this.subDomainRepo.save(data);
    }
    async update(id, data) {
        const entity = await this.subDomainRepo.findOneBy({ id });
        if (!entity)
            throw new app_exception_1.AppException('api.error.sub_domain_not_found');
        const now = new Date();
        data.updatedAt = now;
        delete data.id;
        await this.subDomainRepo.update(id, data);
        return { ...entity, ...data };
    }
    async delete(id) {
        const entity = await this.subDomainRepo.findOneBy({ id });
        if (!entity)
            throw new app_exception_1.AppException('api.error.sub_domain_not_found');
        await this.subDomainRepo.delete(id);
        return true;
    }
    async findBySiteId(siteId) {
        return await this.subDomainRepo.findBy({ siteId });
    }
    async findByProjectId(projectId) {
        return await this.subDomainRepo.findBy({ projectId });
    }
};
exports.SubDomainService = SubDomainService;
__decorate([
    (0, typeorm_1.InjectRepository)(sub_domain_entity_1.SubDomainEntity),
    __metadata("design:type", typeorm_2.Repository)
], SubDomainService.prototype, "subDomainRepo", void 0);
exports.SubDomainService = SubDomainService = __decorate([
    (0, common_1.Injectable)()
], SubDomainService);
//# sourceMappingURL=sub-domain.service.js.map