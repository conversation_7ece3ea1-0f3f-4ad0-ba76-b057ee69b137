# NextGen BindUp - Quick Reference

## 🚀 Getting Started

```powershell
cd test
.\start_env.ps1
```

## 📋 Commands

| Command | PowerShell | Batch File | Description |
|---------|------------|------------|-------------|
| **Setup & Start** | `.\start_env.ps1` | `start_env.bat setup` | Complete setup and start all services |
| **Check Status** | `.\start_env.ps1 -CheckOnly` | `start_env.bat check` | Check if services are running |
| **Stop Services** | `.\start_env.ps1 -StopOnly` | `start_env.bat stop` | Stop all running services |
| **Help** | `.\start_env.ps1 -Help` | `start_env.bat help` | Show detailed help |

## 🌐 Service URLs

- **Backend API**: http://localhost:4000
- **Live Editor**: http://localhost:3000  
- **Design Editor**: http://localhost:5173
- **Dashboard**: http://localhost:5174
- **SSG**: http://localhost:4321

## 🔧 Troubleshooting

### Common Issues

**Services not starting?**
```powershell
.\start_env.ps1 -StopOnly
.\start_env.ps1
```

**Port conflicts?**
```powershell
.\start_env.ps1 -CheckOnly  # See what's running
.\start_env.ps1 -StopOnly   # Stop everything
```

**Yarn errors?**
- The script now handles Yarn 4.x compatibility
- Dependencies install automatically during service startup
- Root dependency issues are usually non-critical

### Database Connection Issues

If you see TypeORM database errors in the backend:
1. The script creates a basic database configuration
2. You may need to update the database password in `test-configs/backend.env`
3. Or configure the backend to use a different database setup

## 💡 Tips

- **First time**: Run `.\start_env.ps1` and wait for all services to start
- **Daily use**: Use `.\start_env.ps1 -CheckOnly` to see what's running
- **Clean shutdown**: Use `.\start_env.ps1 -StopOnly` when done
- **Multiple windows**: Each service runs in its own PowerShell window
- **Dependency issues**: Services auto-install dependencies if missing

## 📁 What Gets Created

```
test/
├── test-data/           # Your test data
├── test-results/        # Test execution results  
├── test-configs/        # Environment files
│   ├── backend.env      # Backend configuration
│   ├── live-editor.env  # Live Editor configuration
│   ├── design-editor.env # Design Editor configuration
│   └── dashboard.env    # Dashboard configuration
```

## 🛡️ Safety

- All test files stay in the `test/` folder
- Source code remains untouched
- Environment files are copied only when needed
- Clean separation between test and production

---

**Need more help?** Run `.\start_env.ps1 -Help` for detailed information.
