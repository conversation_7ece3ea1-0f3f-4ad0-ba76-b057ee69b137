{"version": 3, "sources": ["../../../../../node_modules/@tiptap/extension-image/src/image.ts", "../../../../../node_modules/tiptap-extension-resize-image/esm/index.js"], "sourcesContent": ["import {\n  mergeAttributes,\n  Node,\n  nodeInputRule,\n} from '@tiptap/core'\n\nexport interface ImageOptions {\n  /**\n   * Controls if the image node should be inline or not.\n   * @default false\n   * @example true\n   */\n  inline: boolean,\n\n  /**\n   * Controls if base64 images are allowed. Enable this if you want to allow\n   * base64 image urls in the `src` attribute.\n   * @default false\n   * @example true\n   */\n  allowBase64: boolean,\n\n  /**\n   * HTML attributes to add to the image element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    image: {\n      /**\n       * Add an image\n       * @param options The image attributes\n       * @example\n       * editor\n       *   .commands\n       *   .setImage({ src: 'https://tiptap.dev/logo.png', alt: 'tiptap', title: 'tiptap logo' })\n       */\n      setImage: (options: { src: string, alt?: string, title?: string }) => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an image to a ![image](src \"title\") on input.\n */\nexport const inputRegex = /(?:^|\\s)(!\\[(.+|:?)]\\((\\S+)(?:(?:\\s+)[\"'](\\S+)[\"'])?\\))$/\n\n/**\n * This extension allows you to insert images.\n * @see https://www.tiptap.dev/api/nodes/image\n */\nexport const Image = Node.create<ImageOptions>({\n  name: 'image',\n\n  addOptions() {\n    return {\n      inline: false,\n      allowBase64: false,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline() {\n    return this.options.inline\n  },\n\n  group() {\n    return this.options.inline ? 'inline' : 'block'\n  },\n\n  draggable: true,\n\n  addAttributes() {\n    return {\n      src: {\n        default: null,\n      },\n      alt: {\n        default: null,\n      },\n      title: {\n        default: null,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: this.options.allowBase64\n          ? 'img[src]'\n          : 'img[src]:not([src^=\"data:\"])',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['img', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setImage: options => ({ commands }) => {\n        return commands.insertContent({\n          type: this.name,\n          attrs: options,\n        })\n      },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: inputRegex,\n        type: this.type,\n        getAttributes: match => {\n          const [,, alt, src, title] = match\n\n          return { src, alt, title }\n        },\n      }),\n    ]\n  },\n})\n", "import Image from '@tiptap/extension-image';\n\nconst CONSTANTS = {\r\n    MOBILE_BREAKPOINT: 768,\r\n    ICON_SIZE: '24px',\r\n    CONTROLLER_HEIGHT: '25px',\r\n    DOT_SIZE: {\r\n        MOBILE: 16,\r\n        DESKTOP: 9,\r\n    },\r\n    DOT_POSITION: {\r\n        MOBILE: '-8px',\r\n        DESKTOP: '-4px',\r\n    },\r\n    COLORS: {\r\n        BORDER: '#6C6C6C',\r\n        BACKGROUND: 'rgba(255, 255, 255, 1)',\r\n    },\r\n    ICONS: {\r\n        LEFT: 'https://fonts.gstatic.com/s/i/short-term/release/materialsymbolsoutlined/format_align_left/default/20px.svg',\r\n        CENTER: 'https://fonts.gstatic.com/s/i/short-term/release/materialsymbolsoutlined/format_align_center/default/20px.svg',\r\n        RIGHT: 'https://fonts.gstatic.com/s/i/short-term/release/materialsymbolsoutlined/format_align_right/default/20px.svg',\r\n    },\r\n};\n\nconst utils = {\r\n    isMobile() {\r\n        return document.documentElement.clientWidth < CONSTANTS.MOBILE_BREAKPOINT;\r\n    },\r\n    getDotPosition() {\r\n        return utils.isMobile() ? CONSTANTS.DOT_POSITION.MOBILE : CONSTANTS.DOT_POSITION.DESKTOP;\r\n    },\r\n    getDotSize() {\r\n        return utils.isMobile() ? CONSTANTS.DOT_SIZE.MOBILE : CONSTANTS.DOT_SIZE.DESKTOP;\r\n    },\r\n    clearContainerBorder(container) {\r\n        const containerStyle = container.getAttribute('style');\r\n        const newStyle = containerStyle === null || containerStyle === void 0 ? void 0 : containerStyle.replace('border: 1px dashed #6C6C6C;', '').replace('border: 1px dashed rgb(108, 108, 108)', '');\r\n        container.setAttribute('style', newStyle);\r\n    },\r\n    removeResizeElements(container) {\r\n        if (container.childElementCount > 3) {\r\n            for (let i = 0; i < 5; i++) {\r\n                container.removeChild(container.lastChild);\r\n            }\r\n        }\r\n    },\r\n};\n\nclass StyleManager {\r\n    static getContainerStyle(inline, width) {\r\n        const baseStyle = `width: ${width || '100%'}; height: auto; cursor: pointer;`;\r\n        const inlineStyle = inline ? 'display: inline-block;' : '';\r\n        return `${baseStyle} ${inlineStyle}`;\r\n    }\r\n    static getWrapperStyle(inline) {\r\n        return inline ? 'display: inline-block; float: left; padding-right: 8px;' : 'display: flex';\r\n    }\r\n    static getPositionControllerStyle(inline) {\r\n        const width = inline ? '66px' : '100px';\r\n        return `\n      position: absolute; \n      top: 0%; \n      left: 50%; \n      width: ${width}; \n      height: ${CONSTANTS.CONTROLLER_HEIGHT}; \n      z-index: 999; \n      background-color: ${CONSTANTS.COLORS.BACKGROUND}; \n      border-radius: 3px; \n      border: 1px solid ${CONSTANTS.COLORS.BORDER}; \n      cursor: pointer; \n      transform: translate(-50%, -50%); \n      display: flex; \n      justify-content: space-between; \n      align-items: center; \n      padding: 0 6px;\n    `\r\n            .replace(/\\s+/g, ' ')\r\n            .trim();\r\n    }\r\n    static getDotStyle(index) {\r\n        const dotPosition = utils.getDotPosition();\r\n        const dotSize = utils.getDotSize();\r\n        const positions = [\r\n            `top: ${dotPosition}; left: ${dotPosition}; cursor: nwse-resize;`,\r\n            `top: ${dotPosition}; right: ${dotPosition}; cursor: nesw-resize;`,\r\n            `bottom: ${dotPosition}; left: ${dotPosition}; cursor: nesw-resize;`,\r\n            `bottom: ${dotPosition}; right: ${dotPosition}; cursor: nwse-resize;`,\r\n        ];\r\n        return `\n      position: absolute; \n      width: ${dotSize}px; \n      height: ${dotSize}px; \n      border: 1.5px solid ${CONSTANTS.COLORS.BORDER}; \n      border-radius: 50%; \n      ${positions[index]}\n    `\r\n            .replace(/\\s+/g, ' ')\r\n            .trim();\r\n    }\r\n}\n\nclass AttributeParser {\r\n    static parseImageAttributes(nodeAttrs, imgElement) {\r\n        Object.entries(nodeAttrs).forEach(([key, value]) => {\r\n            if (value === undefined || value === null || key === 'wrapperStyle')\r\n                return;\r\n            if (key === 'containerStyle') {\r\n                const width = value.match(/width:\\s*([0-9.]+)px/);\r\n                if (width) {\r\n                    imgElement.setAttribute('width', width[1]);\r\n                }\r\n                return;\r\n            }\r\n            imgElement.setAttribute(key, value);\r\n        });\r\n    }\r\n    static extractWidthFromStyle(style) {\r\n        const width = style.match(/width:\\s*([0-9.]+)px/);\r\n        return width ? width[1] : null;\r\n    }\r\n}\n\nclass PositionController {\r\n    constructor(elements, inline, dispatchNodeView) {\r\n        this.elements = elements;\r\n        this.inline = inline;\r\n        this.dispatchNodeView = dispatchNodeView;\r\n    }\r\n    createControllerIcon(src) {\r\n        const controller = document.createElement('img');\r\n        controller.setAttribute('src', src);\r\n        controller.setAttribute('style', `width: ${CONSTANTS.ICON_SIZE}; height: ${CONSTANTS.ICON_SIZE}; cursor: pointer;`);\r\n        controller.addEventListener('mouseover', (e) => {\r\n            e.target.style.opacity = '0.6';\r\n        });\r\n        controller.addEventListener('mouseout', (e) => {\r\n            e.target.style.opacity = '1';\r\n        });\r\n        return controller;\r\n    }\r\n    handleLeftClick() {\r\n        if (!this.inline) {\r\n            this.elements.container.setAttribute('style', `${this.elements.container.style.cssText} margin: 0 auto 0 0;`);\r\n        }\r\n        else {\r\n            const style = 'display: inline-block; float: left; padding-right: 8px;';\r\n            this.elements.wrapper.setAttribute('style', style);\r\n            this.elements.container.setAttribute('style', style);\r\n        }\r\n        this.dispatchNodeView();\r\n    }\r\n    handleCenterClick() {\r\n        this.elements.container.setAttribute('style', `${this.elements.container.style.cssText} margin: 0 auto;`);\r\n        this.dispatchNodeView();\r\n    }\r\n    handleRightClick() {\r\n        if (!this.inline) {\r\n            this.elements.container.setAttribute('style', `${this.elements.container.style.cssText} margin: 0 0 0 auto;`);\r\n        }\r\n        else {\r\n            const style = 'display: inline-block; float: right; padding-left: 8px;';\r\n            this.elements.wrapper.setAttribute('style', style);\r\n            this.elements.container.setAttribute('style', style);\r\n        }\r\n        this.dispatchNodeView();\r\n    }\r\n    createPositionControls() {\r\n        const controller = document.createElement('div');\r\n        controller.setAttribute('style', StyleManager.getPositionControllerStyle(this.inline));\r\n        const leftController = this.createControllerIcon(CONSTANTS.ICONS.LEFT);\r\n        leftController.addEventListener('click', () => this.handleLeftClick());\r\n        controller.appendChild(leftController);\r\n        if (!this.inline) {\r\n            const centerController = this.createControllerIcon(CONSTANTS.ICONS.CENTER);\r\n            centerController.addEventListener('click', () => this.handleCenterClick());\r\n            controller.appendChild(centerController);\r\n        }\r\n        const rightController = this.createControllerIcon(CONSTANTS.ICONS.RIGHT);\r\n        rightController.addEventListener('click', () => this.handleRightClick());\r\n        controller.appendChild(rightController);\r\n        this.elements.container.appendChild(controller);\r\n        return this;\r\n    }\r\n}\n\nclass ResizeController {\r\n    constructor(elements, dispatchNodeView) {\r\n        this.state = {\r\n            isResizing: false,\r\n            startX: 0,\r\n            startWidth: 0,\r\n        };\r\n        this.handleMouseMove = (e, index) => {\r\n            if (!this.state.isResizing)\r\n                return;\r\n            const deltaX = index % 2 === 0 ? -(e.clientX - this.state.startX) : e.clientX - this.state.startX;\r\n            const newWidth = this.state.startWidth + deltaX;\r\n            this.elements.container.style.width = newWidth + 'px';\r\n            this.elements.img.style.width = newWidth + 'px';\r\n        };\r\n        this.handleMouseUp = () => {\r\n            if (this.state.isResizing) {\r\n                this.state.isResizing = false;\r\n            }\r\n            this.dispatchNodeView();\r\n        };\r\n        this.handleTouchMove = (e, index) => {\r\n            if (!this.state.isResizing)\r\n                return;\r\n            const deltaX = index % 2 === 0\r\n                ? -(e.touches[0].clientX - this.state.startX)\r\n                : e.touches[0].clientX - this.state.startX;\r\n            const newWidth = this.state.startWidth + deltaX;\r\n            this.elements.container.style.width = newWidth + 'px';\r\n            this.elements.img.style.width = newWidth + 'px';\r\n        };\r\n        this.handleTouchEnd = () => {\r\n            if (this.state.isResizing) {\r\n                this.state.isResizing = false;\r\n            }\r\n            this.dispatchNodeView();\r\n        };\r\n        this.elements = elements;\r\n        this.dispatchNodeView = dispatchNodeView;\r\n    }\r\n    createResizeHandle(index) {\r\n        const dot = document.createElement('div');\r\n        dot.setAttribute('style', StyleManager.getDotStyle(index));\r\n        dot.addEventListener('mousedown', (e) => {\r\n            e.preventDefault();\r\n            this.state.isResizing = true;\r\n            this.state.startX = e.clientX;\r\n            this.state.startWidth = this.elements.container.offsetWidth;\r\n            const onMouseMove = (e) => this.handleMouseMove(e, index);\r\n            const onMouseUp = () => {\r\n                this.handleMouseUp();\r\n                document.removeEventListener('mousemove', onMouseMove);\r\n                document.removeEventListener('mouseup', onMouseUp);\r\n            };\r\n            document.addEventListener('mousemove', onMouseMove);\r\n            document.addEventListener('mouseup', onMouseUp);\r\n        });\r\n        dot.addEventListener('touchstart', (e) => {\r\n            e.cancelable && e.preventDefault();\r\n            this.state.isResizing = true;\r\n            this.state.startX = e.touches[0].clientX;\r\n            this.state.startWidth = this.elements.container.offsetWidth;\r\n            const onTouchMove = (e) => this.handleTouchMove(e, index);\r\n            const onTouchEnd = () => {\r\n                this.handleTouchEnd();\r\n                document.removeEventListener('touchmove', onTouchMove);\r\n                document.removeEventListener('touchend', onTouchEnd);\r\n            };\r\n            document.addEventListener('touchmove', onTouchMove);\r\n            document.addEventListener('touchend', onTouchEnd);\r\n        }, { passive: false });\r\n        return dot;\r\n    }\r\n}\n\nclass ImageNodeView {\r\n    constructor(context, inline) {\r\n        this.clearContainerBorder = () => {\r\n            utils.clearContainerBorder(this.elements.container);\r\n        };\r\n        this.dispatchNodeView = () => {\r\n            const { view, getPos } = this.context;\r\n            if (typeof getPos === 'function') {\r\n                this.clearContainerBorder();\r\n                const newAttrs = Object.assign(Object.assign({}, this.context.node.attrs), { containerStyle: `${this.elements.container.style.cssText}`, wrapperStyle: `${this.elements.wrapper.style.cssText}` });\r\n                view.dispatch(view.state.tr.setNodeMarkup(getPos(), null, newAttrs));\r\n            }\r\n        };\r\n        this.removeResizeElements = () => {\r\n            utils.removeResizeElements(this.elements.container);\r\n        };\r\n        this.context = context;\r\n        this.inline = inline;\r\n        this.elements = this.createElements();\r\n    }\r\n    createElements() {\r\n        return {\r\n            wrapper: document.createElement('div'),\r\n            container: document.createElement('div'),\r\n            img: document.createElement('img'),\r\n        };\r\n    }\r\n    setupImageAttributes() {\r\n        AttributeParser.parseImageAttributes(this.context.node.attrs, this.elements.img);\r\n    }\r\n    setupDOMStructure() {\r\n        const { wrapperStyle, containerStyle } = this.context.node.attrs;\r\n        this.elements.wrapper.setAttribute('style', wrapperStyle);\r\n        this.elements.wrapper.appendChild(this.elements.container);\r\n        this.elements.container.setAttribute('style', containerStyle);\r\n        this.elements.container.appendChild(this.elements.img);\r\n    }\r\n    createPositionController() {\r\n        const positionController = new PositionController(this.elements, this.inline, this.dispatchNodeView);\r\n        positionController.createPositionControls();\r\n    }\r\n    createResizeHandler() {\r\n        const resizeHandler = new ResizeController(this.elements, this.dispatchNodeView);\r\n        Array.from({ length: 4 }, (_, index) => {\r\n            const dot = resizeHandler.createResizeHandle(index);\r\n            this.elements.container.appendChild(dot);\r\n        });\r\n    }\r\n    setupContainerClick() {\r\n        this.elements.container.addEventListener('click', () => {\r\n            var _a;\r\n            const isMobile = utils.isMobile();\r\n            isMobile && ((_a = document.querySelector('.ProseMirror-focused')) === null || _a === void 0 ? void 0 : _a.blur());\r\n            this.removeResizeElements();\r\n            this.createPositionController();\r\n            this.elements.container.setAttribute('style', `position: relative; border: 1px dashed ${CONSTANTS.COLORS.BORDER}; ${this.context.node.attrs.containerStyle}`);\r\n            this.createResizeHandler();\r\n        });\r\n    }\r\n    setupContentClick() {\r\n        document.addEventListener('click', (e) => {\r\n            const target = e.target;\r\n            const isClickInside = this.elements.container.contains(target) ||\r\n                target.style.cssText ===\r\n                    `width: ${CONSTANTS.ICON_SIZE}; height: ${CONSTANTS.ICON_SIZE}; cursor: pointer;`;\r\n            if (!isClickInside) {\r\n                this.clearContainerBorder();\r\n                this.removeResizeElements();\r\n            }\r\n        });\r\n    }\r\n    initialize() {\r\n        this.setupDOMStructure();\r\n        this.setupImageAttributes();\r\n        const { editable } = this.context.editor.options;\r\n        if (!editable)\r\n            return { dom: this.elements.container };\r\n        this.setupContainerClick();\r\n        this.setupContentClick();\r\n        return {\r\n            dom: this.elements.wrapper,\r\n        };\r\n    }\r\n}\n\nconst ImageResize = Image.extend({\r\n    name: 'imageResize',\r\n    addOptions() {\r\n        var _a;\r\n        return Object.assign(Object.assign({}, (_a = this.parent) === null || _a === void 0 ? void 0 : _a.call(this)), { inline: false });\r\n    },\r\n    addAttributes() {\r\n        var _a;\r\n        const inline = this.options.inline;\r\n        return Object.assign(Object.assign({}, (_a = this.parent) === null || _a === void 0 ? void 0 : _a.call(this)), { containerStyle: {\r\n                default: StyleManager.getContainerStyle(inline),\r\n                parseHTML: (element) => {\r\n                    const width = element.getAttribute('width');\r\n                    return width\r\n                        ? StyleManager.getContainerStyle(inline, `${width}px`)\r\n                        : `${element.style.cssText}`;\r\n                },\r\n            }, wrapperStyle: {\r\n                default: StyleManager.getWrapperStyle(inline),\r\n            } });\r\n    },\r\n    addNodeView() {\r\n        return ({ node, editor, getPos }) => {\r\n            const inline = this.options.inline;\r\n            const context = {\r\n                node,\r\n                editor,\r\n                view: editor.view,\r\n                getPos: typeof getPos === 'function' ? getPos : undefined,\r\n            };\r\n            const nodeView = new ImageNodeView(context, inline);\r\n            return nodeView.initialize();\r\n        };\r\n    },\r\n});\n\nexport { ImageResize, ImageResize as default };\n//# sourceMappingURL=index.js.map\n"], "mappings": ";;;;;;;;AAiDO,IAAM,aAAa;AAMb,IAAA,QAAQ,KAAK,OAAqB;EAC7C,MAAM;EAEN,aAAU;AACR,WAAO;MACL,QAAQ;MACR,aAAa;MACb,gBAAgB,CAAA;;;EAIpB,SAAM;AACJ,WAAO,KAAK,QAAQ;;EAGtB,QAAK;AACH,WAAO,KAAK,QAAQ,SAAS,WAAW;;EAG1C,WAAW;EAEX,gBAAa;AACX,WAAO;MACL,KAAK;QACH,SAAS;MACV;MACD,KAAK;QACH,SAAS;MACV;MACD,OAAO;QACL,SAAS;MACV;;;EAIL,YAAS;AACP,WAAO;MACL;QACE,KAAK,KAAK,QAAQ,cACd,aACA;MACL;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,OAAO,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;;EAG7E,cAAW;AACT,WAAO;MACL,UAAU,aAAW,CAAC,EAAE,SAAQ,MAAM;AACpC,eAAO,SAAS,cAAc;UAC5B,MAAM,KAAK;UACX,OAAO;QACR,CAAA;;;;EAKP,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;QACX,eAAe,WAAQ;AACrB,gBAAM,CAAA,EAAA,EAAI,KAAK,KAAK,KAAK,IAAI;AAE7B,iBAAO,EAAE,KAAK,KAAK,MAAK;;OAE3B;;;AAGN,CAAA;;;AC9HD,IAAM,YAAY;AAAA,EACd,mBAAmB;AAAA,EACnB,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,UAAU;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AAAA,EACA,cAAc;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,YAAY;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,EACX;AACJ;AAEA,IAAM,QAAQ;AAAA,EACV,WAAW;AACP,WAAO,SAAS,gBAAgB,cAAc,UAAU;AAAA,EAC5D;AAAA,EACA,iBAAiB;AACb,WAAO,MAAM,SAAS,IAAI,UAAU,aAAa,SAAS,UAAU,aAAa;AAAA,EACrF;AAAA,EACA,aAAa;AACT,WAAO,MAAM,SAAS,IAAI,UAAU,SAAS,SAAS,UAAU,SAAS;AAAA,EAC7E;AAAA,EACA,qBAAqB,WAAW;AAC5B,UAAM,iBAAiB,UAAU,aAAa,OAAO;AACrD,UAAM,WAAW,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,QAAQ,+BAA+B,EAAE,EAAE,QAAQ,yCAAyC,EAAE;AAC9L,cAAU,aAAa,SAAS,QAAQ;AAAA,EAC5C;AAAA,EACA,qBAAqB,WAAW;AAC5B,QAAI,UAAU,oBAAoB,GAAG;AACjC,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,kBAAU,YAAY,UAAU,SAAS;AAAA,MAC7C;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAM,eAAN,MAAmB;AAAA,EACf,OAAO,kBAAkB,QAAQ,OAAO;AACpC,UAAM,YAAY,UAAU,SAAS,MAAM;AAC3C,UAAM,cAAc,SAAS,2BAA2B;AACxD,WAAO,GAAG,SAAS,IAAI,WAAW;AAAA,EACtC;AAAA,EACA,OAAO,gBAAgB,QAAQ;AAC3B,WAAO,SAAS,4DAA4D;AAAA,EAChF;AAAA,EACA,OAAO,2BAA2B,QAAQ;AACtC,UAAM,QAAQ,SAAS,SAAS;AAChC,WAAO;AAAA;AAAA;AAAA;AAAA,eAIA,KAAK;AAAA,gBACJ,UAAU,iBAAiB;AAAA;AAAA,0BAEjB,UAAU,OAAO,UAAU;AAAA;AAAA,0BAE3B,UAAU,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQpC,QAAQ,QAAQ,GAAG,EACnB,KAAK;AAAA,EACd;AAAA,EACA,OAAO,YAAY,OAAO;AACtB,UAAM,cAAc,MAAM,eAAe;AACzC,UAAM,UAAU,MAAM,WAAW;AACjC,UAAM,YAAY;AAAA,MACd,QAAQ,WAAW,WAAW,WAAW;AAAA,MACzC,QAAQ,WAAW,YAAY,WAAW;AAAA,MAC1C,WAAW,WAAW,WAAW,WAAW;AAAA,MAC5C,WAAW,WAAW,YAAY,WAAW;AAAA,IACjD;AACA,WAAO;AAAA;AAAA,eAEA,OAAO;AAAA,gBACN,OAAO;AAAA,4BACK,UAAU,OAAO,MAAM;AAAA;AAAA,QAE3C,UAAU,KAAK,CAAC;AAAA,MAEX,QAAQ,QAAQ,GAAG,EACnB,KAAK;AAAA,EACd;AACJ;AAEA,IAAM,kBAAN,MAAsB;AAAA,EAClB,OAAO,qBAAqB,WAAW,YAAY;AAC/C,WAAO,QAAQ,SAAS,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAChD,UAAI,UAAU,UAAa,UAAU,QAAQ,QAAQ;AACjD;AACJ,UAAI,QAAQ,kBAAkB;AAC1B,cAAM,QAAQ,MAAM,MAAM,sBAAsB;AAChD,YAAI,OAAO;AACP,qBAAW,aAAa,SAAS,MAAM,CAAC,CAAC;AAAA,QAC7C;AACA;AAAA,MACJ;AACA,iBAAW,aAAa,KAAK,KAAK;AAAA,IACtC,CAAC;AAAA,EACL;AAAA,EACA,OAAO,sBAAsB,OAAO;AAChC,UAAM,QAAQ,MAAM,MAAM,sBAAsB;AAChD,WAAO,QAAQ,MAAM,CAAC,IAAI;AAAA,EAC9B;AACJ;AAEA,IAAM,qBAAN,MAAyB;AAAA,EACrB,YAAY,UAAU,QAAQ,kBAAkB;AAC5C,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EACA,qBAAqB,KAAK;AACtB,UAAM,aAAa,SAAS,cAAc,KAAK;AAC/C,eAAW,aAAa,OAAO,GAAG;AAClC,eAAW,aAAa,SAAS,UAAU,UAAU,SAAS,aAAa,UAAU,SAAS,oBAAoB;AAClH,eAAW,iBAAiB,aAAa,CAAC,MAAM;AAC5C,QAAE,OAAO,MAAM,UAAU;AAAA,IAC7B,CAAC;AACD,eAAW,iBAAiB,YAAY,CAAC,MAAM;AAC3C,QAAE,OAAO,MAAM,UAAU;AAAA,IAC7B,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB;AACd,QAAI,CAAC,KAAK,QAAQ;AACd,WAAK,SAAS,UAAU,aAAa,SAAS,GAAG,KAAK,SAAS,UAAU,MAAM,OAAO,sBAAsB;AAAA,IAChH,OACK;AACD,YAAM,QAAQ;AACd,WAAK,SAAS,QAAQ,aAAa,SAAS,KAAK;AACjD,WAAK,SAAS,UAAU,aAAa,SAAS,KAAK;AAAA,IACvD;AACA,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,oBAAoB;AAChB,SAAK,SAAS,UAAU,aAAa,SAAS,GAAG,KAAK,SAAS,UAAU,MAAM,OAAO,kBAAkB;AACxG,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,mBAAmB;AACf,QAAI,CAAC,KAAK,QAAQ;AACd,WAAK,SAAS,UAAU,aAAa,SAAS,GAAG,KAAK,SAAS,UAAU,MAAM,OAAO,sBAAsB;AAAA,IAChH,OACK;AACD,YAAM,QAAQ;AACd,WAAK,SAAS,QAAQ,aAAa,SAAS,KAAK;AACjD,WAAK,SAAS,UAAU,aAAa,SAAS,KAAK;AAAA,IACvD;AACA,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,yBAAyB;AACrB,UAAM,aAAa,SAAS,cAAc,KAAK;AAC/C,eAAW,aAAa,SAAS,aAAa,2BAA2B,KAAK,MAAM,CAAC;AACrF,UAAM,iBAAiB,KAAK,qBAAqB,UAAU,MAAM,IAAI;AACrE,mBAAe,iBAAiB,SAAS,MAAM,KAAK,gBAAgB,CAAC;AACrE,eAAW,YAAY,cAAc;AACrC,QAAI,CAAC,KAAK,QAAQ;AACd,YAAM,mBAAmB,KAAK,qBAAqB,UAAU,MAAM,MAAM;AACzE,uBAAiB,iBAAiB,SAAS,MAAM,KAAK,kBAAkB,CAAC;AACzE,iBAAW,YAAY,gBAAgB;AAAA,IAC3C;AACA,UAAM,kBAAkB,KAAK,qBAAqB,UAAU,MAAM,KAAK;AACvE,oBAAgB,iBAAiB,SAAS,MAAM,KAAK,iBAAiB,CAAC;AACvE,eAAW,YAAY,eAAe;AACtC,SAAK,SAAS,UAAU,YAAY,UAAU;AAC9C,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,mBAAN,MAAuB;AAAA,EACnB,YAAY,UAAU,kBAAkB;AACpC,SAAK,QAAQ;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,YAAY;AAAA,IAChB;AACA,SAAK,kBAAkB,CAAC,GAAG,UAAU;AACjC,UAAI,CAAC,KAAK,MAAM;AACZ;AACJ,YAAM,SAAS,QAAQ,MAAM,IAAI,EAAE,EAAE,UAAU,KAAK,MAAM,UAAU,EAAE,UAAU,KAAK,MAAM;AAC3F,YAAM,WAAW,KAAK,MAAM,aAAa;AACzC,WAAK,SAAS,UAAU,MAAM,QAAQ,WAAW;AACjD,WAAK,SAAS,IAAI,MAAM,QAAQ,WAAW;AAAA,IAC/C;AACA,SAAK,gBAAgB,MAAM;AACvB,UAAI,KAAK,MAAM,YAAY;AACvB,aAAK,MAAM,aAAa;AAAA,MAC5B;AACA,WAAK,iBAAiB;AAAA,IAC1B;AACA,SAAK,kBAAkB,CAAC,GAAG,UAAU;AACjC,UAAI,CAAC,KAAK,MAAM;AACZ;AACJ,YAAM,SAAS,QAAQ,MAAM,IACvB,EAAE,EAAE,QAAQ,CAAC,EAAE,UAAU,KAAK,MAAM,UACpC,EAAE,QAAQ,CAAC,EAAE,UAAU,KAAK,MAAM;AACxC,YAAM,WAAW,KAAK,MAAM,aAAa;AACzC,WAAK,SAAS,UAAU,MAAM,QAAQ,WAAW;AACjD,WAAK,SAAS,IAAI,MAAM,QAAQ,WAAW;AAAA,IAC/C;AACA,SAAK,iBAAiB,MAAM;AACxB,UAAI,KAAK,MAAM,YAAY;AACvB,aAAK,MAAM,aAAa;AAAA,MAC5B;AACA,WAAK,iBAAiB;AAAA,IAC1B;AACA,SAAK,WAAW;AAChB,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EACA,mBAAmB,OAAO;AACtB,UAAM,MAAM,SAAS,cAAc,KAAK;AACxC,QAAI,aAAa,SAAS,aAAa,YAAY,KAAK,CAAC;AACzD,QAAI,iBAAiB,aAAa,CAAC,MAAM;AACrC,QAAE,eAAe;AACjB,WAAK,MAAM,aAAa;AACxB,WAAK,MAAM,SAAS,EAAE;AACtB,WAAK,MAAM,aAAa,KAAK,SAAS,UAAU;AAChD,YAAM,cAAc,CAACA,OAAM,KAAK,gBAAgBA,IAAG,KAAK;AACxD,YAAM,YAAY,MAAM;AACpB,aAAK,cAAc;AACnB,iBAAS,oBAAoB,aAAa,WAAW;AACrD,iBAAS,oBAAoB,WAAW,SAAS;AAAA,MACrD;AACA,eAAS,iBAAiB,aAAa,WAAW;AAClD,eAAS,iBAAiB,WAAW,SAAS;AAAA,IAClD,CAAC;AACD,QAAI,iBAAiB,cAAc,CAAC,MAAM;AACtC,QAAE,cAAc,EAAE,eAAe;AACjC,WAAK,MAAM,aAAa;AACxB,WAAK,MAAM,SAAS,EAAE,QAAQ,CAAC,EAAE;AACjC,WAAK,MAAM,aAAa,KAAK,SAAS,UAAU;AAChD,YAAM,cAAc,CAACA,OAAM,KAAK,gBAAgBA,IAAG,KAAK;AACxD,YAAM,aAAa,MAAM;AACrB,aAAK,eAAe;AACpB,iBAAS,oBAAoB,aAAa,WAAW;AACrD,iBAAS,oBAAoB,YAAY,UAAU;AAAA,MACvD;AACA,eAAS,iBAAiB,aAAa,WAAW;AAClD,eAAS,iBAAiB,YAAY,UAAU;AAAA,IACpD,GAAG,EAAE,SAAS,MAAM,CAAC;AACrB,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,SAAS,QAAQ;AACzB,SAAK,uBAAuB,MAAM;AAC9B,YAAM,qBAAqB,KAAK,SAAS,SAAS;AAAA,IACtD;AACA,SAAK,mBAAmB,MAAM;AAC1B,YAAM,EAAE,MAAM,OAAO,IAAI,KAAK;AAC9B,UAAI,OAAO,WAAW,YAAY;AAC9B,aAAK,qBAAqB;AAC1B,cAAM,WAAW,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,KAAK,KAAK,GAAG,EAAE,gBAAgB,GAAG,KAAK,SAAS,UAAU,MAAM,OAAO,IAAI,cAAc,GAAG,KAAK,SAAS,QAAQ,MAAM,OAAO,GAAG,CAAC;AACjM,aAAK,SAAS,KAAK,MAAM,GAAG,cAAc,OAAO,GAAG,MAAM,QAAQ,CAAC;AAAA,MACvE;AAAA,IACJ;AACA,SAAK,uBAAuB,MAAM;AAC9B,YAAM,qBAAqB,KAAK,SAAS,SAAS;AAAA,IACtD;AACA,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,WAAW,KAAK,eAAe;AAAA,EACxC;AAAA,EACA,iBAAiB;AACb,WAAO;AAAA,MACH,SAAS,SAAS,cAAc,KAAK;AAAA,MACrC,WAAW,SAAS,cAAc,KAAK;AAAA,MACvC,KAAK,SAAS,cAAc,KAAK;AAAA,IACrC;AAAA,EACJ;AAAA,EACA,uBAAuB;AACnB,oBAAgB,qBAAqB,KAAK,QAAQ,KAAK,OAAO,KAAK,SAAS,GAAG;AAAA,EACnF;AAAA,EACA,oBAAoB;AAChB,UAAM,EAAE,cAAc,eAAe,IAAI,KAAK,QAAQ,KAAK;AAC3D,SAAK,SAAS,QAAQ,aAAa,SAAS,YAAY;AACxD,SAAK,SAAS,QAAQ,YAAY,KAAK,SAAS,SAAS;AACzD,SAAK,SAAS,UAAU,aAAa,SAAS,cAAc;AAC5D,SAAK,SAAS,UAAU,YAAY,KAAK,SAAS,GAAG;AAAA,EACzD;AAAA,EACA,2BAA2B;AACvB,UAAM,qBAAqB,IAAI,mBAAmB,KAAK,UAAU,KAAK,QAAQ,KAAK,gBAAgB;AACnG,uBAAmB,uBAAuB;AAAA,EAC9C;AAAA,EACA,sBAAsB;AAClB,UAAM,gBAAgB,IAAI,iBAAiB,KAAK,UAAU,KAAK,gBAAgB;AAC/E,UAAM,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,GAAG,UAAU;AACpC,YAAM,MAAM,cAAc,mBAAmB,KAAK;AAClD,WAAK,SAAS,UAAU,YAAY,GAAG;AAAA,IAC3C,CAAC;AAAA,EACL;AAAA,EACA,sBAAsB;AAClB,SAAK,SAAS,UAAU,iBAAiB,SAAS,MAAM;AACpD,UAAI;AACJ,YAAM,WAAW,MAAM,SAAS;AAChC,oBAAc,KAAK,SAAS,cAAc,sBAAsB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAChH,WAAK,qBAAqB;AAC1B,WAAK,yBAAyB;AAC9B,WAAK,SAAS,UAAU,aAAa,SAAS,0CAA0C,UAAU,OAAO,MAAM,KAAK,KAAK,QAAQ,KAAK,MAAM,cAAc,EAAE;AAC5J,WAAK,oBAAoB;AAAA,IAC7B,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB;AAChB,aAAS,iBAAiB,SAAS,CAAC,MAAM;AACtC,YAAM,SAAS,EAAE;AACjB,YAAM,gBAAgB,KAAK,SAAS,UAAU,SAAS,MAAM,KACzD,OAAO,MAAM,YACT,UAAU,UAAU,SAAS,aAAa,UAAU,SAAS;AACrE,UAAI,CAAC,eAAe;AAChB,aAAK,qBAAqB;AAC1B,aAAK,qBAAqB;AAAA,MAC9B;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,aAAa;AACT,SAAK,kBAAkB;AACvB,SAAK,qBAAqB;AAC1B,UAAM,EAAE,SAAS,IAAI,KAAK,QAAQ,OAAO;AACzC,QAAI,CAAC;AACD,aAAO,EAAE,KAAK,KAAK,SAAS,UAAU;AAC1C,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,WAAO;AAAA,MACH,KAAK,KAAK,SAAS;AAAA,IACvB;AAAA,EACJ;AACJ;AAEA,IAAM,cAAc,MAAM,OAAO;AAAA,EAC7B,MAAM;AAAA,EACN,aAAa;AACT,QAAI;AACJ,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,IAAI,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,QAAQ,MAAM,CAAC;AAAA,EACpI;AAAA,EACA,gBAAgB;AACZ,QAAI;AACJ,UAAM,SAAS,KAAK,QAAQ;AAC5B,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,IAAI,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,gBAAgB;AAAA,MACzH,SAAS,aAAa,kBAAkB,MAAM;AAAA,MAC9C,WAAW,CAAC,YAAY;AACpB,cAAM,QAAQ,QAAQ,aAAa,OAAO;AAC1C,eAAO,QACD,aAAa,kBAAkB,QAAQ,GAAG,KAAK,IAAI,IACnD,GAAG,QAAQ,MAAM,OAAO;AAAA,MAClC;AAAA,IACJ,GAAG,cAAc;AAAA,MACb,SAAS,aAAa,gBAAgB,MAAM;AAAA,IAChD,EAAE,CAAC;AAAA,EACX;AAAA,EACA,cAAc;AACV,WAAO,CAAC,EAAE,MAAM,QAAQ,OAAO,MAAM;AACjC,YAAM,SAAS,KAAK,QAAQ;AAC5B,YAAM,UAAU;AAAA,QACZ;AAAA,QACA;AAAA,QACA,MAAM,OAAO;AAAA,QACb,QAAQ,OAAO,WAAW,aAAa,SAAS;AAAA,MACpD;AACA,YAAM,WAAW,IAAI,cAAc,SAAS,MAAM;AAClD,aAAO,SAAS,WAAW;AAAA,IAC/B;AAAA,EACJ;AACJ,CAAC;", "names": ["e"]}