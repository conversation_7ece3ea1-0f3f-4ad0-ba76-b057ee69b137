"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTeamService = void 0;
const common_1 = require("@nestjs/common");
const user_team_entity_1 = require("./entities/user-team.entity");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
let UserTeamService = class UserTeamService {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async getAllByRootUserId(rootUserId) {
        return await this.userTeamRepo.find({ where: { rootUserId } });
    }
    async getAllByTeamId(rootUserId, teamId) {
        return await this.userTeamRepo.find({
            where: { rootUserId: rootUserId, teamId: teamId },
        });
    }
    async getMemberInfoByTeamId(rootUserId, teamId) {
        const dataRaw = await this.dataSource.manager.query(`
        SELECT
          ut.id AS id,
          u.userid AS "userId",
          u.email AS email,
          u.name AS fullname,
          t.name AS "teamName",
          t.id AS "teamId",
          ui."firstNameHira" AS "firstNameHira",
          ui."lastNameHira" AS "lastNameHira",
          ut."isAdmin" AS "isAdmin"
        FROM user_team ut
          LEFT JOIN users u ON CAST(u.userid AS TEXT) = ut."userId"
          LEFT JOIN user_info ui ON ui."userId" = ut."userId"
          LEFT JOIN teams t ON ut."teamId" = t.id
        WHERE
          ut."rootUserId" = $1
          AND ut."teamId" = $2
      `, [rootUserId, teamId]);
        for (const data of dataRaw) {
            let fullname = data.firstNameHira || '';
            if (data.lastNameHira)
                fullname += (fullname ? ' ' : '') + data.lastNameHira;
            if (!fullname)
                fullname = data.fullname || '';
            data.fullname = fullname;
        }
        return dataRaw;
    }
    async getTeamsOfMember(rootUserId) {
        const dataRaw = await this.dataSource.manager.query(`
      SELECT
        u.userid AS "userId",
        u.email AS email,
        u.name AS fullname,
        t.name AS "teamName",
        t.id AS "teamId",
        ui."firstNameHira" AS "firstNameHira",
        ui."lastNameHira" AS "lastNameHira",
        ut."isAdmin" AS "isAdmin"
      FROM user_team ut
        LEFT JOIN users u ON CAST(u.userid AS TEXT) = ut."userId"
        LEFT JOIN user_info ui ON ui."userId" = ut."userId"
        LEFT JOIN teams t ON ut."teamId" = t.id
      WHERE
        ut."rootUserId" = $1
    `, [rootUserId]);
        const loginUserRaw = await this.dataSource.manager.query(`
      SELECT
        u.userid AS "userId",
        u.email AS email,
        u.name AS fullname,
        null AS "teamName",
        null AS "teamId",
        ui."firstNameHira" AS "firstNameHira",
        ui."lastNameHira" AS "lastNameHira",
        true AS "isAdmin"
      FROM users u
        LEFT JOIN user_info ui ON ui."userId" = CAST(u.userid AS TEXT)
      WHERE
        CAST(u.userid AS TEXT) = $1
    `, [rootUserId]);
        if (loginUserRaw.length > 0) {
            dataRaw.unshift(loginUserRaw[0]);
        }
        const result = {};
        for (const data of dataRaw) {
            let fullname = data.firstNameHira || '';
            if (data.lastNameHira)
                fullname += (fullname ? ' ' : '') + data.lastNameHira;
            if (!fullname)
                fullname = data.fullname || '';
            const item = result[data.userId] || {
                userId: data.userId,
                email: data.email,
                teamName: '',
                fullname: fullname,
                isAdmin: data.isAdmin || false,
            };
            if (data.teamName) {
                item.teamName += (item.teamName ? ', ' : '') + data.teamName;
            }
            result[data.userId] = item;
        }
        return Object.values(result);
    }
};
exports.UserTeamService = UserTeamService;
__decorate([
    (0, typeorm_2.InjectRepository)(user_team_entity_1.UserTeamEntity),
    __metadata("design:type", typeorm_1.Repository)
], UserTeamService.prototype, "userTeamRepo", void 0);
exports.UserTeamService = UserTeamService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], UserTeamService);
//# sourceMappingURL=user-team.service.js.map