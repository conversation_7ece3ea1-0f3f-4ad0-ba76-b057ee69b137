"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePageVersionsTable1747793781807 = void 0;
class UpdatePageVersionsTable1747793781807 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}page_versions`;
    }
    async up(queryRunner) {
        await queryRunner.dropColumn(this.TABLE_NAME, 'cmsCollectionId');
    }
    async down(queryRunner) {
        console.log(queryRunner);
    }
}
exports.UpdatePageVersionsTable1747793781807 = UpdatePageVersionsTable1747793781807;
//# sourceMappingURL=1747793781807-update-page-versions-table.js.map