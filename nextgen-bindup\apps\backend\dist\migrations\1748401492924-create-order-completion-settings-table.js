"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOrderCompletionSettingsTable1748401492924 = void 0;
const typeorm_1 = require("typeorm");
class CreateOrderCompletionSettingsTable1748401492924 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}order_completion_settings`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isGenerated: true,
                    generationStrategy: 'increment',
                    isPrimary: true,
                },
                {
                    name: 'siteId',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'displayText',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'emailSubject',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'emailHeader',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'emailFooter',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateOrderCompletionSettingsTable1748401492924 = CreateOrderCompletionSettingsTable1748401492924;
//# sourceMappingURL=1748401492924-create-order-completion-settings-table.js.map