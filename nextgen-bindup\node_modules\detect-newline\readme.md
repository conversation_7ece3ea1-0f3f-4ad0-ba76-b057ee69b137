# detect-newline [![Build Status](https://travis-ci.org/sindresorhus/detect-newline.svg?branch=master)](https://travis-ci.org/sindresorhus/detect-newline)

> Detect the dominant newline character of a string


## Install

```
$ npm install detect-newline
```


## Usage

```js
const detectNewline = require('detect-newline');

detectNewline('foo\nbar\nbaz\r\n');
//=> '\n'
```


## API

### detectNewline(string)

Returns the detected newline or `undefined` when no newline character is found.

### detectNewline.graceful(unknown)

Returns the detected newline or `\n` when no newline character is found or the input is not a string.


## Related

- [detect-newline-cli](https://github.com/sindresorhus/detect-newline-cli) - CLI for this module
- [detect-indent](https://github.com/sindresorhus/detect-indent) - Detect the indentation of code


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)
