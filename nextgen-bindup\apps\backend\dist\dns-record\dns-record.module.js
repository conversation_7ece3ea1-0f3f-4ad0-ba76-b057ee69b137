"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DnsRecordModule = void 0;
const common_1 = require("@nestjs/common");
const dns_record_controller_1 = require("./dns-record.controller");
const dns_record_service_1 = require("./dns-record.service");
const dns_record_entity_1 = require("./entities/dns-record.entity");
const typeorm_1 = require("@nestjs/typeorm");
let DnsRecordModule = class DnsRecordModule {
};
exports.DnsRecordModule = DnsRecordModule;
exports.DnsRecordModule = DnsRecordModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([dns_record_entity_1.DnsRecordEntity])],
        controllers: [dns_record_controller_1.DnsRecordController],
        providers: [dns_record_service_1.DnsRecordService],
        exports: [dns_record_service_1.DnsRecordService],
    })
], DnsRecordModule);
//# sourceMappingURL=dns-record.module.js.map