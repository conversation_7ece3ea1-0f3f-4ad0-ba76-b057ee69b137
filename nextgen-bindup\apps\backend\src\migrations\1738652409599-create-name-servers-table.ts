import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateNameserversTable1738652409599 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}name_servers`; // Tên bảng

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: process.env.DATABASE_SCHEMA,
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'projectId',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'type',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'timestamptz',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true,
    );
    await queryRunner.query(
      `alter publication supabase_realtime add table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `alter publication supabase_realtime drop table ${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
    await queryRunner.dropTable(
      `${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`,
    );
  }
}
