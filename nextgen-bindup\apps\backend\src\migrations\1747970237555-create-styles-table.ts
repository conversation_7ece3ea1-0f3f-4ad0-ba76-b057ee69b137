import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableIndex,
} from 'typeorm';

export class UpdateStylesTable1747970237555 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}styles`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const siteIdColumn: TableColumn = new TableColumn({
      name: 'siteId',
      type: 'integer',
      isNullable: false,
      default: '1',
    });
    await queryRunner.addColumn(this.TABLE_NAME, siteIdColumn);

    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'IDX_styles_siteId',
        columnNames: ['siteId'],
        isUnique: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_styles_siteId');
    await queryRunner.dropColumn(this.TABLE_NAME, 'siteId');
  }
}
