import { ColorTheme, TaxMode, TaxRegulation } from '../enums';
export declare class ShopInformationSettingEntity {
    id: number;
    siteId: number;
    isMaintenance: boolean;
    shopName: string;
    logoImages: string[];
    colorTheme: ColorTheme;
    shopUrl: string;
    isSetupGuide: boolean;
    guideUrl: string;
    email: string;
    isAddPrivacy: boolean;
    privacyUrl: string;
    taxMode: TaxMode;
    taxRate: number;
    taxRegulation: TaxRegulation;
    createdAt: Date;
    updatedAt: Date;
}
