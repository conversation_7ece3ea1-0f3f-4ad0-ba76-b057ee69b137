"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCmsArticles1743388643395 = void 0;
const typeorm_1 = require("typeorm");
class CreateCmsArticles1743388643395 {
    constructor() {
        this.TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}cms_articles`;
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            schema: process.env.DATABASE_SCHEMA,
            name: this.TABLE_NAME,
            columns: [
                {
                    name: 'id',
                    type: 'integer',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'cmsCollectionId',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '250',
                    isNullable: false,
                },
                {
                    name: 'data',
                    type: 'jsonb',
                    isNullable: true,
                },
                {
                    name: 'status',
                    type: 'varchar',
                    length: '15',
                    isNullable: false,
                    default: `'draft'`,
                },
                {
                    name: 'siteId',
                    type: 'integer',
                    isNullable: false,
                },
                {
                    name: 'rootUserId',
                    type: 'varchar',
                    length: '36',
                    isNullable: false,
                },
                {
                    name: 'userId',
                    type: 'varchar',
                    length: '36',
                    isNullable: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamptz',
                    isNullable: false,
                    default: 'CURRENT_TIMESTAMP(6)',
                },
            ],
        }), true);
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_cms_articles_cmscollectionid',
            columnNames: ['cmsCollectionId'],
            isUnique: false,
        }));
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_cms_articles_siteid',
            columnNames: ['siteId'],
            isUnique: false,
        }));
        await queryRunner.createIndex(this.TABLE_NAME, new typeorm_1.TableIndex({
            name: 'IDX_cms_articles_rootuserid',
            columnNames: ['rootUserId'],
            isUnique: false,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_cms_articles_cmscollectionid');
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_cms_articles_siteid');
        await queryRunner.dropIndex(this.TABLE_NAME, 'IDX_cms_articles_rootuserid');
        await queryRunner.dropTable(`${process.env.DATABASE_SCHEMA}.${this.TABLE_NAME}`);
    }
}
exports.CreateCmsArticles1743388643395 = CreateCmsArticles1743388643395;
//# sourceMappingURL=1743388643395-create-cms_articles-table.js.map