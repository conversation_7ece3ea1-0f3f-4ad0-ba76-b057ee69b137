import { Module } from '@nestjs/common';
import { ProxyController } from './proxy.controller';
import { ProxyService } from './proxy.service';
import { PaymentModule } from 'src/payment/payment.module';
import { SiteAuthService } from './site-auth.service';
import { ShippingNoteSettingModule } from 'src/shipping-note-settings/shipping-note-settings.module';
import { ShopInformationSettingModule } from 'src/shop-information-settings/shop-information-settings.module';
import { PaymentMethodModule } from 'src/payment-method/payment-method.module';
import { OrderModule } from 'src/order/order.module';
import { ProductStocksModule } from 'src/product-stocks/product-stocks.module';
import { SiteModule } from 'src/site/site.module';
import { OrderCompletionSettingModule } from 'src/order-complete-settings/order-complete-settings.module';

@Module({
  imports: [
    PaymentModule,
    ShippingNoteSettingModule,
    ShopInformationSettingModule,
    PaymentMethodModule,
    OrderCompletionSettingModule,
    OrderModule,
    ProductStocksModule,
    PaymentModule,
    SiteModule,
  ],
  controllers: [ProxyController],
  providers: [ProxyService, SiteAuthService],
  exports: [SiteAuthService],
})
export class ProxyModule {}
