"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentMethodEntity = void 0;
const typeorm_1 = require("typeorm");
let PaymentMethodEntity = class PaymentMethodEntity {
};
exports.PaymentMethodEntity = PaymentMethodEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'integer',
    }),
    __metadata("design:type", Number)
], PaymentMethodEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'siteId',
        type: 'integer',
        nullable: false,
    }),
    __metadata("design:type", Number)
], PaymentMethodEntity.prototype, "siteId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'bankTransfer',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], PaymentMethodEntity.prototype, "bankTransfer", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'postalTransfer',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], PaymentMethodEntity.prototype, "postalTransfer", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'cashOnDelivery',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], PaymentMethodEntity.prototype, "cashOnDelivery", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'stripePaymentGateway',
        type: 'jsonb',
        nullable: true,
    }),
    __metadata("design:type", Object)
], PaymentMethodEntity.prototype, "stripePaymentGateway", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        name: 'createdAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], PaymentMethodEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updatedAt',
        type: 'timestamptz',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP(6)',
    }),
    __metadata("design:type", Date)
], PaymentMethodEntity.prototype, "updatedAt", void 0);
exports.PaymentMethodEntity = PaymentMethodEntity = __decorate([
    (0, typeorm_1.Entity)(`payment_methods`, {
        schema: process.env.DATABASE_SCHEMA,
    })
], PaymentMethodEntity);
//# sourceMappingURL=payment-method.entity.js.map