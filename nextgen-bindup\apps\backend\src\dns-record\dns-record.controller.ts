import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { DnsRecordService } from './dns-record.service';
import { DnsRecordEntity } from './entities/dns-record.entity';

@Controller('dns-record')
@UseGuards(AuthGuard)
export class DnsRecordController {
  constructor(private readonly dnsRecordService: DnsRecordService) {}

  @Post('create')
  async create(@Body() data: DnsRecordEntity) {
    return await this.dnsRecordService.create(data);
  }

  @Put('update/:id')
  async update(
    @Param('id') id: string,
    @Body() data: Partial<DnsRecordEntity>,
  ) {
    return await this.dnsRecordService.update(+id, data);
  }

  @Delete('delete/:id')
  async delete(@Param('id') id: string) {
    return await this.dnsRecordService.delete(+id);
  }

  @Get('site/:siteId')
  async findBySiteId(@Param('siteId') siteId: string) {
    return await this.dnsRecordService.findBySiteId(+siteId);
  }

  @Get('project/:projectId')
  async findByProjectId(@Param('projectId') projectId: string) {
    return await this.dnsRecordService.findByProjectId(+projectId);
  }
}
