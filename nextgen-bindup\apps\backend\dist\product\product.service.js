"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const product_entity_1 = require("./entities/product.entity");
const typeorm_2 = require("typeorm");
const app_exception_1 = require("../common/exceptions/app.exception");
const validate_exception_1 = require("../common/exceptions/validate.exception");
const typeorm_3 = require("typeorm");
const product_stock_entity_1 = require("../product-stocks/entities/product-stock.entity");
const product_stocks_service_1 = require("../product-stocks/product-stocks.service");
const common_util_1 = require("../utils/common.util");
const csv_service_1 = require("./csv.service");
const fs = require("fs");
let ProductService = class ProductService {
    constructor(dataSource, productStockService, csvService) {
        this.dataSource = dataSource;
        this.productStockService = productStockService;
        this.csvService = csvService;
    }
    async getAllProducts(siteId) {
        const products = await this.productRepo.find({
            where: { siteId: siteId, isDeleted: null },
            order: { createdAt: 'DESC' },
        });
        const quantities = await this.productStockService.getBySite(siteId);
        const productQuantities = {};
        quantities.forEach(stock => {
            if (!productQuantities[stock.productId]) {
                productQuantities[stock.productId] = [];
            }
            productQuantities[stock.productId].push(stock);
        });
        products.forEach(product => {
            product.variants.quantities = productQuantities[product.id] || [];
        });
        return products;
    }
    async searchProducts(siteId, page, limit, search, productType, isOrderable) {
        const queryBuilder = this.productRepo.createQueryBuilder('products');
        queryBuilder.where('products.siteId = :siteId', { siteId });
        queryBuilder.andWhere('products.isDeleted IS NULL');
        if (search && search.trim()) {
            queryBuilder.andWhere('(products.name ILIKE :search OR products.code ILIKE :search OR products.title ILIKE :search)', { search: `%${search.trim()}%` });
        }
        if (productType) {
            queryBuilder.andWhere('products.productType = :productType', {
                productType,
            });
        }
        if (isOrderable !== undefined) {
            queryBuilder.andWhere('products.isOrderable = :isOrderable', {
                isOrderable,
            });
        }
        queryBuilder
            .orderBy('products.updatedAt', 'DESC')
            .skip((page - 1) * limit)
            .take(limit);
        const [products, total] = await queryBuilder.getManyAndCount();
        const productIds = products.map(product => product.id);
        const quantities = await this.productStockService.getByProductIds(siteId, productIds);
        const productQuantities = {};
        quantities.forEach(stock => {
            if (!productQuantities[stock.productId]) {
                productQuantities[stock.productId] = [];
            }
            productQuantities[stock.productId].push(stock);
        });
        products.forEach(product => {
            product.variants.quantities = productQuantities[product.id] || [];
        });
        return {
            data: products,
            total: total,
            page: page,
            limit: limit,
            totalPage: Math.ceil(total / limit),
        };
    }
    async updateProductStocks(queryRunner, siteId, productId, variantQuantites) {
        const now = new Date();
        const stocks = await queryRunner.manager
            .getRepository(product_stock_entity_1.ProductStockEntity)
            .find({
            where: { productId: productId },
        });
        for (const variant of variantQuantites) {
            const stock = stocks.find(stock => stock.x === variant.x && stock.y === variant.y);
            if (stock) {
                stock.quantity = variant.quantity;
                this.productStockService.validateProductStockData(stock);
                await queryRunner.manager
                    .getRepository(product_stock_entity_1.ProductStockEntity)
                    .update(stock.id, {
                    quantity: variant.quantity,
                    updatedAt: now,
                });
            }
            else {
                const newStock = new product_stock_entity_1.ProductStockEntity();
                newStock.productId = productId;
                newStock.siteId = siteId;
                newStock.x = variant.x;
                newStock.y = variant.y;
                newStock.quantity = variant.quantity;
                newStock.createdAt = now;
                newStock.updatedAt = now;
                this.productStockService.validateProductStockData(newStock);
                await queryRunner.manager
                    .getRepository(product_stock_entity_1.ProductStockEntity)
                    .save(newStock);
            }
        }
    }
    async validateProductData(product, isUpdate = false, currentId, ignoreValidateFileDownload) {
        if (!product.code) {
            throw new validate_exception_1.ValidateException('cart_management.product.error.code.required');
        }
        else {
            const existingProduct = await this.productRepo.findOneBy({
                siteId: product.siteId,
                code: product.code,
            });
            if (existingProduct && (!isUpdate || existingProduct.id !== currentId)) {
                throw new validate_exception_1.ValidateException('cart_management.product.error.code.duplicated');
            }
            if (product.code.length > 50) {
                throw new validate_exception_1.ValidateException('cart_management.product.error.code.max_length');
            }
            if (product.code.includes(' ')) {
                throw new validate_exception_1.ValidateException('cart_management.product.error.code.contain_space');
            }
            const codeRegex = /^[a-zA-Z0-9-_]+$/;
            if (!codeRegex.test(product.code)) {
                throw new validate_exception_1.ValidateException('cart_management.product.error.code.contain_special_characters');
            }
        }
        if (!product.name) {
            throw new validate_exception_1.ValidateException('cart_management.product.error.name.required');
        }
        if (product.name.length > 100) {
            throw new validate_exception_1.ValidateException('cart_management.product.error.name.max_length');
        }
        if (product.title?.length > 100) {
            throw new validate_exception_1.ValidateException('cart_management.product.error.title.max_length');
        }
        if (product.images?.length > 6) {
            throw new validate_exception_1.ValidateException('cart_management.product.error.images.max_length');
        }
        if ((0, common_util_1.isEmptyNumber)(product.price)) {
            throw new validate_exception_1.ValidateException('cart_management.product.error.price.required');
        }
        if (!(0, common_util_1.isInteger)(product.price, { min: 0, max: common_util_1.MAX_PRICE_VALUE })) {
            throw new validate_exception_1.ValidateException('cart_management.product.error.price.invalid');
        }
        if (!(0, common_util_1.isEmptyNumber)(product.sale) &&
            !(0, common_util_1.isInteger)(product.sale, { min: 0, max: common_util_1.MAX_PRICE_VALUE })) {
            throw new validate_exception_1.ValidateException('cart_management.product.error.sale.invalid');
        }
        if (!ignoreValidateFileDownload &&
            product.productType === "DIGITAL" &&
            !product.fileDownload) {
            throw new validate_exception_1.ValidateException('cart_management.product.error.fileDownload.required');
        }
        switch (product.productVariantType) {
            case "NO_VARIANT": {
                const quantity = product.variants.quantities.find(v => v.x === 0 && v.y === 0);
                if (!quantity || quantity.quantity === null) {
                    throw new validate_exception_1.ValidateException('cart_management.product.error.quantity.required');
                }
                break;
            }
            case "ONE_VARIANT": {
                if (product.variants.variantName1.trim().length === 0) {
                    throw new validate_exception_1.ValidateException('cart_management.product.error.variant_name.required');
                }
                for (let i = 0; i < product.variants.attributes1.length; i++) {
                    const attrLen = product.variants.attributes1[i].trim().length;
                    const quantity = product.variants.quantities.find(v => v.x === i && v.y === 0);
                    if (attrLen === 0 && !quantity?.quantity)
                        continue;
                    if (attrLen > 0 && (!quantity || quantity.quantity === null)) {
                        throw new validate_exception_1.ValidateException('cart_management.product.error.stock.quantity.required');
                    }
                    else if (attrLen === 0 && quantity?.quantity) {
                        throw new validate_exception_1.ValidateException('cart_management.product.error.variant_name.required');
                    }
                }
                break;
            }
            case "TWO_VARIANT": {
                if (product.variants.variantName1.trim().length === 0) {
                    throw new validate_exception_1.ValidateException('cart_management.product.error.variant_name.required');
                }
                if (product.variants.variantName2.trim().length === 0) {
                    throw new validate_exception_1.ValidateException('cart_management.product.error.variant_name.required');
                }
                for (let i = 0; i < product.variants.attributes1.length; i++) {
                    const attr1Len = product.variants.attributes1[i].trim().length;
                    for (let j = 0; j < product.variants.attributes2.length; j++) {
                        const attr2Len = product.variants.attributes2[j].trim().length;
                        const quantity = product.variants.quantities.find(v => v.x === i && v.y === j);
                        if (attr1Len === 0 && attr2Len === 0 && !quantity?.quantity)
                            continue;
                        if (quantity?.quantity) {
                            if (attr1Len === 0 || attr2Len === 0) {
                                throw new validate_exception_1.ValidateException('cart_management.product.error.variant_name.required');
                            }
                        }
                    }
                }
            }
        }
    }
    async create(productEntity) {
        const now = new Date();
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            await this.validateProductData(productEntity);
            const variantQuantites = structuredClone(productEntity.variants.quantities);
            delete productEntity.variants.quantities;
            let product = new product_entity_1.ProductEntity();
            product.siteId = productEntity.siteId;
            product.isOrderable = productEntity.isOrderable;
            product.code = productEntity.code;
            product.name = productEntity.name;
            product.name = productEntity.name;
            product.title = productEntity.title;
            product.description = productEntity.description;
            product.images = productEntity.images;
            product.price = productEntity.price;
            product.sale = productEntity.sale;
            product.purchaseLimitQuantity = productEntity.purchaseLimitQuantity;
            product.individualShippingCharges =
                productEntity.individualShippingCharges;
            product.fileDownload = productEntity.fileDownload;
            product.unlimitedPurchase = productEntity.unlimitedPurchase;
            product.productType = productEntity.productType;
            product.productVariantType = productEntity.productVariantType;
            product.variants = productEntity.variants;
            product.createdAt = now;
            product.updatedAt = now;
            product.priceLabel = productEntity.priceLabel;
            product.saleLabel = productEntity.saleLabel;
            product = await queryRunner.manager
                .getRepository(product_entity_1.ProductEntity)
                .save(product);
            await this.updateProductStocks(queryRunner, product.siteId, product.id, variantQuantites);
            await queryRunner.commitTransaction();
            product.variants.quantities = variantQuantites;
            return product;
        }
        catch (e) {
            console.log(e);
            await queryRunner.rollbackTransaction();
            throw e;
        }
        finally {
            await queryRunner.release();
        }
    }
    async update(id, productData) {
        const product = await this.productRepo.findOneBy({ id: id });
        if (!product)
            throw new app_exception_1.AppException('api.error.product_not_found');
        await this.validateProductData(productData, true, id);
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const variantQuantites = structuredClone(productData.variants.quantities);
            delete productData.id;
            delete productData.variants.quantities;
            productData.updatedAt = new Date();
            await queryRunner.manager
                .getRepository(product_entity_1.ProductEntity)
                .update(id, productData);
            await this.updateProductStocks(queryRunner, product.siteId, product.id, variantQuantites);
            await queryRunner.commitTransaction();
            productData.variants.quantities = variantQuantites;
            return { ...product, ...productData };
        }
        catch (e) {
            console.log(e);
            await queryRunner.rollbackTransaction();
            throw e;
        }
        finally {
            await queryRunner.release();
        }
    }
    async findById(id) {
        const product = await this.productRepo.findOneBy({ id });
        const quantities = await this.productStockService.getByProduct(product.siteId, product.id);
        product.variants.quantities = quantities || [];
        return product;
    }
    async duplicate(id) {
        const originalProduct = await this.findById(id);
        if (!originalProduct) {
            throw new app_exception_1.AppException('api.error.product_not_found');
        }
        const duplicatedProduct = {
            ...originalProduct,
            id: undefined,
            code: await this.generateUniqueCode(originalProduct.code, originalProduct.siteId),
            name: `${originalProduct.name} - Copy`,
            createdAt: undefined,
            updatedAt: undefined,
            isDeleted: false,
        };
        return await this.create(duplicatedProduct);
    }
    async generateUniqueCode(originalCode, siteId) {
        let counter = 1;
        let newCode = `${originalCode}-copy`;
        while (await this.productRepo.findOneBy({ siteId, code: newCode })) {
            counter++;
            newCode = `${originalCode}-copy-${counter}`;
        }
        return newCode;
    }
    async delete(id) {
        const product = await this.productRepo.findOneBy({ id });
        if (!product)
            throw new app_exception_1.AppException('api.error.product_not_found');
        await this.productRepo.update(id, {
            isDeleted: true,
            updatedAt: new Date(),
        });
        return true;
    }
    async findByIds(siteId, ids) {
        const products = await this.productRepo.find({
            where: { siteId: siteId, id: (0, typeorm_2.In)(ids), isDeleted: null },
        });
        return products;
    }
    async uploadCSV(siteId, file) {
        const columns = [
            '商品ID',
            '注文可能',
            '商品管理番号',
            '商品名',
            '商品説明（見出し）',
            '商品説明（本文）',
            '価格見出し',
            '価格',
            'セール価格見出し',
            'セール価格',
            '購入制限数量',
            '個別配送料',
            '無制限購入',
            '商品種別',
        ];
        let results;
        try {
            results = await this.csvService.parseCsv(file.path, columns);
        }
        finally {
            fs.unlinkSync(file.path);
        }
        if (!results || results.length === 0) {
            throw new validate_exception_1.ValidateException('cart_management.product.error.csv.empty');
        }
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        const productCodesInCsv = new Set();
        try {
            for (const row of results) {
                const productId = row['商品ID']
                    ? parseInt(row['商品ID'], 10)
                    : undefined;
                const code = row['商品管理番号']
                    ? String(row['商品管理番号']).trim()
                    : '';
                const name = row['商品名'] ? String(row['商品名']).trim() : '';
                const title = row['商品説明（見出し）']
                    ? String(row['商品説明（見出し）']).trim()
                    : undefined;
                const description = row['商品説明（本文）']
                    ? String(row['商品説明（本文）']).trim()
                    : undefined;
                const priceLabel = row['価格見出し']
                    ? String(row['価格見出し']).trim()
                    : undefined;
                const saleLabel = row['セール価格見出し']
                    ? String(row['セール価格見出し']).trim()
                    : undefined;
                const price = row['価格'] ? parseInt(row['価格'], 10) : undefined;
                const sale = row['セール価格']
                    ? parseInt(row['セール価格'], 10)
                    : undefined;
                const purchaseLimitQuantity = row['購入制限数量']
                    ? parseInt(row['購入制限数量'], 10)
                    : undefined;
                const individualShippingCharges = row['個別配送料']
                    ? parseInt(row['個別配送料'], 10)
                    : undefined;
                const unlimitedPurchaseStr = row['無制限購入']
                    ? String(row['無制限購入']).trim()
                    : '';
                const productTypeStr = row['商品種別']
                    ? String(row['商品種別']).trim().toUpperCase()
                    : '';
                const isOrderableStr = row['注文可能']
                    ? String(row['注文可能']).trim()
                    : '';
                if (productCodesInCsv.has(code)) {
                    throw new validate_exception_1.ValidateException(`cart_management.product.error.code.duplicated_in_csv: ${code}`);
                }
                productCodesInCsv.add(code);
                if (productTypeStr &&
                    !["DIGITAL", "PRODUCT"].includes(productTypeStr)) {
                    throw new validate_exception_1.ValidateException(`cart_management.product.error.product_type.invalid: ${productTypeStr} for code ${code}`);
                }
                const productType = productTypeStr
                    ? productTypeStr
                    : "PRODUCT";
                if (!['1', '0'].includes(unlimitedPurchaseStr)) {
                    throw new validate_exception_1.ValidateException(`cart_management.product.error.unlimited_purchase.invalid: ${unlimitedPurchaseStr} for code ${code}`);
                }
                const unlimitedPurchase = unlimitedPurchaseStr === '1';
                if (!['1', '0'].includes(isOrderableStr)) {
                    throw new validate_exception_1.ValidateException(`cart_management.product.error.is_orderable.invalid: ${isOrderableStr} for code ${code}`);
                }
                const isOrderable = isOrderableStr === '1';
                const productDataPartial = {
                    siteId: siteId,
                    code: code,
                    name: name,
                    title: title,
                    description: description,
                    images: [],
                    priceLabel: priceLabel,
                    saleLabel: saleLabel,
                    price: price,
                    sale: sale ?? 0,
                    purchaseLimitQuantity: purchaseLimitQuantity ?? 0,
                    individualShippingCharges: individualShippingCharges ?? 0,
                    fileDownload: undefined,
                    unlimitedPurchase: unlimitedPurchase,
                    productType: productType,
                    productVariantType: "NO_VARIANT",
                    isOrderable: isOrderable,
                    variants: {
                        variantName1: 'Color',
                        attributes1: ['White'],
                        variantName2: 'Size',
                        attributes2: ['XXL'],
                        quantities: [
                            {
                                x: 0,
                                y: 0,
                                quantity: 0,
                            },
                        ],
                    },
                };
                await this.validateProductData(productDataPartial, !!productId, productId, true);
                let existingProduct = null;
                if (productId) {
                    existingProduct = await queryRunner.manager
                        .getRepository(product_entity_1.ProductEntity)
                        .findOneBy({ id: productId, siteId: siteId });
                    if (!existingProduct) {
                        throw new validate_exception_1.ValidateException(`cart_management.product.error.id.not_found: Product with ID ${productId} not found for update.`);
                    }
                }
                if (existingProduct) {
                    delete productDataPartial.variants;
                    delete productDataPartial.productVariantType;
                    delete productDataPartial.images;
                    delete productDataPartial.fileDownload;
                    await queryRunner.manager
                        .getRepository(product_entity_1.ProductEntity)
                        .update(productId, {
                        ...productDataPartial,
                        updatedAt: new Date(),
                    });
                }
                else {
                    let newProduct = new product_entity_1.ProductEntity();
                    Object.assign(newProduct, productDataPartial);
                    newProduct.createdAt = new Date();
                    newProduct.updatedAt = new Date();
                    newProduct = await queryRunner.manager
                        .getRepository(product_entity_1.ProductEntity)
                        .save(newProduct);
                    const variantQuantites = structuredClone(productDataPartial.variants.quantities);
                    await this.updateProductStocks(queryRunner, siteId, newProduct.id, variantQuantites);
                }
            }
            await queryRunner.commitTransaction();
            return { success: true, message: 'Products uploaded successfully.' };
        }
        catch (e) {
            await queryRunner.rollbackTransaction();
            console.error('CSV Upload Error:', e);
            throw e;
        }
        finally {
            await queryRunner.release();
        }
    }
    async getCSVTemplate() {
        const headers = [
            '商品ID',
            '注文可能',
            '商品管理番号',
            '商品名',
            '商品説明（見出し）',
            '商品説明（本文）',
            '価格見出し',
            '価格',
            'セール価格見出し',
            'セール価格',
            '購入制限数量',
            '個別配送料',
            '無制限購入',
            '商品種別',
        ];
        return await this.csvService.exportToCsv([], headers);
    }
    async downloadCSV(siteId, query) {
        const queryBuilder = this.productRepo.createQueryBuilder('products');
        queryBuilder.where('products.siteId = :siteId', { siteId });
        queryBuilder.andWhere('products.isDeleted IS NULL');
        if (query?.search?.trim()) {
            queryBuilder.andWhere('(products.name ILIKE :search OR products.code ILIKE :search OR products.title ILIKE :search)', { search: `%${query.search.trim()}%` });
        }
        if (query?.productType) {
            queryBuilder.andWhere('products.productType = :productType', {
                productType: query.productType,
            });
        }
        if (query?.isOrderable !== undefined) {
            queryBuilder.andWhere('products.isOrderable = :isOrderable', {
                isOrderable: query.isOrderable,
            });
        }
        const products = await queryBuilder.getMany();
        const headers = [
            '商品ID',
            '注文可能',
            '商品管理番号',
            '商品名',
            '商品説明（見出し）',
            '商品説明（本文）',
            '価格見出し',
            '価格',
            'セール価格見出し',
            'セール価格',
            '購入制限数量',
            '個別配送料',
            '無制限購入',
            '商品種別',
        ];
        const rows = products.map(product => ({
            商品ID: product.id,
            注文可能: product.isOrderable ? '1' : '0',
            商品管理番号: product.code,
            商品名: product.name,
            '商品説明（見出し）': product.title || '',
            '商品説明（本文）': product.description || '',
            価格見出し: product.priceLabel || '',
            価格: product.price,
            セール価格見出し: product.saleLabel || '',
            セール価格: product.sale || '',
            購入制限数量: product.purchaseLimitQuantity || '',
            個別配送料: product.individualShippingCharges || '',
            無制限購入: product.unlimitedPurchase ? '1' : '0',
            商品種別: product.productType,
        }));
        return await this.csvService.exportToCsv(rows, headers);
    }
};
exports.ProductService = ProductService;
__decorate([
    (0, typeorm_1.InjectRepository)(product_entity_1.ProductEntity),
    __metadata("design:type", typeorm_2.Repository)
], ProductService.prototype, "productRepo", void 0);
exports.ProductService = ProductService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_3.DataSource,
        product_stocks_service_1.ProductStocksService,
        csv_service_1.CsvService])
], ProductService);
//# sourceMappingURL=product.service.js.map